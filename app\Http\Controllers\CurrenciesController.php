<?php

namespace App\Http\Controllers;

use App\Models\Currency;
use Illuminate\Http\Request;

class CurrenciesController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $currencies = Currency::all();
        return view('backend.currencies.index',compact('currencies'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string',
            'symbol' => 'required|string',
            'exchange_rate' => 'required|numeric',
            'status' => 'required|string',
            'code' => 'required|string|max:5',
        ]);

        $currency = Currency::create($request->all());

        return response()->json(['message' => 'Currency created successfully', 'currency' => $currency]);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $currency = Currency::findOrFail($id);

        $request->validate([
            'name' => 'required|string',
            'symbol' => 'required|string',
            'exchange_rate' => 'required|numeric',
            'status' => 'required|string',
            'code' => 'required|string|max:5',
        ]);

        $currency->update($request->all());

        return response()->json(['message' => 'Currency updated successfully', 'currency' => $currency]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $currency = Currency::findOrFail($id);
        $currency->delete();

        return response()->json(['message' => 'Currency deleted successfully']);
    }
}

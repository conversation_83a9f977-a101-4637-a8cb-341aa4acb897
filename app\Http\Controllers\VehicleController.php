<?php

namespace App\Http\Controllers;

use App\Http\Controllers\ReferralController;
use App\Http\Controllers\VehicleDocumentController;
use App\Models\Vehicle;
use App\Models\VehicleType;
use App\Models\City;
use App\Models\VehicleImage;
use App\Models\DocumentType;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class VehicleController extends Controller
{
    public function index(Request $request)
    {
        $perPage = $request->input('per_page', 10);
        $query = Vehicle::with(['vehicleType', 'city', 'user', 'images.file']);

        // Filter by status
        if ($request->has('status') && !empty($request->status)) {
            $query->where('status', $request->status);
        }

        // Filter by vehicle type
        if ($request->has('vehicle_type_id') && !empty($request->vehicle_type_id)) {
            $query->where('vehicle_type_id', $request->vehicle_type_id);
        }

        // Filter by city
        if ($request->has('city_id') && !empty($request->city_id)) {
            $query->where('city_id', $request->city_id);
        }

        // Improved search functionality
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('make', 'like', '%' . $search . '%')
                    ->orWhere('model', 'like', '%' . $search . '%')
                    ->orWhere('license_plate', 'like', '%' . $search . '%')
                    ->orWhere('address', 'like', '%' . $search . '%');
            });
        }

        // Order by creation date with newest first by default
        if ($request->has('sort_by') && !empty($request->sort_by)) {
            $direction = $request->input('sort_dir', 'desc');
            $query->orderBy($request->sort_by, $direction);
        } else {
            $query->orderBy('created_at', 'desc');
        }

        // If not admin, only show the user's own vehicles
        if (!auth()->user()->hasRole('admin')) {
            $query->where('user_id', auth()->id());
        }

        // Nearby search if coordinates are provided
        if ($request->has('latitude') && $request->has('longitude') && $request->has('radius')) {
            $lat = $request->input('latitude');
            $lng = $request->input('longitude');
            $radius = $request->input('radius', 10); // Default 10km

            $query->nearby($lat, $lng, $radius);
        }

        $vehicles = $query->paginate($perPage);

        // Load vehicle types for filter dropdown
        $vehicleTypes = VehicleType::where('is_active', true)->orderBy('name')->get();

        // Load cities for filter dropdown
        $cities = City::where('is_active', true)->orderBy('name')->get();

        // For AJAX requests, return JSON
        if ($request->ajax() || $request->wantsJson()) {
            return response()->json($vehicles);
        }

        // For normal requests, return view
        return view('backend.vehicles.index', compact('vehicles', 'vehicleTypes', 'cities'));
    }

    public function create()
    {
        // Get all active vehicle types
        $vehicleTypes = VehicleType::where('is_active', true)->orderBy('name')->get();

        // Get cities for the dropdown
        $cities = City::where('is_active', true)->orderBy('name')->get();

        return view('backend.vehicles.form', compact('vehicleTypes', 'cities'));
    }

    public function store(Request $request)
    {
        // Validate the basic vehicle information
        $validator = Validator::make($request->all(), [
            'vehicle_type_id' => 'required|exists:vehicle_types,id',
            'city_id' => 'required|exists:cities,id',
            'make' => 'required|string|max:255',
            'model' => 'required|string|max:255',
            'year' => 'required|integer|min:1900|max:'.(date('Y')+1),
            'license_plate' => 'required|string|max:12|unique:vehicles',
            'color' => 'required|string|max:255',
            'mileage' => 'required|integer|min:0',
            'transmission' => 'required|in:automatic,manual',
            'fuel_type' => 'required|in:gasoline,diesel,hybrid,electric,plugin_hybrid',
            'seats' => 'required|in:2,4,5,6,7,8+',
            'doors' => 'required|integer|min:1|max:6',
            'features' => 'nullable|array',
            'description' => 'nullable|string',
            'availability' => 'required|in:weekends,weekdays,everyday,custom',
            'advance_notice' => 'required|in:instant,1_hour,3_hours,6_hours,12_hours,24_hours',
            'daily_rate' => 'required|numeric|min:0',
            'weekly_discount' => 'nullable|numeric|min:0|max:100',
            'monthly_discount' => 'nullable|numeric|min:0|max:100',
            'with_driver' => 'sometimes|boolean',
            'security_deposit' => 'nullable|numeric|min:0',
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
            'address' => 'required|string|max:255',
            'images' => 'nullable|string',
            'primary_image' => 'required|string',
        ]);

        // If submitting for approval (not saving as draft), validate required documents
        if (!$request->has('save_draft')) {
            // Get required document types
            $requiredDocumentTypes = DocumentType::where(function($query) {
                $query->where('applies_to', 'vehicle')
                      ->orWhere('applies_to', 'all');
            })
            ->where('is_required', true)
            ->get();

            // Add validation rules for required documents
            foreach ($requiredDocumentTypes as $documentType) {
                $validator->addRules([
                    'documents.'.$documentType->id.'.file_id' => 'required|string',
                ]);

                if ($documentType->has_expiry) {
                    $validator->addRules([
                        'documents.'.$documentType->id.'.expiry_date' => 'required|date|after:today',
                    ]);
                }
            }
        }

        if ($validator->fails()) {
            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // No need to manually encode features as JSON since the model has array casting

        // Create the vehicle
        $vehicle = Vehicle::create([
            'user_id' => auth()->id(),
            'vehicle_type_id' => $request->vehicle_type_id,
            'city_id' => $request->city_id,
            'make' => $request->make,
            'model' => $request->model,
            'year' => $request->year,
            'license_plate' => $request->license_plate,
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
            'address' => $request->address,
            'color' => $request->color,
            'mileage' => $request->mileage,
            'transmission' => $request->transmission,
            'fuel_type' => $request->fuel_type,
            'seats' => $request->seats,
            'doors' => $request->doors,
            'features' => $request->features,
            'description' => $request->description,
            'availability' => $request->availability,
            'advance_notice' => $request->advance_notice,
            'daily_rate' => $request->daily_rate,
            'weekly_discount' => $request->weekly_discount ?? 0,
            'monthly_discount' => $request->monthly_discount ?? 0,
            'with_driver' => $request->has('with_driver'),
            'security_deposit' => $request->security_deposit ?? 0,
            'status' => $request->has('save_draft') ? 'draft' : 'pending' // Set status based on button clicked
        ]);

        // Process images
        $this->processVehicleImages($vehicle, $request);

        // Process documents
        try {
            VehicleDocumentController::processVehicleDocuments($vehicle, $request);
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Error processing documents: ' . $e->getMessage())
                ->withInput();
        }

        // Track if this is a referred user adding their first vehicle
        $user = Auth::user();
        if ($user->referred_by) {
            ReferralController::trackVehicleAdded($user, $vehicle);
        }

        if ($request->wantsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Vehicle created successfully and is awaiting approval',
                'vehicle' => $vehicle->load(['vehicleType', 'city'])
            ]);
        }

        return redirect()->route('vehicles.index')
            ->with('success', 'Vehicle created successfully and is awaiting approval');
    }

    public function edit($id)
    {
        $vehicle = Vehicle::findOrFail($id);

        // Check authorization
        if (!auth()->user()->hasRole('admin') && $vehicle->user_id !== auth()->id()) {
            abort(403, 'You are not authorized to edit this vehicle');
        }

        $vehicleTypes = VehicleType::where('is_active', true)->orderBy('name')->get();
        $cities = City::where('is_active', true)->orderBy('name')->get();

        // Get vehicle images
        $images = $vehicle->images;

        return view('backend.vehicles.form', compact('vehicle', 'vehicleTypes', 'cities', 'images'));
    }

    public function update(Request $request, $id)
    {
        $vehicle = Vehicle::findOrFail($id);

        // Check authorization
        if (!auth()->user()->hasRole('admin') && $vehicle->user_id !== auth()->id()) {
            abort(403, 'You are not authorized to update this vehicle');
        }

        // Validate the basic vehicle information
        $validator = Validator::make($request->all(), [
            'vehicle_type_id' => 'required|exists:vehicle_types,id',
            'city_id' => 'required|exists:cities,id',
            'make' => 'required|string|max:255',
            'model' => 'required|string|max:255',
            'year' => 'required|integer|min:1900|max:'.(date('Y')+1),
            'license_plate' => 'required|string|max:12|unique:vehicles,license_plate,'.$id,
            'color' => 'required|string|max:255',
            'mileage' => 'required|integer|min:0',
            'transmission' => 'required|in:automatic,manual',
            'fuel_type' => 'required|in:gasoline,diesel,hybrid,electric,plugin_hybrid',
            'seats' => 'required|in:2,4,5,6,7,8+',
            'doors' => 'required|integer|min:1|max:6',
            'features' => 'nullable|array',
            'description' => 'nullable|string',
            'availability' => 'required|in:weekends,weekdays,everyday,custom',
            'advance_notice' => 'required|in:instant,1_hour,3_hours,6_hours,12_hours,24_hours',
            'daily_rate' => 'required|numeric|min:0',
            'weekly_discount' => 'nullable|numeric|min:0|max:100',
            'monthly_discount' => 'nullable|numeric|min:0|max:100',
            'with_driver' => 'sometimes|boolean',
            'security_deposit' => 'nullable|numeric|min:0',
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
            'address' => 'required|string|max:255',
            'images' => 'required|string',
            'primary_image' => 'required|string',
        ]);

        if ($validator->fails()) {
            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // No need to manually encode features as JSON since the model has array casting

        // Update the vehicle
        $vehicle->update([
            'vehicle_type_id' => $request->vehicle_type_id,
            'city_id' => $request->city_id,
            'make' => $request->make,
            'model' => $request->model,
            'year' => $request->year,
            'license_plate' => $request->license_plate,
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
            'address' => $request->address,
            'color' => $request->color,
            'mileage' => $request->mileage,
            'transmission' => $request->transmission,
            'fuel_type' => $request->fuel_type,
            'seats' => $request->seats,
            'doors' => $request->doors,
            'features' => $request->features,
            'description' => $request->description,
            'availability' => $request->availability,
            'advance_notice' => $request->advance_notice,
            'daily_rate' => $request->daily_rate,
            'weekly_discount' => $request->weekly_discount ?? 0,
            'monthly_discount' => $request->monthly_discount ?? 0,
            'with_driver' => $request->has('with_driver'),
            'security_deposit' => $request->security_deposit ?? 0,
            // Status doesn't change here to maintain approval workflow
        ]);

        // Process images
        $this->processVehicleImages($vehicle, $request);

        // Process documents
        try {
            VehicleDocumentController::processVehicleDocuments($vehicle, $request);
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Error processing documents: ' . $e->getMessage())
                ->withInput();
        }

        if ($request->wantsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Vehicle updated successfully',
                'vehicle' => $vehicle->load(['vehicleType', 'city'])
            ]);
        }

        return redirect()->route('vehicles.index')
            ->with('success', 'Vehicle updated successfully');
    }

    public function show($id)
    {
        $vehicle = Vehicle::with(['vehicleType', 'city', 'user', 'images', 'documents.documentType', 'documents.file'])->findOrFail($id);

        // Check authorization
        if (!auth()->user()->hasRole(['admin', 'agent']) && $vehicle->user_id !== auth()->id()) {
            abort(403, 'You are not authorized to view this vehicle');
        }

        if (request()->wantsJson()) {
            return response()->json([
                'success' => true,
                'vehicle' => $vehicle
            ]);
        }

        return view('backend.vehicles.show', compact('vehicle'));
    }

    public function destroy($id)
    {
        try {
            $vehicle = Vehicle::findOrFail($id);

            // Check authorization
            if (!auth()->user()->hasRole('admin') && $vehicle->user_id !== auth()->id()) {
                abort(403, 'You are not authorized to delete this vehicle');
            }

            // Delete all images associated with the vehicle
            $vehicle->images()->delete();

            // Soft delete the vehicle
            $vehicle->delete();

            if (request()->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Vehicle deleted successfully'
                ]);
            }

            return redirect()->route('vehicles.index')
                ->with('success', 'Vehicle deleted successfully');
        } catch (\Exception $e) {
            \Log::error('Vehicle Deletion Error: ' . $e->getMessage());

            if (request()->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error deleting vehicle: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Error deleting vehicle: ' . $e->getMessage());
        }
    }

    public function updateStatus(Request $request, $id)
    {
        // Only admins and agents can update status
        if (!auth()->user()->hasRole(['admin', 'agent'])) {
            abort(403, 'You are not authorized to update vehicle status');
        }

        $vehicle = Vehicle::with('documents')->findOrFail($id);

        $validator = Validator::make($request->all(), [
            'status' => 'required|in:draft,pending,active,rejected,inactive',
            'rejection_reason' => 'required_if:status,rejected|nullable|string',
        ]);

        // If activating a vehicle, check that all required documents are approved
        if ($request->status === 'active') {
            $requiredDocumentTypes = DocumentType::where(function($query) {
                $query->where('applies_to', 'vehicle')
                      ->orWhere('applies_to', 'all');
            })
            ->where('is_required', true)
            ->pluck('id');

            $pendingOrRejectedDocs = $vehicle->documents()
                ->whereIn('document_type_id', $requiredDocumentTypes)
                ->where(function($query) {
                    $query->where('status', 'pending')
                          ->orWhere('status', 'rejected');
                })
                ->count();

            if ($pendingOrRejectedDocs > 0) {
                if ($request->wantsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'All required documents must be approved before activating the vehicle'
                    ], 422);
                }

                return redirect()->back()
                    ->with('error', 'All required documents must be approved before activating the vehicle');
            }
        }

        if ($validator->fails()) {
            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $vehicle->update([
            'status' => $request->status,
            'rejection_reason' => $request->rejection_reason,
        ]);

        // Notify owner about status change
        // TODO: Add notification logic here

        if ($request->wantsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Vehicle status updated successfully',
                'vehicle' => $vehicle->fresh()
            ]);
        }

        return redirect()->route('vehicles.index')
            ->with('success', 'Vehicle status updated successfully');
    }

    public function toggleFeatured(Request $request, $id)
    {
        // Only admins can update featured status
        if (!auth()->user()->hasRole('admin')) {
            abort(403, 'You are not authorized to update vehicle featured status');
        }

        $vehicle = Vehicle::findOrFail($id);
        $vehicle->is_featured = !$vehicle->is_featured;
        $vehicle->save();

        if ($request->wantsJson()) {
            return response()->json([
                'success' => true,
                'message' => $vehicle->is_featured ? 'Vehicle marked as featured' : 'Vehicle removed from featured',
                'is_featured' => $vehicle->is_featured
            ]);
        }

        return redirect()->back()
            ->with('success', $vehicle->is_featured ? 'Vehicle marked as featured' : 'Vehicle removed from featured');
    }

    public function submitDraft(Request $request, $id)
    {
        $vehicle = Vehicle::findOrFail($id);

        // Check authorization
        if ($vehicle->user_id !== auth()->id()) {
            abort(403, 'You are not authorized to submit this vehicle');
        }

        // Check if the vehicle is in draft status
        if ($vehicle->status !== 'draft') {
            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Only draft vehicles can be submitted for approval'
                ], 422);
            }

            return redirect()->back()
                ->with('error', 'Only draft vehicles can be submitted for approval');
        }

        // Validate that all required documents are uploaded
        if (!VehicleDocumentController::validateRequiredDocuments($vehicle)) {
            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'All required documents must be uploaded before submitting for approval'
                ], 422);
            }

            return redirect()->back()
                ->with('error', 'All required documents must be uploaded before submitting for approval');
        }

        // Update the status to pending
        $vehicle->update([
            'status' => 'pending'
        ]);

        if ($request->wantsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Vehicle submitted for approval',
                'vehicle' => $vehicle->fresh()
            ]);
        }

        return redirect()->route('vehicles.index')
            ->with('success', 'Vehicle submitted for approval');
    }

    public function searchNearby(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
            'radius' => 'nullable|numeric|min:1|max:100', // Search radius in kilometers
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $lat = $request->latitude;
        $lng = $request->longitude;
        $radius = $request->radius ?? 10; // Default 10km radius

        $query = Vehicle::with(['vehicleType', 'city', 'images'])
            ->where('status', 'active')
            ->nearby($lat, $lng, $radius);

        // Additional filters
        if ($request->has('vehicle_type_id')) {
            $query->where('vehicle_type_id', $request->vehicle_type_id);
        }

        if ($request->has('price_min')) {
            $query->where('daily_rate', '>=', $request->price_min);
        }

        if ($request->has('price_max')) {
            $query->where('daily_rate', '<=', $request->price_max);
        }

        $vehicles = $query->get();

        return response()->json([
            'success' => true,
            'vehicles' => $vehicles,
            'count' => $vehicles->count()
        ]);
    }

    private function determineImageType($index, $total)
    {
        // A simple logic to determine image type based on index
        if ($index === 0) return 'front';
        if ($index === 1 && $total > 1) return 'back';
        if ($index === 2 && $total > 2) return 'interior';
        if ($index === 3 && $total > 3) return 'side';

        return 'additional';
    }

    /**
     * Process vehicle images from the request.
     *
     * @param Vehicle $vehicle
     * @param Request $request
     * @return void
     */
    private function processVehicleImages(Vehicle $vehicle, Request $request)
    {
        // Remove all existing images
        $vehicle->images()->delete();

        // Process main image and gallery images
        if ($request->has('images') && !empty($request->images)) {
            $imageIds = explode(',', $request->images);
            $primaryImageId = $request->primary_image;

            // If primary image is not in the images list, add it
            if (!in_array($primaryImageId, $imageIds) && !empty($primaryImageId)) {
                $imageIds[] = $primaryImageId;
            }

            foreach ($imageIds as $index => $fileId) {
                $isPrimary = $fileId === $primaryImageId;
                $imageType = $this->determineImageType($index, count($imageIds));

                VehicleImage::create([
                    'vehicle_id' => $vehicle->id,
                    'file_id' => $fileId,
                    'is_primary' => $isPrimary,
                    'type' => $imageType,
                ]);
            }
        } elseif ($request->has('primary_image') && !empty($request->primary_image)) {
            // If only primary image is provided
            VehicleImage::create([
                'vehicle_id' => $vehicle->id,
                'file_id' => $request->primary_image,
                'is_primary' => true,
                'type' => 'front',
            ]);
        }
    }
}

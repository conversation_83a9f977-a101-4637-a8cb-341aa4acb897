<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('files', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('filename');  // Added to match the model
            $table->string('url');
            $table->integer('fileSize');  // Changed from longText to integer to match the model casting
            $table->string('fileType');   // Added to match the model
            
            // Optional: Keep any of these columns from your original migration if needed
            $table->enum('storage', ['LOCAL', 'S3'])->nullable();
            $table->string('mimeType')->nullable();
            $table->enum('documentType', ['ATTACHMENT', 'IMPORT', 'EXPORT'])->default('ATTACHMENT');
            $table->string('label')->nullable();
            $table->string('description', 500)->nullable();  // Made nullable
            $table->string('bucketName')->nullable();  // Made nullable
            $table->string('key', 255)->nullable();  // Made nullable
            $table->boolean('imported')->default(0);
            $table->boolean('generated')->default(0);
            $table->boolean('deleted')->default(0);
            $table->boolean('archived')->default(0);
            $table->string('name', 255)->nullable();
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('files');
    }
};
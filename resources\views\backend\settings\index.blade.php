<x-app-backend-layout>
    <div class="container bg-white p-6 mx-auto rounded-lg shadow-lg" x-data="settingsApp()">
        <div class="flex justify-between items-center mb-4">
            <h1 class="text-3xl font-semibold text-gray-800">Settings</h1>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            @foreach($settings as $setting)
                <div class="bg-gradient-to-br from-gray-50 to-white border border-gray-300 rounded-xl shadow-md hover:shadow-lg transition duration-200 transform hover:-translate-y-1">
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-800 truncate" title="{{ $setting->key }}">{{ str_replace('_', ' ', ucfirst($setting->key)) }}</h3>
                        <p class="text-sm text-gray-500 mt-2 break-words">{{ $setting->value }}</p>
                    </div>
                    <div class="bg-gray-100 border-t border-gray-300 p-4 flex justify-end rounded-b-xl">
                        <button @click="openEditModal({{ json_encode($setting) }})"
                                class="px-4 py-2 text-sm font-medium text-white bg-blue-500 rounded-lg hover:bg-blue-600 transition duration-200">
                            Edit
                        </button>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Create/Edit Modal -->
        <div x-show="isModalOpen" class="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50" x-cloak>
            <div class="bg-white p-8 rounded-lg w-1/3 relative shadow-lg">
                <button @click="closeModal" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentSetting">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>

                <h2 class="text-xl font-semibold mb-6" x-text="formatKey(form.key)"></h2>

                <form @submit.prevent="updateSetting">
                    <div class="mb-2">
                        <!-- Dynamic Input Rendering -->
                        <template x-if="form.type === 'textarea'">
                            <textarea id="value" x-model="form.value"
                                      class="mt-1 block w-full border border-gray-300 rounded-md p-3 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                      required></textarea>
                        </template>
                        <template x-if="form.type !== 'textarea'">
                            <input :type="form.type" id="value" x-model="form.value"
                                   class="mt-1 block w-full border border-gray-300 rounded-md p-3 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   required />
                        </template>
                        <template x-if="form.type === 'file'">
                            <input type="file" id="file" name="file"
                                   @change="handleFileUpload"
                                   class="mt-1 block w-full border border-gray-300 rounded-md p-3 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   accept="image/*" />
                        </template>
                        <template x-if="form.type === 'file' && previewUrl">
                            <img :src="previewUrl" alt="Preview" class="mt-4 max-w-full rounded-md shadow-md" />
                        </template>
                    </div>

                    <div class="flex justify-between">
                        <button @click="closeModal" type="button" class="bg-gray-500 text-white px-6 py-2 rounded-md hover:bg-gray-600 transition duration-200">Cancel</button>
                        <button type="submit" class="bg-blue-500 text-white px-6 py-2 rounded-md hover:bg-blue-600 transition duration-200">Update</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function settingsApp() {
            return {
                isModalOpen: false,
                isEditMode: false,
                form: {
                    id: null,
                    key: '',
                    value: '',
                    type: 'text'
                },

                openEditModal(setting) {
                    this.isEditMode = true;
                    this.form = {
                        id: setting.id,
                        key: setting.key,
                        value: setting.value,
                        type: setting.type || 'text' // Default to 'text' if no type is provided
                    };
                    this.isModalOpen = true;
                },

                closeModal() {
                    this.isModalOpen = false;
                },

                handleFileUpload(event) {
                    const file = event.target.files[0];
                    if (file) {
                        this.form.file = file;

                        // Generate preview URL for images
                        if (file.type.startsWith('image/')) {
                            this.previewUrl = URL.createObjectURL(file);
                        }
                    }
                },

                async updateSetting() {
                    try {
                        const response = await fetch(`{{ route('settings.update') }}`, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': '{{ csrf_token() }}'
                            },
                            body: JSON.stringify(this.form)
                        });

                        if (!response.ok) {
                            const errorData = await response.json();
                            throw new Error(errorData.message || 'Error updating setting');
                        }

                        Swal.fire('Success', 'Setting has been updated successfully.', 'success');
                        window.location.reload();
                    } catch (error) {
                        Swal.fire('Error', error.message, 'error');
                    }
                },

                formatKey(key) {
                    return key.replace(/_/g, ' ').toUpperCase();
                },
            }
        }
    </script>
</x-app-backend-layout>

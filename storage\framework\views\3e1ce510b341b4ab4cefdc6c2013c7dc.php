<?php if (isset($component)) { $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742 = $attributes; } ?>
<?php $component = App\View\Components\AppBackendLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-backend-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppBackendLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container mx-auto p-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-semibold text-gray-800">Roles</h1>
            <button @click="openModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg shadow-md">
                + Create Role
            </button>
        </div>

        <?php if(session('success')): ?>
            <div class="bg-green-500 text-white p-3 rounded-lg mb-4">
                <?php echo e(session('success')); ?>

            </div>
        <?php endif; ?>

        <div class="bg-white shadow rounded-lg overflow-hidden">
            <table class="w-full border-collapse">
                <thead class="bg-gray-100">
                <tr>
                    <th class="py-3 px-6 text-left text-sm font-semibold text-gray-600">#</th>
                    <th class="py-3 px-6 text-left text-sm font-semibold text-gray-600">Name</th>
                    <th class="py-3 px-6 text-right text-sm font-semibold text-gray-600">Actions</th>
                </tr>
                </thead>
                <tbody>
                <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr class="border-t hover:bg-gray-50">
                        <td class="py-4 px-6 text-gray-800"><?php echo e($role->id); ?></td>
                        <td class="py-4 px-6 text-gray-800"><?php echo e($role->name); ?></td>
                        <td class="py-4 px-6 text-right">
                            <button @click="openModal(<?php echo e(json_encode($role)); ?>)" class="text-blue-500 hover:underline">Edit</button>
                            <a href="<?php echo e(route('roles.permissions.index', $role->id)); ?>" class="text-green-500 hover:underline mx-2">Manage Permissions</a>
                            <form action="<?php echo e(route('roles.destroy', $role->id)); ?>" method="POST" class="inline">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit" class="text-red-500 hover:underline" onclick="return confirm('Are you sure?')">Delete</button>
                            </form>
                        </td>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Modal -->
    <div x-data="{ open: false, role: {} }" x-cloak>
        <div x-show="open" class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center">
            <div class="bg-white rounded-lg p-6 w-1/3 shadow-lg">
                <h2 class="text-xl font-semibold mb-4" x-text="role.id ? 'Edit Role' : 'Create Role'"></h2>
                <form :action="role.id ? `/roles/${role.id}` : '<?php echo e(route('roles.store')); ?>'" method="POST">
                    <?php echo csrf_field(); ?>
                    <template x-if="role.id">
                        <input type="hidden" name="_method" value="PUT">
                    </template>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700">Role Name</label>
                        <input type="text" name="name" x-model="role.name" class="w-full mt-1 border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 p-2">
                    </div>
                    <div class="flex justify-end">
                        <button type="button" @click="open = false" class="bg-gray-500 text-white px-4 py-2 rounded-lg mr-2">Cancel</button>
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">Save</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function roleModal() {
            return {
                isOpen: false,
                isEdit: false,
                roleId: '',
                roleName: '',
                formAction: '<?php echo e(route('roles.store')); ?>',
                modalTitle: 'Create Role',

                openModal() {
                    this.isOpen = true;
                    this.isEdit = false;
                    this.roleId = '';
                    this.roleName = '';
                    this.formAction = '<?php echo e(route('roles.store')); ?>';
                    this.modalTitle = 'Create Role';
                },

                editRole(id, name) {
                    this.isOpen = true;
                    this.isEdit = true;
                    this.roleId = id;
                    this.roleName = name;
                    this.formAction = `/roles/${id}`;
                    this.modalTitle = 'Edit Role';
                },

                closeModal() {
                    this.isOpen = false;
                }
            }
        }
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $attributes = $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $component = $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\carbnb\resources\views\backend\access-control\roles.blade.php ENDPATH**/ ?>
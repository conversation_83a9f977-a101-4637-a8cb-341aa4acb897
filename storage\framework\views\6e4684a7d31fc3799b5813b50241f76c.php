<?php if (isset($component)) { $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742 = $attributes; } ?>
<?php $component = App\View\Components\AppBackendLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-backend-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppBackendLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            <?php echo e(__('File Upload Example')); ?>

        </h2>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Single File Upload</h3>
                    
                    <form action="<?php echo e(route('example.file-upload')); ?>" method="POST" class="space-y-6">
                        <?php echo csrf_field(); ?>
                        
                        <!-- Single File Upload -->
                        <div>
                            <?php if (isset($component)) { $__componentOriginal22dd814e34b3292120e6fee9433ec671 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal22dd814e34b3292120e6fee9433ec671 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.file-upload','data' => ['name' => 'single_file','label' => 'Upload Single File','multiple' => false,'accept' => 'image/*','required' => true]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('file-upload'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'single_file','label' => 'Upload Single File','multiple' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(false),'accept' => 'image/*','required' => true]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal22dd814e34b3292120e6fee9433ec671)): ?>
<?php $attributes = $__attributesOriginal22dd814e34b3292120e6fee9433ec671; ?>
<?php unset($__attributesOriginal22dd814e34b3292120e6fee9433ec671); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal22dd814e34b3292120e6fee9433ec671)): ?>
<?php $component = $__componentOriginal22dd814e34b3292120e6fee9433ec671; ?>
<?php unset($__componentOriginal22dd814e34b3292120e6fee9433ec671); ?>
<?php endif; ?>
                        </div>
                        
                        <div class="mt-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Multiple Files Upload</h3>
                            
                            <!-- Multiple Files Upload -->
                            <div>
                                <?php if (isset($component)) { $__componentOriginal22dd814e34b3292120e6fee9433ec671 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal22dd814e34b3292120e6fee9433ec671 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.file-upload','data' => ['name' => 'multiple_files','label' => 'Upload Multiple Files','multiple' => true,'accept' => 'image/*,.pdf,.doc,.docx','required' => false]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('file-upload'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'multiple_files','label' => 'Upload Multiple Files','multiple' => true,'accept' => 'image/*,.pdf,.doc,.docx','required' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(false)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal22dd814e34b3292120e6fee9433ec671)): ?>
<?php $attributes = $__attributesOriginal22dd814e34b3292120e6fee9433ec671; ?>
<?php unset($__attributesOriginal22dd814e34b3292120e6fee9433ec671); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal22dd814e34b3292120e6fee9433ec671)): ?>
<?php $component = $__componentOriginal22dd814e34b3292120e6fee9433ec671; ?>
<?php unset($__componentOriginal22dd814e34b3292120e6fee9433ec671); ?>
<?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="flex justify-end mt-6">
                            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                Submit
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Include File Management Modal -->
    <?php echo $__env->make('components.file-management.file-management-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $attributes = $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $component = $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\carbnb\resources\views\examples\file-upload-example.blade.php ENDPATH**/ ?>
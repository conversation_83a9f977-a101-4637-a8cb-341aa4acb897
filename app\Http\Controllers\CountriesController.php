<?php

namespace App\Http\Controllers;

use App\Models\Country;
use Illuminate\Http\Request;

class CountriesController extends Controller
{
    // Fetch all countries
    public function index()
    {
        $countries = Country::all();
        
        return view('backend.country.index', compact('countries'));
    }

    // Store a new country
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:10|unique:countries,code',
            'zone_id' => 'required|integer',
            'status' => 'required|in:0,1',
        ]);

        $country = Country::create($validatedData);

        return response()->json([
            'message' => 'Country created successfully',
            'country' => $country
        ]);
    }

    // Update an existing country
    public function update(Request $request, $id)
    {
        $country = Country::findOrFail($id);

        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:10|unique:countries,code,' . $country->id,
            'zone_id' => 'required|integer',
            'status' => 'required|in:0,1',
        ]);

        $country->update($validatedData);

        return response()->json([
            'message' => 'Country updated successfully',
            'country' => $country->fresh()
        ]);
    }

    // Delete a country
    public function destroy($id)
    {
        $country = Country::findOrFail($id);
        $country->delete();

        return response()->json([
            'message' => 'Country deleted successfully'
        ]);
    }
}

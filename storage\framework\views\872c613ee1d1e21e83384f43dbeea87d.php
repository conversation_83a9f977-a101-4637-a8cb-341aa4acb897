<?php
    $user = Auth::user();
    $unreadMessageCount = 0;

    // Get unread message count if the Messages model exists
    if (class_exists('\App\Models\Conversation')) {
        $unreadMessageCount = \App\Models\Conversation::unreadForUser(Auth::id())->count();
    }
?>

<!-- Top bar -->
<header class="bg-white border-b border-gray-200 shadow-sm">
    <div class="flex items-center justify-between p-3 md:p-4 header-container">
        <!-- Mobile menu button -->
        <div class="flex items-center lg:hidden">
            <button id="mobile-menu-button" class="p-2 rounded-full hover:bg-gray-100 text-gray-600 hover:text-gray-900 focus:outline-none">
                <i class="fas fa-bars text-xl"></i>
            </button>
        </div>

        <div class="flex items-center gap-4">
            <!-- Search bar - hidden on mobile -->
            <div class="relative hidden md:block">
                <i class="fas fa-search absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
                <input
                    type="text"
                    placeholder="Search..."
                    class="pl-10 pr-4 py-2 border rounded-full w-48 md:w-64 focus:outline-none focus:ring-2 focus:ring-primary"
                />
            </div>
        </div>

        <div class="flex items-center space-x-3 md:space-x-4">
            <!-- Notifications dropdown -->
            <div class="relative" x-data="{ open: false }">
                <button @click="open = !open" class="p-2 rounded-full hover:bg-gray-100 relative">
                    <i class="fas fa-bell text-gray-600"></i>
                    <span class="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
                </button>

                <!-- Notifications dropdown menu -->
                <div x-show="open" @click.away="open = false"
                     class="absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg py-1 z-50 notification-dropdown"
                     x-transition:enter="transition ease-out duration-100"
                     x-transition:enter-start="transform opacity-0 scale-95"
                     x-transition:enter-end="transform opacity-100 scale-100"
                     x-transition:leave="transition ease-in duration-75"
                     x-transition:leave-start="transform opacity-100 scale-100"
                     x-transition:leave-end="transform opacity-0 scale-95"
                     style="display: none;">
                    <div class="px-4 py-2 border-b border-gray-100">
                        <h3 class="text-sm font-semibold text-gray-700">Notifications</h3>
                    </div>
                    <div class="max-h-60 overflow-y-auto">
                        <!-- Sample notification items -->
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 border-b border-gray-100">
                            <div class="flex items-start">
                                <div class="flex-shrink-0 bg-blue-100 rounded-full p-1">
                                    <i class="fas fa-car text-blue-500"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="font-medium">New booking request</p>
                                    <p class="text-xs text-gray-500 mt-1">30 minutes ago</p>
                                </div>
                            </div>
                        </a>
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 border-b border-gray-100">
                            <div class="flex items-start">
                                <div class="flex-shrink-0 bg-green-100 rounded-full p-1">
                                    <i class="fas fa-check text-green-500"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="font-medium">Your vehicle was approved</p>
                                    <p class="text-xs text-gray-500 mt-1">1 hour ago</p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <a href="#" class="block text-center px-4 py-2 text-sm text-blue-600 font-medium border-t border-gray-100">
                        View all notifications
                    </a>
                </div>
            </div>

            <!-- Messages dropdown -->
            <div class="relative" x-data="{ open: false }">
                <a href="<?php echo e(route('messages.index')); ?>" class="p-2 rounded-full hover:bg-gray-100 relative">
                    <i class="fas fa-envelope text-gray-600"></i>
                    <?php if($unreadMessageCount > 0): ?>
                        <span class="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-500 rounded-full"><?php echo e($unreadMessageCount); ?></span>
                    <?php endif; ?>
                </a>
            </div>

            <!-- User profile dropdown -->
            <div class="relative" x-data="{ open: false }">
                <button @click="open = !open" class="flex items-center space-x-3 focus:outline-none">
                    <div class="w-8 h-8 rounded-full bg-primary flex items-center justify-center text-white">
                        <?php echo e(substr($user->first_name, 0, 1)); ?><?php echo e(substr($user->last_name, 0, 1)); ?>

                    </div>
                    <span class="hidden md:block text-sm font-medium text-gray-700 max-w-[120px] lg:max-w-[180px] truncate"><?php echo e($user->first_name); ?> <?php echo e($user->last_name); ?></span>
                    <i class="fas fa-chevron-down text-xs text-gray-500"></i>
                </button>

                <!-- Profile dropdown menu -->
                <div x-show="open" @click.away="open = false"
                     class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 user-dropdown"
                     x-transition:enter="transition ease-out duration-100"
                     x-transition:enter-start="transform opacity-0 scale-95"
                     x-transition:enter-end="transform opacity-100 scale-100"
                     x-transition:leave="transition ease-in duration-75"
                     x-transition:leave-start="transform opacity-100 scale-100"
                     x-transition:leave-end="transform opacity-0 scale-95"
                     style="display: none;">
                    <a href="<?php echo e(route('profile.edit')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fas fa-user mr-2"></i> Profile
                    </a>
                    <div class="border-t border-gray-100"></div>
                    <a href="<?php echo e(route('logout')); ?>"
                       onclick="event.preventDefault(); document.getElementById('logout-form').submit();"
                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fas fa-sign-out-alt mr-2"></i> Logout
                    </a>
                    <form id="logout-form" action="<?php echo e(route('logout')); ?>" method="POST" class="hidden">
                        <?php echo csrf_field(); ?>
                    </form>
                </div>
            </div>
        </div>
    </div>
</header>
<?php /**PATH C:\laragon\www\carbnb\resources\views\backend\layouts\partials\header.blade.php ENDPATH**/ ?>
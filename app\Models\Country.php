<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Country extends Model
{
    const STATUS_INACTIVE = 0;
    const STATUS_ACTIVE = 1;
    
    protected $fillable = [
        'code',
        'name',
        'zone_id',
        'status',
    ];
    
    protected $casts = [
        'status' => 'integer',
        'zone_id' => 'integer',
    ];
    
    /**
     * Get the status attribute as text
     *
     * @return string
     */
    public function getStatusTextAttribute()
    {
        return $this->status == self::STATUS_ACTIVE ? 'Active' : 'Inactive';
    }
}

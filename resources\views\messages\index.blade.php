<x-app-backend-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                {{ __('Messages') }}
            </h2>
            <a href="{{ route('messages.create') }}" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-500 active:bg-blue-700 focus:outline-none focus:border-blue-700 focus:ring ring-blue-300 disabled:opacity-25 transition ease-in-out duration-150">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                {{ __('New Message') }}
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    @if(session('success'))
                        <div class="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
                            {{ session('error') }}
                        </div>
                    @endif

                    @if($conversations->isEmpty())
                        <div class="text-center py-8">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"></path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">{{ __('No messages') }}</h3>
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">{{ __('Get started by creating a new message.') }}</p>
                            <div class="mt-6">
                                <a href="{{ route('messages.create') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    {{ __('New Message') }}
                                </a>
                            </div>
                        </div>
                    @else
                        <div class="overflow-hidden bg-white shadow sm:rounded-lg">
                            <ul role="list" class="divide-y divide-gray-200">
                                @foreach($conversations as $conversation)
                                    @php
                                        $otherUser = $conversation->getOtherUser(Auth::id());
                                        $isUnread = $conversation->isUnreadFor(Auth::id());
                                        $latestMessage = $conversation->latestMessage;
                                    @endphp
                                    <li class="relative {{ $isUnread ? 'bg-blue-50' : '' }}">
                                        <a href="{{ route('messages.show', $conversation->id) }}" class="block hover:bg-gray-50">
                                            <div class="px-4 py-4 sm:px-6">
                                                <div class="flex items-center justify-between">
                                                    <div class="flex items-center">
                                                        <div class="flex-shrink-0">
                                                            <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center text-gray-600 font-semibold text-lg">
                                                                {{ substr($otherUser->first_name, 0, 1) }}{{ substr($otherUser->last_name, 0, 1) }}
                                                            </div>
                                                        </div>
                                                        <div class="ml-4">
                                                            <div class="text-sm font-medium text-gray-900">
                                                                {{ $otherUser->first_name }} {{ $otherUser->last_name }}
                                                                @if($isUnread)
                                                                    <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                                        {{ __('New') }}
                                                                    </span>
                                                                @endif
                                                            </div>
                                                            <div class="text-sm text-gray-500 truncate max-w-md">
                                                                @if($latestMessage)
                                                                    {{ Str::limit($latestMessage->message, 50) }}
                                                                @else
                                                                    {{ __('No messages yet') }}
                                                                @endif
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="flex flex-col items-end">
                                                        <div class="text-xs text-gray-500">
                                                            @if($conversation->last_message_at)
                                                                {{ $conversation->last_message_at->diffForHumans() }}
                                                            @else
                                                                {{ $conversation->created_at->diffForHumans() }}
                                                            @endif
                                                        </div>
                                                        @if($conversation->booking)
                                                            <span class="mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                                {{ __('Booking') }} #{{ $conversation->booking->booking_number }}
                                                            </span>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </a>
                                    </li>
                                @endforeach
                            </ul>
                        </div>

                        <div class="mt-4">
                            {{ $conversations->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-backend-layout>

<?php

namespace App\Http\Controllers;

use App\Models\Document;
use App\Models\DocumentType;
use App\Models\Driver;
use App\Models\DriverLicense;
use App\Models\DriverAvailability;
use App\Models\File;
use App\Models\City;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Carbon\Carbon;
use Spatie\Permission\Models\Role;

class DriverController extends Controller
{
    /**
     * Show the driver registration form.
     */
    public function register()
    {
        // Check if user is already a driver
        $existingDriver = Driver::where('user_id', Auth::id())->first();
        if ($existingDriver) {
            return redirect()->route('drivers.profile');
        }

        $cities = City::where('is_active', true)->get();
        return view('drivers.register', compact('cities'));
    }



    /**
     * Show the document upload form.
     */
    public function documentsUpload()
    {
        // Check if user is a driver
        $driver = Driver::where('user_id', Auth::id())->first();
        if (!$driver) {
            return redirect()->route('drivers.register')
                ->with('info', 'You need to register as a driver first.');
        }

        // Get document types that apply to drivers
        $documentTypes = DocumentType::where(function($query) {
            $query->where('applies_to', 'driver')
                  ->orWhere('applies_to', 'all');
        })->get();

        return view('drivers.documents.upload', compact('documentTypes'));
    }

    /**
     * Store uploaded documents.
     */
    public function documentsStore(Request $request)
    {
        // Check if user is a driver
        $driver = Driver::where('user_id', Auth::id())->first();
        if (!$driver) {
            return redirect()->route('drivers.register')
                ->with('info', 'You need to register as a driver first.');
        }

        $request->validate([
            'documents' => 'required|array',
            'documents.*.file_id' => 'required|string',
            'documents.*.expiry_date' => 'nullable|date|after:today',
        ]);

        DB::beginTransaction();

        try {
            foreach ($request->documents as $documentTypeId => $documentData) {
                if (empty($documentData['file_id'])) {
                    continue; // Skip if no file was selected
                }

                $documentType = DocumentType::findOrFail($documentTypeId);
                $fileId = $documentData['file_id'];

                // Find the file record
                $fileModel = File::findOrFail($fileId);

                // Check if a document of this type already exists for this user
                $existingDocument = Document::where([
                    'user_id' => Auth::id(),
                    'document_type_id' => $documentTypeId,
                ])->first();

                if ($existingDocument) {
                    // Update existing document
                    $existingDocument->update([
                        'file_id' => $fileId,
                        'file_name' => $fileModel->title,
                        'file_path' => str_replace('storage/', '', $fileModel->url),
                        'expiry_date' => $documentType->has_expiry ? $documentData['expiry_date'] : null,
                        'status' => 'pending', // Reset status to pending when document is updated
                        'rejection_reason' => null, // Clear any previous rejection reason
                    ]);
                } else {
                    // Create new document record
                    Document::create([
                        'user_id' => Auth::id(),
                        'document_type_id' => $documentTypeId,
                        'file_id' => $fileId,
                        'file_name' => $fileModel->title,
                        'file_path' => str_replace('storage/', '', $fileModel->url),
                        'expiry_date' => $documentType->has_expiry ? $documentData['expiry_date'] : null,
                        'status' => 'pending',
                    ]);
                }
            }

            DB::commit();

            return redirect()->route('drivers.profile')
                ->with('success', 'Documents uploaded successfully and are pending review.');
        } catch (\Exception $e) {
            DB::rollBack();

            return redirect()->back()
                ->with('error', 'An error occurred while uploading documents: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Process the driver registration.
     */
    public function storeRegistration(Request $request)
    {
        $validated = $request->validate([
            'bio' => 'nullable|string',
            'license_number' => 'required|string|max:255',
            'license_expiry' => 'required|date|after:today',
            'experience_years' => 'required|string',
            'languages' => 'required|array|min:1',
            'hourly_rate' => 'nullable|numeric|min:0',
            'daily_rate' => 'required|numeric|min:1',
            'available_weekdays' => 'sometimes|boolean',
            'available_weekends' => 'sometimes|boolean',
            'city_id' => 'required|exists:cities,id',
            'license_front_image_id' => 'required|string',
            'license_back_image_id' => 'nullable|string',
            'profile_image_id' => 'required|string',
            'proof_of_address_id' => 'required|string',
        ]);

        // Convert languages to JSON
        $validated['languages'] = json_encode($validated['languages']);
        $validated['user_id'] = Auth::id();
        $validated['status'] = 'pending';

        // Create driver record
        $driver = Driver::create($validated);

        // Get file records
        $frontImageFile = File::findOrFail($request->license_front_image_id);
        $backImageFile = null;
        if ($request->license_back_image_id) {
            $backImageFile = File::findOrFail($request->license_back_image_id);
        }
        $profileImageFile = File::findOrFail($request->profile_image_id);
        $proofOfAddressFile = File::findOrFail($request->proof_of_address_id);

        // Create driver license
        $driver->license()->create([
            'license_number' => $validated['license_number'],
            'issuing_country' => $request->input('issuing_country', 'Default Country'),
            'issue_date' => $request->input('issue_date', now()),
            'expiry_date' => $validated['license_expiry'],
            'license_class' => $request->input('license_class', 'Standard'),
            'front_image' => $frontImageFile->url,
            'back_image' => $backImageFile ? $backImageFile->url : null,
            'status' => 'pending'
        ]);

        // Update user profile image
        Auth::user()->update(['profile_image' => $profileImageFile->url]);

        // Create proof of address document
        Document::create([
            'user_id' => Auth::id(),
            'document_type_id' => DocumentType::where('name', 'Proof of Address')->first()->id,
            'file_id' => $proofOfAddressFile->id,
            'file_name' => $proofOfAddressFile->title,
            'file_path' => str_replace('storage/', '', $proofOfAddressFile->url),
            'expiry_date' => null,
            'status' => 'pending',
        ]);

        // Create license document
        Document::create([
            'user_id' => Auth::id(),
            'document_type_id' => DocumentType::where('name', 'Driving License')->first()->id,
            'file_id' => $frontImageFile->id,
            'file_name' => $frontImageFile->title,
            'file_path' => str_replace('storage/', '', $frontImageFile->url),
            'expiry_date' => $validated['license_expiry'],
            'status' => 'pending',
        ]);

        // Assign driver role to user
        $user = User::find(Auth::id());
        $driverRole = Role::where('name', 'driver')->first();
        if ($driverRole && !$user->hasRole('driver')) {
            $user->assignRole($driverRole);
        }

        flash('Your driver application has been submitted successfully! It will be reviewed by our team.')->success();
        return redirect()->route('drivers.profile');
    }

    /**
     * Show the driver profile.
     */
    public function profile()
    {
        // Check if user is already a driver
        $driver = Driver::where('user_id', Auth::id())
            ->with(['license', 'user', 'city'])
            ->first();

        // If user is not a driver, redirect to registration page
        if (!$driver) {
            return redirect()->route('drivers.register')
                ->with('info', 'You need to register as a driver first.');
        }

        $documents = Document::where('user_id', Auth::id())
            ->with('documentType')
            ->get();

        return view('drivers.profile', compact('driver', 'documents'));
    }

    /**
     * Show the form for editing the driver profile.
     */
    public function editProfile()
    {
        // Check if user is already a driver
        $driver = Driver::where('user_id', Auth::id())
            ->with(['license', 'user', 'city'])
            ->first();

        // If user is not a driver, redirect to registration page
        if (!$driver) {
            return redirect()->route('drivers.register')
                ->with('info', 'You need to register as a driver first.');
        }

        $cities = City::where('is_active', true)->get();
        $languages = json_decode($driver->languages) ?? [];

        return view('drivers.edit-profile', compact('driver', 'cities', 'languages'));
    }

    /**
     * Update the driver profile.
     */
    public function updateProfile(Request $request)
    {
        // Check if user is already a driver
        $driver = Driver::where('user_id', Auth::id())->first();

        // If user is not a driver, redirect to registration page
        if (!$driver) {
            return redirect()->route('drivers.register')
                ->with('info', 'You need to register as a driver first.');
        }

        $validated = $request->validate([
            'bio' => 'nullable|string',
            'experience_years' => 'required|string',
            'languages' => 'required|array|min:1',
            'hourly_rate' => 'nullable|numeric|min:0',
            'daily_rate' => 'required|numeric|min:1',
            'available_weekdays' => 'sometimes|boolean',
            'available_weekends' => 'sometimes|boolean',
            'city_id' => 'required|exists:cities,id',
            'profile_image' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
        ]);

        // Convert languages to JSON
        $validated['languages'] = json_encode($validated['languages']);

        // Update driver record
        $driver->update($validated);

        // Update user profile image
        if ($request->hasFile('profile_image')) {
            $profileImagePath = $request->file('profile_image')->store('profile-images', 'public');
            Auth::user()->update(['profile_image' => $profileImagePath]);
        }

        flash('Your driver profile has been updated successfully!')->success();
        return redirect()->route('drivers.profile');
    }

    /**
     * Show the driver availability management page.
     */
    public function availability()
    {
        // Check if user is already a driver
        $driver = Driver::where('user_id', Auth::id())->first();

        // If user is not a driver, redirect to registration page
        if (!$driver) {
            return redirect()->route('drivers.register')
                ->with('info', 'You need to register as a driver first.');
        }

        // Get next 30 days for availability calendar
        $startDate = Carbon::today();
        $endDate = Carbon::today()->addDays(30);

        $existingAvailability = DriverAvailability::where('driver_id', $driver->id)
            ->whereBetween('date', [$startDate, $endDate])
            ->get()
            ->keyBy('date');

        $calendar = [];
        for ($date = $startDate->copy(); $date->lte($endDate); $date->addDay()) {
            $dateString = $date->format('Y-m-d');

            if (isset($existingAvailability[$dateString])) {
                $calendar[$dateString] = $existingAvailability[$dateString];
            } else {
                // Default availability based on weekday/weekend settings
                $isWeekend = $date->isWeekend();
                $isAvailable = ($isWeekend && $driver->available_weekends) ||
                    (!$isWeekend && $driver->available_weekdays);

                $calendar[$dateString] = [
                    'date' => $dateString,
                    'is_available' => $isAvailable,
                    'custom_price' => null
                ];
            }
        }

        return view('drivers.availability', compact('driver', 'calendar'));
    }

    /**
     * Update the driver availability.
     */
    public function updateAvailability(Request $request)
    {
        // Check if user is already a driver
        $driver = Driver::where('user_id', Auth::id())->first();

        // If user is not a driver, redirect to registration page
        if (!$driver) {
            return redirect()->route('drivers.register')
                ->with('info', 'You need to register as a driver first.');
        }

        $validated = $request->validate([
            'dates' => 'required|array',
            'dates.*.date' => 'required|date',
            'dates.*.is_available' => 'required|boolean',
            'dates.*.custom_price' => 'nullable|numeric|min:0',
        ]);

        foreach ($validated['dates'] as $dateData) {
            DriverAvailability::updateOrCreate(
                [
                    'driver_id' => $driver->id,
                    'date' => $dateData['date'],
                ],
                [
                    'is_available' => $dateData['is_available'],
                    'custom_price' => $dateData['custom_price'],
                ]
            );
        }

        flash('Your availability has been updated successfully!')->success();
        return redirect()->route('drivers.availability');
    }
}

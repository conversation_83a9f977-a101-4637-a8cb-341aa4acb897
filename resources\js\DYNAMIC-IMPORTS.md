# Dynamic Imports Guide

This document explains how to use the dynamically imported libraries in your application.

## Why Dynamic Imports?

Dynamic imports help reduce the initial bundle size by loading JavaScript libraries only when they're needed. This improves page load times and overall performance.

## Available Dynamic Imports

The following libraries are loaded dynamically:

- **html2pdf**: For generating PDF documents
- **LightGallery**: For image galleries
- **FilePond**: For file uploads
- **Swiper**: For sliders and carousels

## How to Use

### Using Data Attributes (Recommended)

The simplest way to use these libraries is with data attributes:

#### LightGallery

```html
<div data-gallery>
    <a href="image1.jpg">
        <img src="image1-thumb.jpg" alt="Image 1">
    </a>
    <a href="image2.jpg">
        <img src="image2-thumb.jpg" alt="Image 2">
    </a>
</div>
```

With custom options:

```html
<div data-gallery data-gallery-options='{"download": false, "counter": false}'>
    <!-- Gallery items -->
</div>
```

#### FilePond

```html
<input type="file" data-filepond>
```

With custom options:

```html
<input type="file" data-filepond data-filepond-options='{"allowMultiple": true, "maxFiles": 5}'>
```

#### Swiper

```html
<div class="swiper" data-swiper>
    <div class="swiper-wrapper">
        <div class="swiper-slide">Slide 1</div>
        <div class="swiper-slide">Slide 2</div>
    </div>
    <div class="swiper-pagination"></div>
    <div class="swiper-button-next"></div>
    <div class="swiper-button-prev"></div>
</div>
```

With custom options:

```html
<div class="swiper" data-swiper data-swiper-options='{"autoplay": {"delay": 5000}, "loop": false}'>
    <!-- Swiper content -->
</div>
```

### Using JavaScript API

You can also initialize these libraries programmatically:

#### html2pdf

```javascript
import { initHtml2pdf } from './dynamic-imports';

// Generate PDF from an element
document.getElementById('export-pdf-button').addEventListener('click', () => {
    initHtml2pdf('#content-to-export', {
        filename: 'exported-document.pdf',
        margin: 15
    });
});
```

#### LightGallery

```javascript
import { initLightGallery } from './dynamic-imports';

// Initialize a gallery
initLightGallery('.gallery', {
    thumbnail: true,
    animateThumb: false
});
```

#### FilePond

```javascript
import { initFilePond } from './dynamic-imports';

// Initialize file upload
const pond = initFilePond('input[type="file"].upload', {
    allowMultiple: true,
    maxFiles: 5,
    server: {
        process: '/api/upload',
        revert: '/api/revert'
    }
});
```

#### Swiper

```javascript
import { initSwiper } from './dynamic-imports';

// Initialize a slider
const slider = initSwiper('.my-slider', {
    slidesPerView: 3,
    spaceBetween: 30,
    autoplay: {
        delay: 3000
    }
});
```

## Manual Loading

If you need to load a library without initializing it immediately, you can use the global loader functions:

```javascript
// Load html2pdf
window.loadHtml2pdf().then(html2pdf => {
    // Use html2pdf here
});

// Load LightGallery
window.loadLightGallery().then(lightGallery => {
    // Use lightGallery here
});

// Load FilePond
window.loadFilePond().then(FilePond => {
    // Use FilePond here
});

// Load Swiper
window.loadSwiper().then(() => {
    // Use Swiper here (available as window.Swiper)
    // Modules available as window.SwiperModules.Navigation, etc.
});
```

<x-app-layout>
<div class="container py-8">
    <div class="mb-6">
        <a href="javascript:history.back()" class="text-primary hover:text-primary-dark flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clip-rule="evenodd" />
            </svg>
            Back to Booking Details
        </a>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                    <h1 class="text-2xl font-bold text-gray-800">Booking Summary</h1>
                </div>

                <div class="p-6">
                    <div class="mb-8">
                        <h2 class="text-xl font-semibold mb-4 text-gray-700">Vehicle Details</h2>

                        <div class="flex items-center">
                            @if($vehicle->primaryImage)
                                <img src="{{ asset('storage/' . $vehicle->primaryImage->file->path) }}" alt="{{ $vehicle->make }} {{ $vehicle->model }}" class="w-24 h-24 object-cover rounded-md mr-4">
                            @else
                                <div class="w-24 h-24 bg-gray-200 rounded-md mr-4 flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                </div>
                            @endif
                            <div>
                                <h3 class="font-bold text-gray-800 text-lg">{{ $vehicle->make }} {{ $vehicle->model }}</h3>
                                <p class="text-gray-600">{{ $vehicle->year }} · {{ $vehicle->vehicleType->name }}</p>
                                <p class="text-gray-600 mt-1">Hosted by {{ $vehicle->user->name }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="mb-8">
                        <h2 class="text-xl font-semibold mb-4 text-gray-700">Trip Details</h2>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h3 class="font-medium text-gray-700 mb-2">Dates</h3>
                                <p class="text-gray-800">{{ $pickupDate->format('D, M d, Y') }} - {{ $returnDate->format('D, M d, Y') }}</p>
                                <p class="text-gray-600 text-sm mt-1">{{ $days }} days</p>
                            </div>

                            <div>
                                <h3 class="font-medium text-gray-700 mb-2">Pickup & Return</h3>
                                <p class="text-gray-800">Pickup: {{ $bookingData['pickup_location'] }}</p>
                                <p class="text-gray-800 mt-1">Return: {{ $bookingData['return_location'] }}</p>
                            </div>
                        </div>
                    </div>

                    @if($driver)
                    <div class="mb-8">
                        <h2 class="text-xl font-semibold mb-4 text-gray-700">Driver Details</h2>

                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-gray-200 rounded-full mr-4 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                            </div>
                            <div>
                                <h3 class="font-bold text-gray-800">{{ $driver->user->name }}</h3>
                                <p class="text-gray-600 text-sm">{{ $driver->experience_years }} years experience</p>
                            </div>
                        </div>
                    </div>
                    @endif

                    @if(!empty($bookingData['special_requests']))
                    <div class="mb-8">
                        <h2 class="text-xl font-semibold mb-4 text-gray-700">Special Requests</h2>

                        <div class="bg-gray-50 p-4 rounded-md">
                            <p class="text-gray-700">{{ $bookingData['special_requests'] }}</p>
                        </div>
                    </div>
                    @endif

                    <div class="mt-8">
                        <h2 class="text-xl font-semibold mb-4 text-gray-700">Cancellation Policy</h2>

                        <div class="bg-gray-50 p-4 rounded-md">
                            <p class="text-gray-700">Free cancellation up to 48 hours before pickup. After that, a fee of 50% of the total booking amount will be charged.</p>
                        </div>
                    </div>

                    <form action="{{ route('booking.store') }}" method="POST" class="mt-8">
                        @csrf
                        <div class="flex justify-end">
                            <button type="submit" class="inline-flex items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                Confirm & Proceed to Payment
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-md overflow-hidden sticky top-24">
                <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                    <h2 class="text-xl font-bold text-gray-800">Price Details</h2>
                </div>

                <div class="p-6">
                    <div class="mb-4">
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-600">${{ number_format($vehicle->daily_rate, 2) }} × {{ $days }} days</span>
                            <span class="font-medium">${{ number_format($vehiclePrice + $discountAmount, 2) }}</span>
                        </div>

                        @if($discountAmount > 0)
                        <div class="flex justify-between mb-2 text-green-600">
                            <span>{{ $days >= 30 ? 'Monthly' : 'Weekly' }} discount</span>
                            <span>-${{ number_format($discountAmount, 2) }}</span>
                        </div>
                        @endif

                        @if($driverPrice > 0)
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-600">Driver (${{ number_format($driver->daily_rate, 2) }} × {{ $days }} days)</span>
                            <span class="font-medium">${{ number_format($driverPrice, 2) }}</span>
                        </div>
                        @endif
                    </div>

                    <div class="border-t border-gray-200 pt-4 mb-4">
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-600">Subtotal</span>
                            <span class="font-medium">${{ number_format($subtotal, 2) }}</span>
                        </div>
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-600">Tax (10%)</span>
                            <span class="font-medium">${{ number_format($taxAmount, 2) }}</span>
                        </div>
                    </div>

                    <div class="border-t border-gray-200 pt-4 mb-4">
                        <div class="flex justify-between">
                            <span class="text-lg font-bold">Total</span>
                            <span class="text-lg font-bold">${{ number_format($totalAmount, 2) }}</span>
                        </div>
                    </div>

                    @if($securityDeposit > 0)
                    <div class="border-t border-gray-200 pt-4 mb-4">
                        <div class="flex justify-between text-gray-600">
                            <span>Security deposit</span>
                            <span>${{ number_format($securityDeposit, 2) }}</span>
                        </div>
                        <p class="text-sm text-gray-500 mt-1">Refundable if no damage occurs</p>
                    </div>
                    @endif

                    <div class="bg-blue-50 p-4 rounded-md mt-4">
                        <div class="flex">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                            </svg>
                            <div>
                                <p class="text-sm text-blue-700">You won't be charged until the owner accepts your booking request.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</x-app-layout>

<x-app-backend-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Driver Availability') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="mb-6">
                        <h3 class="text-lg font-medium text-gray-900">{{ __('Manage Your Availability') }}</h3>
                        <p class="mt-1 text-sm text-gray-600">
                            {{ __('Set your availability for the next 30 days. You can mark days as available or unavailable, and set custom prices for specific dates.') }}
                        </p>
                    </div>

                    <form method="POST" action="{{ route('drivers.update-availability') }}" class="space-y-8">
                        @csrf

                        <div class="bg-gray-50 p-6 rounded-lg">
                            <div class="mb-4 flex items-center justify-between">
                                <h4 class="text-md font-medium text-gray-900">{{ __('Default Settings') }}</h4>
                                <div class="flex space-x-4">
                                    <div class="flex items-center">
                                        <div class="w-4 h-4 bg-green-100 border border-green-200 rounded-full mr-2"></div>
                                        <span class="text-sm text-gray-600">{{ __('Available') }}</span>
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-4 h-4 bg-red-100 border border-red-200 rounded-full mr-2"></div>
                                        <span class="text-sm text-gray-600">{{ __('Unavailable') }}</span>
                                    </div>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <div>
                                    <p class="text-sm text-gray-600 mb-2">{{ __('Your default availability:') }}</p>
                                    <ul class="text-sm text-gray-700">
                                        <li class="flex items-center">
                                            <span class="w-4 h-4 {{ $driver->available_weekdays ? 'bg-green-100 border-green-200' : 'bg-red-100 border-red-200' }} border rounded-full mr-2"></span>
                                            {{ __('Weekdays') }}: {{ $driver->available_weekdays ? __('Available') : __('Unavailable') }}
                                        </li>
                                        <li class="flex items-center mt-1">
                                            <span class="w-4 h-4 {{ $driver->available_weekends ? 'bg-green-100 border-green-200' : 'bg-red-100 border-red-200' }} border rounded-full mr-2"></span>
                                            {{ __('Weekends') }}: {{ $driver->available_weekends ? __('Available') : __('Unavailable') }}
                                        </li>
                                    </ul>
                                    <p class="text-sm text-gray-500 mt-2">
                                        {{ __('You can change these defaults in your') }}
                                        <a href="{{ route('drivers.edit-profile') }}" class="text-indigo-600 hover:text-indigo-900">{{ __('profile settings') }}</a>.
                                    </p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600 mb-2">{{ __('Your rates:') }}</p>
                                    <ul class="text-sm text-gray-700">
                                        <li>{{ __('Daily Rate') }}: {{ number_format($driver->daily_rate, 2) }}</li>
                                        @if($driver->hourly_rate)
                                            <li class="mt-1">{{ __('Hourly Rate') }}: {{ number_format($driver->hourly_rate, 2) }}</li>
                                        @endif
                                    </ul>
                                </div>
                            </div>

                            <div class="mb-4">
                                <h4 class="text-md font-medium text-gray-900 mb-2">{{ __('Calendar') }}</h4>
                                <p class="text-sm text-gray-600 mb-4">
                                    {{ __('Click on a date to toggle availability or set a custom price.') }}
                                </p>
                            </div>

                            <div class="grid grid-cols-7 gap-2">
                                <!-- Day headers -->
                                @foreach(['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'] as $dayName)
                                    <div class="text-center font-medium text-gray-700 text-sm py-2">
                                        {{ $dayName }}
                                    </div>
                                @endforeach

                                <!-- Calendar days -->
                                @php
                                    $startDate = \Carbon\Carbon::today();
                                    $firstDayOfMonth = $startDate->copy()->startOfMonth();
                                    $startingDayOfWeek = $firstDayOfMonth->dayOfWeek;

                                    // Add empty cells for days before the first day of the month
                                    for ($i = 0; $i < $startingDayOfWeek; $i++) {
                                        echo '<div class="h-24 border border-gray-200 rounded-md bg-gray-50"></div>';
                                    }
                                @endphp

                                @foreach($calendar as $date => $availability)
                                    @php
                                        $dateObj = \Carbon\Carbon::parse($date);
                                        $isAvailable = $availability['is_available'] ?? false;
                                        $customPrice = $availability['custom_price'] ?? null;
                                        $isToday = $dateObj->isToday();
                                        $isWeekend = $dateObj->isWeekend();
                                    @endphp
                                    <div class="h-24 border {{ $isToday ? 'border-indigo-300 ring-1 ring-indigo-300' : 'border-gray-200' }} rounded-md overflow-hidden {{ $isWeekend ? 'bg-gray-50' : 'bg-white' }}">
                                        <div class="p-2">
                                            <div class="flex justify-between items-center mb-2">
                                                <span class="text-sm font-medium {{ $isToday ? 'text-indigo-600' : 'text-gray-700' }}">
                                                    {{ $dateObj->format('j') }}
                                                </span>
                                                <span class="text-xs text-gray-500">
                                                    {{ $dateObj->format('M') }}
                                                </span>
                                            </div>

                                            <input type="hidden" name="dates[{{ $date }}][date]" value="{{ $date }}">

                                            <div class="flex items-center mb-1">
                                                <input type="checkbox"
                                                       id="available_{{ $date }}"
                                                       name="dates[{{ $date }}][is_available]"
                                                       value="1"
                                                       {{ $isAvailable ? 'checked' : '' }}
                                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                                <label for="available_{{ $date }}" class="ml-2 text-xs text-gray-700">
                                                    {{ __('Available') }}
                                                </label>
                                            </div>

                                            <div>
                                                <input type="number"
                                                       name="dates[{{ $date }}][custom_price]"
                                                       value="{{ $customPrice }}"
                                                       placeholder="{{ number_format($driver->daily_rate, 2) }}"
                                                       class="mt-1 block w-full text-xs border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                                       min="0"
                                                       step="0.01">
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>

                        <div class="flex justify-end">
                            <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                {{ __('Save Availability') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-backend-layout>

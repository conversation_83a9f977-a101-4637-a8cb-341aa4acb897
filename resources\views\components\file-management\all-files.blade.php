@foreach($files as $file)
    <div
        class="bg-white border border-gray-200 rounded-lg shadow p-2 relative cursor-pointer transition duration-200 hover:shadow-md"
        id="file-card-{{ $file->id }}"
        data-id="{{ $file->id }}"
        onclick="toggleCheckbox({{ $file->id }})"
        aria-label="Select file: {{ $file->title }}"
        role="button"
        tabindex="0"
        @keydown.enter="toggleCheckbox({{ $file->id }})"
        @keydown.space.prevent="toggleCheckbox({{ $file->id }})"
    >
        <input
            type="checkbox"
            class="absolute top-2 left-2 rounded selected-files"
            id="checkbox-{{ $file->id }}"
            onclick="event.stopPropagation(); toggleCheckbox({{ $file->id }})"
            aria-label="Select {{ $file->title }}"
        >
        <button
            class="text-gray-600 hover:text-gray-800 focus:outline-none absolute top-2 right-2 p-1 rounded"
            id="menu-{{ $file->id }}"
            aria-label="File options for {{ $file->title }}"
            title="File options"
            onclick="event.stopPropagation(); toggleMenu({{ $file->id }})"
        >
            <svg width="20px" height="20px" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M8 12C9.10457 12 10 12.8954 10 14C10 15.1046 9.10457 16 8 16C6.89543 16 6 15.1046 6 14C6 12.8954 6.89543 12 8 12Z" fill="currentColor"/>
                <path d="M8 6C9.10457 6 10 6.89543 10 8C10 9.10457 9.10457 10 8 10C6.89543 10 6 9.10457 6 8C6 6.89543 6.89543 6 8 6Z" fill="currentColor"/>
                <path d="M10 2C10 0.89543 9.10457 -4.82823e-08 8 0C6.89543 4.82823e-08 6 0.895431 6 2C6 3.10457 6.89543 4 8 4C9.10457 4 10 3.10457 10 2Z" fill="currentColor"/>
            </svg>
        </button>
        
        <div class="file-image-container">
            @if(Str::startsWith($file->fileType, 'image/'))
                <img 
                    src="{{ $file->url }}" 
                    alt="{{ $file->title }}" 
                    class="w-full h-32 object-cover rounded"
                    loading="lazy"
                    onerror="this.onerror=null; this.src='/images/fallback-image.jpg'"
                >
            @else
                <!-- For non-image files, show an appropriate icon -->
                <div class="w-full h-32 flex items-center justify-center bg-gray-100 rounded">
                    @php
                        $extension = pathinfo($file->filename, PATHINFO_EXTENSION);
                    @endphp
                    
                    @if(in_array($extension, ['pdf', 'doc', 'docx']))
                        <svg class="w-16 h-16 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                    @elseif(in_array($extension, ['xls', 'xlsx', 'csv']))
                        <svg class="w-16 h-16 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                    @else
                        <svg class="w-16 h-16 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                        </svg>
                    @endif
                </div>
            @endif
        </div>
        
        <div class="flex justify-between items-center mt-1">
            <span class="text-sm font-medium truncate max-w-[70%]" title="{{ $file->title }}">{{ $file->title }}</span>
            <span class="text-xs text-gray-500">{{ round($file->fileSize / 1024, 2) }} KB</span>
        </div>
    </div>
@endforeach
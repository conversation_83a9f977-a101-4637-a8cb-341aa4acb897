<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('conversations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_one_id')->constrained('users');
            $table->foreignId('user_two_id')->constrained('users');
            $table->foreignId('booking_id')->nullable()->constrained();
            $table->timestamp('last_message_at')->nullable();
            $table->boolean('is_read_user_one')->default(false);
            $table->boolean('is_read_user_two')->default(false);
            $table->timestamps();
            $table->softDeletes();
            
            // Ensure unique conversations between users
            $table->unique(['user_one_id', 'user_two_id', 'booking_id'], 'unique_conversation');
        });
        
        // Add conversation_id to messages table
        Schema::table('messages', function (Blueprint $table) {
            $table->foreignId('conversation_id')->nullable()->after('id')->constrained();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('messages', function (Blueprint $table) {
            $table->dropForeign(['conversation_id']);
            $table->dropColumn('conversation_id');
        });
        
        Schema::dropIfExists('conversations');
    }
};

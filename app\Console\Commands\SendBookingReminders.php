<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Booking;
use App\Mail\BookingReminder;
use Illuminate\Support\Facades\Mail;
use Carbon\Carbon;

class SendBookingReminders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bookings:remind';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send reminder emails for upcoming bookings';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Sending booking reminders...');

        // Get bookings that start tomorrow
        $tomorrow = Carbon::tomorrow();
        $bookings = Booking::where('status', 'confirmed')
            ->whereDate('start_date', $tomorrow)
            ->with(['user', 'vehicle', 'vehicle.user'])
            ->get();

        $this->info("Found {$bookings->count()} bookings starting tomorrow.");

        foreach ($bookings as $booking) {
            try {
                // Send reminder to the customer
                if ($booking->user && $booking->user->email) {
                    Mail::to($booking->user->email)
                        ->send(new BookingReminder($booking, 'customer'));
                    
                    $this->line("Sent reminder to customer: {$booking->user->email} for booking #{$booking->id}");
                }
                
                // Send reminder to the vehicle owner
                if ($booking->vehicle && $booking->vehicle->user && $booking->vehicle->user->email) {
                    Mail::to($booking->vehicle->user->email)
                        ->send(new BookingReminder($booking, 'owner'));
                    
                    $this->line("Sent reminder to owner: {$booking->vehicle->user->email} for booking #{$booking->id}");
                }
            } catch (\Exception $e) {
                $this->error("Failed to send reminder for booking #{$booking->id}: " . $e->getMessage());
            }
        }

        $this->info('Booking reminders sent successfully!');
    }
}

<?php

namespace Tests\Unit;

use App\Models\Vehicle;
use App\Models\VehicleType;
use App\Models\User;
use App\Models\City;
use App\Models\VehicleImage;
use Tests\Unit\TestCase;
use Mockery;

test('vehicle has correct fillable attributes', function () {
    $vehicle = new Vehicle();

    $fillable = [
        'user_id',
        'vehicle_type_id',
        'make',
        'model',
        'year',
        'license_plate',
        'color',
        'mileage',
        'transmission',
        'fuel_type',
        'seats',
        'doors',
        'features',
        'description',
        'availability',
        'advance_notice',
        'daily_rate',
        'weekly_discount',
        'monthly_discount',
        'latitude',
        'longitude',
        'address',
        'city_id',
        'with_driver',
        'status',
        'is_featured',
        'security_deposit',
        'rejection_reason'
    ];

    expect($vehicle->getFillable())->toBe($fillable);
});

test('vehicle has correct casts', function () {
    $vehicle = new Vehicle();

    $expectedCasts = [
        'features' => 'array',
        'year' => 'integer',
        'mileage' => 'integer',
        'doors' => 'integer',
        'daily_rate' => 'decimal:2',
        'weekly_discount' => 'decimal:2',
        'monthly_discount' => 'decimal:2',
        'security_deposit' => 'decimal:2',
        'with_driver' => 'boolean',
        'is_featured' => 'boolean',
    ];

    expect($vehicle->getCasts())->toMatchArray($expectedCasts);
});

test('vehicle has user relationship method', function () {
    $vehicle = new Vehicle();

    expect(method_exists($vehicle, 'user'))->toBeTrue();
});

test('vehicle has vehicleType relationship method', function () {
    $vehicle = new Vehicle();

    expect(method_exists($vehicle, 'vehicleType'))->toBeTrue();
});

test('vehicle has city relationship method', function () {
    $vehicle = new Vehicle();

    expect(method_exists($vehicle, 'city'))->toBeTrue();
});

test('vehicle has images relationship method', function () {
    $vehicle = new Vehicle();

    expect(method_exists($vehicle, 'images'))->toBeTrue();
});

test('vehicle has primaryImage relationship method', function () {
    $vehicle = new Vehicle();

    expect(method_exists($vehicle, 'primaryImage'))->toBeTrue();
});

test('vehicle has formatted daily rate attribute', function () {
    $vehicle = new Vehicle();
    $vehicle->daily_rate = 50.00;

    expect($vehicle->formatted_daily_rate)->toBe('$50.00');
});

test('vehicle has full name attribute', function () {
    $vehicle = new Vehicle();
    $vehicle->year = 2022;
    $vehicle->make = 'Toyota';
    $vehicle->model = 'Camry';

    expect($vehicle->full_name)->toBe('2022 Toyota Camry');
});

test('vehicle scope active works correctly', function () {
    // Create a mock query builder
    $query = Mockery::mock('Illuminate\Database\Eloquent\Builder');
    $query->shouldReceive('where')->with('status', 'active')->once()->andReturnSelf();

    // Call the scope
    $vehicle = new Vehicle();
    $result = $vehicle->scopeActive($query);

    // Verify the result
    expect($result)->toBe($query);
});

test('vehicle scope featured works correctly', function () {
    // Create a mock query builder
    $query = Mockery::mock('Illuminate\Database\Eloquent\Builder');
    $query->shouldReceive('where')->with('is_featured', true)->once()->andReturnSelf();

    // Call the scope
    $vehicle = new Vehicle();
    $result = $vehicle->scopeFeatured($query);

    // Verify the result
    expect($result)->toBe($query);
});

test('vehicle scope with driver works correctly', function () {
    // Create a mock query builder
    $query = Mockery::mock('Illuminate\Database\Eloquent\Builder');
    $query->shouldReceive('where')->with('with_driver', true)->once()->andReturnSelf();

    // Call the scope
    $vehicle = new Vehicle();
    $result = $vehicle->scopeWithDriver($query);

    // Verify the result
    expect($result)->toBe($query);
});

test('vehicle scope nearby works correctly', function () {
    // Create a mock query builder
    $query = Mockery::mock('Illuminate\Database\Eloquent\Builder');

    // Set up expectations for the query builder
    $lat = 40.7128;
    $lng = -74.0060;
    $radius = 10;

    $haversine = "(
        6371 * acos(
            cos(radians($lat))
            * cos(radians(latitude))
            * cos(radians(longitude) - radians($lng))
            + sin(radians($lat))
            * sin(radians(latitude))
        )
    )";

    $query->shouldReceive('selectRaw')->with("*, $haversine AS distance")->once()->andReturnSelf();
    $query->shouldReceive('whereRaw')->with("$haversine < ?", [$radius])->once()->andReturnSelf();
    $query->shouldReceive('orderBy')->with('distance')->once()->andReturnSelf();

    // Call the scope
    $vehicle = new Vehicle();
    $result = $vehicle->scopeNearby($query, $lat, $lng, $radius);

    // Verify the result
    expect($result)->toBe($query);
});

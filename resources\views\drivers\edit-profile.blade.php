<x-app-backend-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Edit Driver Profile') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="mb-6">
                        <h3 class="text-lg font-medium text-gray-900">{{ __('Update Your Driver Profile') }}</h3>
                        <p class="mt-1 text-sm text-gray-600">
                            {{ __('Update your driver information below.') }}
                        </p>
                    </div>

                    <form method="POST" action="{{ route('drivers.update-profile') }}" enctype="multipart/form-data" class="space-y-8">
                        @csrf
                        @method('PUT')

                        <!-- Personal Information Section -->
                        <div class="bg-gray-50 p-6 rounded-lg">
                            <h4 class="text-md font-medium text-gray-900 mb-4">{{ __('Personal Information') }}</h4>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Bio -->
                                <div class="col-span-2">
                                    <x-input-label for="bio" :value="__('Bio')" />
                                    <textarea id="bio" name="bio" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">{{ old('bio', $driver->bio) }}</textarea>
                                    <p class="mt-1 text-sm text-gray-500">{{ __('Tell us about yourself and your driving experience.') }}</p>
                                    <x-input-error :messages="$errors->get('bio')" class="mt-2" />
                                </div>

                                <!-- Experience Years -->
                                <div>
                                    <x-input-label for="experience_years" :value="__('Years of Experience')" />
                                    <x-text-input id="experience_years" name="experience_years" type="text" class="mt-1 block w-full" :value="old('experience_years', $driver->experience_years)" required />
                                    <x-input-error :messages="$errors->get('experience_years')" class="mt-2" />
                                </div>

                                <!-- City -->
                                <div>
                                    <x-input-label for="city_id" :value="__('City')" />
                                    <select id="city_id" name="city_id" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                        <option value="">{{ __('Select your city') }}</option>
                                        @foreach($cities as $city)
                                            <option value="{{ $city->id }}" {{ old('city_id', $driver->city_id) == $city->id ? 'selected' : '' }}>{{ $city->name }}</option>
                                        @endforeach
                                    </select>
                                    <x-input-error :messages="$errors->get('city_id')" class="mt-2" />
                                </div>

                                <!-- Languages -->
                                <div>
                                    <x-input-label for="languages" :value="__('Languages Spoken')" />
                                    <div class="mt-1 space-y-2">
                                        <div class="flex flex-wrap gap-2">
                                            @php
                                                $languageOptions = ['English', 'Spanish', 'French', 'German', 'Italian', 'Portuguese', 'Russian', 'Chinese', 'Japanese', 'Arabic', 'Hindi', 'Other'];
                                            @endphp

                                            @foreach($languageOptions as $language)
                                                <label class="inline-flex items-center">
                                                    <input type="checkbox" name="languages[]" value="{{ $language }}" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                                        {{ in_array($language, $languages) ? 'checked' : '' }}>
                                                    <span class="ml-2 text-sm text-gray-600">{{ $language }}</span>
                                                </label>
                                            @endforeach
                                        </div>
                                    </div>
                                    <x-input-error :messages="$errors->get('languages')" class="mt-2" />
                                </div>

                                <!-- Hourly Rate -->
                                <div>
                                    <x-input-label for="hourly_rate" :value="__('Hourly Rate (USD)')" />
                                    <x-text-input id="hourly_rate" name="hourly_rate" type="number" step="0.01" min="0" class="mt-1 block w-full" :value="old('hourly_rate', $driver->hourly_rate)" />
                                    <p class="mt-1 text-sm text-gray-500">{{ __('Leave empty if you don\'t offer hourly rates.') }}</p>
                                    <x-input-error :messages="$errors->get('hourly_rate')" class="mt-2" />
                                </div>

                                <!-- Daily Rate -->
                                <div>
                                    <x-input-label for="daily_rate" :value="__('Daily Rate (USD)')" />
                                    <x-text-input id="daily_rate" name="daily_rate" type="number" step="0.01" min="1" class="mt-1 block w-full" :value="old('daily_rate', $driver->daily_rate)" required />
                                    <x-input-error :messages="$errors->get('daily_rate')" class="mt-2" />
                                </div>

                                <!-- Availability -->
                                <div>
                                    <x-input-label :value="__('Availability')" />
                                    <div class="mt-2 space-y-2">
                                        <label class="inline-flex items-center">
                                            <input type="checkbox" name="available_weekdays" value="1" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" {{ old('available_weekdays', $driver->available_weekdays) ? 'checked' : '' }}>
                                            <span class="ml-2 text-sm text-gray-600">{{ __('Available on Weekdays') }}</span>
                                        </label>
                                        <label class="inline-flex items-center">
                                            <input type="checkbox" name="available_weekends" value="1" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" {{ old('available_weekends', $driver->available_weekends) ? 'checked' : '' }}>
                                            <span class="ml-2 text-sm text-gray-600">{{ __('Available on Weekends') }}</span>
                                        </label>
                                    </div>
                                    <x-input-error :messages="$errors->get('available_weekdays')" class="mt-2" />
                                    <x-input-error :messages="$errors->get('available_weekends')" class="mt-2" />
                                </div>

                                <!-- Profile Image -->
                                <div>
                                    <x-input-label for="profile_image" :value="__('Profile Image')" />
                                    <input id="profile_image" name="profile_image" type="file" class="mt-1 block w-full" accept="image/*">
                                    <p class="mt-1 text-sm text-gray-500">{{ __('Upload a new profile image (optional).') }}</p>
                                    <x-input-error :messages="$errors->get('profile_image')" class="mt-2" />
                                </div>
                            </div>
                        </div>

                        <div class="flex items-center justify-end">
                            <a href="{{ route('drivers.profile') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                {{ __('Cancel') }}
                            </a>
                            <x-primary-button class="ml-4">
                                {{ __('Update Profile') }}
                            </x-primary-button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-backend-layout>

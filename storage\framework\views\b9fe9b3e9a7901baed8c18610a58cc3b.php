<?php if (isset($component)) { $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742 = $attributes; } ?>
<?php $component = App\View\Components\AppBackendLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-backend-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppBackendLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div x-data="cityCrud()" class="p-6">
        <div class="mb-4">
            <div class="flex justify-between items-center mb-3">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4">City Management</h2>
                <button @click="openModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg shadow">Add City</button>
            </div>

            <div class="overflow-x-auto">
                <table class="w-full table-auto border-collapse bg-white shadow-md rounded-lg">
                    <thead>
                        <tr class="bg-gray-200 text-gray-600 uppercase text-sm leading-normal">
                            <th class="py-3 px-6 text-left">#</th>
                            <th class="py-3 px-6 text-left">Name</th>
                            <th class="py-3 px-6 text-left">State</th>
                            <th class="py-3 px-6 text-left">Cost</th>
                            <th class="py-3 px-6 text-left">is_active</th>
                            <th class="py-3 px-6 text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <template x-for="(city, index) in cities" :key="city.id">
                            <tr class="border-b border-gray-200 hover:bg-gray-100">
                                <td class="py-3 px-6 text-left" x-text="index + 1"></td>
                                <td class="py-3 px-6 text-left" x-text="city.name"></td>
                                <td class="py-3 px-6 text-left" x-text="city.state.name"></td>
                                <td class="py-3 px-6 text-left" x-text="city.cost"></td>
                                <td class="py-3 px-6 text-left" x-text="city.is_active == 1 ? 'Active' : 'Inactive'"></td>
                                <td class="py-3 px-6 text-center">
                                    <button @click="openModal(city)" class="bg-yellow-500 hover:bg-yellow-600 text-white px-3 py-1 rounded-lg shadow">Edit</button>
                                    <button @click="deleteCity(city.id)" class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded-lg shadow ml-2">Delete</button>
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Modal -->
        <div x-show="showModal" class="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50" style="z-index: 100">
            <div class="bg-white p-6 rounded-lg shadow-lg w-96">
                <h3 class="text-lg font-semibold text-gray-700 mb-4" x-text="form.id ? 'Edit City' : 'Add City'"></h3>
                <input type="text" placeholder="Name" x-model="form.name" class="border p-2 w-full mb-2 rounded-lg shadow-sm">
                <select x-model="form.state_id" class="border p-2 w-full mb-2 rounded-lg shadow-sm">
                    <option value="">Select State</option>
                    <template x-for="state in states" :key="state.id">
                        <option :value="state.id" x-text="state.name"></option>
                    </template>
                </select>
                <input type="number" placeholder="Cost" x-model="form.cost" class="border p-2 w-full mb-2 rounded-lg shadow-sm">
                <input type="text" placeholder="Lat" x-model="form.latitude" class="border p-2 w-full mb-2 rounded-lg shadow-sm">
                <input type="text" placeholder="Long" x-model="form.longitude" class="border p-2 w-full mb-2 rounded-lg shadow-sm">

                <select x-model="form.is_active" class="border p-2 w-full mb-2 rounded-lg shadow-sm">
                    <option value="">Select is_active</option>
                    <option value="1">Active</option>
                    <option value="0">Inactive</option>
                </select>

                <div class="flex justify-end space-x-2">
                    <button @click="showModal = false" class="bg-gray-400 hover:bg-gray-500 text-white px-4 py-2 rounded-lg shadow">Cancel</button>
                    <button @click="saveCity()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg shadow">
                        <span x-text="form.id ? 'Update' : 'Save'"></span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function cityCrud() {
            return {
                cities: <?php echo json_encode($cities, 15, 512) ?>,
                states: <?php echo json_encode($states, 15, 512) ?>,
                showModal: false,
                form: { id: null, name: '', state_id: '', cost: '', latitude: '', longitude: '', is_active: 0 },

                openModal(city = null) {
                    this.form = city ? { ...city } : { id: null, name: '', state_id: '', cost: '', latitude: '', longitude: '', is_active: 0 };
                    this.showModal = true;
                },

                saveCity() {
                    let url = this.form.id ? `/cities/${this.form.id}` : '/cities';
                    let method = this.form.id ? 'PUT' : 'POST';

                    fetch(url, {
                        method: method,
                        headers: { 'Content-Type': 'application/json', 'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content },
                        body: JSON.stringify(this.form),
                    })
                    .then(res => res.json())
                    .then(data => {
                        Swal.fire('Success!', data.message, 'success');
                        if (!this.form.id) {
                            this.cities.push(data.city);
                        } else {
                            let index = this.cities.findIndex(c => c.id === this.form.id);
                            this.cities[index] = data.city;
                        }
                        this.showModal = false;
                    });
                },

                deleteCity(id) {
                    Swal.fire({
                        title: 'Are you sure?',
                        text: "You won't be able to revert this!",
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#d33',
                        cancelButtonColor: '#3085d6',
                        confirmButtonText: 'Yes, delete it!'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            fetch(`/cities/${id}`, {
                                method: 'DELETE',
                                headers: {'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content}
                            })
                            .then(() => {
                                Swal.fire('Deleted!', 'City has been deleted.', 'success');
                                this.cities = this.cities.filter(c => c.id !== id);
                            });
                        }
                    });
                }
            };
        }
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $attributes = $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $component = $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\carbnb\resources\views\backend\city\index.blade.php ENDPATH**/ ?>
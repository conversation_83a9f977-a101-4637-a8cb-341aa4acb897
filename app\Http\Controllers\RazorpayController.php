<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\Payment;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Razorpay\Api\Api;
use Exception;

class RazorpayController extends Controller
{
    /**
     * Create a Razorpay order for a booking.
     *
     * @param  int  $bookingId
     * @return \Illuminate\Http\JsonResponse
     */
    public function createOrder($bookingId)
    {
        $booking = Booking::findOrFail($bookingId);

        // Check if the user is authorized to pay for this booking
        if (Auth::id() !== $booking->user_id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        // Check if the booking amount is valid for payment
        if ($booking->total_amount <= 0) {
            return response()->json([
                'error' => 'Invalid booking amount. The total amount must be greater than zero.'
            ], 400);
        }

        // Check if Razorpay API keys are configured
        $razorpayKey = config('razorpay.key');
        $razorpaySecret = config('razorpay.secret');

        if (empty($razorpayKey) || empty($razorpaySecret) ||
            $razorpayKey === 'rzp_test_YOUR_TEST_KEY' ||
            $razorpaySecret === 'YOUR_TEST_SECRET') {
            return response()->json([
                'error' => 'Razorpay API keys are not properly configured. Please use the manual payment option for testing.',
                'use_manual' => true
            ], 500);
        }

        try {
            $api = new Api($razorpayKey, $razorpaySecret);

            // Convert amount to smallest currency unit (paise for INR, cents for USD)
            $amountInSmallestUnit = round($booking->total_amount * 100);

            $orderData = [
                'receipt' => $booking->booking_number,
                'amount' => $amountInSmallestUnit,
                'currency' => config('razorpay.currency'),
                'notes' => [
                    'booking_id' => $booking->id,
                    'user_id' => Auth::id(),
                    'vehicle' => $booking->vehicle->make . ' ' . $booking->vehicle->model,
                ],
            ];

            $razorpayOrder = $api->order->create($orderData);

            return response()->json([
                'order_id' => $razorpayOrder->id,
                'amount' => $amountInSmallestUnit,
                'currency' => config('razorpay.currency'),
                'booking' => [
                    'id' => $booking->id,
                    'number' => $booking->booking_number,
                    'total' => $booking->total_amount,
                ],
            ]);
        } catch (Exception $e) {
            Log::error('Razorpay order creation failed: ' . $e->getMessage());
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Handle the payment callback from Razorpay.
     *
     * @param  Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function handlePayment(Request $request)
    {
        $input = $request->all();

        $bookingId = $request->booking_id;
        $booking = Booking::findOrFail($bookingId);

        // Verify signature
        $signature = $request->razorpay_signature;
        $orderId = $request->razorpay_order_id;
        $paymentId = $request->razorpay_payment_id;

        try {
            $api = new Api(config('razorpay.key'), config('razorpay.secret'));
            $attributes = [
                'razorpay_order_id' => $orderId,
                'razorpay_payment_id' => $paymentId,
                'razorpay_signature' => $signature,
            ];

            $api->utility->verifyPaymentSignature($attributes);

            // Start database transaction
            DB::beginTransaction();

            // Create payment record
            $payment = Payment::create([
                'booking_id' => $booking->id,
                'user_id' => Auth::id(),
                'payment_method' => 'razorpay',
                'transaction_id' => $paymentId,
                'amount' => $booking->total_amount,
                'type' => 'booking',
                'status' => 'completed',
                'currency' => config('razorpay.currency'),
                'payment_details' => json_encode([
                    'order_id' => $orderId,
                    'payment_id' => $paymentId,
                ]),
            ]);

            // Create transaction for platform commission
            $commissionRate = 0.10; // 10% commission
            $commissionAmount = $booking->total_amount * $commissionRate;

            Transaction::create([
                'payment_id' => $payment->id,
                'booking_id' => $booking->id,
                'user_id' => 1, // Admin user ID (platform)
                'type' => 'platform_commission',
                'amount' => $commissionAmount,
                'commission_rate' => $commissionRate,
                'currency' => config('razorpay.currency'),
                'status' => 'completed',
                'notes' => 'Platform commission for booking #' . $booking->booking_number,
            ]);

            // Create transaction for owner payout
            $ownerAmount = $booking->total_amount - $commissionAmount;

            Transaction::create([
                'payment_id' => $payment->id,
                'booking_id' => $booking->id,
                'user_id' => $booking->vehicle->user_id,
                'type' => 'owner_payout',
                'amount' => $ownerAmount,
                'commission_rate' => $commissionRate,
                'currency' => config('razorpay.currency'),
                'status' => 'pending', // Will be processed later
                'notes' => 'Owner payout for booking #' . $booking->booking_number,
            ]);

            // Commit transaction
            DB::commit();

            // Redirect to booking confirmation page
            return redirect()->route('booking.confirmation', $booking->id)
                ->with('success', 'Payment successful!');
        } catch (Exception $e) {
            // Rollback transaction on error
            DB::rollBack();

            Log::error('Razorpay payment verification failed: ' . $e->getMessage());
            return redirect()->route('booking.payment', $booking->id)
                ->with('error', 'Payment verification failed: ' . $e->getMessage());
        }
    }

    /**
     * Handle Razorpay webhook events.
     *
     * @param  Request  $request
     * @return \Illuminate\Http\Response
     */
    public function webhook(Request $request)
    {
        $webhookSecret = config('razorpay.webhook_secret');

        if (empty($webhookSecret)) {
            Log::warning('Razorpay webhook secret is not configured');
            return response()->json(['status' => 'ignored'], 200);
        }

        $payload = $request->getContent();
        $signature = $request->header('X-Razorpay-Signature');

        try {
            // Verify webhook signature
            $expectedSignature = hash_hmac('sha256', $payload, $webhookSecret);
            if ($expectedSignature !== $signature) {
                Log::warning('Razorpay webhook signature verification failed');
                return response()->json(['status' => 'invalid signature'], 400);
            }

            $event = json_decode($payload, true);
            $eventType = $event['event'];

            // Handle different webhook events
            switch ($eventType) {
                case 'payment.authorized':
                    // Payment was authorized but not captured yet
                    $this->handlePaymentAuthorized($event);
                    break;

                case 'payment.captured':
                    // Payment was captured (money transferred)
                    $this->handlePaymentCaptured($event);
                    break;

                case 'payment.failed':
                    // Payment failed
                    $this->handlePaymentFailed($event);
                    break;

                case 'refund.created':
                    // Refund was initiated
                    $this->handleRefundCreated($event);
                    break;

                case 'refund.processed':
                    // Refund was processed
                    $this->handleRefundProcessed($event);
                    break;

                default:
                    // Unhandled event
                    Log::info('Unhandled Razorpay webhook event: ' . $eventType);
                    break;
            }

            return response()->json(['status' => 'success'], 200);
        } catch (Exception $e) {
            Log::error('Razorpay webhook processing failed: ' . $e->getMessage());
            return response()->json(['status' => 'error', 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Handle payment.authorized webhook event.
     *
     * @param  array  $event
     * @return void
     */
    private function handlePaymentAuthorized($event)
    {
        $paymentData = $event['payload']['payment']['entity'];
        $orderId = $paymentData['order_id'];
        $paymentId = $paymentData['id'];

        Log::info('Payment authorized: ' . $paymentId . ' for order: ' . $orderId);
    }

    /**
     * Handle payment.captured webhook event.
     *
     * @param  array  $event
     * @return void
     */
    private function handlePaymentCaptured($event)
    {
        $paymentData = $event['payload']['payment']['entity'];
        $paymentId = $paymentData['id'];

        // Find the payment by transaction_id
        $payment = Payment::where('transaction_id', $paymentId)->first();

        if ($payment) {
            // Update payment status if needed
            if ($payment->status !== 'completed') {
                $payment->status = 'completed';
                $payment->save();

                Log::info('Payment status updated to completed: ' . $paymentId);
            }
        } else {
            Log::warning('Payment not found for captured payment: ' . $paymentId);
        }
    }

    /**
     * Handle payment.failed webhook event.
     *
     * @param  array  $event
     * @return void
     */
    private function handlePaymentFailed($event)
    {
        $paymentData = $event['payload']['payment']['entity'];
        $paymentId = $paymentData['id'];
        $orderId = $paymentData['order_id'];

        Log::info('Payment failed: ' . $paymentId . ' for order: ' . $orderId);

        // Find the payment by transaction_id
        $payment = Payment::where('transaction_id', $paymentId)->first();

        if ($payment) {
            // Update payment status
            $payment->status = 'failed';
            $payment->save();

            Log::info('Payment status updated to failed: ' . $paymentId);
        }
    }

    /**
     * Handle refund.created webhook event.
     *
     * @param  array  $event
     * @return void
     */
    private function handleRefundCreated($event)
    {
        $refundData = $event['payload']['refund']['entity'];
        $paymentId = $refundData['payment_id'];
        $refundId = $refundData['id'];
        $amount = $refundData['amount'] / 100; // Convert from smallest unit

        Log::info('Refund created: ' . $refundId . ' for payment: ' . $paymentId . ', amount: ' . $amount);
    }

    /**
     * Handle refund.processed webhook event.
     *
     * @param  array  $event
     * @return void
     */
    private function handleRefundProcessed($event)
    {
        $refundData = $event['payload']['refund']['entity'];
        $paymentId = $refundData['payment_id'];
        $refundId = $refundData['id'];

        // Find the payment by transaction_id
        $payment = Payment::where('transaction_id', $paymentId)->first();

        if ($payment) {
            // Update payment status
            $payment->status = 'refunded';
            $payment->save();

            Log::info('Payment status updated to refunded: ' . $paymentId);
        } else {
            Log::warning('Payment not found for refund: ' . $paymentId);
        }
    }
}

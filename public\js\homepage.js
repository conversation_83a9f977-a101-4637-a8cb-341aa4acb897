// CARBNB Homepage JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Carousel functionality
    initCarousel();
    
    // Counter animation
    initCounters();
    
    // Smooth scroll for anchor links
    initSmoothScroll();
    
    // Date picker validation
    initDateValidation();
});

// Initialize the featured cars carousel
function initCarousel() {
    const carousel = document.getElementById('featured-cars');
    if (!carousel) return;
    
    const container = carousel.querySelector('.carousel-container');
    const prevBtn = document.getElementById('prev-btn');
    const nextBtn = document.getElementById('next-btn');
    const items = carousel.querySelectorAll('.carousel-item');
    
    if (!container || !prevBtn || !nextBtn || items.length === 0) return;
    
    const itemWidth = items[0].offsetWidth + parseInt(window.getComputedStyle(items[0]).marginRight);
    let currentIndex = 0;
    
    // Update carousel position
    function updateCarousel() {
        container.scrollTo({
            left: currentIndex * itemWidth,
            behavior: 'smooth'
        });
    }
    
    // Event listeners for buttons
    prevBtn.addEventListener('click', function() {
        if (currentIndex > 0) {
            currentIndex--;
            updateCarousel();
        }
    });
    
    nextBtn.addEventListener('click', function() {
        if (currentIndex < items.length - 1) {
            currentIndex++;
            updateCarousel();
        }
    });
    
    // Hide/show navigation buttons based on scroll position
    container.addEventListener('scroll', function() {
        const scrollLeft = container.scrollLeft;
        
        // Show/hide prev button
        if (scrollLeft <= 10) {
            prevBtn.classList.add('opacity-0');
            prevBtn.classList.remove('opacity-75');
        } else {
            prevBtn.classList.remove('opacity-0');
            prevBtn.classList.add('opacity-75');
        }
        
        // Show/hide next button
        const maxScroll = container.scrollWidth - container.clientWidth - 10;
        if (scrollLeft >= maxScroll) {
            nextBtn.classList.add('opacity-0');
            nextBtn.classList.remove('opacity-75');
        } else {
            nextBtn.classList.remove('opacity-0');
            nextBtn.classList.add('opacity-75');
        }
    });
    
    // Trigger initial scroll event to set button states
    container.dispatchEvent(new Event('scroll'));
}

// Animate counters when they come into view
function initCounters() {
    const counters = document.querySelectorAll('.counter');
    if (!counters.length) return;
    
    const options = {
        threshold: 0.5
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const counter = entry.target;
                const targetValue = counter.innerText;
                
                // Only animate if it's a number with a plus sign
                if (targetValue.includes('+')) {
                    const numericValue = parseInt(targetValue.replace(/,/g, ''));
                    if (!isNaN(numericValue)) {
                        animateCounter(counter, 0, numericValue);
                    }
                }
                
                // Unobserve after animation
                observer.unobserve(counter);
            }
        });
    }, options);
    
    counters.forEach(counter => {
        observer.observe(counter);
    });
}

// Animate a counter from start to end value
function animateCounter(element, start, end) {
    let current = start;
    const increment = Math.ceil(end / 50); // Adjust for animation speed
    const duration = 1500; // Animation duration in ms
    const stepTime = Math.abs(Math.floor(duration / (end - start)));
    
    const timer = setInterval(() => {
        current += increment;
        
        if (current >= end) {
            element.innerText = end.toLocaleString() + '+';
            clearInterval(timer);
        } else {
            element.innerText = current.toLocaleString() + '+';
        }
    }, stepTime);
}

// Initialize smooth scrolling for anchor links
function initSmoothScroll() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            const targetId = this.getAttribute('href');
            if (targetId === '#') return;
            
            const targetElement = document.querySelector(targetId);
            if (targetElement) {
                e.preventDefault();
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Initialize date validation for the booking form
function initDateValidation() {
    const pickupDate = document.querySelector('input[name="pickup_date"]');
    const returnDate = document.querySelector('input[name="return_date"]');
    
    if (!pickupDate || !returnDate) return;
    
    // Update return date min value when pickup date changes
    pickupDate.addEventListener('change', function() {
        if (this.value) {
            const nextDay = new Date(this.value);
            nextDay.setDate(nextDay.getDate() + 1);
            
            // Format date as YYYY-MM-DD
            const formattedDate = nextDay.toISOString().split('T')[0];
            returnDate.min = formattedDate;
            
            // If return date is now invalid, update it
            if (returnDate.value && new Date(returnDate.value) <= new Date(this.value)) {
                returnDate.value = formattedDate;
            }
        }
    });
}
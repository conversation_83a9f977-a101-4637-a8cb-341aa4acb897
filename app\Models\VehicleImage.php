<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VehicleImage extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'vehicle_id',
        'file_id',
        'is_primary',
        'type'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'is_primary' => 'boolean',
    ];

    /**
     * Get the vehicle that owns the image.
     */
    public function vehicle()
    {
        return $this->belongsTo(Vehicle::class);
    }

    /**
     * Get the file associated with this image.
     */
    public function file()
    {
        return $this->belongsTo(File::class, 'file_id');
    }

    /**
     * Get the URL for the image.
     */
    public function getUrlAttribute()
    {
        // If we have a related file, use its getFullUrl method
        if ($this->file) {
            return $this->file->getFullUrl();
        }

        // Fallback to the old path format if no file relation
        // This is for backward compatibility with existing data
        return asset('storage/vehicle-images/' . $this->file_id);
    }

    /**
     * Mark this image as the primary image for its vehicle.
     * This will also unmark any other primary images.
     */
    public function markAsPrimary()
    {
        // Unmark any existing primary images for this vehicle
        self::where('vehicle_id', $this->vehicle_id)
            ->where('id', '!=', $this->id)
            ->update(['is_primary' => false]);

        // Mark this one as primary
        $this->is_primary = true;
        $this->save();

        return $this;
    }
}

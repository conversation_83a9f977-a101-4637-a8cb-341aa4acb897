<x-app-backend-layout>
    <div x-data="currencyCrud()" class="p-6">
        <div class="mb-4">
            <div class="flex justify-between items-center mb-3">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4">Currency Management</h2>
                <button @click="openModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg shadow">Add Currency</button>
            </div>

            <div class="overflow-x-auto">
                <table class="w-full table-auto border-collapse bg-white shadow-md rounded-lg">
                    <thead>
                        <tr class="bg-gray-200 text-gray-600 uppercase text-sm leading-normal">
                            <th class="py-3 px-6 text-left">#</th>
                            <th class="py-3 px-6 text-left">Name</th>
                            <th class="py-3 px-6 text-left">Symbol</th>
                            <th class="py-3 px-6 text-left">Exchange Rate</th>
                            <th class="py-3 px-6 text-left">Status</th>
                            <th class="py-3 px-6 text-left">Code</th>
                            <th class="py-3 px-6 text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <template x-for="(currency, index) in currencies" :key="currency.id">
                            <tr class="border-b border-gray-200 hover:bg-gray-100">
                                <td class="py-3 px-6 text-left" x-text="index + 1"></td>
                                <td class="py-3 px-6 text-left" x-text="currency.name"></td>
                                <td class="py-3 px-6 text-left" x-text="currency.symbol"></td>
                                <td class="py-3 px-6 text-left" x-text="currency.exchange_rate"></td>
                                <td class="py-3 px-6 text-left" x-text="currency.status"></td>
                                <td class="py-3 px-6 text-left" x-text="currency.code"></td>
                                <td class="py-3 px-6 text-center">
                                    <button @click="openModal(currency)" class="bg-yellow-500 hover:bg-yellow-600 text-white px-3 py-1 rounded-lg shadow">Edit</button>
                                    <button @click="deleteCurrency(currency.id)" class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded-lg shadow ml-2">Delete</button>
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Modal -->
        <div x-show="showModal" class="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50" style="z-index: 100">
            <div class="bg-white p-6 rounded-lg shadow-lg w-96">
                <h3 class="text-lg font-semibold text-gray-700 mb-4" x-text="form.id ? 'Edit Currency' : 'Add Currency'"></h3>
                <input type="text" placeholder="Name" x-model="form.name" class="border p-2 w-full mb-2 rounded-lg shadow-sm">
                <input type="text" placeholder="Symbol" x-model="form.symbol" class="border p-2 w-full mb-2 rounded-lg shadow-sm">
                <input type="number" placeholder="Exchange Rate" x-model="form.exchange_rate" class="border p-2 w-full mb-2 rounded-lg shadow-sm">
                <input type="text" placeholder="Status" x-model="form.status" class="border p-2 w-full mb-2 rounded-lg shadow-sm">
                <input type="text" placeholder="Code" x-model="form.code" class="border p-2 w-full mb-2 rounded-lg shadow-sm">

                <div class="flex justify-end space-x-2">
                    <button @click="showModal = false" class="bg-gray-400 hover:bg-gray-500 text-white px-4 py-2 rounded-lg shadow">Cancel</button>
                    <button @click="saveCurrency()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg shadow">
                        <span x-text="form.id ? 'Update' : 'Save'"></span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function currencyCrud() {
            return {
                currencies: @json($currencies),
                showModal: false,
                form: { id: null, name: '', symbol: '', exchange_rate: '', status: '', code: '' },

                openModal(currency = null) {
                    this.form = currency ? { ...currency } : { id: null, name: '', symbol: '', exchange_rate: '', status: '', code: '' };
                    this.showModal = true;
                },

                saveCurrency() {
                    let url = this.form.id ? `/currencies/${this.form.id}` : '/currencies';
                    let method = this.form.id ? 'PUT' : 'POST';

                    fetch(url, {
                        method: method,
                        headers: { 'Content-Type': 'application/json', 'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content },
                        body: JSON.stringify(this.form),
                    })
                    .then(res => res.json())
                    .then(data => {
                        alert(data.message);
                        if (!this.form.id) {
                            this.currencies.push(data.currency);
                        } else {
                            let index = this.currencies.findIndex(c => c.id === this.form.id);
                            this.currencies[index] = data.currency;
                        }
                        this.showModal = false;
                    });
                },

                deleteCurrency(id) {
                    if (confirm('Are you sure you want to delete this currency?')) {
                        fetch(`/currencies/${id}`, {
                            method: 'DELETE',
                            headers: { 'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content }
                        })
                        .then(() => {
                            this.currencies = this.currencies.filter(c => c.id !== id);
                            alert('Currency deleted successfully');
                        });
                    }
                }
            };
        }
    </script>
</x-app-backend-layout>

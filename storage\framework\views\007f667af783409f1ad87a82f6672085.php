<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['name', 'label', 'value' => '']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['name', 'label', 'value' => '']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>


<?php if (isset($component)) { $__componentOriginal030291ebfbe01b7b21fc10c5b0c01e4c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal030291ebfbe01b7b21fc10c5b0c01e4c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.quill-editor','data' => ['name' => $name,'label' => $label,'value' => $value]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('quill-editor'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($name),'label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($label),'value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($value)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal030291ebfbe01b7b21fc10c5b0c01e4c)): ?>
<?php $attributes = $__attributesOriginal030291ebfbe01b7b21fc10c5b0c01e4c; ?>
<?php unset($__attributesOriginal030291ebfbe01b7b21fc10c5b0c01e4c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal030291ebfbe01b7b21fc10c5b0c01e4c)): ?>
<?php $component = $__componentOriginal030291ebfbe01b7b21fc10c5b0c01e4c; ?>
<?php unset($__componentOriginal030291ebfbe01b7b21fc10c5b0c01e4c); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\carbnb\resources\views\components\tinymce-editor.blade.php ENDPATH**/ ?>
/**
 * Document Debug Helper
 * 
 * This script helps debug document type issues by logging information about
 * document types found in the DOM.
 */
document.addEventListener('DOMContentLoaded', function() {
    // Wait for the page to fully load
    setTimeout(function() {
        console.log('Document Debug Helper: Scanning for document types...');
        
        // Get all document type elements from the DOM
        const documentTypeElements = document.querySelectorAll('[name^="documents["][name$="][file_id]"]');
        
        if (documentTypeElements.length === 0) {
            console.log('No document type elements found in the DOM');
            return;
        }
        
        console.log(`Found ${documentTypeElements.length} document type elements`);
        
        // Extract document type IDs and names
        const documentTypes = [];
        
        documentTypeElements.forEach(element => {
            const matches = element.name.match(/documents\[(\d+)\]/);
            if (matches && matches[1]) {
                const id = parseInt(matches[1]);
                
                // Try to find the document type name
                let name = `Document ${id}`;
                const parentElement = element.closest('.bg-white');
                if (parentElement) {
                    const nameElement = parentElement.querySelector('h4');
                    if (nameElement) {
                        name = nameElement.textContent.trim();
                    }
                }
                
                // Check if it has an expiry date field
                const hasExpiry = !!document.querySelector(`input[name="documents[${id}][expiry_date]"]`);
                
                documentTypes.push({
                    id,
                    name,
                    hasExpiry
                });
            }
        });
        
        console.table(documentTypes);
        
        // Log a message to help users
        console.log('If you are having issues with document validation, make sure you have uploaded all required documents and filled in all expiry dates.');
    }, 2000); // Wait 2 seconds for the page to fully load
});

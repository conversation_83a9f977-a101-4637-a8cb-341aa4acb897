<?php if (isset($component)) { $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742 = $attributes; } ?>
<?php $component = App\View\Components\AppBackendLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-backend-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppBackendLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            <?php echo e(__('Menu Items')); ?>

        </h2>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="mb-4 flex justify-end">
                        <a href="<?php echo e(route('menu-items.create')); ?>" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-700">
                            Add Menu Item
                        </a>
                    </div>
                    
                    <?php if(session('success')): ?>
                        <div class="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded">
                            <?php echo e(session('success')); ?>

                        </div>
                    <?php endif; ?>

                    <div x-data="menuManager()" 
                         x-init="initialize()" 
                         data-items="<?php echo e(json_encode($menuItems->load('children'))); ?>"
                         @menu-updated.window="
                            $el.insertAdjacentHTML('afterbegin', 
                            `<div class='mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded'>${$event.detail.message}</div>`);
                            
                            setTimeout(() => {
                                $el.querySelector('.bg-green-100').remove();
                            }, 3000);
                         ">

                        <div class="mb-4">
                            <button @click="saveOrder()" 
                                    class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-700"
                                    :disabled="loading"
                                    :class="{'opacity-50 cursor-not-allowed': loading}">
                                <span x-show="loading" class="inline-block animate-spin mr-2">↻</span>
                                Save Order
                            </button>
                        </div>

                        <div class="menu-items-container">
                            <ul id="menu-items-list" class="space-y-2">
                                <template x-for="(item, index) in items" :key="item.id">
                                    <li :id="'item-' + item.id" class="border border-gray-200 rounded p-4" :class="{ 'ml-8': item.parent_id }">
                                        <div class="flex justify-between items-center">
                                            <div class="flex items-center space-x-4">
                                                <button class="drag-handle cursor-move px-2 py-1 bg-gray-200 rounded hover:bg-gray-300">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M10 3a1 1 0 01.707.293l3 3a1 1 0 01-1.414 1.414L10 5.414 7.707 7.707a1 1 0 01-1.414-1.414l3-3A1 1 0 0110 3zm-3.707 9.293a1 1 0 011.414 0L10 14.586l2.293-2.293a1 1 0 011.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                                                    </svg>
                                                </button>
                                                <span x-text="item.name" class="font-medium"></span>
                                                <span class="text-sm text-gray-500" x-text="item.url || item.route_name || 'No URL'"></span>
                                                <span x-show="!item.is_active" class="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">Inactive</span>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <select 
                                                    :id="'parent-' + item.id"
                                                    @change="updateParent(item.id, $event.target.value)" 
                                                    class="border-gray-300 rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                                    <option value="">No Parent</option>
                                                    <template x-for="parentItem in getParentOptions(item.id)" :key="parentItem.id">
                                                        <option :value="parentItem.id" :selected="item.parent_id == parentItem.id" x-text="parentItem.name"></option>
                                                    </template>
                                                </select>
                                                <a :href="'<?php echo e(url('/menu-items')); ?>/' + item.id + '/edit'" class="p-2 bg-blue-100 text-blue-600 rounded hover:bg-blue-200">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                        <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                                                    </svg>
                                                </a>
                                                <button @click="confirmDelete(item.id)" class="p-2 bg-red-100 text-red-600 rounded hover:bg-red-200">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                    </li>
                                </template>
                            </ul>
                        </div>

                        <!-- Delete Confirmation Modal -->
                        <div x-show="showDeleteModal" 
                             class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
                             x-transition:enter="transition ease-out duration-300"
                             x-transition:enter-start="opacity-0"
                             x-transition:enter-end="opacity-100"
                             x-transition:leave="transition ease-in duration-200"
                             x-transition:leave-start="opacity-100"
                             x-transition:leave-end="opacity-0">
                            <div class="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
                                <h3 class="text-lg font-bold mb-4">Confirm Delete</h3>
                                <p class="mb-4">Are you sure you want to delete this menu item? This will also delete all child items.</p>
                                <div class="flex justify-end space-x-3">
                                    <button @click="showDeleteModal = false" class="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300">
                                        Cancel
                                    </button>
                                    <form :action="'<?php echo e(url('/menu-items')); ?>/' + itemToDelete" method="POST">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit" class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-700">
                                            Delete
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php $__env->startPush('scripts'); ?>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.14.0/Sortable.min.js"></script>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/js/backend/app.js']); ?>
    <script>
        document.addEventListener('alpine:init', () => {
            // This ensures Alpine.js loads the menuManager function
            if (typeof window.menuManager !== 'function') {
                console.error('menuManager function not found');
            }
        });
    </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $attributes = $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $component = $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?><?php /**PATH C:\laragon\www\carbnb\resources\views\backend\menu-items\index.blade.php ENDPATH**/ ?>
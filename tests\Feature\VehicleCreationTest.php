<?php

use App\Models\User;
use App\Models\Vehicle;
use App\Models\VehicleType;
use App\Models\City;
use App\Models\Country;
use App\Models\State;
use App\Models\File;
use App\Models\VehicleImage;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Seed the database with required data
    $this->seed(\Database\Seeders\RoleSeeder::class);
    $this->seed(\Database\Seeders\PermissionSeeder::class);
    $this->seed(\Database\Seeders\LocationSeeder::class);
    $this->seed(\Database\Seeders\VehicleTypeSeeder::class);
    $this->seed(\Database\Seeders\DocumentTypeSeeder::class);

    // Create admin user
    $this->admin = User::create([
        'first_name' => 'Admin',
        'last_name' => 'User',
        'email' => '<EMAIL>',
        'password' => bcrypt('password'),
        'email_verified_at' => now(),
    ]);
    $this->admin->assignRole('admin');

    // Get references to seeded data
    $this->vehicleType = VehicleType::where('slug', 'sedan')->first();
    $this->city = City::where('name', 'New York City')->first();
});

test('admin can access vehicle creation page', function () {
    $response = $this->actingAs($this->admin)->get('/management/vehicles/create');

    $response->assertStatus(200);
    $response->assertViewIs('backend.vehicles.form');
    $response->assertViewHas('vehicleTypes');
    $response->assertViewHas('cities');
});

test('admin can create a vehicle with required fields', function () {
    // Setup fake storage for file uploads
    Storage::fake('public');

    // Create file records in the database
    $primaryImageFile = File::create([
        'title' => 'car.jpg',
        'filename' => 'car.jpg',
        'url' => '/storage/uploads/car.jpg',
        'fileSize' => 1000,
        'fileType' => 'image/jpeg'
    ]);

    $ownershipDocFile = File::create([
        'title' => 'ownership.pdf',
        'filename' => 'ownership.pdf',
        'url' => '/storage/uploads/ownership.pdf',
        'fileSize' => 2000,
        'fileType' => 'application/pdf'
    ]);

    $insuranceDocFile = File::create([
        'title' => 'insurance.pdf',
        'filename' => 'insurance.pdf',
        'url' => '/storage/uploads/insurance.pdf',
        'fileSize' => 2000,
        'fileType' => 'application/pdf'
    ]);

    // Get document type IDs
    $ownershipDocTypeId = 2; // Vehicle Registration
    $insuranceDocTypeId = 3; // Vehicle Insurance

    $vehicleData = [
        'vehicle_type_id' => $this->vehicleType->id,
        'make' => 'Toyota',
        'model' => 'Camry',
        'year' => 2022,
        'license_plate' => 'ABC123',
        'color' => 'Blue',
        'mileage' => 15000,
        'transmission' => 'automatic',
        'fuel_type' => 'gasoline',
        'seats' => '5',
        'doors' => 4,
        'features' => ['bluetooth', 'air_conditioning'],
        'description' => 'A comfortable sedan for your journey',
        'daily_rate' => 50.00,
        'city_id' => $this->city->id,
        'primary_image' => $primaryImageFile->id,
        'documents' => [
            $ownershipDocTypeId => [
                'file_id' => $ownershipDocFile->id,
            ],
            $insuranceDocTypeId => [
                'file_id' => $insuranceDocFile->id,
                'expiry_date' => now()->addYear()->format('Y-m-d'),
            ],
        ],
    ];

    $response = $this->actingAs($this->admin)->post('/management/vehicles', $vehicleData);

    // Check if redirected to vehicles index
    $response->assertRedirect(route('vehicles.index'));
    $response->assertSessionHas('success');

    // Check if vehicle was created in database
    $this->assertDatabaseHas('vehicles', [
        'user_id' => $this->admin->id,
        'vehicle_type_id' => $this->vehicleType->id,
        'make' => 'Toyota',
        'model' => 'Camry',
        'year' => 2022,
        'license_plate' => 'ABC123',
        'color' => 'Blue',
        'mileage' => 15000,
        'transmission' => 'automatic',
        'fuel_type' => 'gasoline',
        'seats' => '5',
        'doors' => 4,
        'daily_rate' => 50.00,
        'city_id' => $this->city->id,
        'status' => 'pending', // Should be pending by default
    ]);

    // Check if the primary image was associated with the vehicle
    $vehicle = Vehicle::where('license_plate', 'ABC123')->first();
    $this->assertNotNull($vehicle);

    $vehicleImage = VehicleImage::where('vehicle_id', $vehicle->id)->first();
    $this->assertNotNull($vehicleImage);
    $this->assertEquals($primaryImageFile->id, $vehicleImage->file_id);
    $this->assertTrue($vehicleImage->is_primary);
});

test('vehicle creation fails without required fields', function () {
    // Missing required fields
    $incompleteData = [
        'make' => 'Toyota',
        'model' => 'Camry',
        // Missing vehicle_type_id, city_id, and other required fields
    ];

    $response = $this->actingAs($this->admin)->post('/management/vehicles', $incompleteData);

    // Should return with validation errors
    $response->assertSessionHasErrors(['vehicle_type_id', 'city_id', 'year', 'license_plate']);

    // No vehicle should be created
    $this->assertEquals(0, Vehicle::count());
});

test('vehicle can be saved as draft', function () {
    // Setup fake storage for file uploads
    Storage::fake('public');

    // Create file record for primary image
    $primaryImageFile = File::create([
        'title' => 'draft_car.jpg',
        'filename' => 'draft_car.jpg',
        'url' => '/storage/uploads/draft_car.jpg',
        'fileSize' => 1000,
        'fileType' => 'image/jpeg'
    ]);

    $vehicleData = [
        'vehicle_type_id' => $this->vehicleType->id,
        'make' => 'Toyota',
        'model' => 'Camry',
        'year' => 2022,
        'license_plate' => 'DRAFT123',
        'color' => 'Red',
        'mileage' => 10000,
        'transmission' => 'automatic',
        'fuel_type' => 'gasoline',
        'seats' => '5',
        'doors' => 4,
        'city_id' => $this->city->id,
        'daily_rate' => 45.00,
        'primary_image' => $primaryImageFile->id,
        'save_draft' => true, // This indicates it should be saved as draft
    ];

    $response = $this->actingAs($this->admin)->post('/management/vehicles', $vehicleData);

    // Check if redirected to vehicles index
    $response->assertRedirect(route('vehicles.index'));

    // Check if vehicle was created with draft status
    $this->assertDatabaseHas('vehicles', [
        'license_plate' => 'DRAFT123',
        'status' => 'draft',
    ]);

    // Check if the primary image was associated with the vehicle
    $vehicle = Vehicle::where('license_plate', 'DRAFT123')->first();
    $this->assertNotNull($vehicle);

    $vehicleImage = VehicleImage::where('vehicle_id', $vehicle->id)->first();
    $this->assertNotNull($vehicleImage);
    $this->assertEquals($primaryImageFile->id, $vehicleImage->file_id);
    $this->assertTrue($vehicleImage->is_primary);
});

test('vehicle creation requires login', function () {
    $response = $this->get('/management/vehicles/create');

    // Should redirect to login page
    $response->assertRedirect('/login');
});

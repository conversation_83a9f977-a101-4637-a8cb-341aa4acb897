<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'name' => 'file', 
    'fieldName' => null,
    'label' => 'Upload File',
    'multiple' => false,
    'accept' => '*/*',
    'required' => false,
    'fileIds' => '',
    'primaryFileId' => '',
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'name' => 'file', 
    'fieldName' => null,
    'label' => 'Upload File',
    'multiple' => false,
    'accept' => '*/*',
    'required' => false,
    'fileIds' => '',
    'primaryFileId' => '',
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div>
    <!-- Label -->
    <label for="<?php echo e($name); ?>" class="block text-sm font-medium text-gray-700 mb-1"><?php echo e($label); ?></label>

    <!-- Hidden inputs to store file IDs -->
    <input type="hidden" name="<?php echo e($fieldName ? $fieldName : $name); ?>" value="<?php echo e($fileIds); ?>">
    <?php if($multiple): ?>
        <input type="hidden" name="<?php echo e($fieldName ? $fieldName . '_primary' : $name . '_primary'); ?>" value="<?php echo e($primaryFileId); ?>">
    <?php endif; ?>

    <!-- File input -->
    <div class="flex flex-wrap gap-2 mb-2">
        <div class="flex-grow">
            <input
                type="file"
                id="<?php echo e($name); ?>"
                name="<?php echo e($fieldName ? $fieldName . '_file' : $name . '_file'); ?>"
                class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                <?php echo e($multiple ? 'multiple' : ''); ?>

                accept="<?php echo e($accept); ?>"
                <?php echo e($required ? 'required' : ''); ?>

            >
        </div>
    </div>
</div>
<?php /**PATH C:\laragon\www\carbnb\resources\views\components\file-upload-simple.blade.php ENDPATH**/ ?>
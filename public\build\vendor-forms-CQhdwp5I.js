var Ot={exports:{}};/*!
 * Quill Editor v1.3.7
 * https://quilljs.com/
 * Copyright (c) 2014, <PERSON>
 * Copyright (c) 2013, salesforce.com
 */var bi=Ot.exports,Dn;function jl(){return Dn||(Dn=1,function(e,t){(function(r,i){e.exports=i()})(typeof self<"u"?self:bi,function(){return function(n){var r={};function i(a){if(r[a])return r[a].exports;var s=r[a]={i:a,l:!1,exports:{}};return n[a].call(s.exports,s,s.exports,i),s.l=!0,s.exports}return i.m=n,i.c=r,i.d=function(a,s,d){i.o(a,s)||Object.defineProperty(a,s,{configurable:!1,enumerable:!0,get:d})},i.n=function(a){var s=a&&a.__esModule?function(){return a.default}:function(){return a};return i.d(s,"a",s),s},i.o=function(a,s){return Object.prototype.hasOwnProperty.call(a,s)},i.p="",i(i.s=109)}([function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0});var a=i(17),s=i(18),d=i(19),c=i(45),y=i(46),f=i(47),u=i(48),o=i(49),l=i(12),h=i(32),v=i(33),E=i(31),p=i(1),m={Scope:p.Scope,create:p.create,find:p.find,query:p.query,register:p.register,Container:a.default,Format:s.default,Leaf:d.default,Embed:u.default,Scroll:c.default,Block:f.default,Inline:y.default,Text:o.default,Attributor:{Attribute:l.default,Class:h.default,Style:v.default,Store:E.default}};r.default=m},function(n,r,i){var a=this&&this.__extends||function(){var E=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(p,m){p.__proto__=m}||function(p,m){for(var _ in m)m.hasOwnProperty(_)&&(p[_]=m[_])};return function(p,m){E(p,m);function _(){this.constructor=p}p.prototype=m===null?Object.create(m):(_.prototype=m.prototype,new _)}}();Object.defineProperty(r,"__esModule",{value:!0});var s=function(E){a(p,E);function p(m){var _=this;return m="[Parchment] "+m,_=E.call(this,m)||this,_.message=m,_.name=_.constructor.name,_}return p}(Error);r.ParchmentError=s;var d={},c={},y={},f={};r.DATA_KEY="__blot";var u;(function(E){E[E.TYPE=3]="TYPE",E[E.LEVEL=12]="LEVEL",E[E.ATTRIBUTE=13]="ATTRIBUTE",E[E.BLOT=14]="BLOT",E[E.INLINE=7]="INLINE",E[E.BLOCK=11]="BLOCK",E[E.BLOCK_BLOT=10]="BLOCK_BLOT",E[E.INLINE_BLOT=6]="INLINE_BLOT",E[E.BLOCK_ATTRIBUTE=9]="BLOCK_ATTRIBUTE",E[E.INLINE_ATTRIBUTE=5]="INLINE_ATTRIBUTE",E[E.ANY=15]="ANY"})(u=r.Scope||(r.Scope={}));function o(E,p){var m=h(E);if(m==null)throw new s("Unable to create "+E+" blot");var _=m,g=E instanceof Node||E.nodeType===Node.TEXT_NODE?E:_.create(p);return new _(g,p)}r.create=o;function l(E,p){return p===void 0&&(p=!1),E==null?null:E[r.DATA_KEY]!=null?E[r.DATA_KEY].blot:p?l(E.parentNode,p):null}r.find=l;function h(E,p){p===void 0&&(p=u.ANY);var m;if(typeof E=="string")m=f[E]||d[E];else if(E instanceof Text||E.nodeType===Node.TEXT_NODE)m=f.text;else if(typeof E=="number")E&u.LEVEL&u.BLOCK?m=f.block:E&u.LEVEL&u.INLINE&&(m=f.inline);else if(E instanceof HTMLElement){var _=(E.getAttribute("class")||"").split(/\s+/);for(var g in _)if(m=c[_[g]],m)break;m=m||y[E.tagName]}return m==null?null:p&u.LEVEL&m.scope&&p&u.TYPE&m.scope?m:null}r.query=h;function v(){for(var E=[],p=0;p<arguments.length;p++)E[p]=arguments[p];if(E.length>1)return E.map(function(g){return v(g)});var m=E[0];if(typeof m.blotName!="string"&&typeof m.attrName!="string")throw new s("Invalid definition");if(m.blotName==="abstract")throw new s("Cannot register abstract class");if(f[m.blotName||m.attrName]=m,typeof m.keyName=="string")d[m.keyName]=m;else if(m.className!=null&&(c[m.className]=m),m.tagName!=null){Array.isArray(m.tagName)?m.tagName=m.tagName.map(function(g){return g.toUpperCase()}):m.tagName=m.tagName.toUpperCase();var _=Array.isArray(m.tagName)?m.tagName:[m.tagName];_.forEach(function(g){(y[g]==null||m.className==null)&&(y[g]=m)})}return m}r.register=v},function(n,r,i){var a=i(51),s=i(11),d=i(3),c=i(20),y="\0",f=function(u){Array.isArray(u)?this.ops=u:u!=null&&Array.isArray(u.ops)?this.ops=u.ops:this.ops=[]};f.prototype.insert=function(u,o){var l={};return u.length===0?this:(l.insert=u,o!=null&&typeof o=="object"&&Object.keys(o).length>0&&(l.attributes=o),this.push(l))},f.prototype.delete=function(u){return u<=0?this:this.push({delete:u})},f.prototype.retain=function(u,o){if(u<=0)return this;var l={retain:u};return o!=null&&typeof o=="object"&&Object.keys(o).length>0&&(l.attributes=o),this.push(l)},f.prototype.push=function(u){var o=this.ops.length,l=this.ops[o-1];if(u=d(!0,{},u),typeof l=="object"){if(typeof u.delete=="number"&&typeof l.delete=="number")return this.ops[o-1]={delete:l.delete+u.delete},this;if(typeof l.delete=="number"&&u.insert!=null&&(o-=1,l=this.ops[o-1],typeof l!="object"))return this.ops.unshift(u),this;if(s(u.attributes,l.attributes)){if(typeof u.insert=="string"&&typeof l.insert=="string")return this.ops[o-1]={insert:l.insert+u.insert},typeof u.attributes=="object"&&(this.ops[o-1].attributes=u.attributes),this;if(typeof u.retain=="number"&&typeof l.retain=="number")return this.ops[o-1]={retain:l.retain+u.retain},typeof u.attributes=="object"&&(this.ops[o-1].attributes=u.attributes),this}}return o===this.ops.length?this.ops.push(u):this.ops.splice(o,0,u),this},f.prototype.chop=function(){var u=this.ops[this.ops.length-1];return u&&u.retain&&!u.attributes&&this.ops.pop(),this},f.prototype.filter=function(u){return this.ops.filter(u)},f.prototype.forEach=function(u){this.ops.forEach(u)},f.prototype.map=function(u){return this.ops.map(u)},f.prototype.partition=function(u){var o=[],l=[];return this.forEach(function(h){var v=u(h)?o:l;v.push(h)}),[o,l]},f.prototype.reduce=function(u,o){return this.ops.reduce(u,o)},f.prototype.changeLength=function(){return this.reduce(function(u,o){return o.insert?u+c.length(o):o.delete?u-o.delete:u},0)},f.prototype.length=function(){return this.reduce(function(u,o){return u+c.length(o)},0)},f.prototype.slice=function(u,o){u=u||0,typeof o!="number"&&(o=1/0);for(var l=[],h=c.iterator(this.ops),v=0;v<o&&h.hasNext();){var E;v<u?E=h.next(u-v):(E=h.next(o-v),l.push(E)),v+=c.length(E)}return new f(l)},f.prototype.compose=function(u){var o=c.iterator(this.ops),l=c.iterator(u.ops),h=[],v=l.peek();if(v!=null&&typeof v.retain=="number"&&v.attributes==null){for(var E=v.retain;o.peekType()==="insert"&&o.peekLength()<=E;)E-=o.peekLength(),h.push(o.next());v.retain-E>0&&l.next(v.retain-E)}for(var p=new f(h);o.hasNext()||l.hasNext();)if(l.peekType()==="insert")p.push(l.next());else if(o.peekType()==="delete")p.push(o.next());else{var m=Math.min(o.peekLength(),l.peekLength()),_=o.next(m),g=l.next(m);if(typeof g.retain=="number"){var b={};typeof _.retain=="number"?b.retain=m:b.insert=_.insert;var A=c.attributes.compose(_.attributes,g.attributes,typeof _.retain=="number");if(A&&(b.attributes=A),p.push(b),!l.hasNext()&&s(p.ops[p.ops.length-1],b)){var S=new f(o.rest());return p.concat(S).chop()}}else typeof g.delete=="number"&&typeof _.retain=="number"&&p.push(g)}return p.chop()},f.prototype.concat=function(u){var o=new f(this.ops.slice());return u.ops.length>0&&(o.push(u.ops[0]),o.ops=o.ops.concat(u.ops.slice(1))),o},f.prototype.diff=function(u,o){if(this.ops===u.ops)return new f;var l=[this,u].map(function(m){return m.map(function(_){if(_.insert!=null)return typeof _.insert=="string"?_.insert:y;var g=m===u?"on":"with";throw new Error("diff() called "+g+" non-document")}).join("")}),h=new f,v=a(l[0],l[1],o),E=c.iterator(this.ops),p=c.iterator(u.ops);return v.forEach(function(m){for(var _=m[1].length;_>0;){var g=0;switch(m[0]){case a.INSERT:g=Math.min(p.peekLength(),_),h.push(p.next(g));break;case a.DELETE:g=Math.min(_,E.peekLength()),E.next(g),h.delete(g);break;case a.EQUAL:g=Math.min(E.peekLength(),p.peekLength(),_);var b=E.next(g),A=p.next(g);s(b.insert,A.insert)?h.retain(g,c.attributes.diff(b.attributes,A.attributes)):h.push(A).delete(g);break}_-=g}}),h.chop()},f.prototype.eachLine=function(u,o){o=o||`
`;for(var l=c.iterator(this.ops),h=new f,v=0;l.hasNext();){if(l.peekType()!=="insert")return;var E=l.peek(),p=c.length(E)-l.peekLength(),m=typeof E.insert=="string"?E.insert.indexOf(o,p)-p:-1;if(m<0)h.push(l.next());else if(m>0)h.push(l.next(m));else{if(u(h,l.next(1).attributes||{},v)===!1)return;v+=1,h=new f}}h.length()>0&&u(h,{},v)},f.prototype.transform=function(u,o){if(o=!!o,typeof u=="number")return this.transformPosition(u,o);for(var l=c.iterator(this.ops),h=c.iterator(u.ops),v=new f;l.hasNext()||h.hasNext();)if(l.peekType()==="insert"&&(o||h.peekType()!=="insert"))v.retain(c.length(l.next()));else if(h.peekType()==="insert")v.push(h.next());else{var E=Math.min(l.peekLength(),h.peekLength()),p=l.next(E),m=h.next(E);if(p.delete)continue;m.delete?v.push(m):v.retain(E,c.attributes.transform(p.attributes,m.attributes,o))}return v.chop()},f.prototype.transformPosition=function(u,o){o=!!o;for(var l=c.iterator(this.ops),h=0;l.hasNext()&&h<=u;){var v=l.peekLength(),E=l.peekType();if(l.next(),E==="delete"){u-=Math.min(v,u-h);continue}else E==="insert"&&(h<u||!o)&&(u+=v);h+=v}return u},n.exports=f},function(n,r){var i=Object.prototype.hasOwnProperty,a=Object.prototype.toString,s=Object.defineProperty,d=Object.getOwnPropertyDescriptor,c=function(l){return typeof Array.isArray=="function"?Array.isArray(l):a.call(l)==="[object Array]"},y=function(l){if(!l||a.call(l)!=="[object Object]")return!1;var h=i.call(l,"constructor"),v=l.constructor&&l.constructor.prototype&&i.call(l.constructor.prototype,"isPrototypeOf");if(l.constructor&&!h&&!v)return!1;var E;for(E in l);return typeof E>"u"||i.call(l,E)},f=function(l,h){s&&h.name==="__proto__"?s(l,h.name,{enumerable:!0,configurable:!0,value:h.newValue,writable:!0}):l[h.name]=h.newValue},u=function(l,h){if(h==="__proto__")if(i.call(l,h)){if(d)return d(l,h).value}else return;return l[h]};n.exports=function o(){var l,h,v,E,p,m,_=arguments[0],g=1,b=arguments.length,A=!1;for(typeof _=="boolean"&&(A=_,_=arguments[1]||{},g=2),(_==null||typeof _!="object"&&typeof _!="function")&&(_={});g<b;++g)if(l=arguments[g],l!=null)for(h in l)v=u(_,h),E=u(l,h),_!==E&&(A&&E&&(y(E)||(p=c(E)))?(p?(p=!1,m=v&&c(v)?v:[]):m=v&&y(v)?v:{},f(_,{name:h,newValue:o(A,m,E)})):typeof E<"u"&&f(_,{name:h,newValue:E}));return _}},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0}),r.default=r.BlockEmbed=r.bubbleFormats=void 0;var a=function(){function T(O,L){for(var D=0;D<L.length;D++){var M=L[D];M.enumerable=M.enumerable||!1,M.configurable=!0,"value"in M&&(M.writable=!0),Object.defineProperty(O,M.key,M)}}return function(O,L,D){return L&&T(O.prototype,L),D&&T(O,D),O}}(),s=function T(O,L,D){O===null&&(O=Function.prototype);var M=Object.getOwnPropertyDescriptor(O,L);if(M===void 0){var x=Object.getPrototypeOf(O);return x===null?void 0:T(x,L,D)}else{if("value"in M)return M.value;var B=M.get;return B===void 0?void 0:B.call(D)}},d=i(3),c=_(d),y=i(2),f=_(y),u=i(0),o=_(u),l=i(16),h=_(l),v=i(6),E=_(v),p=i(7),m=_(p);function _(T){return T&&T.__esModule?T:{default:T}}function g(T,O){if(!(T instanceof O))throw new TypeError("Cannot call a class as a function")}function b(T,O){if(!T)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return O&&(typeof O=="object"||typeof O=="function")?O:T}function A(T,O){if(typeof O!="function"&&O!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof O);T.prototype=Object.create(O&&O.prototype,{constructor:{value:T,enumerable:!1,writable:!0,configurable:!0}}),O&&(Object.setPrototypeOf?Object.setPrototypeOf(T,O):T.__proto__=O)}var S=1,I=function(T){A(O,T);function O(){return g(this,O),b(this,(O.__proto__||Object.getPrototypeOf(O)).apply(this,arguments))}return a(O,[{key:"attach",value:function(){s(O.prototype.__proto__||Object.getPrototypeOf(O.prototype),"attach",this).call(this),this.attributes=new o.default.Attributor.Store(this.domNode)}},{key:"delta",value:function(){return new f.default().insert(this.value(),(0,c.default)(this.formats(),this.attributes.values()))}},{key:"format",value:function(D,M){var x=o.default.query(D,o.default.Scope.BLOCK_ATTRIBUTE);x!=null&&this.attributes.attribute(x,M)}},{key:"formatAt",value:function(D,M,x,B){this.format(x,B)}},{key:"insertAt",value:function(D,M,x){if(typeof M=="string"&&M.endsWith(`
`)){var B=o.default.create(N.blotName);this.parent.insertBefore(B,D===0?this:this.next),B.insertAt(0,M.slice(0,-1))}else s(O.prototype.__proto__||Object.getPrototypeOf(O.prototype),"insertAt",this).call(this,D,M,x)}}]),O}(o.default.Embed);I.scope=o.default.Scope.BLOCK_BLOT;var N=function(T){A(O,T);function O(L){g(this,O);var D=b(this,(O.__proto__||Object.getPrototypeOf(O)).call(this,L));return D.cache={},D}return a(O,[{key:"delta",value:function(){return this.cache.delta==null&&(this.cache.delta=this.descendants(o.default.Leaf).reduce(function(D,M){return M.length()===0?D:D.insert(M.value(),w(M))},new f.default).insert(`
`,w(this))),this.cache.delta}},{key:"deleteAt",value:function(D,M){s(O.prototype.__proto__||Object.getPrototypeOf(O.prototype),"deleteAt",this).call(this,D,M),this.cache={}}},{key:"formatAt",value:function(D,M,x,B){M<=0||(o.default.query(x,o.default.Scope.BLOCK)?D+M===this.length()&&this.format(x,B):s(O.prototype.__proto__||Object.getPrototypeOf(O.prototype),"formatAt",this).call(this,D,Math.min(M,this.length()-D-1),x,B),this.cache={})}},{key:"insertAt",value:function(D,M,x){if(x!=null)return s(O.prototype.__proto__||Object.getPrototypeOf(O.prototype),"insertAt",this).call(this,D,M,x);if(M.length!==0){var B=M.split(`
`),V=B.shift();V.length>0&&(D<this.length()-1||this.children.tail==null?s(O.prototype.__proto__||Object.getPrototypeOf(O.prototype),"insertAt",this).call(this,Math.min(D,this.length()-1),V):this.children.tail.insertAt(this.children.tail.length(),V),this.cache={});var F=this;B.reduce(function(k,R){return F=F.split(k,!0),F.insertAt(0,R),R.length},D+V.length)}}},{key:"insertBefore",value:function(D,M){var x=this.children.head;s(O.prototype.__proto__||Object.getPrototypeOf(O.prototype),"insertBefore",this).call(this,D,M),x instanceof h.default&&x.remove(),this.cache={}}},{key:"length",value:function(){return this.cache.length==null&&(this.cache.length=s(O.prototype.__proto__||Object.getPrototypeOf(O.prototype),"length",this).call(this)+S),this.cache.length}},{key:"moveChildren",value:function(D,M){s(O.prototype.__proto__||Object.getPrototypeOf(O.prototype),"moveChildren",this).call(this,D,M),this.cache={}}},{key:"optimize",value:function(D){s(O.prototype.__proto__||Object.getPrototypeOf(O.prototype),"optimize",this).call(this,D),this.cache={}}},{key:"path",value:function(D){return s(O.prototype.__proto__||Object.getPrototypeOf(O.prototype),"path",this).call(this,D,!0)}},{key:"removeChild",value:function(D){s(O.prototype.__proto__||Object.getPrototypeOf(O.prototype),"removeChild",this).call(this,D),this.cache={}}},{key:"split",value:function(D){var M=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(M&&(D===0||D>=this.length()-S)){var x=this.clone();return D===0?(this.parent.insertBefore(x,this),this):(this.parent.insertBefore(x,this.next),x)}else{var B=s(O.prototype.__proto__||Object.getPrototypeOf(O.prototype),"split",this).call(this,D,M);return this.cache={},B}}}]),O}(o.default.Block);N.blotName="block",N.tagName="P",N.defaultChild="break",N.allowedChildren=[E.default,o.default.Embed,m.default];function w(T){var O=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return T==null||(typeof T.formats=="function"&&(O=(0,c.default)(O,T.formats())),T.parent==null||T.parent.blotName=="scroll"||T.parent.statics.scope!==T.statics.scope)?O:w(T.parent,O)}r.bubbleFormats=w,r.BlockEmbed=I,r.default=N},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0}),r.default=r.overload=r.expandConfig=void 0;var a=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(F){return typeof F}:function(F){return F&&typeof Symbol=="function"&&F.constructor===Symbol&&F!==Symbol.prototype?"symbol":typeof F},s=function(){function F(k,R){var C=[],q=!0,G=!1,U=void 0;try{for(var P=k[Symbol.iterator](),j;!(q=(j=P.next()).done)&&(C.push(j.value),!(R&&C.length===R));q=!0);}catch(H){G=!0,U=H}finally{try{!q&&P.return&&P.return()}finally{if(G)throw U}}return C}return function(k,R){if(Array.isArray(k))return k;if(Symbol.iterator in Object(k))return F(k,R);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),d=function(){function F(k,R){for(var C=0;C<R.length;C++){var q=R[C];q.enumerable=q.enumerable||!1,q.configurable=!0,"value"in q&&(q.writable=!0),Object.defineProperty(k,q.key,q)}}return function(k,R,C){return R&&F(k.prototype,R),C&&F(k,C),k}}();i(50);var c=i(2),y=w(c),f=i(14),u=w(f),o=i(8),l=w(o),h=i(9),v=w(h),E=i(0),p=w(E),m=i(15),_=w(m),g=i(3),b=w(g),A=i(10),S=w(A),I=i(34),N=w(I);function w(F){return F&&F.__esModule?F:{default:F}}function T(F,k,R){return k in F?Object.defineProperty(F,k,{value:R,enumerable:!0,configurable:!0,writable:!0}):F[k]=R,F}function O(F,k){if(!(F instanceof k))throw new TypeError("Cannot call a class as a function")}var L=(0,S.default)("quill"),D=function(){d(F,null,[{key:"debug",value:function(R){R===!0&&(R="log"),S.default.level(R)}},{key:"find",value:function(R){return R.__quill||p.default.find(R)}},{key:"import",value:function(R){return this.imports[R]==null&&L.error("Cannot import "+R+". Are you sure it was registered?"),this.imports[R]}},{key:"register",value:function(R,C){var q=this,G=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;if(typeof R!="string"){var U=R.attrName||R.blotName;typeof U=="string"?this.register("formats/"+U,R,C):Object.keys(R).forEach(function(P){q.register(P,R[P],C)})}else this.imports[R]!=null&&!G&&L.warn("Overwriting "+R+" with",C),this.imports[R]=C,(R.startsWith("blots/")||R.startsWith("formats/"))&&C.blotName!=="abstract"?p.default.register(C):R.startsWith("modules")&&typeof C.register=="function"&&C.register()}}]);function F(k){var R=this,C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(O(this,F),this.options=M(k,C),this.container=this.options.container,this.container==null)return L.error("Invalid Quill container",k);this.options.debug&&F.debug(this.options.debug);var q=this.container.innerHTML.trim();this.container.classList.add("ql-container"),this.container.innerHTML="",this.container.__quill=this,this.root=this.addContainer("ql-editor"),this.root.classList.add("ql-blank"),this.root.setAttribute("data-gramm",!1),this.scrollingContainer=this.options.scrollingContainer||this.root,this.emitter=new l.default,this.scroll=p.default.create(this.root,{emitter:this.emitter,whitelist:this.options.formats}),this.editor=new u.default(this.scroll),this.selection=new _.default(this.scroll,this.emitter),this.theme=new this.options.theme(this,this.options),this.keyboard=this.theme.addModule("keyboard"),this.clipboard=this.theme.addModule("clipboard"),this.history=this.theme.addModule("history"),this.theme.init(),this.emitter.on(l.default.events.EDITOR_CHANGE,function(U){U===l.default.events.TEXT_CHANGE&&R.root.classList.toggle("ql-blank",R.editor.isBlank())}),this.emitter.on(l.default.events.SCROLL_UPDATE,function(U,P){var j=R.selection.lastRange,H=j&&j.length===0?j.index:void 0;x.call(R,function(){return R.editor.update(null,P,H)},U)});var G=this.clipboard.convert(`<div class='ql-editor' style="white-space: normal;">`+q+"<p><br></p></div>");this.setContents(G),this.history.clear(),this.options.placeholder&&this.root.setAttribute("data-placeholder",this.options.placeholder),this.options.readOnly&&this.disable()}return d(F,[{key:"addContainer",value:function(R){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;if(typeof R=="string"){var q=R;R=document.createElement("div"),R.classList.add(q)}return this.container.insertBefore(R,C),R}},{key:"blur",value:function(){this.selection.setRange(null)}},{key:"deleteText",value:function(R,C,q){var G=this,U=B(R,C,q),P=s(U,4);return R=P[0],C=P[1],q=P[3],x.call(this,function(){return G.editor.deleteText(R,C)},q,R,-1*C)}},{key:"disable",value:function(){this.enable(!1)}},{key:"enable",value:function(){var R=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this.scroll.enable(R),this.container.classList.toggle("ql-disabled",!R)}},{key:"focus",value:function(){var R=this.scrollingContainer.scrollTop;this.selection.focus(),this.scrollingContainer.scrollTop=R,this.scrollIntoView()}},{key:"format",value:function(R,C){var q=this,G=arguments.length>2&&arguments[2]!==void 0?arguments[2]:l.default.sources.API;return x.call(this,function(){var U=q.getSelection(!0),P=new y.default;if(U==null)return P;if(p.default.query(R,p.default.Scope.BLOCK))P=q.editor.formatLine(U.index,U.length,T({},R,C));else{if(U.length===0)return q.selection.format(R,C),P;P=q.editor.formatText(U.index,U.length,T({},R,C))}return q.setSelection(U,l.default.sources.SILENT),P},G)}},{key:"formatLine",value:function(R,C,q,G,U){var P=this,j=void 0,H=B(R,C,q,G,U),z=s(H,4);return R=z[0],C=z[1],j=z[2],U=z[3],x.call(this,function(){return P.editor.formatLine(R,C,j)},U,R,0)}},{key:"formatText",value:function(R,C,q,G,U){var P=this,j=void 0,H=B(R,C,q,G,U),z=s(H,4);return R=z[0],C=z[1],j=z[2],U=z[3],x.call(this,function(){return P.editor.formatText(R,C,j)},U,R,0)}},{key:"getBounds",value:function(R){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,q=void 0;typeof R=="number"?q=this.selection.getBounds(R,C):q=this.selection.getBounds(R.index,R.length);var G=this.container.getBoundingClientRect();return{bottom:q.bottom-G.top,height:q.height,left:q.left-G.left,right:q.right-G.left,top:q.top-G.top,width:q.width}}},{key:"getContents",value:function(){var R=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.getLength()-R,q=B(R,C),G=s(q,2);return R=G[0],C=G[1],this.editor.getContents(R,C)}},{key:"getFormat",value:function(){var R=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.getSelection(!0),C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return typeof R=="number"?this.editor.getFormat(R,C):this.editor.getFormat(R.index,R.length)}},{key:"getIndex",value:function(R){return R.offset(this.scroll)}},{key:"getLength",value:function(){return this.scroll.length()}},{key:"getLeaf",value:function(R){return this.scroll.leaf(R)}},{key:"getLine",value:function(R){return this.scroll.line(R)}},{key:"getLines",value:function(){var R=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.MAX_VALUE;return typeof R!="number"?this.scroll.lines(R.index,R.length):this.scroll.lines(R,C)}},{key:"getModule",value:function(R){return this.theme.modules[R]}},{key:"getSelection",value:function(){var R=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;return R&&this.focus(),this.update(),this.selection.getRange()[0]}},{key:"getText",value:function(){var R=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.getLength()-R,q=B(R,C),G=s(q,2);return R=G[0],C=G[1],this.editor.getText(R,C)}},{key:"hasFocus",value:function(){return this.selection.hasFocus()}},{key:"insertEmbed",value:function(R,C,q){var G=this,U=arguments.length>3&&arguments[3]!==void 0?arguments[3]:F.sources.API;return x.call(this,function(){return G.editor.insertEmbed(R,C,q)},U,R)}},{key:"insertText",value:function(R,C,q,G,U){var P=this,j=void 0,H=B(R,0,q,G,U),z=s(H,4);return R=z[0],j=z[2],U=z[3],x.call(this,function(){return P.editor.insertText(R,C,j)},U,R,C.length)}},{key:"isEnabled",value:function(){return!this.container.classList.contains("ql-disabled")}},{key:"off",value:function(){return this.emitter.off.apply(this.emitter,arguments)}},{key:"on",value:function(){return this.emitter.on.apply(this.emitter,arguments)}},{key:"once",value:function(){return this.emitter.once.apply(this.emitter,arguments)}},{key:"pasteHTML",value:function(R,C,q){this.clipboard.dangerouslyPasteHTML(R,C,q)}},{key:"removeFormat",value:function(R,C,q){var G=this,U=B(R,C,q),P=s(U,4);return R=P[0],C=P[1],q=P[3],x.call(this,function(){return G.editor.removeFormat(R,C)},q,R)}},{key:"scrollIntoView",value:function(){this.selection.scrollIntoView(this.scrollingContainer)}},{key:"setContents",value:function(R){var C=this,q=arguments.length>1&&arguments[1]!==void 0?arguments[1]:l.default.sources.API;return x.call(this,function(){R=new y.default(R);var G=C.getLength(),U=C.editor.deleteText(0,G),P=C.editor.applyDelta(R),j=P.ops[P.ops.length-1];j!=null&&typeof j.insert=="string"&&j.insert[j.insert.length-1]===`
`&&(C.editor.deleteText(C.getLength()-1,1),P.delete(1));var H=U.compose(P);return H},q)}},{key:"setSelection",value:function(R,C,q){if(R==null)this.selection.setRange(null,C||F.sources.API);else{var G=B(R,C,q),U=s(G,4);R=U[0],C=U[1],q=U[3],this.selection.setRange(new m.Range(R,C),q),q!==l.default.sources.SILENT&&this.selection.scrollIntoView(this.scrollingContainer)}}},{key:"setText",value:function(R){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:l.default.sources.API,q=new y.default().insert(R);return this.setContents(q,C)}},{key:"update",value:function(){var R=arguments.length>0&&arguments[0]!==void 0?arguments[0]:l.default.sources.USER,C=this.scroll.update(R);return this.selection.update(R),C}},{key:"updateContents",value:function(R){var C=this,q=arguments.length>1&&arguments[1]!==void 0?arguments[1]:l.default.sources.API;return x.call(this,function(){return R=new y.default(R),C.editor.applyDelta(R,q)},q,!0)}}]),F}();D.DEFAULTS={bounds:null,formats:null,modules:{},placeholder:"",readOnly:!1,scrollingContainer:null,strict:!0,theme:"default"},D.events=l.default.events,D.sources=l.default.sources,D.version="1.3.7",D.imports={delta:y.default,parchment:p.default,"core/module":v.default,"core/theme":N.default};function M(F,k){if(k=(0,b.default)(!0,{container:F,modules:{clipboard:!0,keyboard:!0,history:!0}},k),!k.theme||k.theme===D.DEFAULTS.theme)k.theme=N.default;else if(k.theme=D.import("themes/"+k.theme),k.theme==null)throw new Error("Invalid theme "+k.theme+". Did you register it?");var R=(0,b.default)(!0,{},k.theme.DEFAULTS);[R,k].forEach(function(G){G.modules=G.modules||{},Object.keys(G.modules).forEach(function(U){G.modules[U]===!0&&(G.modules[U]={})})});var C=Object.keys(R.modules).concat(Object.keys(k.modules)),q=C.reduce(function(G,U){var P=D.import("modules/"+U);return P==null?L.error("Cannot load "+U+" module. Are you sure you registered it?"):G[U]=P.DEFAULTS||{},G},{});return k.modules!=null&&k.modules.toolbar&&k.modules.toolbar.constructor!==Object&&(k.modules.toolbar={container:k.modules.toolbar}),k=(0,b.default)(!0,{},D.DEFAULTS,{modules:q},R,k),["bounds","container","scrollingContainer"].forEach(function(G){typeof k[G]=="string"&&(k[G]=document.querySelector(k[G]))}),k.modules=Object.keys(k.modules).reduce(function(G,U){return k.modules[U]&&(G[U]=k.modules[U]),G},{}),k}function x(F,k,R,C){if(this.options.strict&&!this.isEnabled()&&k===l.default.sources.USER)return new y.default;var q=R==null?null:this.getSelection(),G=this.editor.delta,U=F();if(q!=null&&(R===!0&&(R=q.index),C==null?q=V(q,U,k):C!==0&&(q=V(q,R,C,k)),this.setSelection(q,l.default.sources.SILENT)),U.length()>0){var P,j=[l.default.events.TEXT_CHANGE,U,G,k];if((P=this.emitter).emit.apply(P,[l.default.events.EDITOR_CHANGE].concat(j)),k!==l.default.sources.SILENT){var H;(H=this.emitter).emit.apply(H,j)}}return U}function B(F,k,R,C,q){var G={};return typeof F.index=="number"&&typeof F.length=="number"?typeof k!="number"?(q=C,C=R,R=k,k=F.length,F=F.index):(k=F.length,F=F.index):typeof k!="number"&&(q=C,C=R,R=k,k=0),(typeof R>"u"?"undefined":a(R))==="object"?(G=R,q=C):typeof R=="string"&&(C!=null?G[R]=C:q=R),q=q||l.default.sources.API,[F,k,G,q]}function V(F,k,R,C){if(F==null)return null;var q=void 0,G=void 0;if(k instanceof y.default){var U=[F.index,F.index+F.length].map(function(z){return k.transformPosition(z,C!==l.default.sources.USER)}),P=s(U,2);q=P[0],G=P[1]}else{var j=[F.index,F.index+F.length].map(function(z){return z<k||z===k&&C===l.default.sources.USER?z:R>=0?z+R:Math.max(k,z+R)}),H=s(j,2);q=H[0],G=H[1]}return new m.Range(q,G-q)}r.expandConfig=M,r.overload=B,r.default=D},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0});var a=function(){function E(p,m){for(var _=0;_<m.length;_++){var g=m[_];g.enumerable=g.enumerable||!1,g.configurable=!0,"value"in g&&(g.writable=!0),Object.defineProperty(p,g.key,g)}}return function(p,m,_){return m&&E(p.prototype,m),_&&E(p,_),p}}(),s=function E(p,m,_){p===null&&(p=Function.prototype);var g=Object.getOwnPropertyDescriptor(p,m);if(g===void 0){var b=Object.getPrototypeOf(p);return b===null?void 0:E(b,m,_)}else{if("value"in g)return g.value;var A=g.get;return A===void 0?void 0:A.call(_)}},d=i(7),c=u(d),y=i(0),f=u(y);function u(E){return E&&E.__esModule?E:{default:E}}function o(E,p){if(!(E instanceof p))throw new TypeError("Cannot call a class as a function")}function l(E,p){if(!E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return p&&(typeof p=="object"||typeof p=="function")?p:E}function h(E,p){if(typeof p!="function"&&p!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof p);E.prototype=Object.create(p&&p.prototype,{constructor:{value:E,enumerable:!1,writable:!0,configurable:!0}}),p&&(Object.setPrototypeOf?Object.setPrototypeOf(E,p):E.__proto__=p)}var v=function(E){h(p,E);function p(){return o(this,p),l(this,(p.__proto__||Object.getPrototypeOf(p)).apply(this,arguments))}return a(p,[{key:"formatAt",value:function(_,g,b,A){if(p.compare(this.statics.blotName,b)<0&&f.default.query(b,f.default.Scope.BLOT)){var S=this.isolate(_,g);A&&S.wrap(b,A)}else s(p.prototype.__proto__||Object.getPrototypeOf(p.prototype),"formatAt",this).call(this,_,g,b,A)}},{key:"optimize",value:function(_){if(s(p.prototype.__proto__||Object.getPrototypeOf(p.prototype),"optimize",this).call(this,_),this.parent instanceof p&&p.compare(this.statics.blotName,this.parent.statics.blotName)>0){var g=this.parent.isolate(this.offset(),this.length());this.moveChildren(g),g.wrap(this)}}}],[{key:"compare",value:function(_,g){var b=p.order.indexOf(_),A=p.order.indexOf(g);return b>=0||A>=0?b-A:_===g?0:_<g?-1:1}}]),p}(f.default.Inline);v.allowedChildren=[v,f.default.Embed,c.default],v.order=["cursor","inline","underline","strike","italic","bold","script","link","code"],r.default=v},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0});var a=i(0),s=d(a);function d(o){return o&&o.__esModule?o:{default:o}}function c(o,l){if(!(o instanceof l))throw new TypeError("Cannot call a class as a function")}function y(o,l){if(!o)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l&&(typeof l=="object"||typeof l=="function")?l:o}function f(o,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof l);o.prototype=Object.create(l&&l.prototype,{constructor:{value:o,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(o,l):o.__proto__=l)}var u=function(o){f(l,o);function l(){return c(this,l),y(this,(l.__proto__||Object.getPrototypeOf(l)).apply(this,arguments))}return l}(s.default.Text);r.default=u},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0});var a=function(){function m(_,g){for(var b=0;b<g.length;b++){var A=g[b];A.enumerable=A.enumerable||!1,A.configurable=!0,"value"in A&&(A.writable=!0),Object.defineProperty(_,A.key,A)}}return function(_,g,b){return g&&m(_.prototype,g),b&&m(_,b),_}}(),s=function m(_,g,b){_===null&&(_=Function.prototype);var A=Object.getOwnPropertyDescriptor(_,g);if(A===void 0){var S=Object.getPrototypeOf(_);return S===null?void 0:m(S,g,b)}else{if("value"in A)return A.value;var I=A.get;return I===void 0?void 0:I.call(b)}},d=i(54),c=u(d),y=i(10),f=u(y);function u(m){return m&&m.__esModule?m:{default:m}}function o(m,_){if(!(m instanceof _))throw new TypeError("Cannot call a class as a function")}function l(m,_){if(!m)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return _&&(typeof _=="object"||typeof _=="function")?_:m}function h(m,_){if(typeof _!="function"&&_!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof _);m.prototype=Object.create(_&&_.prototype,{constructor:{value:m,enumerable:!1,writable:!0,configurable:!0}}),_&&(Object.setPrototypeOf?Object.setPrototypeOf(m,_):m.__proto__=_)}var v=(0,f.default)("quill:events"),E=["selectionchange","mousedown","mouseup","click"];E.forEach(function(m){document.addEventListener(m,function(){for(var _=arguments.length,g=Array(_),b=0;b<_;b++)g[b]=arguments[b];[].slice.call(document.querySelectorAll(".ql-container")).forEach(function(A){if(A.__quill&&A.__quill.emitter){var S;(S=A.__quill.emitter).handleDOM.apply(S,g)}})})});var p=function(m){h(_,m);function _(){o(this,_);var g=l(this,(_.__proto__||Object.getPrototypeOf(_)).call(this));return g.listeners={},g.on("error",v.error),g}return a(_,[{key:"emit",value:function(){v.log.apply(v,arguments),s(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"emit",this).apply(this,arguments)}},{key:"handleDOM",value:function(b){for(var A=arguments.length,S=Array(A>1?A-1:0),I=1;I<A;I++)S[I-1]=arguments[I];(this.listeners[b.type]||[]).forEach(function(N){var w=N.node,T=N.handler;(b.target===w||w.contains(b.target))&&T.apply(void 0,[b].concat(S))})}},{key:"listenDOM",value:function(b,A,S){this.listeners[b]||(this.listeners[b]=[]),this.listeners[b].push({node:A,handler:S})}}]),_}(c.default);p.events={EDITOR_CHANGE:"editor-change",SCROLL_BEFORE_UPDATE:"scroll-before-update",SCROLL_OPTIMIZE:"scroll-optimize",SCROLL_UPDATE:"scroll-update",SELECTION_CHANGE:"selection-change",TEXT_CHANGE:"text-change"},p.sources={API:"api",SILENT:"silent",USER:"user"},r.default=p},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0});function a(d,c){if(!(d instanceof c))throw new TypeError("Cannot call a class as a function")}var s=function d(c){var y=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};a(this,d),this.quill=c,this.options=y};s.DEFAULTS={},r.default=s},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0});var a=["error","warn","log","info"],s="warn";function d(y){if(a.indexOf(y)<=a.indexOf(s)){for(var f,u=arguments.length,o=Array(u>1?u-1:0),l=1;l<u;l++)o[l-1]=arguments[l];(f=console)[y].apply(f,o)}}function c(y){return a.reduce(function(f,u){return f[u]=d.bind(console,u,y),f},{})}d.level=c.level=function(y){s=y},r.default=c},function(n,r,i){var a=Array.prototype.slice,s=i(52),d=i(53),c=n.exports=function(o,l,h){return h||(h={}),o===l?!0:o instanceof Date&&l instanceof Date?o.getTime()===l.getTime():!o||!l||typeof o!="object"&&typeof l!="object"?h.strict?o===l:o==l:u(o,l,h)};function y(o){return o==null}function f(o){return!(!o||typeof o!="object"||typeof o.length!="number"||typeof o.copy!="function"||typeof o.slice!="function"||o.length>0&&typeof o[0]!="number")}function u(o,l,h){var v,E;if(y(o)||y(l)||o.prototype!==l.prototype)return!1;if(d(o))return d(l)?(o=a.call(o),l=a.call(l),c(o,l,h)):!1;if(f(o)){if(!f(l)||o.length!==l.length)return!1;for(v=0;v<o.length;v++)if(o[v]!==l[v])return!1;return!0}try{var p=s(o),m=s(l)}catch{return!1}if(p.length!=m.length)return!1;for(p.sort(),m.sort(),v=p.length-1;v>=0;v--)if(p[v]!=m[v])return!1;for(v=p.length-1;v>=0;v--)if(E=p[v],!c(o[E],l[E],h))return!1;return typeof o==typeof l}},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0});var a=i(1),s=function(){function d(c,y,f){f===void 0&&(f={}),this.attrName=c,this.keyName=y;var u=a.Scope.TYPE&a.Scope.ATTRIBUTE;f.scope!=null?this.scope=f.scope&a.Scope.LEVEL|u:this.scope=a.Scope.ATTRIBUTE,f.whitelist!=null&&(this.whitelist=f.whitelist)}return d.keys=function(c){return[].map.call(c.attributes,function(y){return y.name})},d.prototype.add=function(c,y){return this.canAdd(c,y)?(c.setAttribute(this.keyName,y),!0):!1},d.prototype.canAdd=function(c,y){var f=a.query(c,a.Scope.BLOT&(this.scope|a.Scope.TYPE));return f==null?!1:this.whitelist==null?!0:typeof y=="string"?this.whitelist.indexOf(y.replace(/["']/g,""))>-1:this.whitelist.indexOf(y)>-1},d.prototype.remove=function(c){c.removeAttribute(this.keyName)},d.prototype.value=function(c){var y=c.getAttribute(this.keyName);return this.canAdd(c,y)&&y?y:""},d}();r.default=s},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0}),r.default=r.Code=void 0;var a=function(){function I(N,w){var T=[],O=!0,L=!1,D=void 0;try{for(var M=N[Symbol.iterator](),x;!(O=(x=M.next()).done)&&(T.push(x.value),!(w&&T.length===w));O=!0);}catch(B){L=!0,D=B}finally{try{!O&&M.return&&M.return()}finally{if(L)throw D}}return T}return function(N,w){if(Array.isArray(N))return N;if(Symbol.iterator in Object(N))return I(N,w);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),s=function(){function I(N,w){for(var T=0;T<w.length;T++){var O=w[T];O.enumerable=O.enumerable||!1,O.configurable=!0,"value"in O&&(O.writable=!0),Object.defineProperty(N,O.key,O)}}return function(N,w,T){return w&&I(N.prototype,w),T&&I(N,T),N}}(),d=function I(N,w,T){N===null&&(N=Function.prototype);var O=Object.getOwnPropertyDescriptor(N,w);if(O===void 0){var L=Object.getPrototypeOf(N);return L===null?void 0:I(L,w,T)}else{if("value"in O)return O.value;var D=O.get;return D===void 0?void 0:D.call(T)}},c=i(2),y=m(c),f=i(0),u=m(f),o=i(4),l=m(o),h=i(6),v=m(h),E=i(7),p=m(E);function m(I){return I&&I.__esModule?I:{default:I}}function _(I,N){if(!(I instanceof N))throw new TypeError("Cannot call a class as a function")}function g(I,N){if(!I)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return N&&(typeof N=="object"||typeof N=="function")?N:I}function b(I,N){if(typeof N!="function"&&N!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof N);I.prototype=Object.create(N&&N.prototype,{constructor:{value:I,enumerable:!1,writable:!0,configurable:!0}}),N&&(Object.setPrototypeOf?Object.setPrototypeOf(I,N):I.__proto__=N)}var A=function(I){b(N,I);function N(){return _(this,N),g(this,(N.__proto__||Object.getPrototypeOf(N)).apply(this,arguments))}return N}(v.default);A.blotName="code",A.tagName="CODE";var S=function(I){b(N,I);function N(){return _(this,N),g(this,(N.__proto__||Object.getPrototypeOf(N)).apply(this,arguments))}return s(N,[{key:"delta",value:function(){var T=this,O=this.domNode.textContent;return O.endsWith(`
`)&&(O=O.slice(0,-1)),O.split(`
`).reduce(function(L,D){return L.insert(D).insert(`
`,T.formats())},new y.default)}},{key:"format",value:function(T,O){if(!(T===this.statics.blotName&&O)){var L=this.descendant(p.default,this.length()-1),D=a(L,1),M=D[0];M!=null&&M.deleteAt(M.length()-1,1),d(N.prototype.__proto__||Object.getPrototypeOf(N.prototype),"format",this).call(this,T,O)}}},{key:"formatAt",value:function(T,O,L,D){if(O!==0&&!(u.default.query(L,u.default.Scope.BLOCK)==null||L===this.statics.blotName&&D===this.statics.formats(this.domNode))){var M=this.newlineIndex(T);if(!(M<0||M>=T+O)){var x=this.newlineIndex(T,!0)+1,B=M-x+1,V=this.isolate(x,B),F=V.next;V.format(L,D),F instanceof N&&F.formatAt(0,T-x+O-B,L,D)}}}},{key:"insertAt",value:function(T,O,L){if(L==null){var D=this.descendant(p.default,T),M=a(D,2),x=M[0],B=M[1];x.insertAt(B,O)}}},{key:"length",value:function(){var T=this.domNode.textContent.length;return this.domNode.textContent.endsWith(`
`)?T:T+1}},{key:"newlineIndex",value:function(T){var O=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(O)return this.domNode.textContent.slice(0,T).lastIndexOf(`
`);var L=this.domNode.textContent.slice(T).indexOf(`
`);return L>-1?T+L:-1}},{key:"optimize",value:function(T){this.domNode.textContent.endsWith(`
`)||this.appendChild(u.default.create("text",`
`)),d(N.prototype.__proto__||Object.getPrototypeOf(N.prototype),"optimize",this).call(this,T);var O=this.next;O!=null&&O.prev===this&&O.statics.blotName===this.statics.blotName&&this.statics.formats(this.domNode)===O.statics.formats(O.domNode)&&(O.optimize(T),O.moveChildren(this),O.remove())}},{key:"replace",value:function(T){d(N.prototype.__proto__||Object.getPrototypeOf(N.prototype),"replace",this).call(this,T),[].slice.call(this.domNode.querySelectorAll("*")).forEach(function(O){var L=u.default.find(O);L==null?O.parentNode.removeChild(O):L instanceof u.default.Embed?L.remove():L.unwrap()})}}],[{key:"create",value:function(T){var O=d(N.__proto__||Object.getPrototypeOf(N),"create",this).call(this,T);return O.setAttribute("spellcheck",!1),O}},{key:"formats",value:function(){return!0}}]),N}(l.default);S.blotName="code-block",S.tagName="PRE",S.TAB="  ",r.Code=A,r.default=S},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0});var a=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(F){return typeof F}:function(F){return F&&typeof Symbol=="function"&&F.constructor===Symbol&&F!==Symbol.prototype?"symbol":typeof F},s=function(){function F(k,R){var C=[],q=!0,G=!1,U=void 0;try{for(var P=k[Symbol.iterator](),j;!(q=(j=P.next()).done)&&(C.push(j.value),!(R&&C.length===R));q=!0);}catch(H){G=!0,U=H}finally{try{!q&&P.return&&P.return()}finally{if(G)throw U}}return C}return function(k,R){if(Array.isArray(k))return k;if(Symbol.iterator in Object(k))return F(k,R);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),d=function(){function F(k,R){for(var C=0;C<R.length;C++){var q=R[C];q.enumerable=q.enumerable||!1,q.configurable=!0,"value"in q&&(q.writable=!0),Object.defineProperty(k,q.key,q)}}return function(k,R,C){return R&&F(k.prototype,R),C&&F(k,C),k}}(),c=i(2),y=O(c),f=i(20),u=O(f),o=i(0),l=O(o),h=i(13),v=O(h),E=i(24),p=O(E),m=i(4),_=O(m),g=i(16),b=O(g),A=i(21),S=O(A),I=i(11),N=O(I),w=i(3),T=O(w);function O(F){return F&&F.__esModule?F:{default:F}}function L(F,k,R){return k in F?Object.defineProperty(F,k,{value:R,enumerable:!0,configurable:!0,writable:!0}):F[k]=R,F}function D(F,k){if(!(F instanceof k))throw new TypeError("Cannot call a class as a function")}var M=/^[ -~]*$/,x=function(){function F(k){D(this,F),this.scroll=k,this.delta=this.getDelta()}return d(F,[{key:"applyDelta",value:function(R){var C=this,q=!1;this.scroll.update();var G=this.scroll.length();return this.scroll.batchStart(),R=V(R),R.reduce(function(U,P){var j=P.retain||P.delete||P.insert.length||1,H=P.attributes||{};if(P.insert!=null){if(typeof P.insert=="string"){var z=P.insert;z.endsWith(`
`)&&q&&(q=!1,z=z.slice(0,-1)),U>=G&&!z.endsWith(`
`)&&(q=!0),C.scroll.insertAt(U,z);var Y=C.scroll.line(U),Z=s(Y,2),ee=Z[0],ne=Z[1],ae=(0,T.default)({},(0,m.bubbleFormats)(ee));if(ee instanceof _.default){var se=ee.descendant(l.default.Leaf,ne),Oe=s(se,1),ge=Oe[0];ae=(0,T.default)(ae,(0,m.bubbleFormats)(ge))}H=u.default.attributes.diff(ae,H)||{}}else if(a(P.insert)==="object"){var W=Object.keys(P.insert)[0];if(W==null)return U;C.scroll.insertAt(U,W,P.insert[W])}G+=j}return Object.keys(H).forEach(function($){C.scroll.formatAt(U,j,$,H[$])}),U+j},0),R.reduce(function(U,P){return typeof P.delete=="number"?(C.scroll.deleteAt(U,P.delete),U):U+(P.retain||P.insert.length||1)},0),this.scroll.batchEnd(),this.update(R)}},{key:"deleteText",value:function(R,C){return this.scroll.deleteAt(R,C),this.update(new y.default().retain(R).delete(C))}},{key:"formatLine",value:function(R,C){var q=this,G=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return this.scroll.update(),Object.keys(G).forEach(function(U){if(!(q.scroll.whitelist!=null&&!q.scroll.whitelist[U])){var P=q.scroll.lines(R,Math.max(C,1)),j=C;P.forEach(function(H){var z=H.length();if(!(H instanceof v.default))H.format(U,G[U]);else{var Y=R-H.offset(q.scroll),Z=H.newlineIndex(Y+j)-Y+1;H.formatAt(Y,Z,U,G[U])}j-=z})}}),this.scroll.optimize(),this.update(new y.default().retain(R).retain(C,(0,S.default)(G)))}},{key:"formatText",value:function(R,C){var q=this,G=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return Object.keys(G).forEach(function(U){q.scroll.formatAt(R,C,U,G[U])}),this.update(new y.default().retain(R).retain(C,(0,S.default)(G)))}},{key:"getContents",value:function(R,C){return this.delta.slice(R,R+C)}},{key:"getDelta",value:function(){return this.scroll.lines().reduce(function(R,C){return R.concat(C.delta())},new y.default)}},{key:"getFormat",value:function(R){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,q=[],G=[];C===0?this.scroll.path(R).forEach(function(P){var j=s(P,1),H=j[0];H instanceof _.default?q.push(H):H instanceof l.default.Leaf&&G.push(H)}):(q=this.scroll.lines(R,C),G=this.scroll.descendants(l.default.Leaf,R,C));var U=[q,G].map(function(P){if(P.length===0)return{};for(var j=(0,m.bubbleFormats)(P.shift());Object.keys(j).length>0;){var H=P.shift();if(H==null)return j;j=B((0,m.bubbleFormats)(H),j)}return j});return T.default.apply(T.default,U)}},{key:"getText",value:function(R,C){return this.getContents(R,C).filter(function(q){return typeof q.insert=="string"}).map(function(q){return q.insert}).join("")}},{key:"insertEmbed",value:function(R,C,q){return this.scroll.insertAt(R,C,q),this.update(new y.default().retain(R).insert(L({},C,q)))}},{key:"insertText",value:function(R,C){var q=this,G=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return C=C.replace(/\r\n/g,`
`).replace(/\r/g,`
`),this.scroll.insertAt(R,C),Object.keys(G).forEach(function(U){q.scroll.formatAt(R,C.length,U,G[U])}),this.update(new y.default().retain(R).insert(C,(0,S.default)(G)))}},{key:"isBlank",value:function(){if(this.scroll.children.length==0)return!0;if(this.scroll.children.length>1)return!1;var R=this.scroll.children.head;return R.statics.blotName!==_.default.blotName||R.children.length>1?!1:R.children.head instanceof b.default}},{key:"removeFormat",value:function(R,C){var q=this.getText(R,C),G=this.scroll.line(R+C),U=s(G,2),P=U[0],j=U[1],H=0,z=new y.default;P!=null&&(P instanceof v.default?H=P.newlineIndex(j)-j+1:H=P.length()-j,z=P.delta().slice(j,j+H-1).insert(`
`));var Y=this.getContents(R,C+H),Z=Y.diff(new y.default().insert(q).concat(z)),ee=new y.default().retain(R).concat(Z);return this.applyDelta(ee)}},{key:"update",value:function(R){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],q=arguments.length>2&&arguments[2]!==void 0?arguments[2]:void 0,G=this.delta;if(C.length===1&&C[0].type==="characterData"&&C[0].target.data.match(M)&&l.default.find(C[0].target)){var U=l.default.find(C[0].target),P=(0,m.bubbleFormats)(U),j=U.offset(this.scroll),H=C[0].oldValue.replace(p.default.CONTENTS,""),z=new y.default().insert(H),Y=new y.default().insert(U.value()),Z=new y.default().retain(j).concat(z.diff(Y,q));R=Z.reduce(function(ee,ne){return ne.insert?ee.insert(ne.insert,P):ee.push(ne)},new y.default),this.delta=G.compose(R)}else this.delta=this.getDelta(),(!R||!(0,N.default)(G.compose(R),this.delta))&&(R=G.diff(this.delta,q));return R}}]),F}();function B(F,k){return Object.keys(k).reduce(function(R,C){return F[C]==null||(k[C]===F[C]?R[C]=k[C]:Array.isArray(k[C])?k[C].indexOf(F[C])<0&&(R[C]=k[C].concat([F[C]])):R[C]=[k[C],F[C]]),R},{})}function V(F){return F.reduce(function(k,R){if(R.insert===1){var C=(0,S.default)(R.attributes);return delete C.image,k.insert({image:R.attributes.image},C)}if(R.attributes!=null&&(R.attributes.list===!0||R.attributes.bullet===!0)&&(R=(0,S.default)(R),R.attributes.list?R.attributes.list="ordered":(R.attributes.list="bullet",delete R.attributes.bullet)),typeof R.insert=="string"){var q=R.insert.replace(/\r\n/g,`
`).replace(/\r/g,`
`);return k.insert(q,R.attributes)}return k.push(R)},new y.default)}r.default=x},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0}),r.default=r.Range=void 0;var a=function(){function I(N,w){var T=[],O=!0,L=!1,D=void 0;try{for(var M=N[Symbol.iterator](),x;!(O=(x=M.next()).done)&&(T.push(x.value),!(w&&T.length===w));O=!0);}catch(B){L=!0,D=B}finally{try{!O&&M.return&&M.return()}finally{if(L)throw D}}return T}return function(N,w){if(Array.isArray(N))return N;if(Symbol.iterator in Object(N))return I(N,w);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),s=function(){function I(N,w){for(var T=0;T<w.length;T++){var O=w[T];O.enumerable=O.enumerable||!1,O.configurable=!0,"value"in O&&(O.writable=!0),Object.defineProperty(N,O.key,O)}}return function(N,w,T){return w&&I(N.prototype,w),T&&I(N,T),N}}(),d=i(0),c=p(d),y=i(21),f=p(y),u=i(11),o=p(u),l=i(8),h=p(l),v=i(10),E=p(v);function p(I){return I&&I.__esModule?I:{default:I}}function m(I){if(Array.isArray(I)){for(var N=0,w=Array(I.length);N<I.length;N++)w[N]=I[N];return w}else return Array.from(I)}function _(I,N){if(!(I instanceof N))throw new TypeError("Cannot call a class as a function")}var g=(0,E.default)("quill:selection"),b=function I(N){var w=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;_(this,I),this.index=N,this.length=w},A=function(){function I(N,w){var T=this;_(this,I),this.emitter=w,this.scroll=N,this.composing=!1,this.mouseDown=!1,this.root=this.scroll.domNode,this.cursor=c.default.create("cursor",this),this.lastRange=this.savedRange=new b(0,0),this.handleComposition(),this.handleDragging(),this.emitter.listenDOM("selectionchange",document,function(){T.mouseDown||setTimeout(T.update.bind(T,h.default.sources.USER),1)}),this.emitter.on(h.default.events.EDITOR_CHANGE,function(O,L){O===h.default.events.TEXT_CHANGE&&L.length()>0&&T.update(h.default.sources.SILENT)}),this.emitter.on(h.default.events.SCROLL_BEFORE_UPDATE,function(){if(T.hasFocus()){var O=T.getNativeRange();O!=null&&O.start.node!==T.cursor.textNode&&T.emitter.once(h.default.events.SCROLL_UPDATE,function(){try{T.setNativeRange(O.start.node,O.start.offset,O.end.node,O.end.offset)}catch{}})}}),this.emitter.on(h.default.events.SCROLL_OPTIMIZE,function(O,L){if(L.range){var D=L.range,M=D.startNode,x=D.startOffset,B=D.endNode,V=D.endOffset;T.setNativeRange(M,x,B,V)}}),this.update(h.default.sources.SILENT)}return s(I,[{key:"handleComposition",value:function(){var w=this;this.root.addEventListener("compositionstart",function(){w.composing=!0}),this.root.addEventListener("compositionend",function(){if(w.composing=!1,w.cursor.parent){var T=w.cursor.restore();if(!T)return;setTimeout(function(){w.setNativeRange(T.startNode,T.startOffset,T.endNode,T.endOffset)},1)}})}},{key:"handleDragging",value:function(){var w=this;this.emitter.listenDOM("mousedown",document.body,function(){w.mouseDown=!0}),this.emitter.listenDOM("mouseup",document.body,function(){w.mouseDown=!1,w.update(h.default.sources.USER)})}},{key:"focus",value:function(){this.hasFocus()||(this.root.focus(),this.setRange(this.savedRange))}},{key:"format",value:function(w,T){if(!(this.scroll.whitelist!=null&&!this.scroll.whitelist[w])){this.scroll.update();var O=this.getNativeRange();if(!(O==null||!O.native.collapsed||c.default.query(w,c.default.Scope.BLOCK))){if(O.start.node!==this.cursor.textNode){var L=c.default.find(O.start.node,!1);if(L==null)return;if(L instanceof c.default.Leaf){var D=L.split(O.start.offset);L.parent.insertBefore(this.cursor,D)}else L.insertBefore(this.cursor,O.start.node);this.cursor.attach()}this.cursor.format(w,T),this.scroll.optimize(),this.setNativeRange(this.cursor.textNode,this.cursor.textNode.data.length),this.update()}}}},{key:"getBounds",value:function(w){var T=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,O=this.scroll.length();w=Math.min(w,O-1),T=Math.min(w+T,O-1)-w;var L=void 0,D=this.scroll.leaf(w),M=a(D,2),x=M[0],B=M[1];if(x==null)return null;var V=x.position(B,!0),F=a(V,2);L=F[0],B=F[1];var k=document.createRange();if(T>0){k.setStart(L,B);var R=this.scroll.leaf(w+T),C=a(R,2);if(x=C[0],B=C[1],x==null)return null;var q=x.position(B,!0),G=a(q,2);return L=G[0],B=G[1],k.setEnd(L,B),k.getBoundingClientRect()}else{var U="left",P=void 0;return L instanceof Text?(B<L.data.length?(k.setStart(L,B),k.setEnd(L,B+1)):(k.setStart(L,B-1),k.setEnd(L,B),U="right"),P=k.getBoundingClientRect()):(P=x.domNode.getBoundingClientRect(),B>0&&(U="right")),{bottom:P.top+P.height,height:P.height,left:P[U],right:P[U],top:P.top,width:0}}}},{key:"getNativeRange",value:function(){var w=document.getSelection();if(w==null||w.rangeCount<=0)return null;var T=w.getRangeAt(0);if(T==null)return null;var O=this.normalizeNative(T);return g.info("getNativeRange",O),O}},{key:"getRange",value:function(){var w=this.getNativeRange();if(w==null)return[null,null];var T=this.normalizedToRange(w);return[T,w]}},{key:"hasFocus",value:function(){return document.activeElement===this.root}},{key:"normalizedToRange",value:function(w){var T=this,O=[[w.start.node,w.start.offset]];w.native.collapsed||O.push([w.end.node,w.end.offset]);var L=O.map(function(x){var B=a(x,2),V=B[0],F=B[1],k=c.default.find(V,!0),R=k.offset(T.scroll);return F===0?R:k instanceof c.default.Container?R+k.length():R+k.index(V,F)}),D=Math.min(Math.max.apply(Math,m(L)),this.scroll.length()-1),M=Math.min.apply(Math,[D].concat(m(L)));return new b(M,D-M)}},{key:"normalizeNative",value:function(w){if(!S(this.root,w.startContainer)||!w.collapsed&&!S(this.root,w.endContainer))return null;var T={start:{node:w.startContainer,offset:w.startOffset},end:{node:w.endContainer,offset:w.endOffset},native:w};return[T.start,T.end].forEach(function(O){for(var L=O.node,D=O.offset;!(L instanceof Text)&&L.childNodes.length>0;)if(L.childNodes.length>D)L=L.childNodes[D],D=0;else if(L.childNodes.length===D)L=L.lastChild,D=L instanceof Text?L.data.length:L.childNodes.length+1;else break;O.node=L,O.offset=D}),T}},{key:"rangeToNative",value:function(w){var T=this,O=w.collapsed?[w.index]:[w.index,w.index+w.length],L=[],D=this.scroll.length();return O.forEach(function(M,x){M=Math.min(D-1,M);var B=void 0,V=T.scroll.leaf(M),F=a(V,2),k=F[0],R=F[1],C=k.position(R,x!==0),q=a(C,2);B=q[0],R=q[1],L.push(B,R)}),L.length<2&&(L=L.concat(L)),L}},{key:"scrollIntoView",value:function(w){var T=this.lastRange;if(T!=null){var O=this.getBounds(T.index,T.length);if(O!=null){var L=this.scroll.length()-1,D=this.scroll.line(Math.min(T.index,L)),M=a(D,1),x=M[0],B=x;if(T.length>0){var V=this.scroll.line(Math.min(T.index+T.length,L)),F=a(V,1);B=F[0]}if(!(x==null||B==null)){var k=w.getBoundingClientRect();O.top<k.top?w.scrollTop-=k.top-O.top:O.bottom>k.bottom&&(w.scrollTop+=O.bottom-k.bottom)}}}}},{key:"setNativeRange",value:function(w,T){var O=arguments.length>2&&arguments[2]!==void 0?arguments[2]:w,L=arguments.length>3&&arguments[3]!==void 0?arguments[3]:T,D=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1;if(g.info("setNativeRange",w,T,O,L),!(w!=null&&(this.root.parentNode==null||w.parentNode==null||O.parentNode==null))){var M=document.getSelection();if(M!=null)if(w!=null){this.hasFocus()||this.root.focus();var x=(this.getNativeRange()||{}).native;if(x==null||D||w!==x.startContainer||T!==x.startOffset||O!==x.endContainer||L!==x.endOffset){w.tagName=="BR"&&(T=[].indexOf.call(w.parentNode.childNodes,w),w=w.parentNode),O.tagName=="BR"&&(L=[].indexOf.call(O.parentNode.childNodes,O),O=O.parentNode);var B=document.createRange();B.setStart(w,T),B.setEnd(O,L),M.removeAllRanges(),M.addRange(B)}}else M.removeAllRanges(),this.root.blur(),document.body.focus()}}},{key:"setRange",value:function(w){var T=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,O=arguments.length>2&&arguments[2]!==void 0?arguments[2]:h.default.sources.API;if(typeof T=="string"&&(O=T,T=!1),g.info("setRange",w),w!=null){var L=this.rangeToNative(w);this.setNativeRange.apply(this,m(L).concat([T]))}else this.setNativeRange(null);this.update(O)}},{key:"update",value:function(){var w=arguments.length>0&&arguments[0]!==void 0?arguments[0]:h.default.sources.USER,T=this.lastRange,O=this.getRange(),L=a(O,2),D=L[0],M=L[1];if(this.lastRange=D,this.lastRange!=null&&(this.savedRange=this.lastRange),!(0,o.default)(T,this.lastRange)){var x;!this.composing&&M!=null&&M.native.collapsed&&M.start.node!==this.cursor.textNode&&this.cursor.restore();var B=[h.default.events.SELECTION_CHANGE,(0,f.default)(this.lastRange),(0,f.default)(T),w];if((x=this.emitter).emit.apply(x,[h.default.events.EDITOR_CHANGE].concat(B)),w!==h.default.sources.SILENT){var V;(V=this.emitter).emit.apply(V,B)}}}}]),I}();function S(I,N){try{N.parentNode}catch{return!1}return N instanceof Text&&(N=N.parentNode),I.contains(N)}r.Range=b,r.default=A},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0});var a=function(){function h(v,E){for(var p=0;p<E.length;p++){var m=E[p];m.enumerable=m.enumerable||!1,m.configurable=!0,"value"in m&&(m.writable=!0),Object.defineProperty(v,m.key,m)}}return function(v,E,p){return E&&h(v.prototype,E),p&&h(v,p),v}}(),s=function h(v,E,p){v===null&&(v=Function.prototype);var m=Object.getOwnPropertyDescriptor(v,E);if(m===void 0){var _=Object.getPrototypeOf(v);return _===null?void 0:h(_,E,p)}else{if("value"in m)return m.value;var g=m.get;return g===void 0?void 0:g.call(p)}},d=i(0),c=y(d);function y(h){return h&&h.__esModule?h:{default:h}}function f(h,v){if(!(h instanceof v))throw new TypeError("Cannot call a class as a function")}function u(h,v){if(!h)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return v&&(typeof v=="object"||typeof v=="function")?v:h}function o(h,v){if(typeof v!="function"&&v!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof v);h.prototype=Object.create(v&&v.prototype,{constructor:{value:h,enumerable:!1,writable:!0,configurable:!0}}),v&&(Object.setPrototypeOf?Object.setPrototypeOf(h,v):h.__proto__=v)}var l=function(h){o(v,h);function v(){return f(this,v),u(this,(v.__proto__||Object.getPrototypeOf(v)).apply(this,arguments))}return a(v,[{key:"insertInto",value:function(p,m){p.children.length===0?s(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"insertInto",this).call(this,p,m):this.remove()}},{key:"length",value:function(){return 0}},{key:"value",value:function(){return""}}],[{key:"value",value:function(){}}]),v}(c.default.Embed);l.blotName="break",l.tagName="BR",r.default=l},function(n,r,i){var a=this&&this.__extends||function(){var u=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(o,l){o.__proto__=l}||function(o,l){for(var h in l)l.hasOwnProperty(h)&&(o[h]=l[h])};return function(o,l){u(o,l);function h(){this.constructor=o}o.prototype=l===null?Object.create(l):(h.prototype=l.prototype,new h)}}();Object.defineProperty(r,"__esModule",{value:!0});var s=i(44),d=i(30),c=i(1),y=function(u){a(o,u);function o(l){var h=u.call(this,l)||this;return h.build(),h}return o.prototype.appendChild=function(l){this.insertBefore(l)},o.prototype.attach=function(){u.prototype.attach.call(this),this.children.forEach(function(l){l.attach()})},o.prototype.build=function(){var l=this;this.children=new s.default,[].slice.call(this.domNode.childNodes).reverse().forEach(function(h){try{var v=f(h);l.insertBefore(v,l.children.head||void 0)}catch(E){if(E instanceof c.ParchmentError)return;throw E}})},o.prototype.deleteAt=function(l,h){if(l===0&&h===this.length())return this.remove();this.children.forEachAt(l,h,function(v,E,p){v.deleteAt(E,p)})},o.prototype.descendant=function(l,h){var v=this.children.find(h),E=v[0],p=v[1];return l.blotName==null&&l(E)||l.blotName!=null&&E instanceof l?[E,p]:E instanceof o?E.descendant(l,p):[null,-1]},o.prototype.descendants=function(l,h,v){h===void 0&&(h=0),v===void 0&&(v=Number.MAX_VALUE);var E=[],p=v;return this.children.forEachAt(h,v,function(m,_,g){(l.blotName==null&&l(m)||l.blotName!=null&&m instanceof l)&&E.push(m),m instanceof o&&(E=E.concat(m.descendants(l,_,p))),p-=g}),E},o.prototype.detach=function(){this.children.forEach(function(l){l.detach()}),u.prototype.detach.call(this)},o.prototype.formatAt=function(l,h,v,E){this.children.forEachAt(l,h,function(p,m,_){p.formatAt(m,_,v,E)})},o.prototype.insertAt=function(l,h,v){var E=this.children.find(l),p=E[0],m=E[1];if(p)p.insertAt(m,h,v);else{var _=v==null?c.create("text",h):c.create(h,v);this.appendChild(_)}},o.prototype.insertBefore=function(l,h){if(this.statics.allowedChildren!=null&&!this.statics.allowedChildren.some(function(v){return l instanceof v}))throw new c.ParchmentError("Cannot insert "+l.statics.blotName+" into "+this.statics.blotName);l.insertInto(this,h)},o.prototype.length=function(){return this.children.reduce(function(l,h){return l+h.length()},0)},o.prototype.moveChildren=function(l,h){this.children.forEach(function(v){l.insertBefore(v,h)})},o.prototype.optimize=function(l){if(u.prototype.optimize.call(this,l),this.children.length===0)if(this.statics.defaultChild!=null){var h=c.create(this.statics.defaultChild);this.appendChild(h),h.optimize(l)}else this.remove()},o.prototype.path=function(l,h){h===void 0&&(h=!1);var v=this.children.find(l,h),E=v[0],p=v[1],m=[[this,l]];return E instanceof o?m.concat(E.path(p,h)):(E!=null&&m.push([E,p]),m)},o.prototype.removeChild=function(l){this.children.remove(l)},o.prototype.replace=function(l){l instanceof o&&l.moveChildren(this),u.prototype.replace.call(this,l)},o.prototype.split=function(l,h){if(h===void 0&&(h=!1),!h){if(l===0)return this;if(l===this.length())return this.next}var v=this.clone();return this.parent.insertBefore(v,this.next),this.children.forEachAt(l,this.length(),function(E,p,m){E=E.split(p,h),v.appendChild(E)}),v},o.prototype.unwrap=function(){this.moveChildren(this.parent,this.next),this.remove()},o.prototype.update=function(l,h){var v=this,E=[],p=[];l.forEach(function(m){m.target===v.domNode&&m.type==="childList"&&(E.push.apply(E,m.addedNodes),p.push.apply(p,m.removedNodes))}),p.forEach(function(m){if(!(m.parentNode!=null&&m.tagName!=="IFRAME"&&document.body.compareDocumentPosition(m)&Node.DOCUMENT_POSITION_CONTAINED_BY)){var _=c.find(m);_!=null&&(_.domNode.parentNode==null||_.domNode.parentNode===v.domNode)&&_.detach()}}),E.filter(function(m){return m.parentNode==v.domNode}).sort(function(m,_){return m===_?0:m.compareDocumentPosition(_)&Node.DOCUMENT_POSITION_FOLLOWING?1:-1}).forEach(function(m){var _=null;m.nextSibling!=null&&(_=c.find(m.nextSibling));var g=f(m);(g.next!=_||g.next==null)&&(g.parent!=null&&g.parent.removeChild(v),v.insertBefore(g,_||void 0))})},o}(d.default);function f(u){var o=c.find(u);if(o==null)try{o=c.create(u)}catch{o=c.create(c.Scope.INLINE),[].slice.call(u.childNodes).forEach(function(h){o.domNode.appendChild(h)}),u.parentNode&&u.parentNode.replaceChild(o.domNode,u),o.attach()}return o}r.default=y},function(n,r,i){var a=this&&this.__extends||function(){var u=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(o,l){o.__proto__=l}||function(o,l){for(var h in l)l.hasOwnProperty(h)&&(o[h]=l[h])};return function(o,l){u(o,l);function h(){this.constructor=o}o.prototype=l===null?Object.create(l):(h.prototype=l.prototype,new h)}}();Object.defineProperty(r,"__esModule",{value:!0});var s=i(12),d=i(31),c=i(17),y=i(1),f=function(u){a(o,u);function o(l){var h=u.call(this,l)||this;return h.attributes=new d.default(h.domNode),h}return o.formats=function(l){if(typeof this.tagName=="string")return!0;if(Array.isArray(this.tagName))return l.tagName.toLowerCase()},o.prototype.format=function(l,h){var v=y.query(l);v instanceof s.default?this.attributes.attribute(v,h):h&&v!=null&&(l!==this.statics.blotName||this.formats()[l]!==h)&&this.replaceWith(l,h)},o.prototype.formats=function(){var l=this.attributes.values(),h=this.statics.formats(this.domNode);return h!=null&&(l[this.statics.blotName]=h),l},o.prototype.replaceWith=function(l,h){var v=u.prototype.replaceWith.call(this,l,h);return this.attributes.copy(v),v},o.prototype.update=function(l,h){var v=this;u.prototype.update.call(this,l,h),l.some(function(E){return E.target===v.domNode&&E.type==="attributes"})&&this.attributes.build()},o.prototype.wrap=function(l,h){var v=u.prototype.wrap.call(this,l,h);return v instanceof o&&v.statics.scope===this.statics.scope&&this.attributes.move(v),v},o}(c.default);r.default=f},function(n,r,i){var a=this&&this.__extends||function(){var y=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(f,u){f.__proto__=u}||function(f,u){for(var o in u)u.hasOwnProperty(o)&&(f[o]=u[o])};return function(f,u){y(f,u);function o(){this.constructor=f}f.prototype=u===null?Object.create(u):(o.prototype=u.prototype,new o)}}();Object.defineProperty(r,"__esModule",{value:!0});var s=i(30),d=i(1),c=function(y){a(f,y);function f(){return y!==null&&y.apply(this,arguments)||this}return f.value=function(u){return!0},f.prototype.index=function(u,o){return this.domNode===u||this.domNode.compareDocumentPosition(u)&Node.DOCUMENT_POSITION_CONTAINED_BY?Math.min(o,1):-1},f.prototype.position=function(u,o){var l=[].indexOf.call(this.parent.domNode.childNodes,this.domNode);return u>0&&(l+=1),[this.parent.domNode,l]},f.prototype.value=function(){var u;return u={},u[this.statics.blotName]=this.statics.value(this.domNode)||!0,u},f.scope=d.Scope.INLINE_BLOT,f}(s.default);r.default=c},function(n,r,i){var a=i(11),s=i(3),d={attributes:{compose:function(y,f,u){typeof y!="object"&&(y={}),typeof f!="object"&&(f={});var o=s(!0,{},f);u||(o=Object.keys(o).reduce(function(h,v){return o[v]!=null&&(h[v]=o[v]),h},{}));for(var l in y)y[l]!==void 0&&f[l]===void 0&&(o[l]=y[l]);return Object.keys(o).length>0?o:void 0},diff:function(y,f){typeof y!="object"&&(y={}),typeof f!="object"&&(f={});var u=Object.keys(y).concat(Object.keys(f)).reduce(function(o,l){return a(y[l],f[l])||(o[l]=f[l]===void 0?null:f[l]),o},{});return Object.keys(u).length>0?u:void 0},transform:function(y,f,u){if(typeof y!="object")return f;if(typeof f=="object"){if(!u)return f;var o=Object.keys(f).reduce(function(l,h){return y[h]===void 0&&(l[h]=f[h]),l},{});return Object.keys(o).length>0?o:void 0}}},iterator:function(y){return new c(y)},length:function(y){return typeof y.delete=="number"?y.delete:typeof y.retain=="number"?y.retain:typeof y.insert=="string"?y.insert.length:1}};function c(y){this.ops=y,this.index=0,this.offset=0}c.prototype.hasNext=function(){return this.peekLength()<1/0},c.prototype.next=function(y){y||(y=1/0);var f=this.ops[this.index];if(f){var u=this.offset,o=d.length(f);if(y>=o-u?(y=o-u,this.index+=1,this.offset=0):this.offset+=y,typeof f.delete=="number")return{delete:y};var l={};return f.attributes&&(l.attributes=f.attributes),typeof f.retain=="number"?l.retain=y:typeof f.insert=="string"?l.insert=f.insert.substr(u,y):l.insert=f.insert,l}else return{retain:1/0}},c.prototype.peek=function(){return this.ops[this.index]},c.prototype.peekLength=function(){return this.ops[this.index]?d.length(this.ops[this.index])-this.offset:1/0},c.prototype.peekType=function(){return this.ops[this.index]?typeof this.ops[this.index].delete=="number"?"delete":typeof this.ops[this.index].retain=="number"?"retain":"insert":"retain"},c.prototype.rest=function(){if(this.hasNext()){if(this.offset===0)return this.ops.slice(this.index);var y=this.offset,f=this.index,u=this.next(),o=this.ops.slice(this.index);return this.offset=y,this.index=f,[u].concat(o)}else return[]},n.exports=d},function(n,r){var i=function(){function a(v,E){return E!=null&&v instanceof E}var s;try{s=Map}catch{s=function(){}}var d;try{d=Set}catch{d=function(){}}var c;try{c=Promise}catch{c=function(){}}function y(v,E,p,m,_){typeof E=="object"&&(p=E.depth,m=E.prototype,_=E.includeNonEnumerable,E=E.circular);var g=[],b=[],A=typeof Buffer<"u";typeof E>"u"&&(E=!0),typeof p>"u"&&(p=1/0);function S(I,N){if(I===null)return null;if(N===0)return I;var w,T;if(typeof I!="object")return I;if(a(I,s))w=new s;else if(a(I,d))w=new d;else if(a(I,c))w=new c(function(k,R){I.then(function(C){k(S(C,N-1))},function(C){R(S(C,N-1))})});else if(y.__isArray(I))w=[];else if(y.__isRegExp(I))w=new RegExp(I.source,h(I)),I.lastIndex&&(w.lastIndex=I.lastIndex);else if(y.__isDate(I))w=new Date(I.getTime());else{if(A&&Buffer.isBuffer(I))return Buffer.allocUnsafe?w=Buffer.allocUnsafe(I.length):w=new Buffer(I.length),I.copy(w),w;a(I,Error)?w=Object.create(I):typeof m>"u"?(T=Object.getPrototypeOf(I),w=Object.create(T)):(w=Object.create(m),T=m)}if(E){var O=g.indexOf(I);if(O!=-1)return b[O];g.push(I),b.push(w)}a(I,s)&&I.forEach(function(k,R){var C=S(R,N-1),q=S(k,N-1);w.set(C,q)}),a(I,d)&&I.forEach(function(k){var R=S(k,N-1);w.add(R)});for(var L in I){var D;T&&(D=Object.getOwnPropertyDescriptor(T,L)),!(D&&D.set==null)&&(w[L]=S(I[L],N-1))}if(Object.getOwnPropertySymbols)for(var M=Object.getOwnPropertySymbols(I),L=0;L<M.length;L++){var x=M[L],B=Object.getOwnPropertyDescriptor(I,x);B&&!B.enumerable&&!_||(w[x]=S(I[x],N-1),B.enumerable||Object.defineProperty(w,x,{enumerable:!1}))}if(_)for(var V=Object.getOwnPropertyNames(I),L=0;L<V.length;L++){var F=V[L],B=Object.getOwnPropertyDescriptor(I,F);B&&B.enumerable||(w[F]=S(I[F],N-1),Object.defineProperty(w,F,{enumerable:!1}))}return w}return S(v,p)}y.clonePrototype=function(E){if(E===null)return null;var p=function(){};return p.prototype=E,new p};function f(v){return Object.prototype.toString.call(v)}y.__objToStr=f;function u(v){return typeof v=="object"&&f(v)==="[object Date]"}y.__isDate=u;function o(v){return typeof v=="object"&&f(v)==="[object Array]"}y.__isArray=o;function l(v){return typeof v=="object"&&f(v)==="[object RegExp]"}y.__isRegExp=l;function h(v){var E="";return v.global&&(E+="g"),v.ignoreCase&&(E+="i"),v.multiline&&(E+="m"),E}return y.__getRegExpFlags=h,y}();typeof n=="object"&&n.exports&&(n.exports=i)},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0});var a=function(){function w(T,O){var L=[],D=!0,M=!1,x=void 0;try{for(var B=T[Symbol.iterator](),V;!(D=(V=B.next()).done)&&(L.push(V.value),!(O&&L.length===O));D=!0);}catch(F){M=!0,x=F}finally{try{!D&&B.return&&B.return()}finally{if(M)throw x}}return L}return function(T,O){if(Array.isArray(T))return T;if(Symbol.iterator in Object(T))return w(T,O);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),s=function(){function w(T,O){for(var L=0;L<O.length;L++){var D=O[L];D.enumerable=D.enumerable||!1,D.configurable=!0,"value"in D&&(D.writable=!0),Object.defineProperty(T,D.key,D)}}return function(T,O,L){return O&&w(T.prototype,O),L&&w(T,L),T}}(),d=function w(T,O,L){T===null&&(T=Function.prototype);var D=Object.getOwnPropertyDescriptor(T,O);if(D===void 0){var M=Object.getPrototypeOf(T);return M===null?void 0:w(M,O,L)}else{if("value"in D)return D.value;var x=D.get;return x===void 0?void 0:x.call(L)}},c=i(0),y=g(c),f=i(8),u=g(f),o=i(4),l=g(o),h=i(16),v=g(h),E=i(13),p=g(E),m=i(25),_=g(m);function g(w){return w&&w.__esModule?w:{default:w}}function b(w,T){if(!(w instanceof T))throw new TypeError("Cannot call a class as a function")}function A(w,T){if(!w)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return T&&(typeof T=="object"||typeof T=="function")?T:w}function S(w,T){if(typeof T!="function"&&T!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof T);w.prototype=Object.create(T&&T.prototype,{constructor:{value:w,enumerable:!1,writable:!0,configurable:!0}}),T&&(Object.setPrototypeOf?Object.setPrototypeOf(w,T):w.__proto__=T)}function I(w){return w instanceof l.default||w instanceof o.BlockEmbed}var N=function(w){S(T,w);function T(O,L){b(this,T);var D=A(this,(T.__proto__||Object.getPrototypeOf(T)).call(this,O));return D.emitter=L.emitter,Array.isArray(L.whitelist)&&(D.whitelist=L.whitelist.reduce(function(M,x){return M[x]=!0,M},{})),D.domNode.addEventListener("DOMNodeInserted",function(){}),D.optimize(),D.enable(),D}return s(T,[{key:"batchStart",value:function(){this.batch=!0}},{key:"batchEnd",value:function(){this.batch=!1,this.optimize()}},{key:"deleteAt",value:function(L,D){var M=this.line(L),x=a(M,2),B=x[0],V=x[1],F=this.line(L+D),k=a(F,1),R=k[0];if(d(T.prototype.__proto__||Object.getPrototypeOf(T.prototype),"deleteAt",this).call(this,L,D),R!=null&&B!==R&&V>0){if(B instanceof o.BlockEmbed||R instanceof o.BlockEmbed){this.optimize();return}if(B instanceof p.default){var C=B.newlineIndex(B.length(),!0);if(C>-1&&(B=B.split(C+1),B===R)){this.optimize();return}}else if(R instanceof p.default){var q=R.newlineIndex(0);q>-1&&R.split(q+1)}var G=R.children.head instanceof v.default?null:R.children.head;B.moveChildren(R,G),B.remove()}this.optimize()}},{key:"enable",value:function(){var L=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this.domNode.setAttribute("contenteditable",L)}},{key:"formatAt",value:function(L,D,M,x){this.whitelist!=null&&!this.whitelist[M]||(d(T.prototype.__proto__||Object.getPrototypeOf(T.prototype),"formatAt",this).call(this,L,D,M,x),this.optimize())}},{key:"insertAt",value:function(L,D,M){if(!(M!=null&&this.whitelist!=null&&!this.whitelist[D])){if(L>=this.length())if(M==null||y.default.query(D,y.default.Scope.BLOCK)==null){var x=y.default.create(this.statics.defaultChild);this.appendChild(x),M==null&&D.endsWith(`
`)&&(D=D.slice(0,-1)),x.insertAt(0,D,M)}else{var B=y.default.create(D,M);this.appendChild(B)}else d(T.prototype.__proto__||Object.getPrototypeOf(T.prototype),"insertAt",this).call(this,L,D,M);this.optimize()}}},{key:"insertBefore",value:function(L,D){if(L.statics.scope===y.default.Scope.INLINE_BLOT){var M=y.default.create(this.statics.defaultChild);M.appendChild(L),L=M}d(T.prototype.__proto__||Object.getPrototypeOf(T.prototype),"insertBefore",this).call(this,L,D)}},{key:"leaf",value:function(L){return this.path(L).pop()||[null,-1]}},{key:"line",value:function(L){return L===this.length()?this.line(L-1):this.descendant(I,L)}},{key:"lines",value:function(){var L=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,D=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.MAX_VALUE,M=function x(B,V,F){var k=[],R=F;return B.children.forEachAt(V,F,function(C,q,G){I(C)?k.push(C):C instanceof y.default.Container&&(k=k.concat(x(C,q,R))),R-=G}),k};return M(this,L,D)}},{key:"optimize",value:function(){var L=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],D=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.batch!==!0&&(d(T.prototype.__proto__||Object.getPrototypeOf(T.prototype),"optimize",this).call(this,L,D),L.length>0&&this.emitter.emit(u.default.events.SCROLL_OPTIMIZE,L,D))}},{key:"path",value:function(L){return d(T.prototype.__proto__||Object.getPrototypeOf(T.prototype),"path",this).call(this,L).slice(1)}},{key:"update",value:function(L){if(this.batch!==!0){var D=u.default.sources.USER;typeof L=="string"&&(D=L),Array.isArray(L)||(L=this.observer.takeRecords()),L.length>0&&this.emitter.emit(u.default.events.SCROLL_BEFORE_UPDATE,D,L),d(T.prototype.__proto__||Object.getPrototypeOf(T.prototype),"update",this).call(this,L.concat([])),L.length>0&&this.emitter.emit(u.default.events.SCROLL_UPDATE,D,L)}}}]),T}(y.default.Scroll);N.blotName="scroll",N.className="ql-editor",N.tagName="DIV",N.defaultChild="block",N.allowedChildren=[l.default,o.BlockEmbed,_.default],r.default=N},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0}),r.SHORTKEY=r.default=void 0;var a=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(P){return typeof P}:function(P){return P&&typeof Symbol=="function"&&P.constructor===Symbol&&P!==Symbol.prototype?"symbol":typeof P},s=function(){function P(j,H){var z=[],Y=!0,Z=!1,ee=void 0;try{for(var ne=j[Symbol.iterator](),ae;!(Y=(ae=ne.next()).done)&&(z.push(ae.value),!(H&&z.length===H));Y=!0);}catch(se){Z=!0,ee=se}finally{try{!Y&&ne.return&&ne.return()}finally{if(Z)throw ee}}return z}return function(j,H){if(Array.isArray(j))return j;if(Symbol.iterator in Object(j))return P(j,H);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),d=function(){function P(j,H){for(var z=0;z<H.length;z++){var Y=H[z];Y.enumerable=Y.enumerable||!1,Y.configurable=!0,"value"in Y&&(Y.writable=!0),Object.defineProperty(j,Y.key,Y)}}return function(j,H,z){return H&&P(j.prototype,H),z&&P(j,z),j}}(),c=i(21),y=w(c),f=i(11),u=w(f),o=i(3),l=w(o),h=i(2),v=w(h),E=i(20),p=w(E),m=i(0),_=w(m),g=i(5),b=w(g),A=i(10),S=w(A),I=i(9),N=w(I);function w(P){return P&&P.__esModule?P:{default:P}}function T(P,j,H){return j in P?Object.defineProperty(P,j,{value:H,enumerable:!0,configurable:!0,writable:!0}):P[j]=H,P}function O(P,j){if(!(P instanceof j))throw new TypeError("Cannot call a class as a function")}function L(P,j){if(!P)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return j&&(typeof j=="object"||typeof j=="function")?j:P}function D(P,j){if(typeof j!="function"&&j!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof j);P.prototype=Object.create(j&&j.prototype,{constructor:{value:P,enumerable:!1,writable:!0,configurable:!0}}),j&&(Object.setPrototypeOf?Object.setPrototypeOf(P,j):P.__proto__=j)}var M=(0,S.default)("quill:keyboard"),x=/Mac/i.test(navigator.platform)?"metaKey":"ctrlKey",B=function(P){D(j,P),d(j,null,[{key:"match",value:function(z,Y){return Y=U(Y),["altKey","ctrlKey","metaKey","shiftKey"].some(function(Z){return!!Y[Z]!==z[Z]&&Y[Z]!==null})?!1:Y.key===(z.which||z.keyCode)}}]);function j(H,z){O(this,j);var Y=L(this,(j.__proto__||Object.getPrototypeOf(j)).call(this,H,z));return Y.bindings={},Object.keys(Y.options.bindings).forEach(function(Z){Z==="list autofill"&&H.scroll.whitelist!=null&&!H.scroll.whitelist.list||Y.options.bindings[Z]&&Y.addBinding(Y.options.bindings[Z])}),Y.addBinding({key:j.keys.ENTER,shiftKey:null},C),Y.addBinding({key:j.keys.ENTER,metaKey:null,ctrlKey:null,altKey:null},function(){}),/Firefox/i.test(navigator.userAgent)?(Y.addBinding({key:j.keys.BACKSPACE},{collapsed:!0},F),Y.addBinding({key:j.keys.DELETE},{collapsed:!0},k)):(Y.addBinding({key:j.keys.BACKSPACE},{collapsed:!0,prefix:/^.?$/},F),Y.addBinding({key:j.keys.DELETE},{collapsed:!0,suffix:/^.?$/},k)),Y.addBinding({key:j.keys.BACKSPACE},{collapsed:!1},R),Y.addBinding({key:j.keys.DELETE},{collapsed:!1},R),Y.addBinding({key:j.keys.BACKSPACE,altKey:null,ctrlKey:null,metaKey:null,shiftKey:null},{collapsed:!0,offset:0},F),Y.listen(),Y}return d(j,[{key:"addBinding",value:function(z){var Y=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Z=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},ee=U(z);if(ee==null||ee.key==null)return M.warn("Attempted to add invalid keyboard binding",ee);typeof Y=="function"&&(Y={handler:Y}),typeof Z=="function"&&(Z={handler:Z}),ee=(0,l.default)(ee,Y,Z),this.bindings[ee.key]=this.bindings[ee.key]||[],this.bindings[ee.key].push(ee)}},{key:"listen",value:function(){var z=this;this.quill.root.addEventListener("keydown",function(Y){if(!Y.defaultPrevented){var Z=Y.which||Y.keyCode,ee=(z.bindings[Z]||[]).filter(function(he){return j.match(Y,he)});if(ee.length!==0){var ne=z.quill.getSelection();if(!(ne==null||!z.quill.hasFocus())){var ae=z.quill.getLine(ne.index),se=s(ae,2),Oe=se[0],ge=se[1],W=z.quill.getLeaf(ne.index),$=s(W,2),Q=$[0],J=$[1],X=ne.length===0?[Q,J]:z.quill.getLeaf(ne.index+ne.length),re=s(X,2),ie=re[0],oe=re[1],Se=Q instanceof _.default.Text?Q.value().slice(0,J):"",xe=ie instanceof _.default.Text?ie.value().slice(oe):"",ue={collapsed:ne.length===0,empty:ne.length===0&&Oe.length()<=1,format:z.quill.getFormat(ne),offset:ge,prefix:Se,suffix:xe},_i=ee.some(function(he){if(he.collapsed!=null&&he.collapsed!==ue.collapsed||he.empty!=null&&he.empty!==ue.empty||he.offset!=null&&he.offset!==ue.offset)return!1;if(Array.isArray(he.format)){if(he.format.every(function(qe){return ue.format[qe]==null}))return!1}else if(a(he.format)==="object"&&!Object.keys(he.format).every(function(qe){return he.format[qe]===!0?ue.format[qe]!=null:he.format[qe]===!1?ue.format[qe]==null:(0,u.default)(he.format[qe],ue.format[qe])}))return!1;return he.prefix!=null&&!he.prefix.test(ue.prefix)||he.suffix!=null&&!he.suffix.test(ue.suffix)?!1:he.handler.call(z,ne,ue)!==!0});_i&&Y.preventDefault()}}}})}}]),j}(N.default);B.keys={BACKSPACE:8,TAB:9,ENTER:13,ESCAPE:27,LEFT:37,UP:38,RIGHT:39,DOWN:40,DELETE:46},B.DEFAULTS={bindings:{bold:G("bold"),italic:G("italic"),underline:G("underline"),indent:{key:B.keys.TAB,format:["blockquote","indent","list"],handler:function(j,H){if(H.collapsed&&H.offset!==0)return!0;this.quill.format("indent","+1",b.default.sources.USER)}},outdent:{key:B.keys.TAB,shiftKey:!0,format:["blockquote","indent","list"],handler:function(j,H){if(H.collapsed&&H.offset!==0)return!0;this.quill.format("indent","-1",b.default.sources.USER)}},"outdent backspace":{key:B.keys.BACKSPACE,collapsed:!0,shiftKey:null,metaKey:null,ctrlKey:null,altKey:null,format:["indent","list"],offset:0,handler:function(j,H){H.format.indent!=null?this.quill.format("indent","-1",b.default.sources.USER):H.format.list!=null&&this.quill.format("list",!1,b.default.sources.USER)}},"indent code-block":q(!0),"outdent code-block":q(!1),"remove tab":{key:B.keys.TAB,shiftKey:!0,collapsed:!0,prefix:/\t$/,handler:function(j){this.quill.deleteText(j.index-1,1,b.default.sources.USER)}},tab:{key:B.keys.TAB,handler:function(j){this.quill.history.cutoff();var H=new v.default().retain(j.index).delete(j.length).insert("	");this.quill.updateContents(H,b.default.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(j.index+1,b.default.sources.SILENT)}},"list empty enter":{key:B.keys.ENTER,collapsed:!0,format:["list"],empty:!0,handler:function(j,H){this.quill.format("list",!1,b.default.sources.USER),H.format.indent&&this.quill.format("indent",!1,b.default.sources.USER)}},"checklist enter":{key:B.keys.ENTER,collapsed:!0,format:{list:"checked"},handler:function(j){var H=this.quill.getLine(j.index),z=s(H,2),Y=z[0],Z=z[1],ee=(0,l.default)({},Y.formats(),{list:"checked"}),ne=new v.default().retain(j.index).insert(`
`,ee).retain(Y.length()-Z-1).retain(1,{list:"unchecked"});this.quill.updateContents(ne,b.default.sources.USER),this.quill.setSelection(j.index+1,b.default.sources.SILENT),this.quill.scrollIntoView()}},"header enter":{key:B.keys.ENTER,collapsed:!0,format:["header"],suffix:/^$/,handler:function(j,H){var z=this.quill.getLine(j.index),Y=s(z,2),Z=Y[0],ee=Y[1],ne=new v.default().retain(j.index).insert(`
`,H.format).retain(Z.length()-ee-1).retain(1,{header:null});this.quill.updateContents(ne,b.default.sources.USER),this.quill.setSelection(j.index+1,b.default.sources.SILENT),this.quill.scrollIntoView()}},"list autofill":{key:" ",collapsed:!0,format:{list:!1},prefix:/^\s*?(\d+\.|-|\*|\[ ?\]|\[x\])$/,handler:function(j,H){var z=H.prefix.length,Y=this.quill.getLine(j.index),Z=s(Y,2),ee=Z[0],ne=Z[1];if(ne>z)return!0;var ae=void 0;switch(H.prefix.trim()){case"[]":case"[ ]":ae="unchecked";break;case"[x]":ae="checked";break;case"-":case"*":ae="bullet";break;default:ae="ordered"}this.quill.insertText(j.index," ",b.default.sources.USER),this.quill.history.cutoff();var se=new v.default().retain(j.index-ne).delete(z+1).retain(ee.length()-2-ne).retain(1,{list:ae});this.quill.updateContents(se,b.default.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(j.index-z,b.default.sources.SILENT)}},"code exit":{key:B.keys.ENTER,collapsed:!0,format:["code-block"],prefix:/\n\n$/,suffix:/^\s+$/,handler:function(j){var H=this.quill.getLine(j.index),z=s(H,2),Y=z[0],Z=z[1],ee=new v.default().retain(j.index+Y.length()-Z-2).retain(1,{"code-block":null}).delete(1);this.quill.updateContents(ee,b.default.sources.USER)}},"embed left":V(B.keys.LEFT,!1),"embed left shift":V(B.keys.LEFT,!0),"embed right":V(B.keys.RIGHT,!1),"embed right shift":V(B.keys.RIGHT,!0)}};function V(P,j){var H,z=P===B.keys.LEFT?"prefix":"suffix";return H={key:P,shiftKey:j,altKey:null},T(H,z,/^$/),T(H,"handler",function(Z){var ee=Z.index;P===B.keys.RIGHT&&(ee+=Z.length+1);var ne=this.quill.getLeaf(ee),ae=s(ne,1),se=ae[0];return se instanceof _.default.Embed?(P===B.keys.LEFT?j?this.quill.setSelection(Z.index-1,Z.length+1,b.default.sources.USER):this.quill.setSelection(Z.index-1,b.default.sources.USER):j?this.quill.setSelection(Z.index,Z.length+1,b.default.sources.USER):this.quill.setSelection(Z.index+Z.length+1,b.default.sources.USER),!1):!0}),H}function F(P,j){if(!(P.index===0||this.quill.getLength()<=1)){var H=this.quill.getLine(P.index),z=s(H,1),Y=z[0],Z={};if(j.offset===0){var ee=this.quill.getLine(P.index-1),ne=s(ee,1),ae=ne[0];if(ae!=null&&ae.length()>1){var se=Y.formats(),Oe=this.quill.getFormat(P.index-1,1);Z=p.default.attributes.diff(se,Oe)||{}}}var ge=/[\uD800-\uDBFF][\uDC00-\uDFFF]$/.test(j.prefix)?2:1;this.quill.deleteText(P.index-ge,ge,b.default.sources.USER),Object.keys(Z).length>0&&this.quill.formatLine(P.index-ge,ge,Z,b.default.sources.USER),this.quill.focus()}}function k(P,j){var H=/^[\uD800-\uDBFF][\uDC00-\uDFFF]/.test(j.suffix)?2:1;if(!(P.index>=this.quill.getLength()-H)){var z={},Y=0,Z=this.quill.getLine(P.index),ee=s(Z,1),ne=ee[0];if(j.offset>=ne.length()-1){var ae=this.quill.getLine(P.index+1),se=s(ae,1),Oe=se[0];if(Oe){var ge=ne.formats(),W=this.quill.getFormat(P.index,1);z=p.default.attributes.diff(ge,W)||{},Y=Oe.length()}}this.quill.deleteText(P.index,H,b.default.sources.USER),Object.keys(z).length>0&&this.quill.formatLine(P.index+Y-1,H,z,b.default.sources.USER)}}function R(P){var j=this.quill.getLines(P),H={};if(j.length>1){var z=j[0].formats(),Y=j[j.length-1].formats();H=p.default.attributes.diff(Y,z)||{}}this.quill.deleteText(P,b.default.sources.USER),Object.keys(H).length>0&&this.quill.formatLine(P.index,1,H,b.default.sources.USER),this.quill.setSelection(P.index,b.default.sources.SILENT),this.quill.focus()}function C(P,j){var H=this;P.length>0&&this.quill.scroll.deleteAt(P.index,P.length);var z=Object.keys(j.format).reduce(function(Y,Z){return _.default.query(Z,_.default.Scope.BLOCK)&&!Array.isArray(j.format[Z])&&(Y[Z]=j.format[Z]),Y},{});this.quill.insertText(P.index,`
`,z,b.default.sources.USER),this.quill.setSelection(P.index+1,b.default.sources.SILENT),this.quill.focus(),Object.keys(j.format).forEach(function(Y){z[Y]==null&&(Array.isArray(j.format[Y])||Y!=="link"&&H.quill.format(Y,j.format[Y],b.default.sources.USER))})}function q(P){return{key:B.keys.TAB,shiftKey:!P,format:{"code-block":!0},handler:function(H){var z=_.default.query("code-block"),Y=H.index,Z=H.length,ee=this.quill.scroll.descendant(z,Y),ne=s(ee,2),ae=ne[0],se=ne[1];if(ae!=null){var Oe=this.quill.getIndex(ae),ge=ae.newlineIndex(se,!0)+1,W=ae.newlineIndex(Oe+se+Z),$=ae.domNode.textContent.slice(ge,W).split(`
`);se=0,$.forEach(function(Q,J){P?(ae.insertAt(ge+se,z.TAB),se+=z.TAB.length,J===0?Y+=z.TAB.length:Z+=z.TAB.length):Q.startsWith(z.TAB)&&(ae.deleteAt(ge+se,z.TAB.length),se-=z.TAB.length,J===0?Y-=z.TAB.length:Z-=z.TAB.length),se+=Q.length+1}),this.quill.update(b.default.sources.USER),this.quill.setSelection(Y,Z,b.default.sources.SILENT)}}}}function G(P){return{key:P[0].toUpperCase(),shortKey:!0,handler:function(H,z){this.quill.format(P,!z.format[P],b.default.sources.USER)}}}function U(P){if(typeof P=="string"||typeof P=="number")return U({key:P});if((typeof P>"u"?"undefined":a(P))==="object"&&(P=(0,y.default)(P,!1)),typeof P.key=="string")if(B.keys[P.key.toUpperCase()]!=null)P.key=B.keys[P.key.toUpperCase()];else if(P.key.length===1)P.key=P.key.toUpperCase().charCodeAt(0);else return null;return P.shortKey&&(P[x]=P.shortKey,delete P.shortKey),P}r.default=B,r.SHORTKEY=x},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0});var a=function(){function p(m,_){var g=[],b=!0,A=!1,S=void 0;try{for(var I=m[Symbol.iterator](),N;!(b=(N=I.next()).done)&&(g.push(N.value),!(_&&g.length===_));b=!0);}catch(w){A=!0,S=w}finally{try{!b&&I.return&&I.return()}finally{if(A)throw S}}return g}return function(m,_){if(Array.isArray(m))return m;if(Symbol.iterator in Object(m))return p(m,_);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),s=function p(m,_,g){m===null&&(m=Function.prototype);var b=Object.getOwnPropertyDescriptor(m,_);if(b===void 0){var A=Object.getPrototypeOf(m);return A===null?void 0:p(A,_,g)}else{if("value"in b)return b.value;var S=b.get;return S===void 0?void 0:S.call(g)}},d=function(){function p(m,_){for(var g=0;g<_.length;g++){var b=_[g];b.enumerable=b.enumerable||!1,b.configurable=!0,"value"in b&&(b.writable=!0),Object.defineProperty(m,b.key,b)}}return function(m,_,g){return _&&p(m.prototype,_),g&&p(m,g),m}}(),c=i(0),y=o(c),f=i(7),u=o(f);function o(p){return p&&p.__esModule?p:{default:p}}function l(p,m){if(!(p instanceof m))throw new TypeError("Cannot call a class as a function")}function h(p,m){if(!p)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return m&&(typeof m=="object"||typeof m=="function")?m:p}function v(p,m){if(typeof m!="function"&&m!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof m);p.prototype=Object.create(m&&m.prototype,{constructor:{value:p,enumerable:!1,writable:!0,configurable:!0}}),m&&(Object.setPrototypeOf?Object.setPrototypeOf(p,m):p.__proto__=m)}var E=function(p){v(m,p),d(m,null,[{key:"value",value:function(){}}]);function m(_,g){l(this,m);var b=h(this,(m.__proto__||Object.getPrototypeOf(m)).call(this,_));return b.selection=g,b.textNode=document.createTextNode(m.CONTENTS),b.domNode.appendChild(b.textNode),b._length=0,b}return d(m,[{key:"detach",value:function(){this.parent!=null&&this.parent.removeChild(this)}},{key:"format",value:function(g,b){if(this._length!==0)return s(m.prototype.__proto__||Object.getPrototypeOf(m.prototype),"format",this).call(this,g,b);for(var A=this,S=0;A!=null&&A.statics.scope!==y.default.Scope.BLOCK_BLOT;)S+=A.offset(A.parent),A=A.parent;A!=null&&(this._length=m.CONTENTS.length,A.optimize(),A.formatAt(S,m.CONTENTS.length,g,b),this._length=0)}},{key:"index",value:function(g,b){return g===this.textNode?0:s(m.prototype.__proto__||Object.getPrototypeOf(m.prototype),"index",this).call(this,g,b)}},{key:"length",value:function(){return this._length}},{key:"position",value:function(){return[this.textNode,this.textNode.data.length]}},{key:"remove",value:function(){s(m.prototype.__proto__||Object.getPrototypeOf(m.prototype),"remove",this).call(this),this.parent=null}},{key:"restore",value:function(){if(!(this.selection.composing||this.parent==null)){var g=this.textNode,b=this.selection.getNativeRange(),A=void 0,S=void 0,I=void 0;if(b!=null&&b.start.node===g&&b.end.node===g){var N=[g,b.start.offset,b.end.offset];A=N[0],S=N[1],I=N[2]}for(;this.domNode.lastChild!=null&&this.domNode.lastChild!==this.textNode;)this.domNode.parentNode.insertBefore(this.domNode.lastChild,this.domNode);if(this.textNode.data!==m.CONTENTS){var w=this.textNode.data.split(m.CONTENTS).join("");this.next instanceof u.default?(A=this.next.domNode,this.next.insertAt(0,w),this.textNode.data=m.CONTENTS):(this.textNode.data=w,this.parent.insertBefore(y.default.create(this.textNode),this),this.textNode=document.createTextNode(m.CONTENTS),this.domNode.appendChild(this.textNode))}if(this.remove(),S!=null){var T=[S,I].map(function(L){return Math.max(0,Math.min(A.data.length,L-1))}),O=a(T,2);return S=O[0],I=O[1],{startNode:A,startOffset:S,endNode:A,endOffset:I}}}}},{key:"update",value:function(g,b){var A=this;if(g.some(function(I){return I.type==="characterData"&&I.target===A.textNode})){var S=this.restore();S&&(b.range=S)}}},{key:"value",value:function(){return""}}]),m}(y.default.Embed);E.blotName="cursor",E.className="ql-cursor",E.tagName="span",E.CONTENTS="\uFEFF",r.default=E},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0});var a=i(0),s=y(a),d=i(4),c=y(d);function y(h){return h&&h.__esModule?h:{default:h}}function f(h,v){if(!(h instanceof v))throw new TypeError("Cannot call a class as a function")}function u(h,v){if(!h)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return v&&(typeof v=="object"||typeof v=="function")?v:h}function o(h,v){if(typeof v!="function"&&v!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof v);h.prototype=Object.create(v&&v.prototype,{constructor:{value:h,enumerable:!1,writable:!0,configurable:!0}}),v&&(Object.setPrototypeOf?Object.setPrototypeOf(h,v):h.__proto__=v)}var l=function(h){o(v,h);function v(){return f(this,v),u(this,(v.__proto__||Object.getPrototypeOf(v)).apply(this,arguments))}return v}(s.default.Container);l.allowedChildren=[c.default,d.BlockEmbed,l],r.default=l},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0}),r.ColorStyle=r.ColorClass=r.ColorAttributor=void 0;var a=function(){function E(p,m){for(var _=0;_<m.length;_++){var g=m[_];g.enumerable=g.enumerable||!1,g.configurable=!0,"value"in g&&(g.writable=!0),Object.defineProperty(p,g.key,g)}}return function(p,m,_){return m&&E(p.prototype,m),_&&E(p,_),p}}(),s=function E(p,m,_){p===null&&(p=Function.prototype);var g=Object.getOwnPropertyDescriptor(p,m);if(g===void 0){var b=Object.getPrototypeOf(p);return b===null?void 0:E(b,m,_)}else{if("value"in g)return g.value;var A=g.get;return A===void 0?void 0:A.call(_)}},d=i(0),c=y(d);function y(E){return E&&E.__esModule?E:{default:E}}function f(E,p){if(!(E instanceof p))throw new TypeError("Cannot call a class as a function")}function u(E,p){if(!E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return p&&(typeof p=="object"||typeof p=="function")?p:E}function o(E,p){if(typeof p!="function"&&p!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof p);E.prototype=Object.create(p&&p.prototype,{constructor:{value:E,enumerable:!1,writable:!0,configurable:!0}}),p&&(Object.setPrototypeOf?Object.setPrototypeOf(E,p):E.__proto__=p)}var l=function(E){o(p,E);function p(){return f(this,p),u(this,(p.__proto__||Object.getPrototypeOf(p)).apply(this,arguments))}return a(p,[{key:"value",value:function(_){var g=s(p.prototype.__proto__||Object.getPrototypeOf(p.prototype),"value",this).call(this,_);return g.startsWith("rgb(")?(g=g.replace(/^[^\d]+/,"").replace(/[^\d]+$/,""),"#"+g.split(",").map(function(b){return("00"+parseInt(b).toString(16)).slice(-2)}).join("")):g}}]),p}(c.default.Attributor.Style),h=new c.default.Attributor.Class("color","ql-color",{scope:c.default.Scope.INLINE}),v=new l("color","color",{scope:c.default.Scope.INLINE});r.ColorAttributor=l,r.ColorClass=h,r.ColorStyle=v},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0}),r.sanitize=r.default=void 0;var a=function(){function v(E,p){for(var m=0;m<p.length;m++){var _=p[m];_.enumerable=_.enumerable||!1,_.configurable=!0,"value"in _&&(_.writable=!0),Object.defineProperty(E,_.key,_)}}return function(E,p,m){return p&&v(E.prototype,p),m&&v(E,m),E}}(),s=function v(E,p,m){E===null&&(E=Function.prototype);var _=Object.getOwnPropertyDescriptor(E,p);if(_===void 0){var g=Object.getPrototypeOf(E);return g===null?void 0:v(g,p,m)}else{if("value"in _)return _.value;var b=_.get;return b===void 0?void 0:b.call(m)}},d=i(6),c=y(d);function y(v){return v&&v.__esModule?v:{default:v}}function f(v,E){if(!(v instanceof E))throw new TypeError("Cannot call a class as a function")}function u(v,E){if(!v)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return E&&(typeof E=="object"||typeof E=="function")?E:v}function o(v,E){if(typeof E!="function"&&E!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof E);v.prototype=Object.create(E&&E.prototype,{constructor:{value:v,enumerable:!1,writable:!0,configurable:!0}}),E&&(Object.setPrototypeOf?Object.setPrototypeOf(v,E):v.__proto__=E)}var l=function(v){o(E,v);function E(){return f(this,E),u(this,(E.__proto__||Object.getPrototypeOf(E)).apply(this,arguments))}return a(E,[{key:"format",value:function(m,_){if(m!==this.statics.blotName||!_)return s(E.prototype.__proto__||Object.getPrototypeOf(E.prototype),"format",this).call(this,m,_);_=this.constructor.sanitize(_),this.domNode.setAttribute("href",_)}}],[{key:"create",value:function(m){var _=s(E.__proto__||Object.getPrototypeOf(E),"create",this).call(this,m);return m=this.sanitize(m),_.setAttribute("href",m),_.setAttribute("rel","noopener noreferrer"),_.setAttribute("target","_blank"),_}},{key:"formats",value:function(m){return m.getAttribute("href")}},{key:"sanitize",value:function(m){return h(m,this.PROTOCOL_WHITELIST)?m:this.SANITIZED_URL}}]),E}(c.default);l.blotName="link",l.tagName="A",l.SANITIZED_URL="about:blank",l.PROTOCOL_WHITELIST=["http","https","mailto","tel"];function h(v,E){var p=document.createElement("a");p.href=v;var m=p.href.slice(0,p.href.indexOf(":"));return E.indexOf(m)>-1}r.default=l,r.sanitize=h},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0});var a=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(E){return typeof E}:function(E){return E&&typeof Symbol=="function"&&E.constructor===Symbol&&E!==Symbol.prototype?"symbol":typeof E},s=function(){function E(p,m){for(var _=0;_<m.length;_++){var g=m[_];g.enumerable=g.enumerable||!1,g.configurable=!0,"value"in g&&(g.writable=!0),Object.defineProperty(p,g.key,g)}}return function(p,m,_){return m&&E(p.prototype,m),_&&E(p,_),p}}(),d=i(23),c=u(d),y=i(107),f=u(y);function u(E){return E&&E.__esModule?E:{default:E}}function o(E,p){if(!(E instanceof p))throw new TypeError("Cannot call a class as a function")}var l=0;function h(E,p){E.setAttribute(p,E.getAttribute(p)!=="true")}var v=function(){function E(p){var m=this;o(this,E),this.select=p,this.container=document.createElement("span"),this.buildPicker(),this.select.style.display="none",this.select.parentNode.insertBefore(this.container,this.select),this.label.addEventListener("mousedown",function(){m.togglePicker()}),this.label.addEventListener("keydown",function(_){switch(_.keyCode){case c.default.keys.ENTER:m.togglePicker();break;case c.default.keys.ESCAPE:m.escape(),_.preventDefault();break}}),this.select.addEventListener("change",this.update.bind(this))}return s(E,[{key:"togglePicker",value:function(){this.container.classList.toggle("ql-expanded"),h(this.label,"aria-expanded"),h(this.options,"aria-hidden")}},{key:"buildItem",value:function(m){var _=this,g=document.createElement("span");return g.tabIndex="0",g.setAttribute("role","button"),g.classList.add("ql-picker-item"),m.hasAttribute("value")&&g.setAttribute("data-value",m.getAttribute("value")),m.textContent&&g.setAttribute("data-label",m.textContent),g.addEventListener("click",function(){_.selectItem(g,!0)}),g.addEventListener("keydown",function(b){switch(b.keyCode){case c.default.keys.ENTER:_.selectItem(g,!0),b.preventDefault();break;case c.default.keys.ESCAPE:_.escape(),b.preventDefault();break}}),g}},{key:"buildLabel",value:function(){var m=document.createElement("span");return m.classList.add("ql-picker-label"),m.innerHTML=f.default,m.tabIndex="0",m.setAttribute("role","button"),m.setAttribute("aria-expanded","false"),this.container.appendChild(m),m}},{key:"buildOptions",value:function(){var m=this,_=document.createElement("span");_.classList.add("ql-picker-options"),_.setAttribute("aria-hidden","true"),_.tabIndex="-1",_.id="ql-picker-options-"+l,l+=1,this.label.setAttribute("aria-controls",_.id),this.options=_,[].slice.call(this.select.options).forEach(function(g){var b=m.buildItem(g);_.appendChild(b),g.selected===!0&&m.selectItem(b)}),this.container.appendChild(_)}},{key:"buildPicker",value:function(){var m=this;[].slice.call(this.select.attributes).forEach(function(_){m.container.setAttribute(_.name,_.value)}),this.container.classList.add("ql-picker"),this.label=this.buildLabel(),this.buildOptions()}},{key:"escape",value:function(){var m=this;this.close(),setTimeout(function(){return m.label.focus()},1)}},{key:"close",value:function(){this.container.classList.remove("ql-expanded"),this.label.setAttribute("aria-expanded","false"),this.options.setAttribute("aria-hidden","true")}},{key:"selectItem",value:function(m){var _=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,g=this.container.querySelector(".ql-selected");if(m!==g&&(g!=null&&g.classList.remove("ql-selected"),m!=null&&(m.classList.add("ql-selected"),this.select.selectedIndex=[].indexOf.call(m.parentNode.children,m),m.hasAttribute("data-value")?this.label.setAttribute("data-value",m.getAttribute("data-value")):this.label.removeAttribute("data-value"),m.hasAttribute("data-label")?this.label.setAttribute("data-label",m.getAttribute("data-label")):this.label.removeAttribute("data-label"),_))){if(typeof Event=="function")this.select.dispatchEvent(new Event("change"));else if((typeof Event>"u"?"undefined":a(Event))==="object"){var b=document.createEvent("Event");b.initEvent("change",!0,!0),this.select.dispatchEvent(b)}this.close()}}},{key:"update",value:function(){var m=void 0;if(this.select.selectedIndex>-1){var _=this.container.querySelector(".ql-picker-options").children[this.select.selectedIndex];m=this.select.options[this.select.selectedIndex],this.selectItem(_)}else this.selectItem(null);var g=m!=null&&m!==this.select.querySelector("option[selected]");this.label.classList.toggle("ql-active",g)}}]),E}();r.default=v},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0});var a=i(0),s=M(a),d=i(5),c=M(d),y=i(4),f=M(y),u=i(16),o=M(u),l=i(25),h=M(l),v=i(24),E=M(v),p=i(35),m=M(p),_=i(6),g=M(_),b=i(22),A=M(b),S=i(7),I=M(S),N=i(55),w=M(N),T=i(42),O=M(T),L=i(23),D=M(L);function M(x){return x&&x.__esModule?x:{default:x}}c.default.register({"blots/block":f.default,"blots/block/embed":y.BlockEmbed,"blots/break":o.default,"blots/container":h.default,"blots/cursor":E.default,"blots/embed":m.default,"blots/inline":g.default,"blots/scroll":A.default,"blots/text":I.default,"modules/clipboard":w.default,"modules/history":O.default,"modules/keyboard":D.default}),s.default.register(f.default,o.default,E.default,g.default,A.default,I.default),r.default=c.default},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0});var a=i(1),s=function(){function d(c){this.domNode=c,this.domNode[a.DATA_KEY]={blot:this}}return Object.defineProperty(d.prototype,"statics",{get:function(){return this.constructor},enumerable:!0,configurable:!0}),d.create=function(c){if(this.tagName==null)throw new a.ParchmentError("Blot definition missing tagName");var y;return Array.isArray(this.tagName)?(typeof c=="string"&&(c=c.toUpperCase(),parseInt(c).toString()===c&&(c=parseInt(c))),typeof c=="number"?y=document.createElement(this.tagName[c-1]):this.tagName.indexOf(c)>-1?y=document.createElement(c):y=document.createElement(this.tagName[0])):y=document.createElement(this.tagName),this.className&&y.classList.add(this.className),y},d.prototype.attach=function(){this.parent!=null&&(this.scroll=this.parent.scroll)},d.prototype.clone=function(){var c=this.domNode.cloneNode(!1);return a.create(c)},d.prototype.detach=function(){this.parent!=null&&this.parent.removeChild(this),delete this.domNode[a.DATA_KEY]},d.prototype.deleteAt=function(c,y){var f=this.isolate(c,y);f.remove()},d.prototype.formatAt=function(c,y,f,u){var o=this.isolate(c,y);if(a.query(f,a.Scope.BLOT)!=null&&u)o.wrap(f,u);else if(a.query(f,a.Scope.ATTRIBUTE)!=null){var l=a.create(this.statics.scope);o.wrap(l),l.format(f,u)}},d.prototype.insertAt=function(c,y,f){var u=f==null?a.create("text",y):a.create(y,f),o=this.split(c);this.parent.insertBefore(u,o)},d.prototype.insertInto=function(c,y){y===void 0&&(y=null),this.parent!=null&&this.parent.children.remove(this);var f=null;c.children.insertBefore(this,y),y!=null&&(f=y.domNode),(this.domNode.parentNode!=c.domNode||this.domNode.nextSibling!=f)&&c.domNode.insertBefore(this.domNode,f),this.parent=c,this.attach()},d.prototype.isolate=function(c,y){var f=this.split(c);return f.split(y),f},d.prototype.length=function(){return 1},d.prototype.offset=function(c){return c===void 0&&(c=this.parent),this.parent==null||this==c?0:this.parent.children.offset(this)+this.parent.offset(c)},d.prototype.optimize=function(c){this.domNode[a.DATA_KEY]!=null&&delete this.domNode[a.DATA_KEY].mutations},d.prototype.remove=function(){this.domNode.parentNode!=null&&this.domNode.parentNode.removeChild(this.domNode),this.detach()},d.prototype.replace=function(c){c.parent!=null&&(c.parent.insertBefore(this,c.next),c.remove())},d.prototype.replaceWith=function(c,y){var f=typeof c=="string"?a.create(c,y):c;return f.replace(this),f},d.prototype.split=function(c,y){return c===0?this:this.next},d.prototype.update=function(c,y){},d.prototype.wrap=function(c,y){var f=typeof c=="string"?a.create(c,y):c;return this.parent!=null&&this.parent.insertBefore(f,this.next),f.appendChild(this),f},d.blotName="abstract",d}();r.default=s},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0});var a=i(12),s=i(32),d=i(33),c=i(1),y=function(){function f(u){this.attributes={},this.domNode=u,this.build()}return f.prototype.attribute=function(u,o){o?u.add(this.domNode,o)&&(u.value(this.domNode)!=null?this.attributes[u.attrName]=u:delete this.attributes[u.attrName]):(u.remove(this.domNode),delete this.attributes[u.attrName])},f.prototype.build=function(){var u=this;this.attributes={};var o=a.default.keys(this.domNode),l=s.default.keys(this.domNode),h=d.default.keys(this.domNode);o.concat(l).concat(h).forEach(function(v){var E=c.query(v,c.Scope.ATTRIBUTE);E instanceof a.default&&(u.attributes[E.attrName]=E)})},f.prototype.copy=function(u){var o=this;Object.keys(this.attributes).forEach(function(l){var h=o.attributes[l].value(o.domNode);u.format(l,h)})},f.prototype.move=function(u){var o=this;this.copy(u),Object.keys(this.attributes).forEach(function(l){o.attributes[l].remove(o.domNode)}),this.attributes={}},f.prototype.values=function(){var u=this;return Object.keys(this.attributes).reduce(function(o,l){return o[l]=u.attributes[l].value(u.domNode),o},{})},f}();r.default=y},function(n,r,i){var a=this&&this.__extends||function(){var y=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(f,u){f.__proto__=u}||function(f,u){for(var o in u)u.hasOwnProperty(o)&&(f[o]=u[o])};return function(f,u){y(f,u);function o(){this.constructor=f}f.prototype=u===null?Object.create(u):(o.prototype=u.prototype,new o)}}();Object.defineProperty(r,"__esModule",{value:!0});var s=i(12);function d(y,f){var u=y.getAttribute("class")||"";return u.split(/\s+/).filter(function(o){return o.indexOf(f+"-")===0})}var c=function(y){a(f,y);function f(){return y!==null&&y.apply(this,arguments)||this}return f.keys=function(u){return(u.getAttribute("class")||"").split(/\s+/).map(function(o){return o.split("-").slice(0,-1).join("-")})},f.prototype.add=function(u,o){return this.canAdd(u,o)?(this.remove(u),u.classList.add(this.keyName+"-"+o),!0):!1},f.prototype.remove=function(u){var o=d(u,this.keyName);o.forEach(function(l){u.classList.remove(l)}),u.classList.length===0&&u.removeAttribute("class")},f.prototype.value=function(u){var o=d(u,this.keyName)[0]||"",l=o.slice(this.keyName.length+1);return this.canAdd(u,l)?l:""},f}(s.default);r.default=c},function(n,r,i){var a=this&&this.__extends||function(){var y=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(f,u){f.__proto__=u}||function(f,u){for(var o in u)u.hasOwnProperty(o)&&(f[o]=u[o])};return function(f,u){y(f,u);function o(){this.constructor=f}f.prototype=u===null?Object.create(u):(o.prototype=u.prototype,new o)}}();Object.defineProperty(r,"__esModule",{value:!0});var s=i(12);function d(y){var f=y.split("-"),u=f.slice(1).map(function(o){return o[0].toUpperCase()+o.slice(1)}).join("");return f[0]+u}var c=function(y){a(f,y);function f(){return y!==null&&y.apply(this,arguments)||this}return f.keys=function(u){return(u.getAttribute("style")||"").split(";").map(function(o){var l=o.split(":");return l[0].trim()})},f.prototype.add=function(u,o){return this.canAdd(u,o)?(u.style[d(this.keyName)]=o,!0):!1},f.prototype.remove=function(u){u.style[d(this.keyName)]="",u.getAttribute("style")||u.removeAttribute("style")},f.prototype.value=function(u){var o=u.style[d(this.keyName)];return this.canAdd(u,o)?o:""},f}(s.default);r.default=c},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0});var a=function(){function c(y,f){for(var u=0;u<f.length;u++){var o=f[u];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(y,o.key,o)}}return function(y,f,u){return f&&c(y.prototype,f),u&&c(y,u),y}}();function s(c,y){if(!(c instanceof y))throw new TypeError("Cannot call a class as a function")}var d=function(){function c(y,f){s(this,c),this.quill=y,this.options=f,this.modules={}}return a(c,[{key:"init",value:function(){var f=this;Object.keys(this.options.modules).forEach(function(u){f.modules[u]==null&&f.addModule(u)})}},{key:"addModule",value:function(f){var u=this.quill.constructor.import("modules/"+f);return this.modules[f]=new u(this.quill,this.options.modules[f]||{}),this.modules[f]}}]),c}();d.DEFAULTS={modules:{}},d.themes={default:d},r.default=d},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0});var a=function(){function p(m,_){for(var g=0;g<_.length;g++){var b=_[g];b.enumerable=b.enumerable||!1,b.configurable=!0,"value"in b&&(b.writable=!0),Object.defineProperty(m,b.key,b)}}return function(m,_,g){return _&&p(m.prototype,_),g&&p(m,g),m}}(),s=function p(m,_,g){m===null&&(m=Function.prototype);var b=Object.getOwnPropertyDescriptor(m,_);if(b===void 0){var A=Object.getPrototypeOf(m);return A===null?void 0:p(A,_,g)}else{if("value"in b)return b.value;var S=b.get;return S===void 0?void 0:S.call(g)}},d=i(0),c=u(d),y=i(7),f=u(y);function u(p){return p&&p.__esModule?p:{default:p}}function o(p,m){if(!(p instanceof m))throw new TypeError("Cannot call a class as a function")}function l(p,m){if(!p)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return m&&(typeof m=="object"||typeof m=="function")?m:p}function h(p,m){if(typeof m!="function"&&m!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof m);p.prototype=Object.create(m&&m.prototype,{constructor:{value:p,enumerable:!1,writable:!0,configurable:!0}}),m&&(Object.setPrototypeOf?Object.setPrototypeOf(p,m):p.__proto__=m)}var v="\uFEFF",E=function(p){h(m,p);function m(_){o(this,m);var g=l(this,(m.__proto__||Object.getPrototypeOf(m)).call(this,_));return g.contentNode=document.createElement("span"),g.contentNode.setAttribute("contenteditable",!1),[].slice.call(g.domNode.childNodes).forEach(function(b){g.contentNode.appendChild(b)}),g.leftGuard=document.createTextNode(v),g.rightGuard=document.createTextNode(v),g.domNode.appendChild(g.leftGuard),g.domNode.appendChild(g.contentNode),g.domNode.appendChild(g.rightGuard),g}return a(m,[{key:"index",value:function(g,b){return g===this.leftGuard?0:g===this.rightGuard?1:s(m.prototype.__proto__||Object.getPrototypeOf(m.prototype),"index",this).call(this,g,b)}},{key:"restore",value:function(g){var b=void 0,A=void 0,S=g.data.split(v).join("");if(g===this.leftGuard)if(this.prev instanceof f.default){var I=this.prev.length();this.prev.insertAt(I,S),b={startNode:this.prev.domNode,startOffset:I+S.length}}else A=document.createTextNode(S),this.parent.insertBefore(c.default.create(A),this),b={startNode:A,startOffset:S.length};else g===this.rightGuard&&(this.next instanceof f.default?(this.next.insertAt(0,S),b={startNode:this.next.domNode,startOffset:S.length}):(A=document.createTextNode(S),this.parent.insertBefore(c.default.create(A),this.next),b={startNode:A,startOffset:S.length}));return g.data=v,b}},{key:"update",value:function(g,b){var A=this;g.forEach(function(S){if(S.type==="characterData"&&(S.target===A.leftGuard||S.target===A.rightGuard)){var I=A.restore(S.target);I&&(b.range=I)}})}}]),m}(c.default.Embed);r.default=E},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0}),r.AlignStyle=r.AlignClass=r.AlignAttribute=void 0;var a=i(0),s=d(a);function d(o){return o&&o.__esModule?o:{default:o}}var c={scope:s.default.Scope.BLOCK,whitelist:["right","center","justify"]},y=new s.default.Attributor.Attribute("align","align",c),f=new s.default.Attributor.Class("align","ql-align",c),u=new s.default.Attributor.Style("align","text-align",c);r.AlignAttribute=y,r.AlignClass=f,r.AlignStyle=u},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0}),r.BackgroundStyle=r.BackgroundClass=void 0;var a=i(0),s=c(a),d=i(26);function c(u){return u&&u.__esModule?u:{default:u}}var y=new s.default.Attributor.Class("background","ql-bg",{scope:s.default.Scope.INLINE}),f=new d.ColorAttributor("background","background-color",{scope:s.default.Scope.INLINE});r.BackgroundClass=y,r.BackgroundStyle=f},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0}),r.DirectionStyle=r.DirectionClass=r.DirectionAttribute=void 0;var a=i(0),s=d(a);function d(o){return o&&o.__esModule?o:{default:o}}var c={scope:s.default.Scope.BLOCK,whitelist:["rtl"]},y=new s.default.Attributor.Attribute("direction","dir",c),f=new s.default.Attributor.Class("direction","ql-direction",c),u=new s.default.Attributor.Style("direction","direction",c);r.DirectionAttribute=y,r.DirectionClass=f,r.DirectionStyle=u},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0}),r.FontClass=r.FontStyle=void 0;var a=function(){function p(m,_){for(var g=0;g<_.length;g++){var b=_[g];b.enumerable=b.enumerable||!1,b.configurable=!0,"value"in b&&(b.writable=!0),Object.defineProperty(m,b.key,b)}}return function(m,_,g){return _&&p(m.prototype,_),g&&p(m,g),m}}(),s=function p(m,_,g){m===null&&(m=Function.prototype);var b=Object.getOwnPropertyDescriptor(m,_);if(b===void 0){var A=Object.getPrototypeOf(m);return A===null?void 0:p(A,_,g)}else{if("value"in b)return b.value;var S=b.get;return S===void 0?void 0:S.call(g)}},d=i(0),c=y(d);function y(p){return p&&p.__esModule?p:{default:p}}function f(p,m){if(!(p instanceof m))throw new TypeError("Cannot call a class as a function")}function u(p,m){if(!p)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return m&&(typeof m=="object"||typeof m=="function")?m:p}function o(p,m){if(typeof m!="function"&&m!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof m);p.prototype=Object.create(m&&m.prototype,{constructor:{value:p,enumerable:!1,writable:!0,configurable:!0}}),m&&(Object.setPrototypeOf?Object.setPrototypeOf(p,m):p.__proto__=m)}var l={scope:c.default.Scope.INLINE,whitelist:["serif","monospace"]},h=new c.default.Attributor.Class("font","ql-font",l),v=function(p){o(m,p);function m(){return f(this,m),u(this,(m.__proto__||Object.getPrototypeOf(m)).apply(this,arguments))}return a(m,[{key:"value",value:function(g){return s(m.prototype.__proto__||Object.getPrototypeOf(m.prototype),"value",this).call(this,g).replace(/["']/g,"")}}]),m}(c.default.Attributor.Style),E=new v("font","font-family",l);r.FontStyle=E,r.FontClass=h},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0}),r.SizeStyle=r.SizeClass=void 0;var a=i(0),s=d(a);function d(f){return f&&f.__esModule?f:{default:f}}var c=new s.default.Attributor.Class("size","ql-size",{scope:s.default.Scope.INLINE,whitelist:["small","large","huge"]}),y=new s.default.Attributor.Style("size","font-size",{scope:s.default.Scope.INLINE,whitelist:["10px","18px","32px"]});r.SizeClass=c,r.SizeStyle=y},function(n,r,i){n.exports={align:{"":i(76),center:i(77),right:i(78),justify:i(79)},background:i(80),blockquote:i(81),bold:i(82),clean:i(83),code:i(58),"code-block":i(58),color:i(84),direction:{"":i(85),rtl:i(86)},float:{center:i(87),full:i(88),left:i(89),right:i(90)},formula:i(91),header:{1:i(92),2:i(93)},italic:i(94),image:i(95),indent:{"+1":i(96),"-1":i(97)},link:i(98),list:{ordered:i(99),bullet:i(100),check:i(101)},script:{sub:i(102),super:i(103)},strike:i(104),underline:i(105),video:i(106)}},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0}),r.getLastChangeIndex=r.default=void 0;var a=function(){function _(g,b){for(var A=0;A<b.length;A++){var S=b[A];S.enumerable=S.enumerable||!1,S.configurable=!0,"value"in S&&(S.writable=!0),Object.defineProperty(g,S.key,S)}}return function(g,b,A){return b&&_(g.prototype,b),A&&_(g,A),g}}(),s=i(0),d=o(s),c=i(5),y=o(c),f=i(9),u=o(f);function o(_){return _&&_.__esModule?_:{default:_}}function l(_,g){if(!(_ instanceof g))throw new TypeError("Cannot call a class as a function")}function h(_,g){if(!_)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return g&&(typeof g=="object"||typeof g=="function")?g:_}function v(_,g){if(typeof g!="function"&&g!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof g);_.prototype=Object.create(g&&g.prototype,{constructor:{value:_,enumerable:!1,writable:!0,configurable:!0}}),g&&(Object.setPrototypeOf?Object.setPrototypeOf(_,g):_.__proto__=g)}var E=function(_){v(g,_);function g(b,A){l(this,g);var S=h(this,(g.__proto__||Object.getPrototypeOf(g)).call(this,b,A));return S.lastRecorded=0,S.ignoreChange=!1,S.clear(),S.quill.on(y.default.events.EDITOR_CHANGE,function(I,N,w,T){I!==y.default.events.TEXT_CHANGE||S.ignoreChange||(!S.options.userOnly||T===y.default.sources.USER?S.record(N,w):S.transform(N))}),S.quill.keyboard.addBinding({key:"Z",shortKey:!0},S.undo.bind(S)),S.quill.keyboard.addBinding({key:"Z",shortKey:!0,shiftKey:!0},S.redo.bind(S)),/Win/i.test(navigator.platform)&&S.quill.keyboard.addBinding({key:"Y",shortKey:!0},S.redo.bind(S)),S}return a(g,[{key:"change",value:function(A,S){if(this.stack[A].length!==0){var I=this.stack[A].pop();this.stack[S].push(I),this.lastRecorded=0,this.ignoreChange=!0,this.quill.updateContents(I[A],y.default.sources.USER),this.ignoreChange=!1;var N=m(I[A]);this.quill.setSelection(N)}}},{key:"clear",value:function(){this.stack={undo:[],redo:[]}}},{key:"cutoff",value:function(){this.lastRecorded=0}},{key:"record",value:function(A,S){if(A.ops.length!==0){this.stack.redo=[];var I=this.quill.getContents().diff(S),N=Date.now();if(this.lastRecorded+this.options.delay>N&&this.stack.undo.length>0){var w=this.stack.undo.pop();I=I.compose(w.undo),A=w.redo.compose(A)}else this.lastRecorded=N;this.stack.undo.push({redo:A,undo:I}),this.stack.undo.length>this.options.maxStack&&this.stack.undo.shift()}}},{key:"redo",value:function(){this.change("redo","undo")}},{key:"transform",value:function(A){this.stack.undo.forEach(function(S){S.undo=A.transform(S.undo,!0),S.redo=A.transform(S.redo,!0)}),this.stack.redo.forEach(function(S){S.undo=A.transform(S.undo,!0),S.redo=A.transform(S.redo,!0)})}},{key:"undo",value:function(){this.change("undo","redo")}}]),g}(u.default);E.DEFAULTS={delay:1e3,maxStack:100,userOnly:!1};function p(_){var g=_.ops[_.ops.length-1];return g==null?!1:g.insert!=null?typeof g.insert=="string"&&g.insert.endsWith(`
`):g.attributes!=null?Object.keys(g.attributes).some(function(b){return d.default.query(b,d.default.Scope.BLOCK)!=null}):!1}function m(_){var g=_.reduce(function(A,S){return A+=S.delete||0,A},0),b=_.length()-g;return p(_)&&(b-=1),b}r.default=E,r.getLastChangeIndex=m},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0}),r.default=r.BaseTooltip=void 0;var a=function(){function C(q,G){for(var U=0;U<G.length;U++){var P=G[U];P.enumerable=P.enumerable||!1,P.configurable=!0,"value"in P&&(P.writable=!0),Object.defineProperty(q,P.key,P)}}return function(q,G,U){return G&&C(q.prototype,G),U&&C(q,U),q}}(),s=function C(q,G,U){q===null&&(q=Function.prototype);var P=Object.getOwnPropertyDescriptor(q,G);if(P===void 0){var j=Object.getPrototypeOf(q);return j===null?void 0:C(j,G,U)}else{if("value"in P)return P.value;var H=P.get;return H===void 0?void 0:H.call(U)}},d=i(3),c=N(d),y=i(2),f=N(y),u=i(8),o=N(u),l=i(23),h=N(l),v=i(34),E=N(v),p=i(59),m=N(p),_=i(60),g=N(_),b=i(28),A=N(b),S=i(61),I=N(S);function N(C){return C&&C.__esModule?C:{default:C}}function w(C,q){if(!(C instanceof q))throw new TypeError("Cannot call a class as a function")}function T(C,q){if(!C)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return q&&(typeof q=="object"||typeof q=="function")?q:C}function O(C,q){if(typeof q!="function"&&q!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof q);C.prototype=Object.create(q&&q.prototype,{constructor:{value:C,enumerable:!1,writable:!0,configurable:!0}}),q&&(Object.setPrototypeOf?Object.setPrototypeOf(C,q):C.__proto__=q)}var L=[!1,"center","right","justify"],D=["#000000","#e60000","#ff9900","#ffff00","#008a00","#0066cc","#9933ff","#ffffff","#facccc","#ffebcc","#ffffcc","#cce8cc","#cce0f5","#ebd6ff","#bbbbbb","#f06666","#ffc266","#ffff66","#66b966","#66a3e0","#c285ff","#888888","#a10000","#b26b00","#b2b200","#006100","#0047b2","#6b24b2","#444444","#5c0000","#663d00","#666600","#003700","#002966","#3d1466"],M=[!1,"serif","monospace"],x=["1","2","3",!1],B=["small",!1,"large","huge"],V=function(C){O(q,C);function q(G,U){w(this,q);var P=T(this,(q.__proto__||Object.getPrototypeOf(q)).call(this,G,U)),j=function H(z){if(!document.body.contains(G.root))return document.body.removeEventListener("click",H);P.tooltip!=null&&!P.tooltip.root.contains(z.target)&&document.activeElement!==P.tooltip.textbox&&!P.quill.hasFocus()&&P.tooltip.hide(),P.pickers!=null&&P.pickers.forEach(function(Y){Y.container.contains(z.target)||Y.close()})};return G.emitter.listenDOM("click",document.body,j),P}return a(q,[{key:"addModule",value:function(U){var P=s(q.prototype.__proto__||Object.getPrototypeOf(q.prototype),"addModule",this).call(this,U);return U==="toolbar"&&this.extendToolbar(P),P}},{key:"buildButtons",value:function(U,P){U.forEach(function(j){var H=j.getAttribute("class")||"";H.split(/\s+/).forEach(function(z){if(z.startsWith("ql-")&&(z=z.slice(3),P[z]!=null))if(z==="direction")j.innerHTML=P[z][""]+P[z].rtl;else if(typeof P[z]=="string")j.innerHTML=P[z];else{var Y=j.value||"";Y!=null&&P[z][Y]&&(j.innerHTML=P[z][Y])}})})}},{key:"buildPickers",value:function(U,P){var j=this;this.pickers=U.map(function(z){if(z.classList.contains("ql-align"))return z.querySelector("option")==null&&R(z,L),new g.default(z,P.align);if(z.classList.contains("ql-background")||z.classList.contains("ql-color")){var Y=z.classList.contains("ql-background")?"background":"color";return z.querySelector("option")==null&&R(z,D,Y==="background"?"#ffffff":"#000000"),new m.default(z,P[Y])}else return z.querySelector("option")==null&&(z.classList.contains("ql-font")?R(z,M):z.classList.contains("ql-header")?R(z,x):z.classList.contains("ql-size")&&R(z,B)),new A.default(z)});var H=function(){j.pickers.forEach(function(Y){Y.update()})};this.quill.on(o.default.events.EDITOR_CHANGE,H)}}]),q}(E.default);V.DEFAULTS=(0,c.default)(!0,{},E.default.DEFAULTS,{modules:{toolbar:{handlers:{formula:function(){this.quill.theme.tooltip.edit("formula")},image:function(){var q=this,G=this.container.querySelector("input.ql-image[type=file]");G==null&&(G=document.createElement("input"),G.setAttribute("type","file"),G.setAttribute("accept","image/png, image/gif, image/jpeg, image/bmp, image/x-icon"),G.classList.add("ql-image"),G.addEventListener("change",function(){if(G.files!=null&&G.files[0]!=null){var U=new FileReader;U.onload=function(P){var j=q.quill.getSelection(!0);q.quill.updateContents(new f.default().retain(j.index).delete(j.length).insert({image:P.target.result}),o.default.sources.USER),q.quill.setSelection(j.index+1,o.default.sources.SILENT),G.value=""},U.readAsDataURL(G.files[0])}}),this.container.appendChild(G)),G.click()},video:function(){this.quill.theme.tooltip.edit("video")}}}}});var F=function(C){O(q,C);function q(G,U){w(this,q);var P=T(this,(q.__proto__||Object.getPrototypeOf(q)).call(this,G,U));return P.textbox=P.root.querySelector('input[type="text"]'),P.listen(),P}return a(q,[{key:"listen",value:function(){var U=this;this.textbox.addEventListener("keydown",function(P){h.default.match(P,"enter")?(U.save(),P.preventDefault()):h.default.match(P,"escape")&&(U.cancel(),P.preventDefault())})}},{key:"cancel",value:function(){this.hide()}},{key:"edit",value:function(){var U=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"link",P=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;this.root.classList.remove("ql-hidden"),this.root.classList.add("ql-editing"),P!=null?this.textbox.value=P:U!==this.root.getAttribute("data-mode")&&(this.textbox.value=""),this.position(this.quill.getBounds(this.quill.selection.savedRange)),this.textbox.select(),this.textbox.setAttribute("placeholder",this.textbox.getAttribute("data-"+U)||""),this.root.setAttribute("data-mode",U)}},{key:"restoreFocus",value:function(){var U=this.quill.scrollingContainer.scrollTop;this.quill.focus(),this.quill.scrollingContainer.scrollTop=U}},{key:"save",value:function(){var U=this.textbox.value;switch(this.root.getAttribute("data-mode")){case"link":{var P=this.quill.root.scrollTop;this.linkRange?(this.quill.formatText(this.linkRange,"link",U,o.default.sources.USER),delete this.linkRange):(this.restoreFocus(),this.quill.format("link",U,o.default.sources.USER)),this.quill.root.scrollTop=P;break}case"video":U=k(U);case"formula":{if(!U)break;var j=this.quill.getSelection(!0);if(j!=null){var H=j.index+j.length;this.quill.insertEmbed(H,this.root.getAttribute("data-mode"),U,o.default.sources.USER),this.root.getAttribute("data-mode")==="formula"&&this.quill.insertText(H+1," ",o.default.sources.USER),this.quill.setSelection(H+2,o.default.sources.USER)}break}}this.textbox.value="",this.hide()}}]),q}(I.default);function k(C){var q=C.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtube\.com\/watch.*v=([a-zA-Z0-9_-]+)/)||C.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtu\.be\/([a-zA-Z0-9_-]+)/);return q?(q[1]||"https")+"://www.youtube.com/embed/"+q[2]+"?showinfo=0":(q=C.match(/^(?:(https?):\/\/)?(?:www\.)?vimeo\.com\/(\d+)/))?(q[1]||"https")+"://player.vimeo.com/video/"+q[2]+"/":C}function R(C,q){var G=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;q.forEach(function(U){var P=document.createElement("option");U===G?P.setAttribute("selected","selected"):P.setAttribute("value",U),C.appendChild(P)})}r.BaseTooltip=F,r.default=V},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0});var a=function(){function s(){this.head=this.tail=null,this.length=0}return s.prototype.append=function(){for(var d=[],c=0;c<arguments.length;c++)d[c]=arguments[c];this.insertBefore(d[0],null),d.length>1&&this.append.apply(this,d.slice(1))},s.prototype.contains=function(d){for(var c,y=this.iterator();c=y();)if(c===d)return!0;return!1},s.prototype.insertBefore=function(d,c){d&&(d.next=c,c!=null?(d.prev=c.prev,c.prev!=null&&(c.prev.next=d),c.prev=d,c===this.head&&(this.head=d)):this.tail!=null?(this.tail.next=d,d.prev=this.tail,this.tail=d):(d.prev=null,this.head=this.tail=d),this.length+=1)},s.prototype.offset=function(d){for(var c=0,y=this.head;y!=null;){if(y===d)return c;c+=y.length(),y=y.next}return-1},s.prototype.remove=function(d){this.contains(d)&&(d.prev!=null&&(d.prev.next=d.next),d.next!=null&&(d.next.prev=d.prev),d===this.head&&(this.head=d.next),d===this.tail&&(this.tail=d.prev),this.length-=1)},s.prototype.iterator=function(d){return d===void 0&&(d=this.head),function(){var c=d;return d!=null&&(d=d.next),c}},s.prototype.find=function(d,c){c===void 0&&(c=!1);for(var y,f=this.iterator();y=f();){var u=y.length();if(d<u||c&&d===u&&(y.next==null||y.next.length()!==0))return[y,d];d-=u}return[null,0]},s.prototype.forEach=function(d){for(var c,y=this.iterator();c=y();)d(c)},s.prototype.forEachAt=function(d,c,y){if(!(c<=0))for(var f=this.find(d),u=f[0],o=f[1],l,h=d-o,v=this.iterator(u);(l=v())&&h<d+c;){var E=l.length();d>h?y(l,d-h,Math.min(c,h+E-d)):y(l,0,Math.min(E,d+c-h)),h+=E}},s.prototype.map=function(d){return this.reduce(function(c,y){return c.push(d(y)),c},[])},s.prototype.reduce=function(d,c){for(var y,f=this.iterator();y=f();)c=d(c,y);return c},s}();r.default=a},function(n,r,i){var a=this&&this.__extends||function(){var u=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(o,l){o.__proto__=l}||function(o,l){for(var h in l)l.hasOwnProperty(h)&&(o[h]=l[h])};return function(o,l){u(o,l);function h(){this.constructor=o}o.prototype=l===null?Object.create(l):(h.prototype=l.prototype,new h)}}();Object.defineProperty(r,"__esModule",{value:!0});var s=i(17),d=i(1),c={attributes:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0},y=100,f=function(u){a(o,u);function o(l){var h=u.call(this,l)||this;return h.scroll=h,h.observer=new MutationObserver(function(v){h.update(v)}),h.observer.observe(h.domNode,c),h.attach(),h}return o.prototype.detach=function(){u.prototype.detach.call(this),this.observer.disconnect()},o.prototype.deleteAt=function(l,h){this.update(),l===0&&h===this.length()?this.children.forEach(function(v){v.remove()}):u.prototype.deleteAt.call(this,l,h)},o.prototype.formatAt=function(l,h,v,E){this.update(),u.prototype.formatAt.call(this,l,h,v,E)},o.prototype.insertAt=function(l,h,v){this.update(),u.prototype.insertAt.call(this,l,h,v)},o.prototype.optimize=function(l,h){var v=this;l===void 0&&(l=[]),h===void 0&&(h={}),u.prototype.optimize.call(this,h);for(var E=[].slice.call(this.observer.takeRecords());E.length>0;)l.push(E.pop());for(var p=function(b,A){A===void 0&&(A=!0),!(b==null||b===v)&&b.domNode.parentNode!=null&&(b.domNode[d.DATA_KEY].mutations==null&&(b.domNode[d.DATA_KEY].mutations=[]),A&&p(b.parent))},m=function(b){b.domNode[d.DATA_KEY]==null||b.domNode[d.DATA_KEY].mutations==null||(b instanceof s.default&&b.children.forEach(m),b.optimize(h))},_=l,g=0;_.length>0;g+=1){if(g>=y)throw new Error("[Parchment] Maximum optimize iterations reached");for(_.forEach(function(b){var A=d.find(b.target,!0);A!=null&&(A.domNode===b.target&&(b.type==="childList"?(p(d.find(b.previousSibling,!1)),[].forEach.call(b.addedNodes,function(S){var I=d.find(S,!1);p(I,!1),I instanceof s.default&&I.children.forEach(function(N){p(N,!1)})})):b.type==="attributes"&&p(A.prev)),p(A))}),this.children.forEach(m),_=[].slice.call(this.observer.takeRecords()),E=_.slice();E.length>0;)l.push(E.pop())}},o.prototype.update=function(l,h){var v=this;h===void 0&&(h={}),l=l||this.observer.takeRecords(),l.map(function(E){var p=d.find(E.target,!0);return p==null?null:p.domNode[d.DATA_KEY].mutations==null?(p.domNode[d.DATA_KEY].mutations=[E],p):(p.domNode[d.DATA_KEY].mutations.push(E),null)}).forEach(function(E){E==null||E===v||E.domNode[d.DATA_KEY]==null||E.update(E.domNode[d.DATA_KEY].mutations||[],h)}),this.domNode[d.DATA_KEY].mutations!=null&&u.prototype.update.call(this,this.domNode[d.DATA_KEY].mutations,h),this.optimize(l,h)},o.blotName="scroll",o.defaultChild="block",o.scope=d.Scope.BLOCK_BLOT,o.tagName="DIV",o}(s.default);r.default=f},function(n,r,i){var a=this&&this.__extends||function(){var f=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(u,o){u.__proto__=o}||function(u,o){for(var l in o)o.hasOwnProperty(l)&&(u[l]=o[l])};return function(u,o){f(u,o);function l(){this.constructor=u}u.prototype=o===null?Object.create(o):(l.prototype=o.prototype,new l)}}();Object.defineProperty(r,"__esModule",{value:!0});var s=i(18),d=i(1);function c(f,u){if(Object.keys(f).length!==Object.keys(u).length)return!1;for(var o in f)if(f[o]!==u[o])return!1;return!0}var y=function(f){a(u,f);function u(){return f!==null&&f.apply(this,arguments)||this}return u.formats=function(o){if(o.tagName!==u.tagName)return f.formats.call(this,o)},u.prototype.format=function(o,l){var h=this;o===this.statics.blotName&&!l?(this.children.forEach(function(v){v instanceof s.default||(v=v.wrap(u.blotName,!0)),h.attributes.copy(v)}),this.unwrap()):f.prototype.format.call(this,o,l)},u.prototype.formatAt=function(o,l,h,v){if(this.formats()[h]!=null||d.query(h,d.Scope.ATTRIBUTE)){var E=this.isolate(o,l);E.format(h,v)}else f.prototype.formatAt.call(this,o,l,h,v)},u.prototype.optimize=function(o){f.prototype.optimize.call(this,o);var l=this.formats();if(Object.keys(l).length===0)return this.unwrap();var h=this.next;h instanceof u&&h.prev===this&&c(l,h.formats())&&(h.moveChildren(this),h.remove())},u.blotName="inline",u.scope=d.Scope.INLINE_BLOT,u.tagName="SPAN",u}(s.default);r.default=y},function(n,r,i){var a=this&&this.__extends||function(){var y=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(f,u){f.__proto__=u}||function(f,u){for(var o in u)u.hasOwnProperty(o)&&(f[o]=u[o])};return function(f,u){y(f,u);function o(){this.constructor=f}f.prototype=u===null?Object.create(u):(o.prototype=u.prototype,new o)}}();Object.defineProperty(r,"__esModule",{value:!0});var s=i(18),d=i(1),c=function(y){a(f,y);function f(){return y!==null&&y.apply(this,arguments)||this}return f.formats=function(u){var o=d.query(f.blotName).tagName;if(u.tagName!==o)return y.formats.call(this,u)},f.prototype.format=function(u,o){d.query(u,d.Scope.BLOCK)!=null&&(u===this.statics.blotName&&!o?this.replaceWith(f.blotName):y.prototype.format.call(this,u,o))},f.prototype.formatAt=function(u,o,l,h){d.query(l,d.Scope.BLOCK)!=null?this.format(l,h):y.prototype.formatAt.call(this,u,o,l,h)},f.prototype.insertAt=function(u,o,l){if(l==null||d.query(o,d.Scope.INLINE)!=null)y.prototype.insertAt.call(this,u,o,l);else{var h=this.split(u),v=d.create(o,l);h.parent.insertBefore(v,h)}},f.prototype.update=function(u,o){navigator.userAgent.match(/Trident/)?this.build():y.prototype.update.call(this,u,o)},f.blotName="block",f.scope=d.Scope.BLOCK_BLOT,f.tagName="P",f}(s.default);r.default=c},function(n,r,i){var a=this&&this.__extends||function(){var c=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(y,f){y.__proto__=f}||function(y,f){for(var u in f)f.hasOwnProperty(u)&&(y[u]=f[u])};return function(y,f){c(y,f);function u(){this.constructor=y}y.prototype=f===null?Object.create(f):(u.prototype=f.prototype,new u)}}();Object.defineProperty(r,"__esModule",{value:!0});var s=i(19),d=function(c){a(y,c);function y(){return c!==null&&c.apply(this,arguments)||this}return y.formats=function(f){},y.prototype.format=function(f,u){c.prototype.formatAt.call(this,0,this.length(),f,u)},y.prototype.formatAt=function(f,u,o,l){f===0&&u===this.length()?this.format(o,l):c.prototype.formatAt.call(this,f,u,o,l)},y.prototype.formats=function(){return this.statics.formats(this.domNode)},y}(s.default);r.default=d},function(n,r,i){var a=this&&this.__extends||function(){var y=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(f,u){f.__proto__=u}||function(f,u){for(var o in u)u.hasOwnProperty(o)&&(f[o]=u[o])};return function(f,u){y(f,u);function o(){this.constructor=f}f.prototype=u===null?Object.create(u):(o.prototype=u.prototype,new o)}}();Object.defineProperty(r,"__esModule",{value:!0});var s=i(19),d=i(1),c=function(y){a(f,y);function f(u){var o=y.call(this,u)||this;return o.text=o.statics.value(o.domNode),o}return f.create=function(u){return document.createTextNode(u)},f.value=function(u){var o=u.data;return o.normalize&&(o=o.normalize()),o},f.prototype.deleteAt=function(u,o){this.domNode.data=this.text=this.text.slice(0,u)+this.text.slice(u+o)},f.prototype.index=function(u,o){return this.domNode===u?o:-1},f.prototype.insertAt=function(u,o,l){l==null?(this.text=this.text.slice(0,u)+o+this.text.slice(u),this.domNode.data=this.text):y.prototype.insertAt.call(this,u,o,l)},f.prototype.length=function(){return this.text.length},f.prototype.optimize=function(u){y.prototype.optimize.call(this,u),this.text=this.statics.value(this.domNode),this.text.length===0?this.remove():this.next instanceof f&&this.next.prev===this&&(this.insertAt(this.length(),this.next.value()),this.next.remove())},f.prototype.position=function(u,o){return[this.domNode,u]},f.prototype.split=function(u,o){if(o===void 0&&(o=!1),!o){if(u===0)return this;if(u===this.length())return this.next}var l=d.create(this.domNode.splitText(u));return this.parent.insertBefore(l,this.next),this.text=this.statics.value(this.domNode),l},f.prototype.update=function(u,o){var l=this;u.some(function(h){return h.type==="characterData"&&h.target===l.domNode})&&(this.text=this.statics.value(this.domNode))},f.prototype.value=function(){return this.text},f.blotName="text",f.scope=d.Scope.INLINE_BLOT,f}(s.default);r.default=c},function(n,r,i){var a=document.createElement("div");if(a.classList.toggle("test-class",!1),a.classList.contains("test-class")){var s=DOMTokenList.prototype.toggle;DOMTokenList.prototype.toggle=function(d,c){return arguments.length>1&&!this.contains(d)==!c?c:s.call(this,d)}}String.prototype.startsWith||(String.prototype.startsWith=function(d,c){return c=c||0,this.substr(c,d.length)===d}),String.prototype.endsWith||(String.prototype.endsWith=function(d,c){var y=this.toString();(typeof c!="number"||!isFinite(c)||Math.floor(c)!==c||c>y.length)&&(c=y.length),c-=d.length;var f=y.indexOf(d,c);return f!==-1&&f===c}),Array.prototype.find||Object.defineProperty(Array.prototype,"find",{value:function(c){if(this===null)throw new TypeError("Array.prototype.find called on null or undefined");if(typeof c!="function")throw new TypeError("predicate must be a function");for(var y=Object(this),f=y.length>>>0,u=arguments[1],o,l=0;l<f;l++)if(o=y[l],c.call(u,o,l,y))return o}}),document.addEventListener("DOMContentLoaded",function(){document.execCommand("enableObjectResizing",!1,!1),document.execCommand("autoUrlDetect",!1,!1)})},function(n,r){var i=-1,a=1,s=0;function d(g,b,A){if(g==b)return g?[[s,g]]:[];(A<0||g.length<A)&&(A=null);var S=u(g,b),I=g.substring(0,S);g=g.substring(S),b=b.substring(S),S=o(g,b);var N=g.substring(g.length-S);g=g.substring(0,g.length-S),b=b.substring(0,b.length-S);var w=c(g,b);return I&&w.unshift([s,I]),N&&w.push([s,N]),h(w),A!=null&&(w=p(w,A)),w=m(w),w}function c(g,b){var A;if(!g)return[[a,b]];if(!b)return[[i,g]];var S=g.length>b.length?g:b,I=g.length>b.length?b:g,N=S.indexOf(I);if(N!=-1)return A=[[a,S.substring(0,N)],[s,I],[a,S.substring(N+I.length)]],g.length>b.length&&(A[0][0]=A[2][0]=i),A;if(I.length==1)return[[i,g],[a,b]];var w=l(g,b);if(w){var T=w[0],O=w[1],L=w[2],D=w[3],M=w[4],x=d(T,L),B=d(O,D);return x.concat([[s,M]],B)}return y(g,b)}function y(g,b){for(var A=g.length,S=b.length,I=Math.ceil((A+S)/2),N=I,w=2*I,T=new Array(w),O=new Array(w),L=0;L<w;L++)T[L]=-1,O[L]=-1;T[N+1]=0,O[N+1]=0;for(var D=A-S,M=D%2!=0,x=0,B=0,V=0,F=0,k=0;k<I;k++){for(var R=-k+x;R<=k-B;R+=2){var C=N+R,q;R==-k||R!=k&&T[C-1]<T[C+1]?q=T[C+1]:q=T[C-1]+1;for(var G=q-R;q<A&&G<S&&g.charAt(q)==b.charAt(G);)q++,G++;if(T[C]=q,q>A)B+=2;else if(G>S)x+=2;else if(M){var U=N+D-R;if(U>=0&&U<w&&O[U]!=-1){var P=A-O[U];if(q>=P)return f(g,b,q,G)}}}for(var j=-k+V;j<=k-F;j+=2){var U=N+j,P;j==-k||j!=k&&O[U-1]<O[U+1]?P=O[U+1]:P=O[U-1]+1;for(var H=P-j;P<A&&H<S&&g.charAt(A-P-1)==b.charAt(S-H-1);)P++,H++;if(O[U]=P,P>A)F+=2;else if(H>S)V+=2;else if(!M){var C=N+D-j;if(C>=0&&C<w&&T[C]!=-1){var q=T[C],G=N+q-C;if(P=A-P,q>=P)return f(g,b,q,G)}}}}return[[i,g],[a,b]]}function f(g,b,A,S){var I=g.substring(0,A),N=b.substring(0,S),w=g.substring(A),T=b.substring(S),O=d(I,N),L=d(w,T);return O.concat(L)}function u(g,b){if(!g||!b||g.charAt(0)!=b.charAt(0))return 0;for(var A=0,S=Math.min(g.length,b.length),I=S,N=0;A<I;)g.substring(N,I)==b.substring(N,I)?(A=I,N=A):S=I,I=Math.floor((S-A)/2+A);return I}function o(g,b){if(!g||!b||g.charAt(g.length-1)!=b.charAt(b.length-1))return 0;for(var A=0,S=Math.min(g.length,b.length),I=S,N=0;A<I;)g.substring(g.length-I,g.length-N)==b.substring(b.length-I,b.length-N)?(A=I,N=A):S=I,I=Math.floor((S-A)/2+A);return I}function l(g,b){var A=g.length>b.length?g:b,S=g.length>b.length?b:g;if(A.length<4||S.length*2<A.length)return null;function I(B,V,F){for(var k=B.substring(F,F+Math.floor(B.length/4)),R=-1,C="",q,G,U,P;(R=V.indexOf(k,R+1))!=-1;){var j=u(B.substring(F),V.substring(R)),H=o(B.substring(0,F),V.substring(0,R));C.length<H+j&&(C=V.substring(R-H,R)+V.substring(R,R+j),q=B.substring(0,F-H),G=B.substring(F+j),U=V.substring(0,R-H),P=V.substring(R+j))}return C.length*2>=B.length?[q,G,U,P,C]:null}var N=I(A,S,Math.ceil(A.length/4)),w=I(A,S,Math.ceil(A.length/2)),T;if(!N&&!w)return null;w?N?T=N[4].length>w[4].length?N:w:T=w:T=N;var O,L,D,M;g.length>b.length?(O=T[0],L=T[1],D=T[2],M=T[3]):(D=T[0],M=T[1],O=T[2],L=T[3]);var x=T[4];return[O,L,D,M,x]}function h(g){g.push([s,""]);for(var b=0,A=0,S=0,I="",N="",w;b<g.length;)switch(g[b][0]){case a:S++,N+=g[b][1],b++;break;case i:A++,I+=g[b][1],b++;break;case s:A+S>1?(A!==0&&S!==0&&(w=u(N,I),w!==0&&(b-A-S>0&&g[b-A-S-1][0]==s?g[b-A-S-1][1]+=N.substring(0,w):(g.splice(0,0,[s,N.substring(0,w)]),b++),N=N.substring(w),I=I.substring(w)),w=o(N,I),w!==0&&(g[b][1]=N.substring(N.length-w)+g[b][1],N=N.substring(0,N.length-w),I=I.substring(0,I.length-w))),A===0?g.splice(b-S,A+S,[a,N]):S===0?g.splice(b-A,A+S,[i,I]):g.splice(b-A-S,A+S,[i,I],[a,N]),b=b-A-S+(A?1:0)+(S?1:0)+1):b!==0&&g[b-1][0]==s?(g[b-1][1]+=g[b][1],g.splice(b,1)):b++,S=0,A=0,I="",N="";break}g[g.length-1][1]===""&&g.pop();var T=!1;for(b=1;b<g.length-1;)g[b-1][0]==s&&g[b+1][0]==s&&(g[b][1].substring(g[b][1].length-g[b-1][1].length)==g[b-1][1]?(g[b][1]=g[b-1][1]+g[b][1].substring(0,g[b][1].length-g[b-1][1].length),g[b+1][1]=g[b-1][1]+g[b+1][1],g.splice(b-1,1),T=!0):g[b][1].substring(0,g[b+1][1].length)==g[b+1][1]&&(g[b-1][1]+=g[b+1][1],g[b][1]=g[b][1].substring(g[b+1][1].length)+g[b+1][1],g.splice(b+1,1),T=!0)),b++;T&&h(g)}var v=d;v.INSERT=a,v.DELETE=i,v.EQUAL=s,n.exports=v;function E(g,b){if(b===0)return[s,g];for(var A=0,S=0;S<g.length;S++){var I=g[S];if(I[0]===i||I[0]===s){var N=A+I[1].length;if(b===N)return[S+1,g];if(b<N){g=g.slice();var w=b-A,T=[I[0],I[1].slice(0,w)],O=[I[0],I[1].slice(w)];return g.splice(S,1,T,O),[S+1,g]}else A=N}}throw new Error("cursor_pos is out of bounds!")}function p(g,b){var A=E(g,b),S=A[1],I=A[0],N=S[I],w=S[I+1];if(N==null)return g;if(N[0]!==s)return g;if(w!=null&&N[1]+w[1]===w[1]+N[1])return S.splice(I,2,w,N),_(S,I,2);if(w!=null&&w[1].indexOf(N[1])===0){S.splice(I,2,[w[0],N[1]],[0,N[1]]);var T=w[1].slice(N[1].length);return T.length>0&&S.splice(I+2,0,[w[0],T]),_(S,I,3)}else return g}function m(g){for(var b=!1,A=function(w){return w.charCodeAt(0)>=56320&&w.charCodeAt(0)<=57343},S=function(w){return w.charCodeAt(w.length-1)>=55296&&w.charCodeAt(w.length-1)<=56319},I=2;I<g.length;I+=1)g[I-2][0]===s&&S(g[I-2][1])&&g[I-1][0]===i&&A(g[I-1][1])&&g[I][0]===a&&A(g[I][1])&&(b=!0,g[I-1][1]=g[I-2][1].slice(-1)+g[I-1][1],g[I][1]=g[I-2][1].slice(-1)+g[I][1],g[I-2][1]=g[I-2][1].slice(0,-1));if(!b)return g;for(var N=[],I=0;I<g.length;I+=1)g[I][1].length>0&&N.push(g[I]);return N}function _(g,b,A){for(var S=b+A-1;S>=0&&S>=b-1;S--)if(S+1<g.length){var I=g[S],N=g[S+1];I[0]===N[1]&&g.splice(S,2,[I[0],I[1]+N[1]])}return g}},function(n,r){r=n.exports=typeof Object.keys=="function"?Object.keys:i,r.shim=i;function i(a){var s=[];for(var d in a)s.push(d);return s}},function(n,r){var i=function(){return Object.prototype.toString.call(arguments)}()=="[object Arguments]";r=n.exports=i?a:s,r.supported=a;function a(d){return Object.prototype.toString.call(d)=="[object Arguments]"}r.unsupported=s;function s(d){return d&&typeof d=="object"&&typeof d.length=="number"&&Object.prototype.hasOwnProperty.call(d,"callee")&&!Object.prototype.propertyIsEnumerable.call(d,"callee")||!1}},function(n,r){var i=Object.prototype.hasOwnProperty,a="~";function s(){}Object.create&&(s.prototype=Object.create(null),new s().__proto__||(a=!1));function d(y,f,u){this.fn=y,this.context=f,this.once=u||!1}function c(){this._events=new s,this._eventsCount=0}c.prototype.eventNames=function(){var f=[],u,o;if(this._eventsCount===0)return f;for(o in u=this._events)i.call(u,o)&&f.push(a?o.slice(1):o);return Object.getOwnPropertySymbols?f.concat(Object.getOwnPropertySymbols(u)):f},c.prototype.listeners=function(f,u){var o=a?a+f:f,l=this._events[o];if(u)return!!l;if(!l)return[];if(l.fn)return[l.fn];for(var h=0,v=l.length,E=new Array(v);h<v;h++)E[h]=l[h].fn;return E},c.prototype.emit=function(f,u,o,l,h,v){var E=a?a+f:f;if(!this._events[E])return!1;var p=this._events[E],m=arguments.length,_,g;if(p.fn){switch(p.once&&this.removeListener(f,p.fn,void 0,!0),m){case 1:return p.fn.call(p.context),!0;case 2:return p.fn.call(p.context,u),!0;case 3:return p.fn.call(p.context,u,o),!0;case 4:return p.fn.call(p.context,u,o,l),!0;case 5:return p.fn.call(p.context,u,o,l,h),!0;case 6:return p.fn.call(p.context,u,o,l,h,v),!0}for(g=1,_=new Array(m-1);g<m;g++)_[g-1]=arguments[g];p.fn.apply(p.context,_)}else{var b=p.length,A;for(g=0;g<b;g++)switch(p[g].once&&this.removeListener(f,p[g].fn,void 0,!0),m){case 1:p[g].fn.call(p[g].context);break;case 2:p[g].fn.call(p[g].context,u);break;case 3:p[g].fn.call(p[g].context,u,o);break;case 4:p[g].fn.call(p[g].context,u,o,l);break;default:if(!_)for(A=1,_=new Array(m-1);A<m;A++)_[A-1]=arguments[A];p[g].fn.apply(p[g].context,_)}}return!0},c.prototype.on=function(f,u,o){var l=new d(u,o||this),h=a?a+f:f;return this._events[h]?this._events[h].fn?this._events[h]=[this._events[h],l]:this._events[h].push(l):(this._events[h]=l,this._eventsCount++),this},c.prototype.once=function(f,u,o){var l=new d(u,o||this,!0),h=a?a+f:f;return this._events[h]?this._events[h].fn?this._events[h]=[this._events[h],l]:this._events[h].push(l):(this._events[h]=l,this._eventsCount++),this},c.prototype.removeListener=function(f,u,o,l){var h=a?a+f:f;if(!this._events[h])return this;if(!u)return--this._eventsCount===0?this._events=new s:delete this._events[h],this;var v=this._events[h];if(v.fn)v.fn===u&&(!l||v.once)&&(!o||v.context===o)&&(--this._eventsCount===0?this._events=new s:delete this._events[h]);else{for(var E=0,p=[],m=v.length;E<m;E++)(v[E].fn!==u||l&&!v[E].once||o&&v[E].context!==o)&&p.push(v[E]);p.length?this._events[h]=p.length===1?p[0]:p:--this._eventsCount===0?this._events=new s:delete this._events[h]}return this},c.prototype.removeAllListeners=function(f){var u;return f?(u=a?a+f:f,this._events[u]&&(--this._eventsCount===0?this._events=new s:delete this._events[u])):(this._events=new s,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prototype.setMaxListeners=function(){return this},c.prefixed=a,c.EventEmitter=c,typeof n<"u"&&(n.exports=c)},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0}),r.matchText=r.matchSpacing=r.matchNewline=r.matchBlot=r.matchAttributor=r.default=void 0;var a=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(W){return typeof W}:function(W){return W&&typeof Symbol=="function"&&W.constructor===Symbol&&W!==Symbol.prototype?"symbol":typeof W},s=function(){function W($,Q){var J=[],X=!0,re=!1,ie=void 0;try{for(var oe=$[Symbol.iterator](),Se;!(X=(Se=oe.next()).done)&&(J.push(Se.value),!(Q&&J.length===Q));X=!0);}catch(xe){re=!0,ie=xe}finally{try{!X&&oe.return&&oe.return()}finally{if(re)throw ie}}return J}return function($,Q){if(Array.isArray($))return $;if(Symbol.iterator in Object($))return W($,Q);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),d=function(){function W($,Q){for(var J=0;J<Q.length;J++){var X=Q[J];X.enumerable=X.enumerable||!1,X.configurable=!0,"value"in X&&(X.writable=!0),Object.defineProperty($,X.key,X)}}return function($,Q,J){return Q&&W($.prototype,Q),J&&W($,J),$}}(),c=i(3),y=O(c),f=i(2),u=O(f),o=i(0),l=O(o),h=i(5),v=O(h),E=i(10),p=O(E),m=i(9),_=O(m),g=i(36),b=i(37),A=i(13),S=O(A),I=i(26),N=i(38),w=i(39),T=i(40);function O(W){return W&&W.__esModule?W:{default:W}}function L(W,$,Q){return $ in W?Object.defineProperty(W,$,{value:Q,enumerable:!0,configurable:!0,writable:!0}):W[$]=Q,W}function D(W,$){if(!(W instanceof $))throw new TypeError("Cannot call a class as a function")}function M(W,$){if(!W)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return $&&(typeof $=="object"||typeof $=="function")?$:W}function x(W,$){if(typeof $!="function"&&$!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof $);W.prototype=Object.create($&&$.prototype,{constructor:{value:W,enumerable:!1,writable:!0,configurable:!0}}),$&&(Object.setPrototypeOf?Object.setPrototypeOf(W,$):W.__proto__=$)}var B=(0,p.default)("quill:clipboard"),V="__ql-matcher",F=[[Node.TEXT_NODE,ge],[Node.TEXT_NODE,ae],["br",Z],[Node.ELEMENT_NODE,ae],[Node.ELEMENT_NODE,Y],[Node.ELEMENT_NODE,se],[Node.ELEMENT_NODE,z],[Node.ELEMENT_NODE,Oe],["li",ne],["b",H.bind(H,"bold")],["i",H.bind(H,"italic")],["style",ee]],k=[g.AlignAttribute,N.DirectionAttribute].reduce(function(W,$){return W[$.keyName]=$,W},{}),R=[g.AlignStyle,b.BackgroundStyle,I.ColorStyle,N.DirectionStyle,w.FontStyle,T.SizeStyle].reduce(function(W,$){return W[$.keyName]=$,W},{}),C=function(W){x($,W);function $(Q,J){D(this,$);var X=M(this,($.__proto__||Object.getPrototypeOf($)).call(this,Q,J));return X.quill.root.addEventListener("paste",X.onPaste.bind(X)),X.container=X.quill.addContainer("ql-clipboard"),X.container.setAttribute("contenteditable",!0),X.container.setAttribute("tabindex",-1),X.matchers=[],F.concat(X.options.matchers).forEach(function(re){var ie=s(re,2),oe=ie[0],Se=ie[1];!J.matchVisual&&Se===se||X.addMatcher(oe,Se)}),X}return d($,[{key:"addMatcher",value:function(J,X){this.matchers.push([J,X])}},{key:"convert",value:function(J){if(typeof J=="string")return this.container.innerHTML=J.replace(/\>\r?\n +\</g,"><"),this.convert();var X=this.quill.getFormat(this.quill.selection.savedRange.index);if(X[S.default.blotName]){var re=this.container.innerText;return this.container.innerHTML="",new u.default().insert(re,L({},S.default.blotName,X[S.default.blotName]))}var ie=this.prepareMatching(),oe=s(ie,2),Se=oe[0],xe=oe[1],ue=j(this.container,Se,xe);return U(ue,`
`)&&ue.ops[ue.ops.length-1].attributes==null&&(ue=ue.compose(new u.default().retain(ue.length()-1).delete(1))),B.log("convert",this.container.innerHTML,ue),this.container.innerHTML="",ue}},{key:"dangerouslyPasteHTML",value:function(J,X){var re=arguments.length>2&&arguments[2]!==void 0?arguments[2]:v.default.sources.API;if(typeof J=="string")this.quill.setContents(this.convert(J),X),this.quill.setSelection(0,v.default.sources.SILENT);else{var ie=this.convert(X);this.quill.updateContents(new u.default().retain(J).concat(ie),re),this.quill.setSelection(J+ie.length(),v.default.sources.SILENT)}}},{key:"onPaste",value:function(J){var X=this;if(!(J.defaultPrevented||!this.quill.isEnabled())){var re=this.quill.getSelection(),ie=new u.default().retain(re.index),oe=this.quill.scrollingContainer.scrollTop;this.container.focus(),this.quill.selection.update(v.default.sources.SILENT),setTimeout(function(){ie=ie.concat(X.convert()).delete(re.length),X.quill.updateContents(ie,v.default.sources.USER),X.quill.setSelection(ie.length()-re.length,v.default.sources.SILENT),X.quill.scrollingContainer.scrollTop=oe,X.quill.focus()},1)}}},{key:"prepareMatching",value:function(){var J=this,X=[],re=[];return this.matchers.forEach(function(ie){var oe=s(ie,2),Se=oe[0],xe=oe[1];switch(Se){case Node.TEXT_NODE:re.push(xe);break;case Node.ELEMENT_NODE:X.push(xe);break;default:[].forEach.call(J.container.querySelectorAll(Se),function(ue){ue[V]=ue[V]||[],ue[V].push(xe)});break}}),[X,re]}}]),$}(_.default);C.DEFAULTS={matchers:[],matchVisual:!0};function q(W,$,Q){return(typeof $>"u"?"undefined":a($))==="object"?Object.keys($).reduce(function(J,X){return q(J,X,$[X])},W):W.reduce(function(J,X){return X.attributes&&X.attributes[$]?J.push(X):J.insert(X.insert,(0,y.default)({},L({},$,Q),X.attributes))},new u.default)}function G(W){if(W.nodeType!==Node.ELEMENT_NODE)return{};var $="__ql-computed-style";return W[$]||(W[$]=window.getComputedStyle(W))}function U(W,$){for(var Q="",J=W.ops.length-1;J>=0&&Q.length<$.length;--J){var X=W.ops[J];if(typeof X.insert!="string")break;Q=X.insert+Q}return Q.slice(-1*$.length)===$}function P(W){if(W.childNodes.length===0)return!1;var $=G(W);return["block","list-item"].indexOf($.display)>-1}function j(W,$,Q){return W.nodeType===W.TEXT_NODE?Q.reduce(function(J,X){return X(W,J)},new u.default):W.nodeType===W.ELEMENT_NODE?[].reduce.call(W.childNodes||[],function(J,X){var re=j(X,$,Q);return X.nodeType===W.ELEMENT_NODE&&(re=$.reduce(function(ie,oe){return oe(X,ie)},re),re=(X[V]||[]).reduce(function(ie,oe){return oe(X,ie)},re)),J.concat(re)},new u.default):new u.default}function H(W,$,Q){return q(Q,W,!0)}function z(W,$){var Q=l.default.Attributor.Attribute.keys(W),J=l.default.Attributor.Class.keys(W),X=l.default.Attributor.Style.keys(W),re={};return Q.concat(J).concat(X).forEach(function(ie){var oe=l.default.query(ie,l.default.Scope.ATTRIBUTE);oe!=null&&(re[oe.attrName]=oe.value(W),re[oe.attrName])||(oe=k[ie],oe!=null&&(oe.attrName===ie||oe.keyName===ie)&&(re[oe.attrName]=oe.value(W)||void 0),oe=R[ie],oe!=null&&(oe.attrName===ie||oe.keyName===ie)&&(oe=R[ie],re[oe.attrName]=oe.value(W)||void 0))}),Object.keys(re).length>0&&($=q($,re)),$}function Y(W,$){var Q=l.default.query(W);if(Q==null)return $;if(Q.prototype instanceof l.default.Embed){var J={},X=Q.value(W);X!=null&&(J[Q.blotName]=X,$=new u.default().insert(J,Q.formats(W)))}else typeof Q.formats=="function"&&($=q($,Q.blotName,Q.formats(W)));return $}function Z(W,$){return U($,`
`)||$.insert(`
`),$}function ee(){return new u.default}function ne(W,$){var Q=l.default.query(W);if(Q==null||Q.blotName!=="list-item"||!U($,`
`))return $;for(var J=-1,X=W.parentNode;!X.classList.contains("ql-clipboard");)(l.default.query(X)||{}).blotName==="list"&&(J+=1),X=X.parentNode;return J<=0?$:$.compose(new u.default().retain($.length()-1).retain(1,{indent:J}))}function ae(W,$){return U($,`
`)||(P(W)||$.length()>0&&W.nextSibling&&P(W.nextSibling))&&$.insert(`
`),$}function se(W,$){if(P(W)&&W.nextElementSibling!=null&&!U($,`

`)){var Q=W.offsetHeight+parseFloat(G(W).marginTop)+parseFloat(G(W).marginBottom);W.nextElementSibling.offsetTop>W.offsetTop+Q*1.5&&$.insert(`
`)}return $}function Oe(W,$){var Q={},J=W.style||{};return J.fontStyle&&G(W).fontStyle==="italic"&&(Q.italic=!0),J.fontWeight&&(G(W).fontWeight.startsWith("bold")||parseInt(G(W).fontWeight)>=700)&&(Q.bold=!0),Object.keys(Q).length>0&&($=q($,Q)),parseFloat(J.textIndent||0)>0&&($=new u.default().insert("	").concat($)),$}function ge(W,$){var Q=W.data;if(W.parentNode.tagName==="O:P")return $.insert(Q.trim());if(Q.trim().length===0&&W.parentNode.classList.contains("ql-clipboard"))return $;if(!G(W.parentNode).whiteSpace.startsWith("pre")){var J=function(re,ie){return ie=ie.replace(/[^\u00a0]/g,""),ie.length<1&&re?" ":ie};Q=Q.replace(/\r\n/g," ").replace(/\n/g," "),Q=Q.replace(/\s\s+/g,J.bind(J,!0)),(W.previousSibling==null&&P(W.parentNode)||W.previousSibling!=null&&P(W.previousSibling))&&(Q=Q.replace(/^\s+/,J.bind(J,!1))),(W.nextSibling==null&&P(W.parentNode)||W.nextSibling!=null&&P(W.nextSibling))&&(Q=Q.replace(/\s+$/,J.bind(J,!1)))}return $.insert(Q)}r.default=C,r.matchAttributor=z,r.matchBlot=Y,r.matchNewline=ae,r.matchSpacing=se,r.matchText=ge},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0});var a=function(){function h(v,E){for(var p=0;p<E.length;p++){var m=E[p];m.enumerable=m.enumerable||!1,m.configurable=!0,"value"in m&&(m.writable=!0),Object.defineProperty(v,m.key,m)}}return function(v,E,p){return E&&h(v.prototype,E),p&&h(v,p),v}}(),s=function h(v,E,p){v===null&&(v=Function.prototype);var m=Object.getOwnPropertyDescriptor(v,E);if(m===void 0){var _=Object.getPrototypeOf(v);return _===null?void 0:h(_,E,p)}else{if("value"in m)return m.value;var g=m.get;return g===void 0?void 0:g.call(p)}},d=i(6),c=y(d);function y(h){return h&&h.__esModule?h:{default:h}}function f(h,v){if(!(h instanceof v))throw new TypeError("Cannot call a class as a function")}function u(h,v){if(!h)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return v&&(typeof v=="object"||typeof v=="function")?v:h}function o(h,v){if(typeof v!="function"&&v!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof v);h.prototype=Object.create(v&&v.prototype,{constructor:{value:h,enumerable:!1,writable:!0,configurable:!0}}),v&&(Object.setPrototypeOf?Object.setPrototypeOf(h,v):h.__proto__=v)}var l=function(h){o(v,h);function v(){return f(this,v),u(this,(v.__proto__||Object.getPrototypeOf(v)).apply(this,arguments))}return a(v,[{key:"optimize",value:function(p){s(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"optimize",this).call(this,p),this.domNode.tagName!==this.statics.tagName[0]&&this.replaceWith(this.statics.blotName)}}],[{key:"create",value:function(){return s(v.__proto__||Object.getPrototypeOf(v),"create",this).call(this)}},{key:"formats",value:function(){return!0}}]),v}(c.default);l.blotName="bold",l.tagName=["STRONG","B"],r.default=l},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0}),r.addControls=r.default=void 0;var a=function(){function T(O,L){var D=[],M=!0,x=!1,B=void 0;try{for(var V=O[Symbol.iterator](),F;!(M=(F=V.next()).done)&&(D.push(F.value),!(L&&D.length===L));M=!0);}catch(k){x=!0,B=k}finally{try{!M&&V.return&&V.return()}finally{if(x)throw B}}return D}return function(O,L){if(Array.isArray(O))return O;if(Symbol.iterator in Object(O))return T(O,L);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),s=function(){function T(O,L){for(var D=0;D<L.length;D++){var M=L[D];M.enumerable=M.enumerable||!1,M.configurable=!0,"value"in M&&(M.writable=!0),Object.defineProperty(O,M.key,M)}}return function(O,L,D){return L&&T(O.prototype,L),D&&T(O,D),O}}(),d=i(2),c=p(d),y=i(0),f=p(y),u=i(5),o=p(u),l=i(10),h=p(l),v=i(9),E=p(v);function p(T){return T&&T.__esModule?T:{default:T}}function m(T,O,L){return O in T?Object.defineProperty(T,O,{value:L,enumerable:!0,configurable:!0,writable:!0}):T[O]=L,T}function _(T,O){if(!(T instanceof O))throw new TypeError("Cannot call a class as a function")}function g(T,O){if(!T)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return O&&(typeof O=="object"||typeof O=="function")?O:T}function b(T,O){if(typeof O!="function"&&O!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof O);T.prototype=Object.create(O&&O.prototype,{constructor:{value:T,enumerable:!1,writable:!0,configurable:!0}}),O&&(Object.setPrototypeOf?Object.setPrototypeOf(T,O):T.__proto__=O)}var A=(0,h.default)("quill:toolbar"),S=function(T){b(O,T);function O(L,D){_(this,O);var M=g(this,(O.__proto__||Object.getPrototypeOf(O)).call(this,L,D));if(Array.isArray(M.options.container)){var x=document.createElement("div");N(x,M.options.container),L.container.parentNode.insertBefore(x,L.container),M.container=x}else typeof M.options.container=="string"?M.container=document.querySelector(M.options.container):M.container=M.options.container;if(!(M.container instanceof HTMLElement)){var B;return B=A.error("Container required for toolbar",M.options),g(M,B)}return M.container.classList.add("ql-toolbar"),M.controls=[],M.handlers={},Object.keys(M.options.handlers).forEach(function(V){M.addHandler(V,M.options.handlers[V])}),[].forEach.call(M.container.querySelectorAll("button, select"),function(V){M.attach(V)}),M.quill.on(o.default.events.EDITOR_CHANGE,function(V,F){V===o.default.events.SELECTION_CHANGE&&M.update(F)}),M.quill.on(o.default.events.SCROLL_OPTIMIZE,function(){var V=M.quill.selection.getRange(),F=a(V,1),k=F[0];M.update(k)}),M}return s(O,[{key:"addHandler",value:function(D,M){this.handlers[D]=M}},{key:"attach",value:function(D){var M=this,x=[].find.call(D.classList,function(V){return V.indexOf("ql-")===0});if(x){if(x=x.slice(3),D.tagName==="BUTTON"&&D.setAttribute("type","button"),this.handlers[x]==null){if(this.quill.scroll.whitelist!=null&&this.quill.scroll.whitelist[x]==null){A.warn("ignoring attaching to disabled format",x,D);return}if(f.default.query(x)==null){A.warn("ignoring attaching to nonexistent format",x,D);return}}var B=D.tagName==="SELECT"?"change":"click";D.addEventListener(B,function(V){var F=void 0;if(D.tagName==="SELECT"){if(D.selectedIndex<0)return;var k=D.options[D.selectedIndex];k.hasAttribute("selected")?F=!1:F=k.value||!1}else D.classList.contains("ql-active")?F=!1:F=D.value||!D.hasAttribute("value"),V.preventDefault();M.quill.focus();var R=M.quill.selection.getRange(),C=a(R,1),q=C[0];if(M.handlers[x]!=null)M.handlers[x].call(M,F);else if(f.default.query(x).prototype instanceof f.default.Embed){if(F=prompt("Enter "+x),!F)return;M.quill.updateContents(new c.default().retain(q.index).delete(q.length).insert(m({},x,F)),o.default.sources.USER)}else M.quill.format(x,F,o.default.sources.USER);M.update(q)}),this.controls.push([x,D])}}},{key:"update",value:function(D){var M=D==null?{}:this.quill.getFormat(D);this.controls.forEach(function(x){var B=a(x,2),V=B[0],F=B[1];if(F.tagName==="SELECT"){var k=void 0;if(D==null)k=null;else if(M[V]==null)k=F.querySelector("option[selected]");else if(!Array.isArray(M[V])){var R=M[V];typeof R=="string"&&(R=R.replace(/\"/g,'\\"')),k=F.querySelector('option[value="'+R+'"]')}k==null?(F.value="",F.selectedIndex=-1):k.selected=!0}else if(D==null)F.classList.remove("ql-active");else if(F.hasAttribute("value")){var C=M[V]===F.getAttribute("value")||M[V]!=null&&M[V].toString()===F.getAttribute("value")||M[V]==null&&!F.getAttribute("value");F.classList.toggle("ql-active",C)}else F.classList.toggle("ql-active",M[V]!=null)})}}]),O}(E.default);S.DEFAULTS={};function I(T,O,L){var D=document.createElement("button");D.setAttribute("type","button"),D.classList.add("ql-"+O),L!=null&&(D.value=L),T.appendChild(D)}function N(T,O){Array.isArray(O[0])||(O=[O]),O.forEach(function(L){var D=document.createElement("span");D.classList.add("ql-formats"),L.forEach(function(M){if(typeof M=="string")I(D,M);else{var x=Object.keys(M)[0],B=M[x];Array.isArray(B)?w(D,x,B):I(D,x,B)}}),T.appendChild(D)})}function w(T,O,L){var D=document.createElement("select");D.classList.add("ql-"+O),L.forEach(function(M){var x=document.createElement("option");M!==!1?x.setAttribute("value",M):x.setAttribute("selected","selected"),D.appendChild(x)}),T.appendChild(D)}S.DEFAULTS={container:null,handlers:{clean:function(){var O=this,L=this.quill.getSelection();if(L!=null)if(L.length==0){var D=this.quill.getFormat();Object.keys(D).forEach(function(M){f.default.query(M,f.default.Scope.INLINE)!=null&&O.quill.format(M,!1)})}else this.quill.removeFormat(L,o.default.sources.USER)},direction:function(O){var L=this.quill.getFormat().align;O==="rtl"&&L==null?this.quill.format("align","right",o.default.sources.USER):!O&&L==="right"&&this.quill.format("align",!1,o.default.sources.USER),this.quill.format("direction",O,o.default.sources.USER)},indent:function(O){var L=this.quill.getSelection(),D=this.quill.getFormat(L),M=parseInt(D.indent||0);if(O==="+1"||O==="-1"){var x=O==="+1"?1:-1;D.direction==="rtl"&&(x*=-1),this.quill.format("indent",M+x,o.default.sources.USER)}},link:function(O){O===!0&&(O=prompt("Enter link URL:")),this.quill.format("link",O,o.default.sources.USER)},list:function(O){var L=this.quill.getSelection(),D=this.quill.getFormat(L);O==="check"?D.list==="checked"||D.list==="unchecked"?this.quill.format("list",!1,o.default.sources.USER):this.quill.format("list","unchecked",o.default.sources.USER):this.quill.format("list",O,o.default.sources.USER)}}},r.default=S,r.addControls=N},function(n,r){n.exports='<svg viewbox="0 0 18 18"> <polyline class="ql-even ql-stroke" points="5 7 3 9 5 11"></polyline> <polyline class="ql-even ql-stroke" points="13 7 15 9 13 11"></polyline> <line class=ql-stroke x1=10 x2=8 y1=5 y2=13></line> </svg>'},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0});var a=function(){function h(v,E){for(var p=0;p<E.length;p++){var m=E[p];m.enumerable=m.enumerable||!1,m.configurable=!0,"value"in m&&(m.writable=!0),Object.defineProperty(v,m.key,m)}}return function(v,E,p){return E&&h(v.prototype,E),p&&h(v,p),v}}(),s=function h(v,E,p){v===null&&(v=Function.prototype);var m=Object.getOwnPropertyDescriptor(v,E);if(m===void 0){var _=Object.getPrototypeOf(v);return _===null?void 0:h(_,E,p)}else{if("value"in m)return m.value;var g=m.get;return g===void 0?void 0:g.call(p)}},d=i(28),c=y(d);function y(h){return h&&h.__esModule?h:{default:h}}function f(h,v){if(!(h instanceof v))throw new TypeError("Cannot call a class as a function")}function u(h,v){if(!h)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return v&&(typeof v=="object"||typeof v=="function")?v:h}function o(h,v){if(typeof v!="function"&&v!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof v);h.prototype=Object.create(v&&v.prototype,{constructor:{value:h,enumerable:!1,writable:!0,configurable:!0}}),v&&(Object.setPrototypeOf?Object.setPrototypeOf(h,v):h.__proto__=v)}var l=function(h){o(v,h);function v(E,p){f(this,v);var m=u(this,(v.__proto__||Object.getPrototypeOf(v)).call(this,E));return m.label.innerHTML=p,m.container.classList.add("ql-color-picker"),[].slice.call(m.container.querySelectorAll(".ql-picker-item"),0,7).forEach(function(_){_.classList.add("ql-primary")}),m}return a(v,[{key:"buildItem",value:function(p){var m=s(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"buildItem",this).call(this,p);return m.style.backgroundColor=p.getAttribute("value")||"",m}},{key:"selectItem",value:function(p,m){s(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"selectItem",this).call(this,p,m);var _=this.label.querySelector(".ql-color-label"),g=p&&p.getAttribute("data-value")||"";_&&(_.tagName==="line"?_.style.stroke=g:_.style.fill=g)}}]),v}(c.default);r.default=l},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0});var a=function(){function h(v,E){for(var p=0;p<E.length;p++){var m=E[p];m.enumerable=m.enumerable||!1,m.configurable=!0,"value"in m&&(m.writable=!0),Object.defineProperty(v,m.key,m)}}return function(v,E,p){return E&&h(v.prototype,E),p&&h(v,p),v}}(),s=function h(v,E,p){v===null&&(v=Function.prototype);var m=Object.getOwnPropertyDescriptor(v,E);if(m===void 0){var _=Object.getPrototypeOf(v);return _===null?void 0:h(_,E,p)}else{if("value"in m)return m.value;var g=m.get;return g===void 0?void 0:g.call(p)}},d=i(28),c=y(d);function y(h){return h&&h.__esModule?h:{default:h}}function f(h,v){if(!(h instanceof v))throw new TypeError("Cannot call a class as a function")}function u(h,v){if(!h)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return v&&(typeof v=="object"||typeof v=="function")?v:h}function o(h,v){if(typeof v!="function"&&v!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof v);h.prototype=Object.create(v&&v.prototype,{constructor:{value:h,enumerable:!1,writable:!0,configurable:!0}}),v&&(Object.setPrototypeOf?Object.setPrototypeOf(h,v):h.__proto__=v)}var l=function(h){o(v,h);function v(E,p){f(this,v);var m=u(this,(v.__proto__||Object.getPrototypeOf(v)).call(this,E));return m.container.classList.add("ql-icon-picker"),[].forEach.call(m.container.querySelectorAll(".ql-picker-item"),function(_){_.innerHTML=p[_.getAttribute("data-value")||""]}),m.defaultItem=m.container.querySelector(".ql-selected"),m.selectItem(m.defaultItem),m}return a(v,[{key:"selectItem",value:function(p,m){s(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"selectItem",this).call(this,p,m),p=p||this.defaultItem,this.label.innerHTML=p.innerHTML}}]),v}(c.default);r.default=l},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0});var a=function(){function c(y,f){for(var u=0;u<f.length;u++){var o=f[u];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(y,o.key,o)}}return function(y,f,u){return f&&c(y.prototype,f),u&&c(y,u),y}}();function s(c,y){if(!(c instanceof y))throw new TypeError("Cannot call a class as a function")}var d=function(){function c(y,f){var u=this;s(this,c),this.quill=y,this.boundsContainer=f||document.body,this.root=y.addContainer("ql-tooltip"),this.root.innerHTML=this.constructor.TEMPLATE,this.quill.root===this.quill.scrollingContainer&&this.quill.root.addEventListener("scroll",function(){u.root.style.marginTop=-1*u.quill.root.scrollTop+"px"}),this.hide()}return a(c,[{key:"hide",value:function(){this.root.classList.add("ql-hidden")}},{key:"position",value:function(f){var u=f.left+f.width/2-this.root.offsetWidth/2,o=f.bottom+this.quill.root.scrollTop;this.root.style.left=u+"px",this.root.style.top=o+"px",this.root.classList.remove("ql-flip");var l=this.boundsContainer.getBoundingClientRect(),h=this.root.getBoundingClientRect(),v=0;if(h.right>l.right&&(v=l.right-h.right,this.root.style.left=u+v+"px"),h.left<l.left&&(v=l.left-h.left,this.root.style.left=u+v+"px"),h.bottom>l.bottom){var E=h.bottom-h.top,p=f.bottom-f.top+E;this.root.style.top=o-p+"px",this.root.classList.add("ql-flip")}return v}},{key:"show",value:function(){this.root.classList.remove("ql-editing"),this.root.classList.remove("ql-hidden")}}]),c}();r.default=d},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0});var a=function(){function w(T,O){var L=[],D=!0,M=!1,x=void 0;try{for(var B=T[Symbol.iterator](),V;!(D=(V=B.next()).done)&&(L.push(V.value),!(O&&L.length===O));D=!0);}catch(F){M=!0,x=F}finally{try{!D&&B.return&&B.return()}finally{if(M)throw x}}return L}return function(T,O){if(Array.isArray(T))return T;if(Symbol.iterator in Object(T))return w(T,O);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),s=function w(T,O,L){T===null&&(T=Function.prototype);var D=Object.getOwnPropertyDescriptor(T,O);if(D===void 0){var M=Object.getPrototypeOf(T);return M===null?void 0:w(M,O,L)}else{if("value"in D)return D.value;var x=D.get;return x===void 0?void 0:x.call(L)}},d=function(){function w(T,O){for(var L=0;L<O.length;L++){var D=O[L];D.enumerable=D.enumerable||!1,D.configurable=!0,"value"in D&&(D.writable=!0),Object.defineProperty(T,D.key,D)}}return function(T,O,L){return O&&w(T.prototype,O),L&&w(T,L),T}}(),c=i(3),y=_(c),f=i(8),u=_(f),o=i(43),l=_(o),h=i(27),v=_(h),E=i(15),p=i(41),m=_(p);function _(w){return w&&w.__esModule?w:{default:w}}function g(w,T){if(!(w instanceof T))throw new TypeError("Cannot call a class as a function")}function b(w,T){if(!w)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return T&&(typeof T=="object"||typeof T=="function")?T:w}function A(w,T){if(typeof T!="function"&&T!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof T);w.prototype=Object.create(T&&T.prototype,{constructor:{value:w,enumerable:!1,writable:!0,configurable:!0}}),T&&(Object.setPrototypeOf?Object.setPrototypeOf(w,T):w.__proto__=T)}var S=[[{header:["1","2","3",!1]}],["bold","italic","underline","link"],[{list:"ordered"},{list:"bullet"}],["clean"]],I=function(w){A(T,w);function T(O,L){g(this,T),L.modules.toolbar!=null&&L.modules.toolbar.container==null&&(L.modules.toolbar.container=S);var D=b(this,(T.__proto__||Object.getPrototypeOf(T)).call(this,O,L));return D.quill.container.classList.add("ql-snow"),D}return d(T,[{key:"extendToolbar",value:function(L){L.container.classList.add("ql-snow"),this.buildButtons([].slice.call(L.container.querySelectorAll("button")),m.default),this.buildPickers([].slice.call(L.container.querySelectorAll("select")),m.default),this.tooltip=new N(this.quill,this.options.bounds),L.container.querySelector(".ql-link")&&this.quill.keyboard.addBinding({key:"K",shortKey:!0},function(D,M){L.handlers.link.call(L,!M.format.link)})}}]),T}(l.default);I.DEFAULTS=(0,y.default)(!0,{},l.default.DEFAULTS,{modules:{toolbar:{handlers:{link:function(T){if(T){var O=this.quill.getSelection();if(O==null||O.length==0)return;var L=this.quill.getText(O);/^\S+@\S+\.\S+$/.test(L)&&L.indexOf("mailto:")!==0&&(L="mailto:"+L);var D=this.quill.theme.tooltip;D.edit("link",L)}else this.quill.format("link",!1)}}}}});var N=function(w){A(T,w);function T(O,L){g(this,T);var D=b(this,(T.__proto__||Object.getPrototypeOf(T)).call(this,O,L));return D.preview=D.root.querySelector("a.ql-preview"),D}return d(T,[{key:"listen",value:function(){var L=this;s(T.prototype.__proto__||Object.getPrototypeOf(T.prototype),"listen",this).call(this),this.root.querySelector("a.ql-action").addEventListener("click",function(D){L.root.classList.contains("ql-editing")?L.save():L.edit("link",L.preview.textContent),D.preventDefault()}),this.root.querySelector("a.ql-remove").addEventListener("click",function(D){if(L.linkRange!=null){var M=L.linkRange;L.restoreFocus(),L.quill.formatText(M,"link",!1,u.default.sources.USER),delete L.linkRange}D.preventDefault(),L.hide()}),this.quill.on(u.default.events.SELECTION_CHANGE,function(D,M,x){if(D!=null){if(D.length===0&&x===u.default.sources.USER){var B=L.quill.scroll.descendant(v.default,D.index),V=a(B,2),F=V[0],k=V[1];if(F!=null){L.linkRange=new E.Range(D.index-k,F.length());var R=v.default.formats(F.domNode);L.preview.textContent=R,L.preview.setAttribute("href",R),L.show(),L.position(L.quill.getBounds(L.linkRange));return}}else delete L.linkRange;L.hide()}})}},{key:"show",value:function(){s(T.prototype.__proto__||Object.getPrototypeOf(T.prototype),"show",this).call(this),this.root.removeAttribute("data-mode")}}]),T}(o.BaseTooltip);N.TEMPLATE=['<a class="ql-preview" rel="noopener noreferrer" target="_blank" href="about:blank"></a>','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-action"></a>','<a class="ql-remove"></a>'].join(""),r.default=I},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0});var a=i(29),s=X(a),d=i(36),c=i(38),y=i(64),f=i(65),u=X(f),o=i(66),l=X(o),h=i(67),v=X(h),E=i(37),p=i(26),m=i(39),_=i(40),g=i(56),b=X(g),A=i(68),S=X(A),I=i(27),N=X(I),w=i(69),T=X(w),O=i(70),L=X(O),D=i(71),M=X(D),x=i(72),B=X(x),V=i(73),F=X(V),k=i(13),R=X(k),C=i(74),q=X(C),G=i(75),U=X(G),P=i(57),j=X(P),H=i(41),z=X(H),Y=i(28),Z=X(Y),ee=i(59),ne=X(ee),ae=i(60),se=X(ae),Oe=i(61),ge=X(Oe),W=i(108),$=X(W),Q=i(62),J=X(Q);function X(re){return re&&re.__esModule?re:{default:re}}s.default.register({"attributors/attribute/direction":c.DirectionAttribute,"attributors/class/align":d.AlignClass,"attributors/class/background":E.BackgroundClass,"attributors/class/color":p.ColorClass,"attributors/class/direction":c.DirectionClass,"attributors/class/font":m.FontClass,"attributors/class/size":_.SizeClass,"attributors/style/align":d.AlignStyle,"attributors/style/background":E.BackgroundStyle,"attributors/style/color":p.ColorStyle,"attributors/style/direction":c.DirectionStyle,"attributors/style/font":m.FontStyle,"attributors/style/size":_.SizeStyle},!0),s.default.register({"formats/align":d.AlignClass,"formats/direction":c.DirectionClass,"formats/indent":y.IndentClass,"formats/background":E.BackgroundStyle,"formats/color":p.ColorStyle,"formats/font":m.FontClass,"formats/size":_.SizeClass,"formats/blockquote":u.default,"formats/code-block":R.default,"formats/header":l.default,"formats/list":v.default,"formats/bold":b.default,"formats/code":k.Code,"formats/italic":S.default,"formats/link":N.default,"formats/script":T.default,"formats/strike":L.default,"formats/underline":M.default,"formats/image":B.default,"formats/video":F.default,"formats/list/item":h.ListItem,"modules/formula":q.default,"modules/syntax":U.default,"modules/toolbar":j.default,"themes/bubble":$.default,"themes/snow":J.default,"ui/icons":z.default,"ui/picker":Z.default,"ui/icon-picker":se.default,"ui/color-picker":ne.default,"ui/tooltip":ge.default},!0),r.default=s.default},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0}),r.IndentClass=void 0;var a=function(){function v(E,p){for(var m=0;m<p.length;m++){var _=p[m];_.enumerable=_.enumerable||!1,_.configurable=!0,"value"in _&&(_.writable=!0),Object.defineProperty(E,_.key,_)}}return function(E,p,m){return p&&v(E.prototype,p),m&&v(E,m),E}}(),s=function v(E,p,m){E===null&&(E=Function.prototype);var _=Object.getOwnPropertyDescriptor(E,p);if(_===void 0){var g=Object.getPrototypeOf(E);return g===null?void 0:v(g,p,m)}else{if("value"in _)return _.value;var b=_.get;return b===void 0?void 0:b.call(m)}},d=i(0),c=y(d);function y(v){return v&&v.__esModule?v:{default:v}}function f(v,E){if(!(v instanceof E))throw new TypeError("Cannot call a class as a function")}function u(v,E){if(!v)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return E&&(typeof E=="object"||typeof E=="function")?E:v}function o(v,E){if(typeof E!="function"&&E!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof E);v.prototype=Object.create(E&&E.prototype,{constructor:{value:v,enumerable:!1,writable:!0,configurable:!0}}),E&&(Object.setPrototypeOf?Object.setPrototypeOf(v,E):v.__proto__=E)}var l=function(v){o(E,v);function E(){return f(this,E),u(this,(E.__proto__||Object.getPrototypeOf(E)).apply(this,arguments))}return a(E,[{key:"add",value:function(m,_){if(_==="+1"||_==="-1"){var g=this.value(m)||0;_=_==="+1"?g+1:g-1}return _===0?(this.remove(m),!0):s(E.prototype.__proto__||Object.getPrototypeOf(E.prototype),"add",this).call(this,m,_)}},{key:"canAdd",value:function(m,_){return s(E.prototype.__proto__||Object.getPrototypeOf(E.prototype),"canAdd",this).call(this,m,_)||s(E.prototype.__proto__||Object.getPrototypeOf(E.prototype),"canAdd",this).call(this,m,parseInt(_))}},{key:"value",value:function(m){return parseInt(s(E.prototype.__proto__||Object.getPrototypeOf(E.prototype),"value",this).call(this,m))||void 0}}]),E}(c.default.Attributor.Class),h=new l("indent","ql-indent",{scope:c.default.Scope.BLOCK,whitelist:[1,2,3,4,5,6,7,8]});r.IndentClass=h},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0});var a=i(4),s=d(a);function d(o){return o&&o.__esModule?o:{default:o}}function c(o,l){if(!(o instanceof l))throw new TypeError("Cannot call a class as a function")}function y(o,l){if(!o)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l&&(typeof l=="object"||typeof l=="function")?l:o}function f(o,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof l);o.prototype=Object.create(l&&l.prototype,{constructor:{value:o,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(o,l):o.__proto__=l)}var u=function(o){f(l,o);function l(){return c(this,l),y(this,(l.__proto__||Object.getPrototypeOf(l)).apply(this,arguments))}return l}(s.default);u.blotName="blockquote",u.tagName="blockquote",r.default=u},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0});var a=function(){function l(h,v){for(var E=0;E<v.length;E++){var p=v[E];p.enumerable=p.enumerable||!1,p.configurable=!0,"value"in p&&(p.writable=!0),Object.defineProperty(h,p.key,p)}}return function(h,v,E){return v&&l(h.prototype,v),E&&l(h,E),h}}(),s=i(4),d=c(s);function c(l){return l&&l.__esModule?l:{default:l}}function y(l,h){if(!(l instanceof h))throw new TypeError("Cannot call a class as a function")}function f(l,h){if(!l)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return h&&(typeof h=="object"||typeof h=="function")?h:l}function u(l,h){if(typeof h!="function"&&h!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof h);l.prototype=Object.create(h&&h.prototype,{constructor:{value:l,enumerable:!1,writable:!0,configurable:!0}}),h&&(Object.setPrototypeOf?Object.setPrototypeOf(l,h):l.__proto__=h)}var o=function(l){u(h,l);function h(){return y(this,h),f(this,(h.__proto__||Object.getPrototypeOf(h)).apply(this,arguments))}return a(h,null,[{key:"formats",value:function(E){return this.tagName.indexOf(E.tagName)+1}}]),h}(d.default);o.blotName="header",o.tagName=["H1","H2","H3","H4","H5","H6"],r.default=o},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0}),r.default=r.ListItem=void 0;var a=function(){function g(b,A){for(var S=0;S<A.length;S++){var I=A[S];I.enumerable=I.enumerable||!1,I.configurable=!0,"value"in I&&(I.writable=!0),Object.defineProperty(b,I.key,I)}}return function(b,A,S){return A&&g(b.prototype,A),S&&g(b,S),b}}(),s=function g(b,A,S){b===null&&(b=Function.prototype);var I=Object.getOwnPropertyDescriptor(b,A);if(I===void 0){var N=Object.getPrototypeOf(b);return N===null?void 0:g(N,A,S)}else{if("value"in I)return I.value;var w=I.get;return w===void 0?void 0:w.call(S)}},d=i(0),c=l(d),y=i(4),f=l(y),u=i(25),o=l(u);function l(g){return g&&g.__esModule?g:{default:g}}function h(g,b,A){return b in g?Object.defineProperty(g,b,{value:A,enumerable:!0,configurable:!0,writable:!0}):g[b]=A,g}function v(g,b){if(!(g instanceof b))throw new TypeError("Cannot call a class as a function")}function E(g,b){if(!g)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return b&&(typeof b=="object"||typeof b=="function")?b:g}function p(g,b){if(typeof b!="function"&&b!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof b);g.prototype=Object.create(b&&b.prototype,{constructor:{value:g,enumerable:!1,writable:!0,configurable:!0}}),b&&(Object.setPrototypeOf?Object.setPrototypeOf(g,b):g.__proto__=b)}var m=function(g){p(b,g);function b(){return v(this,b),E(this,(b.__proto__||Object.getPrototypeOf(b)).apply(this,arguments))}return a(b,[{key:"format",value:function(S,I){S===_.blotName&&!I?this.replaceWith(c.default.create(this.statics.scope)):s(b.prototype.__proto__||Object.getPrototypeOf(b.prototype),"format",this).call(this,S,I)}},{key:"remove",value:function(){this.prev==null&&this.next==null?this.parent.remove():s(b.prototype.__proto__||Object.getPrototypeOf(b.prototype),"remove",this).call(this)}},{key:"replaceWith",value:function(S,I){return this.parent.isolate(this.offset(this.parent),this.length()),S===this.parent.statics.blotName?(this.parent.replaceWith(S,I),this):(this.parent.unwrap(),s(b.prototype.__proto__||Object.getPrototypeOf(b.prototype),"replaceWith",this).call(this,S,I))}}],[{key:"formats",value:function(S){return S.tagName===this.tagName?void 0:s(b.__proto__||Object.getPrototypeOf(b),"formats",this).call(this,S)}}]),b}(f.default);m.blotName="list-item",m.tagName="LI";var _=function(g){p(b,g),a(b,null,[{key:"create",value:function(S){var I=S==="ordered"?"OL":"UL",N=s(b.__proto__||Object.getPrototypeOf(b),"create",this).call(this,I);return(S==="checked"||S==="unchecked")&&N.setAttribute("data-checked",S==="checked"),N}},{key:"formats",value:function(S){if(S.tagName==="OL")return"ordered";if(S.tagName==="UL")return S.hasAttribute("data-checked")?S.getAttribute("data-checked")==="true"?"checked":"unchecked":"bullet"}}]);function b(A){v(this,b);var S=E(this,(b.__proto__||Object.getPrototypeOf(b)).call(this,A)),I=function(w){if(w.target.parentNode===A){var T=S.statics.formats(A),O=c.default.find(w.target);T==="checked"?O.format("list","unchecked"):T==="unchecked"&&O.format("list","checked")}};return A.addEventListener("touchstart",I),A.addEventListener("mousedown",I),S}return a(b,[{key:"format",value:function(S,I){this.children.length>0&&this.children.tail.format(S,I)}},{key:"formats",value:function(){return h({},this.statics.blotName,this.statics.formats(this.domNode))}},{key:"insertBefore",value:function(S,I){if(S instanceof m)s(b.prototype.__proto__||Object.getPrototypeOf(b.prototype),"insertBefore",this).call(this,S,I);else{var N=I==null?this.length():I.offset(this),w=this.split(N);w.parent.insertBefore(S,w)}}},{key:"optimize",value:function(S){s(b.prototype.__proto__||Object.getPrototypeOf(b.prototype),"optimize",this).call(this,S);var I=this.next;I!=null&&I.prev===this&&I.statics.blotName===this.statics.blotName&&I.domNode.tagName===this.domNode.tagName&&I.domNode.getAttribute("data-checked")===this.domNode.getAttribute("data-checked")&&(I.moveChildren(this),I.remove())}},{key:"replace",value:function(S){if(S.statics.blotName!==this.statics.blotName){var I=c.default.create(this.statics.defaultChild);S.moveChildren(I),this.appendChild(I)}s(b.prototype.__proto__||Object.getPrototypeOf(b.prototype),"replace",this).call(this,S)}}]),b}(o.default);_.blotName="list",_.scope=c.default.Scope.BLOCK_BLOT,_.tagName=["OL","UL"],_.defaultChild="list-item",_.allowedChildren=[m],r.ListItem=m,r.default=_},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0});var a=i(56),s=d(a);function d(o){return o&&o.__esModule?o:{default:o}}function c(o,l){if(!(o instanceof l))throw new TypeError("Cannot call a class as a function")}function y(o,l){if(!o)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l&&(typeof l=="object"||typeof l=="function")?l:o}function f(o,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof l);o.prototype=Object.create(l&&l.prototype,{constructor:{value:o,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(o,l):o.__proto__=l)}var u=function(o){f(l,o);function l(){return c(this,l),y(this,(l.__proto__||Object.getPrototypeOf(l)).apply(this,arguments))}return l}(s.default);u.blotName="italic",u.tagName=["EM","I"],r.default=u},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0});var a=function(){function h(v,E){for(var p=0;p<E.length;p++){var m=E[p];m.enumerable=m.enumerable||!1,m.configurable=!0,"value"in m&&(m.writable=!0),Object.defineProperty(v,m.key,m)}}return function(v,E,p){return E&&h(v.prototype,E),p&&h(v,p),v}}(),s=function h(v,E,p){v===null&&(v=Function.prototype);var m=Object.getOwnPropertyDescriptor(v,E);if(m===void 0){var _=Object.getPrototypeOf(v);return _===null?void 0:h(_,E,p)}else{if("value"in m)return m.value;var g=m.get;return g===void 0?void 0:g.call(p)}},d=i(6),c=y(d);function y(h){return h&&h.__esModule?h:{default:h}}function f(h,v){if(!(h instanceof v))throw new TypeError("Cannot call a class as a function")}function u(h,v){if(!h)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return v&&(typeof v=="object"||typeof v=="function")?v:h}function o(h,v){if(typeof v!="function"&&v!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof v);h.prototype=Object.create(v&&v.prototype,{constructor:{value:h,enumerable:!1,writable:!0,configurable:!0}}),v&&(Object.setPrototypeOf?Object.setPrototypeOf(h,v):h.__proto__=v)}var l=function(h){o(v,h);function v(){return f(this,v),u(this,(v.__proto__||Object.getPrototypeOf(v)).apply(this,arguments))}return a(v,null,[{key:"create",value:function(p){return p==="super"?document.createElement("sup"):p==="sub"?document.createElement("sub"):s(v.__proto__||Object.getPrototypeOf(v),"create",this).call(this,p)}},{key:"formats",value:function(p){if(p.tagName==="SUB")return"sub";if(p.tagName==="SUP")return"super"}}]),v}(c.default);l.blotName="script",l.tagName=["SUB","SUP"],r.default=l},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0});var a=i(6),s=d(a);function d(o){return o&&o.__esModule?o:{default:o}}function c(o,l){if(!(o instanceof l))throw new TypeError("Cannot call a class as a function")}function y(o,l){if(!o)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l&&(typeof l=="object"||typeof l=="function")?l:o}function f(o,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof l);o.prototype=Object.create(l&&l.prototype,{constructor:{value:o,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(o,l):o.__proto__=l)}var u=function(o){f(l,o);function l(){return c(this,l),y(this,(l.__proto__||Object.getPrototypeOf(l)).apply(this,arguments))}return l}(s.default);u.blotName="strike",u.tagName="S",r.default=u},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0});var a=i(6),s=d(a);function d(o){return o&&o.__esModule?o:{default:o}}function c(o,l){if(!(o instanceof l))throw new TypeError("Cannot call a class as a function")}function y(o,l){if(!o)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l&&(typeof l=="object"||typeof l=="function")?l:o}function f(o,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof l);o.prototype=Object.create(l&&l.prototype,{constructor:{value:o,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(o,l):o.__proto__=l)}var u=function(o){f(l,o);function l(){return c(this,l),y(this,(l.__proto__||Object.getPrototypeOf(l)).apply(this,arguments))}return l}(s.default);u.blotName="underline",u.tagName="U",r.default=u},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0});var a=function(){function E(p,m){for(var _=0;_<m.length;_++){var g=m[_];g.enumerable=g.enumerable||!1,g.configurable=!0,"value"in g&&(g.writable=!0),Object.defineProperty(p,g.key,g)}}return function(p,m,_){return m&&E(p.prototype,m),_&&E(p,_),p}}(),s=function E(p,m,_){p===null&&(p=Function.prototype);var g=Object.getOwnPropertyDescriptor(p,m);if(g===void 0){var b=Object.getPrototypeOf(p);return b===null?void 0:E(b,m,_)}else{if("value"in g)return g.value;var A=g.get;return A===void 0?void 0:A.call(_)}},d=i(0),c=f(d),y=i(27);function f(E){return E&&E.__esModule?E:{default:E}}function u(E,p){if(!(E instanceof p))throw new TypeError("Cannot call a class as a function")}function o(E,p){if(!E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return p&&(typeof p=="object"||typeof p=="function")?p:E}function l(E,p){if(typeof p!="function"&&p!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof p);E.prototype=Object.create(p&&p.prototype,{constructor:{value:E,enumerable:!1,writable:!0,configurable:!0}}),p&&(Object.setPrototypeOf?Object.setPrototypeOf(E,p):E.__proto__=p)}var h=["alt","height","width"],v=function(E){l(p,E);function p(){return u(this,p),o(this,(p.__proto__||Object.getPrototypeOf(p)).apply(this,arguments))}return a(p,[{key:"format",value:function(_,g){h.indexOf(_)>-1?g?this.domNode.setAttribute(_,g):this.domNode.removeAttribute(_):s(p.prototype.__proto__||Object.getPrototypeOf(p.prototype),"format",this).call(this,_,g)}}],[{key:"create",value:function(_){var g=s(p.__proto__||Object.getPrototypeOf(p),"create",this).call(this,_);return typeof _=="string"&&g.setAttribute("src",this.sanitize(_)),g}},{key:"formats",value:function(_){return h.reduce(function(g,b){return _.hasAttribute(b)&&(g[b]=_.getAttribute(b)),g},{})}},{key:"match",value:function(_){return/\.(jpe?g|gif|png)$/.test(_)||/^data:image\/.+;base64/.test(_)}},{key:"sanitize",value:function(_){return(0,y.sanitize)(_,["http","https","data"])?_:"//:0"}},{key:"value",value:function(_){return _.getAttribute("src")}}]),p}(c.default.Embed);v.blotName="image",v.tagName="IMG",r.default=v},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0});var a=function(){function E(p,m){for(var _=0;_<m.length;_++){var g=m[_];g.enumerable=g.enumerable||!1,g.configurable=!0,"value"in g&&(g.writable=!0),Object.defineProperty(p,g.key,g)}}return function(p,m,_){return m&&E(p.prototype,m),_&&E(p,_),p}}(),s=function E(p,m,_){p===null&&(p=Function.prototype);var g=Object.getOwnPropertyDescriptor(p,m);if(g===void 0){var b=Object.getPrototypeOf(p);return b===null?void 0:E(b,m,_)}else{if("value"in g)return g.value;var A=g.get;return A===void 0?void 0:A.call(_)}},d=i(4),c=i(27),y=f(c);function f(E){return E&&E.__esModule?E:{default:E}}function u(E,p){if(!(E instanceof p))throw new TypeError("Cannot call a class as a function")}function o(E,p){if(!E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return p&&(typeof p=="object"||typeof p=="function")?p:E}function l(E,p){if(typeof p!="function"&&p!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof p);E.prototype=Object.create(p&&p.prototype,{constructor:{value:E,enumerable:!1,writable:!0,configurable:!0}}),p&&(Object.setPrototypeOf?Object.setPrototypeOf(E,p):E.__proto__=p)}var h=["height","width"],v=function(E){l(p,E);function p(){return u(this,p),o(this,(p.__proto__||Object.getPrototypeOf(p)).apply(this,arguments))}return a(p,[{key:"format",value:function(_,g){h.indexOf(_)>-1?g?this.domNode.setAttribute(_,g):this.domNode.removeAttribute(_):s(p.prototype.__proto__||Object.getPrototypeOf(p.prototype),"format",this).call(this,_,g)}}],[{key:"create",value:function(_){var g=s(p.__proto__||Object.getPrototypeOf(p),"create",this).call(this,_);return g.setAttribute("frameborder","0"),g.setAttribute("allowfullscreen",!0),g.setAttribute("src",this.sanitize(_)),g}},{key:"formats",value:function(_){return h.reduce(function(g,b){return _.hasAttribute(b)&&(g[b]=_.getAttribute(b)),g},{})}},{key:"sanitize",value:function(_){return y.default.sanitize(_)}},{key:"value",value:function(_){return _.getAttribute("src")}}]),p}(d.BlockEmbed);v.blotName="video",v.className="ql-video",v.tagName="IFRAME",r.default=v},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0}),r.default=r.FormulaBlot=void 0;var a=function(){function _(g,b){for(var A=0;A<b.length;A++){var S=b[A];S.enumerable=S.enumerable||!1,S.configurable=!0,"value"in S&&(S.writable=!0),Object.defineProperty(g,S.key,S)}}return function(g,b,A){return b&&_(g.prototype,b),A&&_(g,A),g}}(),s=function _(g,b,A){g===null&&(g=Function.prototype);var S=Object.getOwnPropertyDescriptor(g,b);if(S===void 0){var I=Object.getPrototypeOf(g);return I===null?void 0:_(I,b,A)}else{if("value"in S)return S.value;var N=S.get;return N===void 0?void 0:N.call(A)}},d=i(35),c=l(d),y=i(5),f=l(y),u=i(9),o=l(u);function l(_){return _&&_.__esModule?_:{default:_}}function h(_,g){if(!(_ instanceof g))throw new TypeError("Cannot call a class as a function")}function v(_,g){if(!_)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return g&&(typeof g=="object"||typeof g=="function")?g:_}function E(_,g){if(typeof g!="function"&&g!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof g);_.prototype=Object.create(g&&g.prototype,{constructor:{value:_,enumerable:!1,writable:!0,configurable:!0}}),g&&(Object.setPrototypeOf?Object.setPrototypeOf(_,g):_.__proto__=g)}var p=function(_){E(g,_);function g(){return h(this,g),v(this,(g.__proto__||Object.getPrototypeOf(g)).apply(this,arguments))}return a(g,null,[{key:"create",value:function(A){var S=s(g.__proto__||Object.getPrototypeOf(g),"create",this).call(this,A);return typeof A=="string"&&(window.katex.render(A,S,{throwOnError:!1,errorColor:"#f00"}),S.setAttribute("data-value",A)),S}},{key:"value",value:function(A){return A.getAttribute("data-value")}}]),g}(c.default);p.blotName="formula",p.className="ql-formula",p.tagName="SPAN";var m=function(_){E(g,_),a(g,null,[{key:"register",value:function(){f.default.register(p,!0)}}]);function g(){h(this,g);var b=v(this,(g.__proto__||Object.getPrototypeOf(g)).call(this));if(window.katex==null)throw new Error("Formula module requires KaTeX.");return b}return g}(o.default);r.FormulaBlot=p,r.default=m},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0}),r.default=r.CodeToken=r.CodeBlock=void 0;var a=function(){function A(S,I){for(var N=0;N<I.length;N++){var w=I[N];w.enumerable=w.enumerable||!1,w.configurable=!0,"value"in w&&(w.writable=!0),Object.defineProperty(S,w.key,w)}}return function(S,I,N){return I&&A(S.prototype,I),N&&A(S,N),S}}(),s=function A(S,I,N){S===null&&(S=Function.prototype);var w=Object.getOwnPropertyDescriptor(S,I);if(w===void 0){var T=Object.getPrototypeOf(S);return T===null?void 0:A(T,I,N)}else{if("value"in w)return w.value;var O=w.get;return O===void 0?void 0:O.call(N)}},d=i(0),c=v(d),y=i(5),f=v(y),u=i(9),o=v(u),l=i(13),h=v(l);function v(A){return A&&A.__esModule?A:{default:A}}function E(A,S){if(!(A instanceof S))throw new TypeError("Cannot call a class as a function")}function p(A,S){if(!A)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return S&&(typeof S=="object"||typeof S=="function")?S:A}function m(A,S){if(typeof S!="function"&&S!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof S);A.prototype=Object.create(S&&S.prototype,{constructor:{value:A,enumerable:!1,writable:!0,configurable:!0}}),S&&(Object.setPrototypeOf?Object.setPrototypeOf(A,S):A.__proto__=S)}var _=function(A){m(S,A);function S(){return E(this,S),p(this,(S.__proto__||Object.getPrototypeOf(S)).apply(this,arguments))}return a(S,[{key:"replaceWith",value:function(N){this.domNode.textContent=this.domNode.textContent,this.attach(),s(S.prototype.__proto__||Object.getPrototypeOf(S.prototype),"replaceWith",this).call(this,N)}},{key:"highlight",value:function(N){var w=this.domNode.textContent;this.cachedText!==w&&((w.trim().length>0||this.cachedText==null)&&(this.domNode.innerHTML=N(w),this.domNode.normalize(),this.attach()),this.cachedText=w)}}]),S}(h.default);_.className="ql-syntax";var g=new c.default.Attributor.Class("token","hljs",{scope:c.default.Scope.INLINE}),b=function(A){m(S,A),a(S,null,[{key:"register",value:function(){f.default.register(g,!0),f.default.register(_,!0)}}]);function S(I,N){E(this,S);var w=p(this,(S.__proto__||Object.getPrototypeOf(S)).call(this,I,N));if(typeof w.options.highlight!="function")throw new Error("Syntax module requires highlight.js. Please include the library on the page before Quill.");var T=null;return w.quill.on(f.default.events.SCROLL_OPTIMIZE,function(){clearTimeout(T),T=setTimeout(function(){w.highlight(),T=null},w.options.interval)}),w.highlight(),w}return a(S,[{key:"highlight",value:function(){var N=this;if(!this.quill.selection.composing){this.quill.update(f.default.sources.USER);var w=this.quill.getSelection();this.quill.scroll.descendants(_).forEach(function(T){T.highlight(N.options.highlight)}),this.quill.update(f.default.sources.SILENT),w!=null&&this.quill.setSelection(w,f.default.sources.SILENT)}}}]),S}(o.default);b.DEFAULTS={highlight:function(){return window.hljs==null?null:function(A){var S=window.hljs.highlightAuto(A);return S.value}}(),interval:1e3},r.CodeBlock=_,r.CodeToken=g,r.default=b},function(n,r){n.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=3 x2=13 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=9 y1=4 y2=4></line> </svg>'},function(n,r){n.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=14 x2=4 y1=14 y2=14></line> <line class=ql-stroke x1=12 x2=6 y1=4 y2=4></line> </svg>'},function(n,r){n.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=15 x2=5 y1=14 y2=14></line> <line class=ql-stroke x1=15 x2=9 y1=4 y2=4></line> </svg>'},function(n,r){n.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=15 x2=3 y1=14 y2=14></line> <line class=ql-stroke x1=15 x2=3 y1=4 y2=4></line> </svg>'},function(n,r){n.exports='<svg viewbox="0 0 18 18"> <g class="ql-fill ql-color-label"> <polygon points="6 6.868 6 6 5 6 5 7 5.942 7 6 6.868"></polygon> <rect height=1 width=1 x=4 y=4></rect> <polygon points="6.817 5 6 5 6 6 6.38 6 6.817 5"></polygon> <rect height=1 width=1 x=2 y=6></rect> <rect height=1 width=1 x=3 y=5></rect> <rect height=1 width=1 x=4 y=7></rect> <polygon points="4 11.439 4 11 3 11 3 12 3.755 12 4 11.439"></polygon> <rect height=1 width=1 x=2 y=12></rect> <rect height=1 width=1 x=2 y=9></rect> <rect height=1 width=1 x=2 y=15></rect> <polygon points="4.63 10 4 10 4 11 4.192 11 4.63 10"></polygon> <rect height=1 width=1 x=3 y=8></rect> <path d=M10.832,4.2L11,4.582V4H10.708A1.948,1.948,0,0,1,10.832,4.2Z></path> <path d=M7,4.582L7.168,4.2A1.929,1.929,0,0,1,7.292,4H7V4.582Z></path> <path d=M8,13H7.683l-0.351.8a1.933,1.933,0,0,1-.124.2H8V13Z></path> <rect height=1 width=1 x=12 y=2></rect> <rect height=1 width=1 x=11 y=3></rect> <path d=M9,3H8V3.282A1.985,1.985,0,0,1,9,3Z></path> <rect height=1 width=1 x=2 y=3></rect> <rect height=1 width=1 x=6 y=2></rect> <rect height=1 width=1 x=3 y=2></rect> <rect height=1 width=1 x=5 y=3></rect> <rect height=1 width=1 x=9 y=2></rect> <rect height=1 width=1 x=15 y=14></rect> <polygon points="13.447 10.174 13.469 10.225 13.472 10.232 13.808 11 14 11 14 10 13.37 10 13.447 10.174"></polygon> <rect height=1 width=1 x=13 y=7></rect> <rect height=1 width=1 x=15 y=5></rect> <rect height=1 width=1 x=14 y=6></rect> <rect height=1 width=1 x=15 y=8></rect> <rect height=1 width=1 x=14 y=9></rect> <path d=M3.775,14H3v1H4V14.314A1.97,1.97,0,0,1,3.775,14Z></path> <rect height=1 width=1 x=14 y=3></rect> <polygon points="12 6.868 12 6 11.62 6 12 6.868"></polygon> <rect height=1 width=1 x=15 y=2></rect> <rect height=1 width=1 x=12 y=5></rect> <rect height=1 width=1 x=13 y=4></rect> <polygon points="12.933 9 13 9 13 8 12.495 8 12.933 9"></polygon> <rect height=1 width=1 x=9 y=14></rect> <rect height=1 width=1 x=8 y=15></rect> <path d=M6,14.926V15H7V14.316A1.993,1.993,0,0,1,6,14.926Z></path> <rect height=1 width=1 x=5 y=15></rect> <path d=M10.668,13.8L10.317,13H10v1h0.792A1.947,1.947,0,0,1,10.668,13.8Z></path> <rect height=1 width=1 x=11 y=15></rect> <path d=M14.332,12.2a1.99,1.99,0,0,1,.166.8H15V12H14.245Z></path> <rect height=1 width=1 x=14 y=15></rect> <rect height=1 width=1 x=15 y=11></rect> </g> <polyline class=ql-stroke points="5.5 13 9 5 12.5 13"></polyline> <line class=ql-stroke x1=11.63 x2=6.38 y1=11 y2=11></line> </svg>'},function(n,r){n.exports='<svg viewbox="0 0 18 18"> <rect class="ql-fill ql-stroke" height=3 width=3 x=4 y=5></rect> <rect class="ql-fill ql-stroke" height=3 width=3 x=11 y=5></rect> <path class="ql-even ql-fill ql-stroke" d=M7,8c0,4.031-3,5-3,5></path> <path class="ql-even ql-fill ql-stroke" d=M14,8c0,4.031-3,5-3,5></path> </svg>'},function(n,r){n.exports='<svg viewbox="0 0 18 18"> <path class=ql-stroke d=M5,4H9.5A2.5,2.5,0,0,1,12,6.5v0A2.5,2.5,0,0,1,9.5,9H5A0,0,0,0,1,5,9V4A0,0,0,0,1,5,4Z></path> <path class=ql-stroke d=M5,9h5.5A2.5,2.5,0,0,1,13,11.5v0A2.5,2.5,0,0,1,10.5,14H5a0,0,0,0,1,0,0V9A0,0,0,0,1,5,9Z></path> </svg>'},function(n,r){n.exports='<svg class="" viewbox="0 0 18 18"> <line class=ql-stroke x1=5 x2=13 y1=3 y2=3></line> <line class=ql-stroke x1=6 x2=9.35 y1=12 y2=3></line> <line class=ql-stroke x1=11 x2=15 y1=11 y2=15></line> <line class=ql-stroke x1=15 x2=11 y1=11 y2=15></line> <rect class=ql-fill height=1 rx=0.5 ry=0.5 width=7 x=2 y=14></rect> </svg>'},function(n,r){n.exports='<svg viewbox="0 0 18 18"> <line class="ql-color-label ql-stroke ql-transparent" x1=3 x2=15 y1=15 y2=15></line> <polyline class=ql-stroke points="5.5 11 9 3 12.5 11"></polyline> <line class=ql-stroke x1=11.63 x2=6.38 y1=9 y2=9></line> </svg>'},function(n,r){n.exports='<svg viewbox="0 0 18 18"> <polygon class="ql-stroke ql-fill" points="3 11 5 9 3 7 3 11"></polygon> <line class="ql-stroke ql-fill" x1=15 x2=11 y1=4 y2=4></line> <path class=ql-fill d=M11,3a3,3,0,0,0,0,6h1V3H11Z></path> <rect class=ql-fill height=11 width=1 x=11 y=4></rect> <rect class=ql-fill height=11 width=1 x=13 y=4></rect> </svg>'},function(n,r){n.exports='<svg viewbox="0 0 18 18"> <polygon class="ql-stroke ql-fill" points="15 12 13 10 15 8 15 12"></polygon> <line class="ql-stroke ql-fill" x1=9 x2=5 y1=4 y2=4></line> <path class=ql-fill d=M5,3A3,3,0,0,0,5,9H6V3H5Z></path> <rect class=ql-fill height=11 width=1 x=5 y=4></rect> <rect class=ql-fill height=11 width=1 x=7 y=4></rect> </svg>'},function(n,r){n.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M14,16H4a1,1,0,0,1,0-2H14A1,1,0,0,1,14,16Z /> <path class=ql-fill d=M14,4H4A1,1,0,0,1,4,2H14A1,1,0,0,1,14,4Z /> <rect class=ql-fill x=3 y=6 width=12 height=6 rx=1 ry=1 /> </svg>'},function(n,r){n.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M13,16H5a1,1,0,0,1,0-2h8A1,1,0,0,1,13,16Z /> <path class=ql-fill d=M13,4H5A1,1,0,0,1,5,2h8A1,1,0,0,1,13,4Z /> <rect class=ql-fill x=2 y=6 width=14 height=6 rx=1 ry=1 /> </svg>'},function(n,r){n.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15,8H13a1,1,0,0,1,0-2h2A1,1,0,0,1,15,8Z /> <path class=ql-fill d=M15,12H13a1,1,0,0,1,0-2h2A1,1,0,0,1,15,12Z /> <path class=ql-fill d=M15,16H5a1,1,0,0,1,0-2H15A1,1,0,0,1,15,16Z /> <path class=ql-fill d=M15,4H5A1,1,0,0,1,5,2H15A1,1,0,0,1,15,4Z /> <rect class=ql-fill x=2 y=6 width=8 height=6 rx=1 ry=1 /> </svg>'},function(n,r){n.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M5,8H3A1,1,0,0,1,3,6H5A1,1,0,0,1,5,8Z /> <path class=ql-fill d=M5,12H3a1,1,0,0,1,0-2H5A1,1,0,0,1,5,12Z /> <path class=ql-fill d=M13,16H3a1,1,0,0,1,0-2H13A1,1,0,0,1,13,16Z /> <path class=ql-fill d=M13,4H3A1,1,0,0,1,3,2H13A1,1,0,0,1,13,4Z /> <rect class=ql-fill x=8 y=6 width=8 height=6 rx=1 ry=1 transform="translate(24 18) rotate(-180)"/> </svg>'},function(n,r){n.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M11.759,2.482a2.561,2.561,0,0,0-3.53.607A7.656,7.656,0,0,0,6.8,6.2C6.109,9.188,5.275,14.677,4.15,14.927a1.545,1.545,0,0,0-1.3-.933A0.922,0.922,0,0,0,2,15.036S1.954,16,4.119,16s3.091-2.691,3.7-5.553c0.177-.826.36-1.726,0.554-2.6L8.775,6.2c0.381-1.421.807-2.521,1.306-2.676a1.014,1.014,0,0,0,1.02.56A0.966,0.966,0,0,0,11.759,2.482Z></path> <rect class=ql-fill height=1.6 rx=0.8 ry=0.8 width=5 x=5.15 y=6.2></rect> <path class=ql-fill d=M13.663,12.027a1.662,1.662,0,0,1,.266-0.276q0.193,0.069.456,0.138a2.1,2.1,0,0,0,.535.069,1.075,1.075,0,0,0,.767-0.3,1.044,1.044,0,0,0,.314-0.8,0.84,0.84,0,0,0-.238-0.619,0.8,0.8,0,0,0-.594-0.239,1.154,1.154,0,0,0-.781.3,4.607,4.607,0,0,0-.781,1q-0.091.15-.218,0.346l-0.246.38c-0.068-.288-0.137-0.582-0.212-0.885-0.459-1.847-2.494-.984-2.941-0.8-0.482.2-.353,0.647-0.094,0.529a0.869,0.869,0,0,1,1.281.585c0.217,0.751.377,1.436,0.527,2.038a5.688,5.688,0,0,1-.362.467,2.69,2.69,0,0,1-.264.271q-0.221-.08-0.471-0.147a2.029,2.029,0,0,0-.522-0.066,1.079,1.079,0,0,0-.768.3A1.058,1.058,0,0,0,9,15.131a0.82,0.82,0,0,0,.832.852,1.134,1.134,0,0,0,.787-0.3,5.11,5.11,0,0,0,.776-0.993q0.141-.219.215-0.34c0.046-.076.122-0.194,0.223-0.346a2.786,2.786,0,0,0,.918,1.726,2.582,2.582,0,0,0,2.376-.185c0.317-.181.212-0.565,0-0.494A0.807,0.807,0,0,1,14.176,15a5.159,5.159,0,0,1-.913-2.446l0,0Q13.487,12.24,13.663,12.027Z></path> </svg>'},function(n,r){n.exports='<svg viewBox="0 0 18 18"> <path class=ql-fill d=M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm6.06787,9.209H14.98975V7.59863a.54085.54085,0,0,0-.605-.60547h-.62744a1.01119,1.01119,0,0,0-.748.29688L11.645,8.56641a.5435.5435,0,0,0-.022.8584l.28613.30762a.53861.53861,0,0,0,.84717.0332l.09912-.08789a1.2137,1.2137,0,0,0,.2417-.35254h.02246s-.01123.30859-.01123.60547V13.209H12.041a.54085.54085,0,0,0-.605.60547v.43945a.54085.54085,0,0,0,.605.60547h4.02686a.54085.54085,0,0,0,.605-.60547v-.43945A.54085.54085,0,0,0,16.06787,13.209Z /> </svg>'},function(n,r){n.exports='<svg viewBox="0 0 18 18"> <path class=ql-fill d=M16.73975,13.81445v.43945a.54085.54085,0,0,1-.605.60547H11.855a.58392.58392,0,0,1-.64893-.60547V14.0127c0-2.90527,3.39941-3.42187,3.39941-4.55469a.77675.77675,0,0,0-.84717-.78125,1.17684,1.17684,0,0,0-.83594.38477c-.2749.26367-.561.374-.85791.13184l-.4292-.34082c-.30811-.24219-.38525-.51758-.1543-.81445a2.97155,2.97155,0,0,1,2.45361-1.17676,2.45393,2.45393,0,0,1,2.68408,2.40918c0,2.45312-3.1792,2.92676-3.27832,3.93848h2.79443A.54085.54085,0,0,1,16.73975,13.81445ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z /> </svg>'},function(n,r){n.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=13 y1=4 y2=4></line> <line class=ql-stroke x1=5 x2=11 y1=14 y2=14></line> <line class=ql-stroke x1=8 x2=10 y1=14 y2=4></line> </svg>'},function(n,r){n.exports='<svg viewbox="0 0 18 18"> <rect class=ql-stroke height=10 width=12 x=3 y=4></rect> <circle class=ql-fill cx=6 cy=7 r=1></circle> <polyline class="ql-even ql-fill" points="5 12 5 11 7 9 8 10 11 7 13 9 13 12 5 12"></polyline> </svg>'},function(n,r){n.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class="ql-fill ql-stroke" points="3 7 3 11 5 9 3 7"></polyline> </svg>'},function(n,r){n.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class=ql-stroke points="5 7 5 11 3 9 5 7"></polyline> </svg>'},function(n,r){n.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=11 y1=7 y2=11></line> <path class="ql-even ql-stroke" d=M8.9,4.577a3.476,3.476,0,0,1,.36,4.679A3.476,3.476,0,0,1,4.577,8.9C3.185,7.5,2.035,6.4,4.217,4.217S7.5,3.185,8.9,4.577Z></path> <path class="ql-even ql-stroke" d=M13.423,9.1a3.476,3.476,0,0,0-4.679-.36,3.476,3.476,0,0,0,.36,4.679c1.392,1.392,2.5,2.542,4.679.36S14.815,10.5,13.423,9.1Z></path> </svg>'},function(n,r){n.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=7 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=7 x2=15 y1=14 y2=14></line> <line class="ql-stroke ql-thin" x1=2.5 x2=4.5 y1=5.5 y2=5.5></line> <path class=ql-fill d=M3.5,6A0.5,0.5,0,0,1,3,5.5V3.085l-0.276.138A0.5,0.5,0,0,1,2.053,3c-0.124-.247-0.023-0.324.224-0.447l1-.5A0.5,0.5,0,0,1,4,2.5v3A0.5,0.5,0,0,1,3.5,6Z></path> <path class="ql-stroke ql-thin" d=M4.5,10.5h-2c0-.234,1.85-1.076,1.85-2.234A0.959,0.959,0,0,0,2.5,8.156></path> <path class="ql-stroke ql-thin" d=M2.5,14.846a0.959,0.959,0,0,0,1.85-.109A0.7,0.7,0,0,0,3.75,14a0.688,0.688,0,0,0,.6-0.736,0.959,0.959,0,0,0-1.85-.109></path> </svg>'},function(n,r){n.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=6 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=6 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=6 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=3 y1=4 y2=4></line> <line class=ql-stroke x1=3 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=3 x2=3 y1=14 y2=14></line> </svg>'},function(n,r){n.exports='<svg class="" viewbox="0 0 18 18"> <line class=ql-stroke x1=9 x2=15 y1=4 y2=4></line> <polyline class=ql-stroke points="3 4 4 5 6 3"></polyline> <line class=ql-stroke x1=9 x2=15 y1=14 y2=14></line> <polyline class=ql-stroke points="3 14 4 15 6 13"></polyline> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class=ql-stroke points="3 9 4 10 6 8"></polyline> </svg>'},function(n,r){n.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15.5,15H13.861a3.858,3.858,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.921,1.921,0,0,0,12.021,11.7a0.50013,0.50013,0,1,0,.957.291h0a0.914,0.914,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.076-1.16971,1.86982-1.93971,2.43082A1.45639,1.45639,0,0,0,12,15.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,15Z /> <path class=ql-fill d=M9.65,5.241a1,1,0,0,0-1.409.108L6,7.964,3.759,5.349A1,1,0,0,0,2.192,6.59178Q2.21541,6.6213,2.241,6.649L4.684,9.5,2.241,12.35A1,1,0,0,0,3.71,13.70722q0.02557-.02768.049-0.05722L6,11.036,8.241,13.65a1,1,0,1,0,1.567-1.24277Q9.78459,12.3777,9.759,12.35L7.316,9.5,9.759,6.651A1,1,0,0,0,9.65,5.241Z /> </svg>'},function(n,r){n.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15.5,7H13.861a4.015,4.015,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.922,1.922,0,0,0,12.021,3.7a0.5,0.5,0,1,0,.957.291,0.917,0.917,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.077-1.164,1.925-1.934,2.486A1.423,1.423,0,0,0,12,7.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,7Z /> <path class=ql-fill d=M9.651,5.241a1,1,0,0,0-1.41.108L6,7.964,3.759,5.349a1,1,0,1,0-1.519,1.3L4.683,9.5,2.241,12.35a1,1,0,1,0,1.519,1.3L6,11.036,8.241,13.65a1,1,0,0,0,1.519-1.3L7.317,9.5,9.759,6.651A1,1,0,0,0,9.651,5.241Z /> </svg>'},function(n,r){n.exports='<svg viewbox="0 0 18 18"> <line class="ql-stroke ql-thin" x1=15.5 x2=2.5 y1=8.5 y2=9.5></line> <path class=ql-fill d=M9.007,8C6.542,7.791,6,7.519,6,6.5,6,5.792,7.283,5,9,5c1.571,0,2.765.679,2.969,1.309a1,1,0,0,0,1.9-.617C13.356,4.106,11.354,3,9,3,6.2,3,4,4.538,4,6.5a3.2,3.2,0,0,0,.5,1.843Z></path> <path class=ql-fill d=M8.984,10C11.457,10.208,12,10.479,12,11.5c0,0.708-1.283,1.5-3,1.5-1.571,0-2.765-.679-2.969-1.309a1,1,0,1,0-1.9.617C4.644,13.894,6.646,15,9,15c2.8,0,5-1.538,5-3.5a3.2,3.2,0,0,0-.5-1.843Z></path> </svg>'},function(n,r){n.exports='<svg viewbox="0 0 18 18"> <path class=ql-stroke d=M5,3V9a4.012,4.012,0,0,0,4,4H9a4.012,4.012,0,0,0,4-4V3></path> <rect class=ql-fill height=1 rx=0.5 ry=0.5 width=12 x=3 y=15></rect> </svg>'},function(n,r){n.exports='<svg viewbox="0 0 18 18"> <rect class=ql-stroke height=12 width=12 x=3 y=3></rect> <rect class=ql-fill height=12 width=1 x=5 y=3></rect> <rect class=ql-fill height=12 width=1 x=12 y=3></rect> <rect class=ql-fill height=2 width=8 x=5 y=8></rect> <rect class=ql-fill height=1 width=3 x=3 y=5></rect> <rect class=ql-fill height=1 width=3 x=3 y=7></rect> <rect class=ql-fill height=1 width=3 x=3 y=10></rect> <rect class=ql-fill height=1 width=3 x=3 y=12></rect> <rect class=ql-fill height=1 width=3 x=12 y=5></rect> <rect class=ql-fill height=1 width=3 x=12 y=7></rect> <rect class=ql-fill height=1 width=3 x=12 y=10></rect> <rect class=ql-fill height=1 width=3 x=12 y=12></rect> </svg>'},function(n,r){n.exports='<svg viewbox="0 0 18 18"> <polygon class=ql-stroke points="7 11 9 13 11 11 7 11"></polygon> <polygon class=ql-stroke points="7 7 9 5 11 7 7 7"></polygon> </svg>'},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0}),r.default=r.BubbleTooltip=void 0;var a=function S(I,N,w){I===null&&(I=Function.prototype);var T=Object.getOwnPropertyDescriptor(I,N);if(T===void 0){var O=Object.getPrototypeOf(I);return O===null?void 0:S(O,N,w)}else{if("value"in T)return T.value;var L=T.get;return L===void 0?void 0:L.call(w)}},s=function(){function S(I,N){for(var w=0;w<N.length;w++){var T=N[w];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(I,T.key,T)}}return function(I,N,w){return N&&S(I.prototype,N),w&&S(I,w),I}}(),d=i(3),c=E(d),y=i(8),f=E(y),u=i(43),o=E(u),l=i(15),h=i(41),v=E(h);function E(S){return S&&S.__esModule?S:{default:S}}function p(S,I){if(!(S instanceof I))throw new TypeError("Cannot call a class as a function")}function m(S,I){if(!S)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return I&&(typeof I=="object"||typeof I=="function")?I:S}function _(S,I){if(typeof I!="function"&&I!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof I);S.prototype=Object.create(I&&I.prototype,{constructor:{value:S,enumerable:!1,writable:!0,configurable:!0}}),I&&(Object.setPrototypeOf?Object.setPrototypeOf(S,I):S.__proto__=I)}var g=[["bold","italic","link"],[{header:1},{header:2},"blockquote"]],b=function(S){_(I,S);function I(N,w){p(this,I),w.modules.toolbar!=null&&w.modules.toolbar.container==null&&(w.modules.toolbar.container=g);var T=m(this,(I.__proto__||Object.getPrototypeOf(I)).call(this,N,w));return T.quill.container.classList.add("ql-bubble"),T}return s(I,[{key:"extendToolbar",value:function(w){this.tooltip=new A(this.quill,this.options.bounds),this.tooltip.root.appendChild(w.container),this.buildButtons([].slice.call(w.container.querySelectorAll("button")),v.default),this.buildPickers([].slice.call(w.container.querySelectorAll("select")),v.default)}}]),I}(o.default);b.DEFAULTS=(0,c.default)(!0,{},o.default.DEFAULTS,{modules:{toolbar:{handlers:{link:function(I){I?this.quill.theme.tooltip.edit():this.quill.format("link",!1)}}}}});var A=function(S){_(I,S);function I(N,w){p(this,I);var T=m(this,(I.__proto__||Object.getPrototypeOf(I)).call(this,N,w));return T.quill.on(f.default.events.EDITOR_CHANGE,function(O,L,D,M){if(O===f.default.events.SELECTION_CHANGE)if(L!=null&&L.length>0&&M===f.default.sources.USER){T.show(),T.root.style.left="0px",T.root.style.width="",T.root.style.width=T.root.offsetWidth+"px";var x=T.quill.getLines(L.index,L.length);if(x.length===1)T.position(T.quill.getBounds(L));else{var B=x[x.length-1],V=T.quill.getIndex(B),F=Math.min(B.length()-1,L.index+L.length-V),k=T.quill.getBounds(new l.Range(V,F));T.position(k)}}else document.activeElement!==T.textbox&&T.quill.hasFocus()&&T.hide()}),T}return s(I,[{key:"listen",value:function(){var w=this;a(I.prototype.__proto__||Object.getPrototypeOf(I.prototype),"listen",this).call(this),this.root.querySelector(".ql-close").addEventListener("click",function(){w.root.classList.remove("ql-editing")}),this.quill.on(f.default.events.SCROLL_OPTIMIZE,function(){setTimeout(function(){if(!w.root.classList.contains("ql-hidden")){var T=w.quill.getSelection();T!=null&&w.position(w.quill.getBounds(T))}},1)})}},{key:"cancel",value:function(){this.show()}},{key:"position",value:function(w){var T=a(I.prototype.__proto__||Object.getPrototypeOf(I.prototype),"position",this).call(this,w),O=this.root.querySelector(".ql-tooltip-arrow");if(O.style.marginLeft="",T===0)return T;O.style.marginLeft=-1*T-O.offsetWidth/2+"px"}}]),I}(u.BaseTooltip);A.TEMPLATE=['<span class="ql-tooltip-arrow"></span>','<div class="ql-tooltip-editor">','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-close"></a>',"</div>"].join(""),r.BubbleTooltip=A,r.default=b},function(n,r,i){n.exports=i(63)}]).default})}(Ot)),Ot.exports}var Nn="&#8203;";function qt(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Ln(e){return function(t){if(Array.isArray(t))return qt(t)}(e)||function(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}(e)||function(t,n){if(t){if(typeof t=="string")return qt(t,n);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(r);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return qt(t,n)}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Ue={isEnabled:function(){var e;return(e=window.TAGIFY_DEBUG)===null||e===void 0||e},log:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r;this.isEnabled()&&(r=console).log.apply(r,["[Tagify]:"].concat(Ln(t)))},warn:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r;this.isEnabled()&&(r=console).warn.apply(r,["[Tagify]:"].concat(Ln(t)))}},ft=function(e,t,n,r){return e=""+e,t=""+t,r&&(e=e.trim(),t=t.trim()),n?e==t:e.toLowerCase()==t.toLowerCase()},Pn=function(e,t){return e&&Array.isArray(e)&&e.map(function(n){return Zt(n,t)})};function Zt(e,t){var n,r={};for(n in e)t.indexOf(n)<0&&(r[n]=e[n]);return r}function Ft(e){return new DOMParser().parseFromString(e.trim(),"text/html").body.firstElementChild}function Mn(e,t){for(t=t||"previous";e=e[t+"Sibling"];)if(e.nodeType==3)return e}function He(e){return typeof e=="string"?e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/`|'/g,"&#039;"):e}function Pe(e){var t=Object.prototype.toString.call(e).split(" ")[1].slice(0,-1);return e===Object(e)&&t!="Array"&&t!="Function"&&t!="RegExp"&&t!="HTMLUnknownElement"}function me(e,t,n){var r,i;function a(s,d){for(var c in d)if(d.hasOwnProperty(c)){if(Pe(d[c])){Pe(s[c])?a(s[c],d[c]):s[c]=Object.assign({},d[c]);continue}if(Array.isArray(d[c])){s[c]=Object.assign([],d[c]);continue}s[c]=d[c]}}return r=e,((i=Object)!=null&&typeof Symbol<"u"&&i[Symbol.hasInstance]?i[Symbol.hasInstance](r):r instanceof i)||(e={}),a(e,t),n&&a(e,n),e}function xn(){var e=[],t={},n=!0,r=!1,i=void 0;try{for(var a,s=arguments[Symbol.iterator]();!(n=(a=s.next()).done);n=!0){var d=a.value,c=!0,y=!1,f=void 0;try{for(var u,o=d[Symbol.iterator]();!(c=(u=o.next()).done);c=!0){var l=u.value;Pe(l)?t[l.value]||(e.push(l),t[l.value]=1):e.includes(l)||e.push(l)}}catch(h){y=!0,f=h}finally{try{c||o.return==null||o.return()}finally{if(y)throw f}}}}catch(h){r=!0,i=h}finally{try{n||s.return==null||s.return()}finally{if(r)throw i}}return e}function pt(e){return String.prototype.normalize?typeof e=="string"?e.normalize("NFD").replace(/[\u0300-\u036f]/g,""):void 0:e}var Cn=function(){return/(?=.*chrome)(?=.*android)/i.test(navigator.userAgent)};function kn(){return("10000000-1000-4000-8000"+-1e11).replace(/[018]/g,function(e){return(e^crypto.getRandomValues(new Uint8Array(1))[0]&15>>e/4).toString(16)})}function ze(e){var t;return Ar.call(this,e)&&(e==null||(t=e.classList)===null||t===void 0?void 0:t.contains(this.settings.classNames.tag))}function Bn(e){return Ar.call(this,e)&&(e==null?void 0:e.closest(this.settings.classNames.tagSelector))}function Ar(e){var t;return(e==null||(t=e.closest)===null||t===void 0?void 0:t.call(e,this.settings.classNames.namespaceSelector))===this.DOM.scope}function Rr(e,t){var n=window.getSelection();return t=t||n.getRangeAt(0),typeof e=="string"&&(e=document.createTextNode(e)),t&&(t.deleteContents(),t.insertNode(e)),e}function le(e,t,n){return e?(t&&(e.__tagifyTagData=n?t:me({},e.__tagifyTagData||{},t)),e.__tagifyTagData):(Ue.warn("tag element doesn't exist",{tagElm:e,data:t}),t)}function je(e){if(e&&e.parentNode){var t=e,n=window.getSelection(),r=n.getRangeAt(0);n.rangeCount&&(r.setStartAfter(t),r.collapse(!0),n.removeAllRanges(),n.addRange(r))}}function Dr(e,t){e.forEach(function(n){if(le(n.previousSibling)||!n.previousSibling){var r=document.createTextNode("​");n.before(r),t&&je(r)}})}var jt={delimiters:",",pattern:null,tagTextProp:"value",maxTags:1/0,callbacks:{},addTagOnBlur:!0,addTagOn:["blur","tab","enter"],onChangeAfterBlur:!0,duplicates:!1,whitelist:[],blacklist:[],enforceWhitelist:!1,userInput:!0,focusable:!0,focusInputOnRemove:!0,keepInvalidTags:!1,createInvalidTags:!0,mixTagsAllowedAfter:/,|\.|\:|\s/,mixTagsInterpolator:["[[","]]"],backspace:!0,skipInvalid:!1,pasteAsTags:!0,editTags:{clicks:2,keepInvalid:!0},transformTag:function(){},trim:!0,a11y:{focusableTags:!1},mixMode:{insertAfterTag:" "},autoComplete:{enabled:!0,rightKey:!1,tabKey:!1},classNames:{namespace:"tagify",mixMode:"tagify--mix",selectMode:"tagify--select",input:"tagify__input",focus:"tagify--focus",tagNoAnimation:"tagify--noAnim",tagInvalid:"tagify--invalid",tagNotAllowed:"tagify--notAllowed",scopeLoading:"tagify--loading",hasMaxTags:"tagify--hasMaxTags",hasNoTags:"tagify--noTags",empty:"tagify--empty",inputInvalid:"tagify__input--invalid",dropdown:"tagify__dropdown",dropdownWrapper:"tagify__dropdown__wrapper",dropdownHeader:"tagify__dropdown__header",dropdownFooter:"tagify__dropdown__footer",dropdownItem:"tagify__dropdown__item",dropdownItemActive:"tagify__dropdown__item--active",dropdownItemHidden:"tagify__dropdown__item--hidden",dropdownItemSelected:"tagify__dropdown__item--selected",dropdownInital:"tagify__dropdown--initial",tag:"tagify__tag",tagText:"tagify__tag-text",tagX:"tagify__tag__removeBtn",tagLoading:"tagify__tag--loading",tagEditing:"tagify__tag--editable",tagFlash:"tagify__tag--flash",tagHide:"tagify__tag--hide"},dropdown:{classname:"",enabled:2,maxItems:10,searchKeys:["value","searchBy"],fuzzySearch:!0,caseSensitive:!1,accentedSearch:!0,includeSelectedTags:!1,escapeHTML:!0,highlightFirst:!0,closeOnSelect:!0,clearOnSelect:!0,position:"all",appendTarget:null},hooks:{beforeRemoveTag:function(){return Promise.resolve()},beforePaste:function(){return Promise.resolve()},suggestionClick:function(){return Promise.resolve()},beforeKeyDown:function(){return Promise.resolve()}}};function Ti(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Oi(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{},r=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable}))),r.forEach(function(i){Ti(e,i,n[i])})}return e}function Ii(e,t){return t=t??{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(n,r){var i=Object.keys(n);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(n);i.push.apply(i,a)}return i}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}function Ut(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function wi(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Si(e){return function(t){if(Array.isArray(t))return Ut(t)}(e)||function(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}(e)||function(t,n){if(t){if(typeof t=="string")return Ut(t,n);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(r);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ut(t,n)}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ai(){for(var e in this.dropdown={},this._dropdown)this.dropdown[e]=typeof this._dropdown[e]=="function"?this._dropdown[e].bind(this):this._dropdown[e];this.dropdown.refs(),this.DOM.dropdown.__tagify=this}var gt,tt,Ri=(gt=function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{},r=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable}))),r.forEach(function(i){wi(e,i,n[i])})}return e}({},{events:{binding:function(){var e=!(arguments.length>0&&arguments[0]!==void 0)||arguments[0],t=this.dropdown.events.callbacks,n=this.listeners.dropdown=this.listeners.dropdown||{position:this.dropdown.position.bind(this,null),onKeyDown:t.onKeyDown.bind(this),onMouseOver:t.onMouseOver.bind(this),onMouseLeave:t.onMouseLeave.bind(this),onClick:t.onClick.bind(this),onScroll:t.onScroll.bind(this)},r=e?"addEventListener":"removeEventListener";this.settings.dropdown.position!="manual"&&(document[r]("scroll",n.position,!0),window[r]("resize",n.position),window[r]("keydown",n.onKeyDown)),this.DOM.dropdown[r]("mouseover",n.onMouseOver),this.DOM.dropdown[r]("mouseleave",n.onMouseLeave),this.DOM.dropdown[r]("mousedown",n.onClick),this.DOM.dropdown.content[r]("scroll",n.onScroll)},callbacks:{onKeyDown:function(e){var t=this;if(this.state.hasFocus&&!this.state.composing){var n=this.settings,r=n.dropdown.includeSelectedTags,i=this.DOM.dropdown.querySelector(n.classNames.dropdownItemActiveSelector),a=this.dropdown.getSuggestionDataByNode(i),s=n.mode=="mix",d=n.mode=="select";n.hooks.beforeKeyDown(e,{tagify:this}).then(function(c){switch(e.key){case"ArrowDown":case"ArrowUp":case"Down":case"Up":e.preventDefault();var y=t.dropdown.getAllSuggestionsRefs(),f=e.key=="ArrowUp"||e.key=="Up";i&&(i=t.dropdown.getNextOrPrevOption(i,!f)),i&&i.matches(n.classNames.dropdownItemSelector)||(i=y[f?y.length-1:0]),t.dropdown.highlightOption(i,!0);break;case"PageUp":case"PageDown":var u;e.preventDefault();var o=t.dropdown.getAllSuggestionsRefs(),l=Math.floor(t.DOM.dropdown.content.clientHeight/((u=o[0])===null||u===void 0?void 0:u.offsetHeight))||1,h=e.key==="PageUp";if(i){var v=o.indexOf(i),E=h?Math.max(0,v-l):Math.min(o.length-1,v+l);i=o[E]}else i=o[0];t.dropdown.highlightOption(i,!0);break;case"Home":case"End":e.preventDefault();var p=t.dropdown.getAllSuggestionsRefs();i=p[e.key==="Home"?0:p.length-1],t.dropdown.highlightOption(i,!0);break;case"Escape":case"Esc":t.dropdown.hide();break;case"ArrowRight":if(t.state.actions.ArrowLeft||n.autoComplete.rightKey)return;case"Tab":var m=!n.autoComplete.rightKey||!n.autoComplete.tabKey;if(!s&&!d&&i&&m&&!t.state.editing&&a){e.preventDefault();var _=t.dropdown.getMappedValue(a);return t.state.autoCompleteData=a,t.input.autocomplete.set.call(t,_),!1}return!0;case"Enter":e.preventDefault(),t.state.actions.selectOption=!0,setTimeout(function(){return t.state.actions.selectOption=!1},100),n.hooks.suggestionClick(e,{tagify:t,tagData:a,suggestionElm:i}).then(function(){if(i){var b=r?i:t.dropdown.getNextOrPrevOption(i,!f);t.dropdown.selectOption(i,e,function(){if(b){var A=b.getAttribute("value");b=t.dropdown.getSuggestionNodeByValue(A),t.dropdown.highlightOption(b)}})}else t.dropdown.hide(),s||t.addTags(t.state.inputText.trim(),!0)}).catch(function(b){return Ue.warn(b)});break;case"Backspace":if(s||t.state.editing.scope)return;var g=t.input.raw.call(t);g!=""&&g.charCodeAt(0)!=8203||(n.backspace===!0?t.removeTags():n.backspace=="edit"&&setTimeout(t.editTag.bind(t),0))}})}},onMouseOver:function(e){var t=e.target.closest(this.settings.classNames.dropdownItemSelector);this.dropdown.highlightOption(t)},onMouseLeave:function(e){this.dropdown.highlightOption()},onClick:function(e){var t=this;if(e.button==0&&e.target!=this.DOM.dropdown&&e.target!=this.DOM.dropdown.content){var n=e.target.closest(this.settings.classNames.dropdownItemSelector),r=this.dropdown.getSuggestionDataByNode(n);this.state.actions.selectOption=!0,setTimeout(function(){return t.state.actions.selectOption=!1},100),this.settings.hooks.suggestionClick(e,{tagify:this,tagData:r,suggestionElm:n}).then(function(){n?t.dropdown.selectOption(n,e):t.dropdown.hide()}).catch(function(i){return Ue.warn(i)})}},onScroll:function(e){var t=e.target,n=t.scrollTop/(t.scrollHeight-t.parentNode.clientHeight)*100;this.trigger("dropdown:scroll",{percentage:Math.round(n)})}}},refilter:function(e){e=e||this.state.dropdown.query||"",this.suggestedListItems=this.dropdown.filterListItems(e),this.dropdown.fill(),this.suggestedListItems.length||this.dropdown.hide(),this.trigger("dropdown:updated",this.DOM.dropdown)},getSuggestionDataByNode:function(e){for(var t,n=e&&e.getAttribute("value"),r=this.suggestedListItems.length;r--;){if(Pe(t=this.suggestedListItems[r])&&t.value==n)return t;if(t==n)return{value:t}}},getSuggestionNodeByValue:function(e){return this.dropdown.getAllSuggestionsRefs().find(function(t){return t.getAttribute("value")===e})},getNextOrPrevOption:function(e){var t=!(arguments.length>1&&arguments[1]!==void 0)||arguments[1],n=this.dropdown.getAllSuggestionsRefs(),r=n.findIndex(function(i){return i===e});return t?n[r+1]:n[r-1]},highlightOption:function(e,t){var n,r=this.settings.classNames.dropdownItemActive;if(this.state.ddItemElm&&(this.state.ddItemElm.classList.remove(r),this.state.ddItemElm.removeAttribute("aria-selected")),!e)return this.state.ddItemData=null,this.state.ddItemElm=null,void this.input.autocomplete.suggest.call(this);n=this.dropdown.getSuggestionDataByNode(e),this.state.ddItemData=n,this.state.ddItemElm=e,e.classList.add(r),e.setAttribute("aria-selected",!0),t&&(e.parentNode.scrollTop=e.clientHeight+e.offsetTop-e.parentNode.clientHeight),this.settings.autoComplete&&(this.input.autocomplete.suggest.call(this,n),this.dropdown.position())},selectOption:function(e,t,n){var r=this,i=this.settings,a=i.dropdown.includeSelectedTags,s=i.dropdown,d=s.clearOnSelect,c=s.closeOnSelect;if(!e)return this.addTags(this.state.inputText,!0),void(c&&this.dropdown.hide());t=t||{};var y=e.getAttribute("value"),f=y=="noMatch",u=i.mode=="mix",o=this.suggestedListItems.find(function(h){var v;return((v=h.value)!==null&&v!==void 0?v:h)==y});if(this.trigger("dropdown:select",{data:o,elm:e,event:t}),o||f){if(this.state.editing){var l=this.normalizeTags([o])[0];o=i.transformTag.call(this,l)||l,this.onEditTagDone(null,me({__isValid:!0},o))}else this[u?"addMixTags":"addTags"]([o||this.input.raw.call(this)],d);(u||this.DOM.input.parentNode)&&(setTimeout(function(){r.DOM.input.focus(),r.toggleFocusClass(!0)}),c&&setTimeout(this.dropdown.hide.bind(this)),a?n&&n():(e.addEventListener("transitionend",function(){r.dropdown.fillHeaderFooter(),setTimeout(function(){e.remove(),r.dropdown.refilter(),n&&n()},100)},{once:!0}),e.classList.add(this.settings.classNames.dropdownItemHidden)))}else c&&setTimeout(this.dropdown.hide.bind(this))},selectAll:function(e){this.suggestedListItems.length=0,this.dropdown.hide(),this.dropdown.filterListItems("");var t=this.dropdown.filterListItems("");return e||(t=this.state.dropdown.suggestions),this.addTags(t,!0),this},filterListItems:function(e,t){var n,r,i,a,s,d,c=function(){var b,A,S=void 0,I=void 0;b=h[_],r=((A=Object)!=null&&typeof Symbol<"u"&&A[Symbol.hasInstance]?A[Symbol.hasInstance](b):b instanceof A)?h[_]:{value:h[_]};var N,w=Object.keys(r).some(function(T){return m.includes(T)})?m:["value"];u.fuzzySearch&&!t.exact?(a=w.reduce(function(T,O){return T+" "+(r[O]||"")},"").toLowerCase().trim(),u.accentedSearch&&(a=pt(a),d=pt(d)),S=a.indexOf(d)==0,I=a===d,N=a,i=d.toLowerCase().split(" ").every(function(T){return N.includes(T.toLowerCase())})):(S=!0,i=w.some(function(T){var O=""+(r[T]||"");return u.accentedSearch&&(O=pt(O),d=pt(d)),u.caseSensitive||(O=O.toLowerCase()),I=O===d,t.exact?O===d:O.indexOf(d)==0})),s=!u.includeSelectedTags&&n.isTagDuplicate(Pe(r)?r.value:r),i&&!s&&(I&&S?l.push(r):u.sortby=="startsWith"&&S?o.unshift(r):o.push(r))},y=this,f=this.settings,u=f.dropdown,o=(t=t||{},[]),l=[],h=f.whitelist,v=u.maxItems>=0?u.maxItems:1/0,E=u.includeSelectedTags,p=typeof u.sortby=="function",m=u.searchKeys,_=0;if(!(e=f.mode=="select"&&this.value.length&&this.value[0][f.tagTextProp]==e?"":e)||!m.length){o=E?h:h.filter(function(b){return!y.isTagDuplicate(Pe(b)?b.value:b)});var g=p?u.sortby(o,d):o.slice(0,v);return this.state.dropdown.suggestions=g,g}for(d=u.caseSensitive?""+e:(""+e).toLowerCase();_<h.length;_++)n=this,c();return this.state.dropdown.suggestions=l.concat(o),g=p?u.sortby(l.concat(o),d):l.concat(o).slice(0,v),this.state.dropdown.suggestions=g,g},getMappedValue:function(e){var t=this.settings.dropdown.mapValueTo;return t?typeof t=="function"?t(e):e[t]||e.value:e.value},createListHTML:function(e){var t=this;return me([],e).map(function(n,r){typeof n!="string"&&typeof n!="number"||(n={value:n});var i=t.dropdown.getMappedValue(n);return i=typeof i=="string"&&t.settings.dropdown.escapeHTML?He(i):i,t.settings.templates.dropdownItem.apply(t,[Ii(Oi({},n),{mappedValue:i}),t])}).join("")}}),tt=(tt={refs:function(){this.DOM.dropdown=this.parseTemplate("dropdown",[this.settings]),this.DOM.dropdown.content=this.DOM.dropdown.querySelector("[data-selector='tagify-suggestions-wrapper']")},getHeaderRef:function(){return this.DOM.dropdown.querySelector("[data-selector='tagify-suggestions-header']")},getFooterRef:function(){return this.DOM.dropdown.querySelector("[data-selector='tagify-suggestions-footer']")},getAllSuggestionsRefs:function(){return Si(this.DOM.dropdown.content.querySelectorAll(this.settings.classNames.dropdownItemSelector))},show:function(e){var t,n,r,i=this,a=this.settings,s=a.mode=="mix"&&!a.enforceWhitelist,d=!a.whitelist||!a.whitelist.length,c=a.dropdown.position=="manual";if(e=e===void 0?this.state.inputText:e,!(d&&!s&&!a.templates.dropdownItemNoMatch||a.dropdown.enabled===!1||this.state.isLoading||this.settings.readonly)){if(clearTimeout(this.dropdownHide__bindEventsTimeout),this.suggestedListItems=this.dropdown.filterListItems(e),e&&!this.suggestedListItems.length&&(this.trigger("dropdown:noMatch",e),a.templates.dropdownItemNoMatch&&(r=a.templates.dropdownItemNoMatch.call(this,{value:e}))),!r){if(this.suggestedListItems.length)e&&s&&!this.state.editing.scope&&!ft(this.suggestedListItems[0].value,e)&&this.suggestedListItems.unshift({value:e});else{if(!e||!s||this.state.editing.scope)return this.input.autocomplete.suggest.call(this),void this.dropdown.hide();this.suggestedListItems=[{value:e}]}n=""+(Pe(t=this.suggestedListItems[0])?t.value:t),a.autoComplete&&n&&n.indexOf(e)==0&&this.input.autocomplete.suggest.call(this,t)}this.dropdown.fill(r),a.dropdown.highlightFirst&&this.dropdown.highlightOption(this.DOM.dropdown.content.querySelector(a.classNames.dropdownItemSelector)),this.state.dropdown.visible||setTimeout(this.dropdown.events.binding.bind(this)),this.state.dropdown.visible=e||!0,this.state.dropdown.query=e,this.setStateSelection(),c||setTimeout(function(){i.dropdown.position(),i.dropdown.render()}),setTimeout(function(){i.trigger("dropdown:show",i.DOM.dropdown)})}},hide:function(e){var t=this,n=this.DOM,r=n.scope,i=n.dropdown,a=this.settings.dropdown.position=="manual"&&!e;if(i&&document.body.contains(i)&&!a)return window.removeEventListener("resize",this.dropdown.position),this.dropdown.events.binding.call(this,!1),r.setAttribute("aria-expanded",!1),i.parentNode.removeChild(i),setTimeout(function(){t.state.dropdown.visible=!1},100),this.state.dropdown.query=this.state.ddItemData=this.state.ddItemElm=this.state.selection=null,this.state.tag&&this.state.tag.value.length&&(this.state.flaggedTags[this.state.tag.baseOffset]=this.state.tag),this.trigger("dropdown:hide",i),this},toggle:function(e){this.dropdown[this.state.dropdown.visible&&!e?"hide":"show"]()},getAppendTarget:function(){var e=this.settings.dropdown;return typeof e.appendTarget=="function"?e.appendTarget():e.appendTarget},render:function(){var e,t,n,r=this,i=(e=this.DOM.dropdown,(n=e.cloneNode(!0)).style.cssText="position:fixed; top:-9999px; opacity:0",document.body.appendChild(n),t=n.clientHeight,n.parentNode.removeChild(n),t),a=this.settings,s=this.dropdown.getAppendTarget();return a.dropdown.enabled===!1||(this.DOM.scope.setAttribute("aria-expanded",!0),document.body.contains(this.DOM.dropdown)||(this.DOM.dropdown.classList.add(a.classNames.dropdownInital),this.dropdown.position(i),s.appendChild(this.DOM.dropdown),setTimeout(function(){return r.DOM.dropdown.classList.remove(a.classNames.dropdownInital)}))),this},fill:function(e){e=typeof e=="string"?e:this.dropdown.createListHTML(e||this.suggestedListItems);var t,n=this.settings.templates.dropdownContent.call(this,e);this.DOM.dropdown.content.innerHTML=(t=n)?t.replace(/\>[\r\n ]+\</g,"><").split(/>\s+</).join("><").trim():""},fillHeaderFooter:function(){var e=this.dropdown.filterListItems(this.state.dropdown.query),t=this.parseTemplate("dropdownHeader",[e]),n=this.parseTemplate("dropdownFooter",[e]),r=this.dropdown.getHeaderRef(),i=this.dropdown.getFooterRef();t&&(r==null||r.parentNode.replaceChild(t,r)),n&&(i==null||i.parentNode.replaceChild(n,i))},position:function(e){var t=this.settings.dropdown,n=this.dropdown.getAppendTarget();if(t.position!="manual"&&n){var r,i,a,s,d,c,y,f,u,o,l=this.DOM.dropdown,h=t.RTL,v=n===document.body,E=n===this.DOM.scope,p=v?window.pageYOffset:n.scrollTop,m=document.fullscreenElement||document.webkitFullscreenElement||document.documentElement,_=m.clientHeight,g=Math.max(m.clientWidth||0,window.innerWidth||0),b=g>480?t.position:"all",A=this.DOM[b=="input"?"input":"scope"];if(e=e||l.clientHeight,this.state.dropdown.visible){if(b=="text"?(a=(r=function(){var N=document.getSelection();if(N.rangeCount){var w,T,O=N.getRangeAt(0),L=O.startContainer,D=O.startOffset;if(D>0)return(T=document.createRange()).setStart(L,D-1),T.setEnd(L,D),{left:(w=T.getBoundingClientRect()).right,top:w.top,bottom:w.bottom};if(L.getBoundingClientRect)return L.getBoundingClientRect()}return{left:-9999,top:-9999}}()).bottom,i=r.top,s=r.left,d="auto"):(c=function(N){var w=0,T=0;for(N=N.parentNode;N&&N!=m;)w+=N.offsetTop||0,T+=N.offsetLeft||0,N=N.parentNode;return{top:w,left:T}}(n),r=A.getBoundingClientRect(),i=E?-1:r.top-c.top,a=(E?r.height:r.bottom-c.top)-1,s=E?-1:r.left-c.left,d=r.width+"px"),!v){var S=function(){for(var N=0,w=t.appendTarget.parentNode;w;)N+=w.scrollTop||0,w=w.parentNode;return N}();i+=S,a+=S}var I;i=Math.floor(i),a=Math.ceil(a),f=g-s<120,u=((y=(I=t.placeAbove)!==null&&I!==void 0?I:_-r.bottom<e)?i:a)+p,o=s+(h&&r.width||0)+window.pageXOffset,o=b=="text"&&f?"right: 0;":"left: ".concat(o,"px;"),l.style.cssText="".concat(o," top: ").concat(u,"px; min-width: ").concat(d,"; max-width: ").concat(d),l.setAttribute("placement",y?"top":"bottom"),l.setAttribute("position",b)}}}})!=null?tt:{},Object.getOwnPropertyDescriptors?Object.defineProperties(gt,Object.getOwnPropertyDescriptors(tt)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n.push.apply(n,r)}return n}(Object(tt)).forEach(function(e){Object.defineProperty(gt,e,Object.getOwnPropertyDescriptor(tt,e))}),gt),st="@yaireo/tagify/",Di={empty:"empty",exceed:"number of tags exceeded",pattern:"pattern mismatch",duplicate:"already exists",notAllowed:"not allowed"},Ni={wrapper:function(e,t){return'<tags class="'.concat(t.classNames.namespace," ").concat(t.mode?"".concat(t.classNames[t.mode+"Mode"]):""," ").concat(e.className,`"
                    `).concat(t.readonly?"readonly":"",`
                    `).concat(t.disabled?"disabled":"",`
                    `).concat(t.required?"required":"",`
                    `).concat(t.mode==="select"?"spellcheck='false'":"",`
                    tabIndex="-1">
                    `).concat(this.settings.templates.input.call(this),`
                `).concat(Nn,`
        </tags>`)},input:function(){var e=this.settings,t=e.placeholder||Nn;return"<span ".concat(!e.readonly&&e.userInput?"contenteditable":"",' tabIndex="0" data-placeholder="').concat(t,'" aria-placeholder="').concat(e.placeholder||"",`"
                    class="`).concat(e.classNames.input,`"
                    role="textbox"
                    autocapitalize="false"
                    autocorrect="off"
                    aria-autocomplete="both"
                    aria-multiline="`).concat(e.mode=="mix",'"></span>')},tag:function(e,t){var n=t.settings;return'<tag title="'.concat(e.title||e.value,`"
                    contenteditable='false'
                    tabIndex="`).concat(n.a11y.focusableTags?0:-1,`"
                    class="`).concat(n.classNames.tag," ").concat(e.class||"",`"
                    `).concat(this.getAttributes(e),`>
            <x title='' tabIndex="`).concat(n.a11y.focusableTags?0:-1,'" class="').concat(n.classNames.tagX,`" role='button' aria-label='remove tag'></x>
            <div>
                <span `).concat(n.mode==="select"&&n.userInput?"contenteditable='true'":"",` autocapitalize="false" autocorrect="off" spellcheck='false' class="`).concat(n.classNames.tagText,'">').concat(e[n.tagTextProp]||e.value,`</span>
            </div>
        </tag>`)},dropdown:function(e){var t=e.dropdown,n=t.position=="manual";return'<div class="'.concat(n?"":e.classNames.dropdown," ").concat(t.classname,'" role="listbox" aria-labelledby="dropdown" dir="').concat(t.RTL?"rtl":"",`">
                    <div data-selector='tagify-suggestions-wrapper' class="`).concat(e.classNames.dropdownWrapper,`"></div>
                </div>`)},dropdownContent:function(e){var t=this.settings.templates,n=this.state.dropdown.suggestions;return`
            `.concat(t.dropdownHeader.call(this,n),`
            `).concat(e,`
            `).concat(t.dropdownFooter.call(this,n),`
        `)},dropdownItem:function(e){return"<div ".concat(this.getAttributes(e),`
                    class='`).concat(this.settings.classNames.dropdownItem," ").concat(this.isTagDuplicate(e.value)?this.settings.classNames.dropdownItemSelected:""," ").concat(e.class||"",`'
                    tabindex="0"
                    role="option">`).concat(e.mappedValue||e.value,"</div>")},dropdownHeader:function(e){return`<header data-selector='tagify-suggestions-header' class="`.concat(this.settings.classNames.dropdownHeader,'"></header>')},dropdownFooter:function(e){var t=e.length-this.settings.dropdown.maxItems;return t>0?`<footer data-selector='tagify-suggestions-footer' class="`.concat(this.settings.classNames.dropdownFooter,`">
                `).concat(t,` more items. Refine your search.
            </footer>`):""},dropdownItemNoMatch:null};function qn(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Fn(e,t){return t!=null&&typeof Symbol<"u"&&t[Symbol.hasInstance]?!!t[Symbol.hasInstance](e):e instanceof t}function Li(e,t){return function(n){if(Array.isArray(n))return n}(e)||function(n,r){var i=n==null?null:typeof Symbol<"u"&&n[Symbol.iterator]||n["@@iterator"];if(i!=null){var a,s,d=[],c=!0,y=!1;try{for(i=i.call(n);!(c=(a=i.next()).done)&&(d.push(a.value),!r||d.length!==r);c=!0);}catch(f){y=!0,s=f}finally{try{c||i.return==null||i.return()}finally{if(y)throw s}}return d}}(e,t)||function(n,r){if(n){if(typeof n=="string")return qn(n,r);var i=Object.prototype.toString.call(n).slice(8,-1);if(i==="Object"&&n.constructor&&(i=n.constructor.name),i==="Map"||i==="Set")return Array.from(i);if(i==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return qn(n,r)}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Gt(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Ve(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function jn(e,t){return t!=null&&typeof Symbol<"u"&&t[Symbol.hasInstance]?!!t[Symbol.hasInstance](e):e instanceof t}function Pi(e,t){return t=t??{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(n,r){var i=Object.keys(n);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(n);i.push.apply(i,a)}return i}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}function Vt(e){return function(t){if(Array.isArray(t))return Gt(t)}(e)||function(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}(e)||function(t,n){if(t){if(typeof t=="string")return Gt(t,n);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(r);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Gt(t,n)}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Mi={customBinding:function(){var e=this;this.customEventsList.forEach(function(t){e.on(t,e.settings.callbacks[t])})},binding:function(){var e,t=!(arguments.length>0&&arguments[0]!==void 0)||arguments[0],n=this.settings,r=this.events.callbacks,i=t?"addEventListener":"removeEventListener";if(!this.state.mainEvents||!t){for(var a in this.state.mainEvents=t,t&&!this.listeners.main&&(this.events.bindGlobal.call(this),this.settings.isJQueryPlugin&&jQuery(this.DOM.originalInput).on("tagify.removeAllTags",this.removeAllTags.bind(this))),e=this.listeners.main=this.listeners.main||{keydown:["input",r.onKeydown.bind(this)],click:["scope",r.onClickScope.bind(this)],dblclick:n.mode!="select"&&["scope",r.onDoubleClickScope.bind(this)],paste:["input",r.onPaste.bind(this)],drop:["input",r.onDrop.bind(this)],compositionstart:["input",r.onCompositionStart.bind(this)],compositionend:["input",r.onCompositionEnd.bind(this)]})e[a]&&this.DOM[e[a][0]][i](a,e[a][1]);var s=this.listeners.main.inputMutationObserver||new MutationObserver(r.onInputDOMChange.bind(this));s.disconnect(),n.mode=="mix"&&s.observe(this.DOM.input,{childList:!0}),this.events.bindOriginaInputListener.call(this)}},bindOriginaInputListener:function(e){var t=(e||0)+500;this.listeners.main&&(clearInterval(this.listeners.main.originalInputValueObserverInterval),this.listeners.main.originalInputValueObserverInterval=setInterval(this.events.callbacks.observeOriginalInputValue.bind(this),t))},bindGlobal:function(e){var t,n=this.events.callbacks,r=e?"removeEventListener":"addEventListener";if(this.listeners&&(e||!this.listeners.global)){this.listeners.global=this.listeners.global||[{type:this.isIE?"keydown":"input",target:this.DOM.input,cb:n[this.isIE?"onInputIE":"onInput"].bind(this)},{type:"keydown",target:window,cb:n.onWindowKeyDown.bind(this)},{type:"focusin",target:this.DOM.scope,cb:n.onFocusBlur.bind(this)},{type:"focusout",target:this.DOM.scope,cb:n.onFocusBlur.bind(this)},{type:"click",target:document,cb:n.onClickAnywhere.bind(this),useCapture:!0}];var i=!0,a=!1,s=void 0;try{for(var d,c=this.listeners.global[Symbol.iterator]();!(i=(d=c.next()).done);i=!0)(t=d.value).target[r](t.type,t.cb,!!t.useCapture)}catch(y){a=!0,s=y}finally{try{i||c.return==null||c.return()}finally{if(a)throw s}}}},unbindGlobal:function(){this.events.bindGlobal.call(this,!0)},callbacks:{onFocusBlur:function(e){var t,n,r=this.settings,i=Bn.call(this,e.relatedTarget),a=ze.call(this,e.relatedTarget),s=e.target.classList.contains(r.classNames.tagX),d=e.type=="focusin",c=e.type=="focusout";s&&r.mode!="mix"&&r.focusInputOnRemove&&this.DOM.input.focus(),i&&d&&!a&&!s&&this.toggleFocusClass(this.state.hasFocus=+new Date);var y=e.target?this.trim(this.DOM.input.textContent):"",f=(n=this.value)===null||n===void 0||(t=n[0])===null||t===void 0?void 0:t[r.tagTextProp],u=r.dropdown.enabled>=0,o={relatedTarget:e.relatedTarget},l=this.state.actions.selectOption&&(u||!r.dropdown.closeOnSelect),h=this.state.actions.addNew&&u;if(c){if(e.relatedTarget===this.DOM.scope)return this.dropdown.hide(),void this.DOM.input.focus();this.postUpdate(),r.onChangeAfterBlur&&this.triggerChangeEvent()}if(!(l||h||s))if(this.state.hasFocus=!(!d&&!i)&&+new Date,this.toggleFocusClass(this.state.hasFocus),r.mode!="mix"){if(d){if(!r.focusable)return;r.mode!="select"&&this.DOM.input.focus();var v=r.dropdown.enabled===0&&!this.state.dropdown.visible,E=this.DOM.scope.querySelector(this.settings.classNames.tagTextSelector);return this.trigger("focus",o),void(v&&!a&&(this.dropdown.show(this.value.length?"":void 0),r.mode==="select"&&this.setRangeAtStartEnd(!1,E)))}if(c){if(this.trigger("blur",o),this.loading(!1),r.mode=="select"){if(this.value.length){var p=this.getTagElms()[0];y=this.trim(p.textContent)}f===y&&(y="")}y&&!this.state.actions.selectOption&&r.addTagOnBlur&&r.addTagOn.includes("blur")&&this.addTags(y,!0)}i||(this.DOM.input.removeAttribute("style"),this.dropdown.hide())}else d?this.trigger("focus",o):c&&(this.trigger("blur",o),this.loading(!1),this.dropdown.hide(),this.state.dropdown.visible=void 0,this.setStateSelection())},onCompositionStart:function(e){this.state.composing=!0},onCompositionEnd:function(e){this.state.composing=!1},onWindowKeyDown:function(e){var t,n=this.settings,r=document.activeElement,i=Bn.call(this,r)&&this.DOM.scope.contains(r),a=r===this.DOM.input,s=i&&r.hasAttribute("readonly"),d=this.DOM.scope.querySelector(this.settings.classNames.tagTextSelector),c=this.state.dropdown.visible;if((e.key==="Tab"&&c||this.state.hasFocus||i&&!s)&&!a){t=r.nextElementSibling;var y=e.target.classList.contains(n.classNames.tagX);switch(e.key){case"Backspace":n.readonly||this.state.editing||(this.removeTags(r),(t||this.DOM.input).focus());break;case"Enter":if(y)return void this.removeTags(e.target.parentNode);n.a11y.focusableTags&&ze.call(this,r)&&setTimeout(this.editTag.bind(this),0,r);break;case"ArrowDown":this.state.dropdown.visible||n.mode=="mix"||this.dropdown.show();break;case"Tab":d==null||d.focus()}}},onKeydown:function(e){var t=this,n=this.settings;if(!this.state.composing&&n.userInput){n.mode=="select"&&n.enforceWhitelist&&this.value.length&&e.key!="Tab"&&e.preventDefault();var r=this.trim(e.target.textContent);this.trigger("keydown",{event:e}),n.hooks.beforeKeyDown(e,{tagify:this}).then(function(i){if(n.mode=="mix"){switch(e.key){case"Left":case"ArrowLeft":t.state.actions.ArrowLeft=!0;break;case"Delete":case"Backspace":if(t.state.editing)return;var a=document.getSelection(),s=e.key=="Delete"&&a.anchorOffset==(a.anchorNode.length||0),d=a.anchorNode.previousSibling,c=a.anchorNode.nodeType==1||!a.anchorOffset&&d&&d.nodeType==1&&a.anchorNode.previousSibling;(function(p){var m=document.createElement("div");p.replace(/\&#?[0-9a-z]+;/gi,function(_){return m.innerHTML=_,m.innerText})})(t.DOM.input.innerHTML);var y,f,u,o=t.getTagElms(),l=a.anchorNode.length===1&&a.anchorNode.nodeValue=="​";if(n.backspace=="edit"&&c)return y=a.anchorNode.nodeType==1?null:a.anchorNode.previousElementSibling,setTimeout(t.editTag.bind(t),0,y),void e.preventDefault();if(Cn()&&jn(c,Element))return u=Mn(c),c.hasAttribute("readonly")||c.remove(),t.DOM.input.focus(),void setTimeout(function(){je(u),t.DOM.input.click()});if(a.anchorNode.nodeName=="BR")return;if((s||c)&&a.anchorNode.nodeType==1?f=a.anchorOffset==0?s?o[0]:null:o[Math.min(o.length,a.anchorOffset)-1]:s?f=a.anchorNode.nextElementSibling:jn(c,Element)&&(f=c),a.anchorNode.nodeType==3&&!a.anchorNode.nodeValue&&a.anchorNode.previousElementSibling&&e.preventDefault(),(c||s)&&!n.backspace||a.type!="Range"&&!a.anchorOffset&&a.anchorNode==t.DOM.input&&e.key!="Delete")return void e.preventDefault();if(a.type!="Range"&&f&&f.hasAttribute("readonly"))return void je(Mn(f));e.key=="Delete"&&l&&le(a.anchorNode.nextSibling)&&t.removeTags(a.anchorNode.nextSibling)}return!0}var h=n.dropdown.position=="manual";switch(e.key){case"Backspace":n.mode=="select"&&n.enforceWhitelist&&t.value.length?t.removeTags():t.state.dropdown.visible&&n.dropdown.position!="manual"||e.target.textContent!=""&&r.charCodeAt(0)!=8203||(n.backspace===!0?t.removeTags():n.backspace=="edit"&&setTimeout(t.editTag.bind(t),0));break;case"Esc":case"Escape":if(t.state.dropdown.visible)return;e.target.blur();break;case"Down":case"ArrowDown":t.state.dropdown.visible||t.dropdown.show();break;case"ArrowRight":var v=t.state.inputSuggestion||t.state.ddItemData;if(v&&n.autoComplete.rightKey)return void t.addTags([v],!0);break;case"Tab":return!0;case"Enter":if(t.state.dropdown.visible&&!h)return;e.preventDefault();var E=t.state.autoCompleteData||r;setTimeout(function(){t.state.dropdown.visible&&!h||t.state.actions.selectOption||!n.addTagOn.includes(e.key.toLowerCase())||(t.addTags([E],!0),t.state.autoCompleteData=null)})}}).catch(function(i){return i})}},onInput:function(e){this.postUpdate();var t=this.settings;if(t.mode=="mix")return this.events.callbacks.onMixTagsInput.call(this,e);var n=this.input.normalize.call(this,void 0,{trim:!1}),r=n.length>=t.dropdown.enabled,i={value:n,inputElm:this.DOM.input},a=this.validateTag({value:n});t.mode=="select"&&this.toggleScopeValidation(a),i.isValid=a,this.state.inputText!=n&&(this.input.set.call(this,n,!1),n.search(t.delimiters)!=-1?this.addTags(n)&&this.input.set.call(this):t.dropdown.enabled>=0&&this.dropdown[r?"show":"hide"](n),this.trigger("input",i))},onMixTagsInput:function(e){var t,n,r,i,a,s,d,c,y=this,f=this.settings,u=this.value.length,o=this.getTagElms(),l=document.createDocumentFragment(),h=window.getSelection().getRangeAt(0),v=[].map.call(o,function(E){return le(E).value});if(e.inputType=="deleteContentBackward"&&Cn()&&this.events.callbacks.onKeydown.call(this,{target:e.target,key:"Backspace"}),Dr(this.getTagElms()),this.value.slice().forEach(function(E){E.readonly&&!v.includes(E.value)&&l.appendChild(y.createTagElem(E))}),l.childNodes.length&&(h.insertNode(l),this.setRangeAtStartEnd(!1,l.lastChild)),o.length!=u)return this.value=[].map.call(this.getTagElms(),function(E){return le(E)}),void this.update({withoutChangeEvent:!0});if(this.hasMaxTags())return!0;if(window.getSelection&&(s=window.getSelection()).rangeCount>0&&s.anchorNode.nodeType==3){if((h=s.getRangeAt(0).cloneRange()).collapse(!0),h.setStart(s.focusNode,0),r=(t=h.toString().slice(0,h.endOffset)).split(f.pattern).length-1,(n=t.match(f.pattern))&&(i=t.slice(t.lastIndexOf(n[n.length-1]))),i){if(this.state.actions.ArrowLeft=!1,this.state.tag={prefix:i.match(f.pattern)[0],value:i.replace(f.pattern,"")},this.state.tag.baseOffset=s.baseOffset-this.state.tag.value.length,c=this.state.tag.value.match(f.delimiters))return this.state.tag.value=this.state.tag.value.replace(f.delimiters,""),this.state.tag.delimiters=c[0],this.addTags(this.state.tag.value,f.dropdown.clearOnSelect),void this.dropdown.hide();a=this.state.tag.value.length>=f.dropdown.enabled;try{d=(d=this.state.flaggedTags[this.state.tag.baseOffset]).prefix==this.state.tag.prefix&&d.value[0]==this.state.tag.value[0],this.state.flaggedTags[this.state.tag.baseOffset]&&!this.state.tag.value&&delete this.state.flaggedTags[this.state.tag.baseOffset]}catch{}(d||r<this.state.mixMode.matchedPatternCount)&&(a=!1)}else this.state.flaggedTags={};this.state.mixMode.matchedPatternCount=r}setTimeout(function(){y.update({withoutChangeEvent:!0}),y.trigger("input",me({},y.state.tag,{textContent:y.DOM.input.textContent})),y.state.tag&&y.dropdown[a?"show":"hide"](y.state.tag.value)},10)},onInputIE:function(e){var t=this;setTimeout(function(){t.events.callbacks.onInput.call(t,e)})},observeOriginalInputValue:function(){this.DOM.originalInput.parentNode||this.destroy(),this.DOM.originalInput.value!=this.DOM.originalInput.tagifyValue&&this.loadOriginalValues()},onClickAnywhere:function(e){if(e.target!=this.DOM.scope&&!this.DOM.scope.contains(e.target)){this.toggleFocusClass(!1),this.state.hasFocus=!1;var t=e.target.closest(this.settings.classNames.dropdownSelector);(t==null?void 0:t.__tagify)!=this&&this.dropdown.hide()}},onClickScope:function(e){var t=this.settings,n=e.target.closest("."+t.classNames.tag);e.target,this.DOM.scope;var r=+new Date-this.state.hasFocus;if(!e.target.classList.contains(t.classNames.tagX))return n&&!this.state.editing?(this.trigger("click",{tag:n,index:this.getNodeIndex(n),data:le(n),event:e}),void(t.editTags!==1&&t.editTags.clicks!==1&&t.mode!="select"||this.events.callbacks.onDoubleClickScope.call(this,e))):void(e.target==this.DOM.input&&(t.mode=="mix"&&this.fixFirefoxLastTagNoCaret(),r>500||!t.focusable)?this.state.dropdown.visible?this.dropdown.hide():t.dropdown.enabled===0&&t.mode!="mix"&&this.dropdown.show(this.value.length?"":void 0):t.mode!="select"||t.dropdown.enabled!==0||this.state.dropdown.visible||(this.events.callbacks.onDoubleClickScope.call(this,Pi(function(i){for(var a=1;a<arguments.length;a++){var s=arguments[a]!=null?arguments[a]:{},d=Object.keys(s);typeof Object.getOwnPropertySymbols=="function"&&(d=d.concat(Object.getOwnPropertySymbols(s).filter(function(c){return Object.getOwnPropertyDescriptor(s,c).enumerable}))),d.forEach(function(c){Ve(i,c,s[c])})}return i}({},e),{target:this.getTagElms()[0]})),!t.userInput&&this.dropdown.show()));this.removeTags(e.target.parentNode)},onPaste:function(e){var t=this;e.preventDefault();var n,r,i,a=this.settings;if(!a.userInput)return!1;a.readonly||(r=e.clipboardData||window.clipboardData,i=r.getData("Text"),a.hooks.beforePaste(e,{tagify:this,pastedText:i,clipboardData:r}).then(function(s){s===void 0&&(s=i),s&&(t.injectAtCaret(s,window.getSelection().getRangeAt(0)),t.settings.mode=="mix"?t.events.callbacks.onMixTagsInput.call(t,e):t.settings.pasteAsTags?n=t.addTags(t.state.inputText+s,!0):(t.state.inputText=s,t.dropdown.show(s))),t.trigger("paste",{event:e,pastedText:i,clipboardData:r,tagsElems:n})}).catch(function(s){return s}))},onDrop:function(e){e.preventDefault()},onEditTagInput:function(e,t){var n,r=e.closest("."+this.settings.classNames.tag),i=this.getNodeIndex(r),a=le(r),s=this.input.normalize.call(this,e),d=(Ve(n={},this.settings.tagTextProp,s),Ve(n,"__tagId",a.__tagId),n),c=this.validateTag(d);this.editTagChangeDetected(me(a,d))||e.originalIsValid!==!0||(c=!0),r.classList.toggle(this.settings.classNames.tagInvalid,c!==!0),a.__isValid=c,r.title=c===!0?a.title||a.value:c,s.length>=this.settings.dropdown.enabled&&(this.state.editing&&(this.state.editing.value=s),this.dropdown.show(s)),this.trigger("edit:input",{tag:r,index:i,data:me({},this.value[i],{newValue:s}),event:t})},onEditTagPaste:function(e,t){var n=(t.clipboardData||window.clipboardData).getData("Text");t.preventDefault();var r=Rr(n);this.setRangeAtStartEnd(!1,r)},onEditTagClick:function(e,t){this.events.callbacks.onClickScope.call(this,t)},onEditTagFocus:function(e){this.state.editing={scope:e,input:e.querySelector("[contenteditable]")}},onEditTagBlur:function(e,t){var n=ze.call(this,t.relatedTarget);if(this.settings.mode=="select"&&n&&t.relatedTarget.contains(t.target))this.dropdown.hide();else if(this.state.editing&&(this.state.hasFocus||this.toggleFocusClass(),this.DOM.scope.contains(document.activeElement)||this.trigger("blur",{}),this.DOM.scope.contains(e))){var r,i,a,s=this.settings,d=e.closest("."+s.classNames.tag),c=le(d),y=this.input.normalize.call(this,e),f=(Ve(r={},s.tagTextProp,y),Ve(r,"__tagId",c.__tagId),r),u=c.__originalData,o=this.editTagChangeDetected(me(c,f)),l=this.validateTag(f);if(y)if(o){var h;if(i=this.hasMaxTags(),a=me({},u,(Ve(h={},s.tagTextProp,this.trim(y)),Ve(h,"__isValid",l),h)),s.transformTag.call(this,a,u),(l=(!i||u.__isValid===!0)&&this.validateTag(a))!==!0){if(this.trigger("invalid",{data:a,tag:d,message:l}),s.editTags.keepInvalid)return;s.keepInvalidTags?a.__isValid=l:a=u}else s.keepInvalidTags&&(delete a.title,delete a["aria-invalid"],delete a.class);this.onEditTagDone(d,a)}else this.onEditTagDone(d,u);else this.onEditTagDone(d)}},onEditTagkeydown:function(e,t){if(!this.state.composing)switch(this.trigger("edit:keydown",{event:e}),e.key){case"Esc":case"Escape":this.state.editing=!1,t.__tagifyTagData.__originalData.value?t.parentNode.replaceChild(t.__tagifyTagData.__originalHTML,t):t.remove();break;case"Enter":case"Tab":e.preventDefault(),setTimeout(function(){return e.target.blur()},0)}},onDoubleClickScope:function(e){var t=e.target.closest("."+this.settings.classNames.tag);if(t){var n,r,i=le(t),a=this.settings;(i==null?void 0:i.editable)!==!1&&(n=t.classList.contains(this.settings.classNames.tagEditing),r=t.hasAttribute("readonly"),a.readonly||n||r||!this.settings.editTags||!a.userInput||(this.events.callbacks.onEditTagFocus.call(this,t),this.editTag(t)),this.toggleFocusClass(!0),a.mode!="select"&&this.trigger("dblclick",{tag:t,index:this.getNodeIndex(t),data:le(t)}))}},onInputDOMChange:function(e){var t=this;e.forEach(function(r){r.addedNodes.forEach(function(i){if(i.outerHTML=="<div><br></div>")i.replaceWith(document.createElement("br"));else if(i.nodeType==1&&i.querySelector(t.settings.classNames.tagSelector)){var a,s=document.createTextNode("");i.childNodes[0].nodeType==3&&i.previousSibling.nodeName!="BR"&&(s=document.createTextNode(`
`)),(a=i).replaceWith.apply(a,Vt([s].concat(Vt(Vt(i.childNodes).slice(0,-1))))),je(s)}else if(ze.call(t,i)){var d;if(((d=i.previousSibling)===null||d===void 0?void 0:d.nodeType)!=3||i.previousSibling.textContent||i.previousSibling.remove(),i.previousSibling&&i.previousSibling.nodeName=="BR"){i.previousSibling.replaceWith(`
​`);for(var c=i.nextSibling,y="";c;)y+=c.textContent,c=c.nextSibling;y.trim()&&je(i.previousSibling)}else i.previousSibling&&!le(i.previousSibling)||i.before("​")}}),r.removedNodes.forEach(function(i){i&&i.nodeName=="BR"&&ze.call(t,n)&&(t.removeTags(n),t.fixFirefoxLastTagNoCaret())})});var n=this.DOM.input.lastChild;n&&n.nodeValue==""&&n.remove(),n&&n.nodeName=="BR"||this.DOM.input.appendChild(document.createElement("br"))}}};function Ht(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Jt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ce(e,t){return t!=null&&typeof Symbol<"u"&&t[Symbol.hasInstance]?!!t[Symbol.hasInstance](e):e instanceof t}function Un(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{},r=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable}))),r.forEach(function(i){Jt(e,i,n[i])})}return e}function vt(e){return function(t){if(Array.isArray(t))return Ht(t)}(e)||function(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}(e)||function(t,n){if(t){if(typeof t=="string")return Ht(t,n);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(r);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ht(t,n)}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function zt(e,t){if(!e){Ue.warn("input element not found",e);var n=new Proxy(this,{get:function(){return function(){return n}}});return n}if(e.__tagify)return Ue.warn("input element is already Tagified - Same instance is returned.",e),e.__tagify;var r;me(this,function(i){var a=document.createTextNode(""),s={};function d(c,y,f){f&&y.split(/\s+/g).forEach(function(u){return a[c+"EventListener"].call(a,u,f)})}return{removeAllCustomListeners:function(){Object.entries(s).forEach(function(c){var y=Li(c,2),f=y[0];y[1].forEach(function(u){return d("remove",f,u)})}),s={}},off:function(c,y){return c&&(y?d("remove",c,y):c.split(/\s+/g).forEach(function(f){var u;(u=s[f])===null||u===void 0||u.forEach(function(o){return d("remove",f,o)}),delete s[f]})),this},on:function(c,y){return y&&typeof y=="function"&&(c.split(/\s+/g).forEach(function(f){Array.isArray(s[f])?s[f].push(y):s[f]=[y]}),d("add",c,y)),this},trigger:function(c,y,f){var u;if(f=f||{cloneData:!0},c)if(i.settings.isJQueryPlugin)c=="remove"&&(c="removeTag"),jQuery(i.DOM.originalInput).triggerHandler(c,[y]);else{try{var o=typeof y=="object"?y:{value:y};if((o=f.cloneData?me({},o):o).tagify=this,y.event&&(o.event=this.cloneEvent(y.event)),Fn(y,Object))for(var l in y)Fn(y[l],HTMLElement)&&(o[l]=y[l]);u=new CustomEvent(c,{detail:o})}catch(h){Ue.warn(h)}a.dispatchEvent(u)}}}}(this)),this.isFirefox=/firefox|fxios/i.test(navigator.userAgent)&&!/seamonkey/i.test(navigator.userAgent),this.isIE=window.document.documentMode,t=t||{},this.getPersistedData=(r=t.id,function(i){var a;if(r){var s,d="/"+i;if(((a=localStorage)===null||a===void 0?void 0:a.getItem(st+r+"/v"))===1)try{s=JSON.parse(localStorage[st+r+d])}catch{}return s}}),this.setPersistedData=function(i){var a;return i?((a=localStorage)===null||a===void 0||a.setItem(st+i+"/v",1),function(s,d){var c,y="/"+d,f=JSON.stringify(s);s&&d&&((c=localStorage)===null||c===void 0||c.setItem(st+i+y,f),dispatchEvent(new Event("storage")))}):function(){}}(t.id),this.clearPersistedData=function(i){return function(a){var s=st+"/"+i+"/";if(a)localStorage.removeItem(s+a);else for(var d in localStorage)d.includes(s)&&localStorage.removeItem(d)}}(t.id),this.applySettings(e,t),this.state={inputText:"",editing:!1,composing:!1,actions:{},mixMode:{},dropdown:{},flaggedTags:{}},this.value=[],this.listeners={},this.DOM={},this.build(e),Ai.call(this),this.getCSSVars(),this.loadOriginalValues(),this.events.customBinding.call(this),this.events.binding.call(this),e.autofocus&&this.DOM.input.focus(),e.__tagify=this}zt.prototype={_dropdown:Ri,placeCaretAfterNode:je,getSetTagData:le,helpers:{sameStr:ft,removeCollectionProp:Pn,omit:Zt,isObject:Pe,parseHTML:Ft,escapeHTML:He,extend:me,concatWithoutDups:xn,getUID:kn,isNodeTag:ze},customEventsList:["change","add","remove","invalid","input","paste","click","keydown","focus","blur","edit:input","edit:beforeUpdate","edit:updated","edit:start","edit:keydown","dropdown:show","dropdown:hide","dropdown:select","dropdown:updated","dropdown:noMatch","dropdown:scroll"],dataProps:["__isValid","__removed","__originalData","__originalHTML","__tagId"],trim:function(e){return this.settings.trim&&e&&typeof e=="string"?e.trim():e},parseHTML:Ft,templates:Ni,parseTemplate:function(e,t){return Ft((e=this.settings.templates[e]||e).apply(this,t))},set whitelist(e){var t=e&&Array.isArray(e);this.settings.whitelist=t?e:[],this.setPersistedData(t?e:[],"whitelist")},get whitelist(){return this.settings.whitelist},set userInput(e){this.settings.userInput=!!e,this.setContentEditable(!!e)},get userInput(){return this.settings.userInput},generateClassSelectors:function(e){var t=function(r){var i=r;Object.defineProperty(e,i+"Selector",{get:function(){return"."+this[i].split(" ")[0]}})};for(var n in e)t(n)},applySettings:function(e,t){var n,r;jt.templates=this.templates;var i=me({},jt,t.mode=="mix"?{dropdown:{position:"text"}}:{}),a=this.settings=me({},i,t);if(a.disabled=e.hasAttribute("disabled"),a.readonly=a.readonly||e.hasAttribute("readonly"),a.placeholder=He(e.getAttribute("placeholder")||a.placeholder||""),a.required=e.hasAttribute("required"),this.generateClassSelectors(a.classNames),this.isIE&&(a.autoComplete=!1),["whitelist","blacklist"].forEach(function(d){var c=e.getAttribute("data-"+d);c&&Ce(c=c.split(a.delimiters),Array)&&(a[d]=c)}),"autoComplete"in t&&!Pe(t.autoComplete)&&(a.autoComplete=jt.autoComplete,a.autoComplete.enabled=t.autoComplete),a.mode=="mix"&&(a.pattern=a.pattern||/@/,a.autoComplete.rightKey=!0,a.delimiters=t.delimiters||null,a.tagTextProp&&!a.dropdown.searchKeys.includes(a.tagTextProp)&&a.dropdown.searchKeys.push(a.tagTextProp)),e.pattern)try{a.pattern=new RegExp(e.pattern)}catch{}if(a.delimiters){a._delimiters=a.delimiters;try{a.delimiters=new RegExp(this.settings.delimiters,"g")}catch{}}a.disabled&&(a.userInput=!1),this.TEXTS=Un({},Di,a.texts||{}),a.mode=="select"&&(a.dropdown.includeSelectedTags=!0),(a.mode!="select"||!((n=t.dropdown)===null||n===void 0)&&n.enabled)&&a.userInput||(a.dropdown.enabled=0),a.dropdown.appendTarget=((r=t.dropdown)===null||r===void 0?void 0:r.appendTarget)||document.body,a.dropdown.includeSelectedTags===void 0&&(a.dropdown.includeSelectedTags=a.duplicates);var s=this.getPersistedData("whitelist");Array.isArray(s)&&(this.whitelist=Array.isArray(a.whitelist)?xn(a.whitelist,s):s)},getAttributes:function(e){var t,n=this.getCustomAttributes(e),r="";for(t in n)r+=" "+t+(e[t]!==void 0?'="'.concat(n[t],'"'):"");return r},getCustomAttributes:function(e){if(!Pe(e))return"";var t,n={};for(t in e)t.slice(0,2)!="__"&&t!="class"&&e.hasOwnProperty(t)&&e[t]!==void 0&&(n[t]=He(e[t]));return n},setStateSelection:function(){var e=window.getSelection(),t={anchorOffset:e.anchorOffset,anchorNode:e.anchorNode,range:e.getRangeAt&&e.rangeCount&&e.getRangeAt(0)};return this.state.selection=t,t},getCSSVars:function(){var e,t,n,r=getComputedStyle(this.DOM.scope,null);this.CSSVars={tagHideTransition:(e=function(i){if(!i)return{};var a=(i=i.trim().split(" ")[0]).split(/\d+/g).filter(function(s){return s}).pop().trim();return{value:+i.split(a).filter(function(s){return s})[0].trim(),unit:a}}((n="tag-hide-transition",r.getPropertyValue("--"+n))),t=e.value,e.unit=="s"?1e3*t:t)}},build:function(e){var t=this.DOM,n=e.closest("label");this.settings.mixMode.integrated?(t.originalInput=null,t.scope=e,t.input=e):(t.originalInput=e,t.originalInput_tabIndex=e.tabIndex,t.scope=this.parseTemplate("wrapper",[e,this.settings]),t.input=t.scope.querySelector(this.settings.classNames.inputSelector),e.parentNode.insertBefore(t.scope,e),e.tabIndex=-1),n&&n.setAttribute("for","")},destroy:function(){var e;this.events.unbindGlobal.call(this),(e=this.DOM.scope.parentNode)===null||e===void 0||e.removeChild(this.DOM.scope),this.DOM.originalInput.tabIndex=this.DOM.originalInput_tabIndex,delete this.DOM.originalInput.__tagify,this.dropdown.hide(!0),this.removeAllCustomListeners(),clearTimeout(this.dropdownHide__bindEventsTimeout),clearInterval(this.listeners.main.originalInputValueObserverInterval)},loadOriginalValues:function(e){var t,n=this.settings;if(this.state.blockChangeEvent=!0,e===void 0){var r=this.getPersistedData("value");e=r&&!this.DOM.originalInput.value?r:n.mixMode.integrated?this.DOM.input.textContent:this.DOM.originalInput.value}if(this.removeAllTags(),e)if(n.mode=="mix")this.parseMixTags(e),(t=this.DOM.input.lastChild)&&t.tagName=="BR"||this.DOM.input.insertAdjacentHTML("beforeend","<br>");else{try{Ce(JSON.parse(e),Array)&&(e=JSON.parse(e))}catch{}this.addTags(e,!0).forEach(function(i){return i&&i.classList.add(n.classNames.tagNoAnimation)})}else this.postUpdate();this.state.lastOriginalValueReported=n.mixMode.integrated?"":this.DOM.originalInput.value},cloneEvent:function(e){var t={};for(var n in e)n!="path"&&(t[n]=e[n]);return t},loading:function(e){return this.state.isLoading=e,this.DOM.scope.classList[e?"add":"remove"](this.settings.classNames.scopeLoading),this},tagLoading:function(e,t){return e&&e.classList[t?"add":"remove"](this.settings.classNames.tagLoading),this},toggleClass:function(e,t){typeof e=="string"&&this.DOM.scope.classList.toggle(e,t)},toggleScopeValidation:function(e){var t=e===!0||e===void 0;!this.settings.required&&e&&e===this.TEXTS.empty&&(t=!0),this.toggleClass(this.settings.classNames.tagInvalid,!t),this.DOM.scope.title=t?"":e},toggleFocusClass:function(e){this.toggleClass(this.settings.classNames.focus,!!e)},setPlaceholder:function(e){var t=this;["data","aria"].forEach(function(n){return t.DOM.input.setAttribute("".concat(n,"-placeholder"),e)})},triggerChangeEvent:function(){if(!this.settings.mixMode.integrated){var e=this.DOM.originalInput,t=this.state.lastOriginalValueReported!==e.value,n=new CustomEvent("change",{bubbles:!0});t&&(this.state.lastOriginalValueReported=e.value,n.simulated=!0,e._valueTracker&&e._valueTracker.setValue(Math.random()),e.dispatchEvent(n),this.trigger("change",this.state.lastOriginalValueReported),e.value=this.state.lastOriginalValueReported)}},events:Mi,fixFirefoxLastTagNoCaret:function(){},setRangeAtStartEnd:function(e,t){if(t){e=typeof e=="number"?e:!!e,t=t.lastChild||t;var n=document.getSelection();if(Ce(n.focusNode,Element)&&!this.DOM.input.contains(n.focusNode))return!0;try{n.rangeCount>=1&&["Start","End"].forEach(function(r){return n.getRangeAt(0)["set"+r](t,e||t.length)})}catch(r){console.warn(r)}}},insertAfterTag:function(e,t){if(t=t||this.settings.mixMode.insertAfterTag,e&&e.parentNode&&t)return t=typeof t=="string"?document.createTextNode(t):t,e.parentNode.insertBefore(t,e.nextSibling),t},editTagChangeDetected:function(e){var t=e.__originalData;for(var n in t)if(!this.dataProps.includes(n)&&e[n]!=t[n])return!0;return!1},getTagTextNode:function(e){return e.querySelector(this.settings.classNames.tagTextSelector)},setTagTextNode:function(e,t){this.getTagTextNode(e).innerHTML=He(t)},editTag:function(e,t){var n=this;e=e||this.getLastTag(),t=t||{};var r=this.settings,i=this.getTagTextNode(e),a=this.getNodeIndex(e),s=le(e),d=this.events.callbacks,c=!0,y=r.mode=="select";if(!y&&this.dropdown.hide(),i){if(!Ce(s,Object)||!("editable"in s)||s.editable)return s=le(e,{__originalData:me({},s),__originalHTML:e.cloneNode(!0)}),le(s.__originalHTML,s.__originalData),i.setAttribute("contenteditable",!0),e.classList.add(r.classNames.tagEditing),this.events.callbacks.onEditTagFocus.call(this,e),i.addEventListener("click",d.onEditTagClick.bind(this,e)),i.addEventListener("blur",d.onEditTagBlur.bind(this,this.getTagTextNode(e))),i.addEventListener("input",d.onEditTagInput.bind(this,i)),i.addEventListener("paste",d.onEditTagPaste.bind(this,i)),i.addEventListener("keydown",function(f){return d.onEditTagkeydown.call(n,f,e)}),i.addEventListener("compositionstart",d.onCompositionStart.bind(this)),i.addEventListener("compositionend",d.onCompositionEnd.bind(this)),t.skipValidation||(c=this.editTagToggleValidity(e)),i.originalIsValid=c,this.trigger("edit:start",{tag:e,index:a,data:s,isValid:c}),i.focus(),!y&&this.setRangeAtStartEnd(!1,i),r.dropdown.enabled===0&&!y&&this.dropdown.show(),this.state.hasFocus=!0,this}else Ue.warn("Cannot find element in Tag template: .",r.classNames.tagTextSelector)},editTagToggleValidity:function(e,t){var n;if(t=t||le(e))return(n=!("__isValid"in t)||t.__isValid===!0)||this.removeTagsFromValue(e),this.update(),e.classList.toggle(this.settings.classNames.tagNotAllowed,!n),t.__isValid=n,t.__isValid;Ue.warn("tag has no data: ",e,t)},onEditTagDone:function(e,t){e=e||this.state.editing.scope,t=t||{};var n,r,i=this.settings,a={tag:e,index:this.getNodeIndex(e),previousData:le(e),data:t};this.trigger("edit:beforeUpdate",a,{cloneData:!1}),this.state.editing=!1,delete t.__originalData,delete t.__originalHTML,e&&e.parentNode&&(((r=t[i.tagTextProp])!==void 0?!((n=(r+="").trim)===null||n===void 0)&&n.call(r):!(i.tagTextProp in t)&&t.value)?(e=this.replaceTag(e,t),this.editTagToggleValidity(e,t),i.a11y.focusableTags?e.focus():i.mode!="select"&&je(e)):this.removeTags(e)),this.trigger("edit:updated",a),i.dropdown.closeOnSelect&&this.dropdown.hide(),this.settings.keepInvalidTags&&this.reCheckInvalidTags()},replaceTag:function(e,t){t&&t.value!==""&&t.value!==void 0||(t=e.__tagifyTagData),t.__isValid&&t.__isValid!=1&&me(t,this.getInvalidTagAttrs(t,t.__isValid));var n=this.createTagElem(t);return e.parentNode.replaceChild(n,e),this.updateValueByDOMTags(),n},updateValueByDOMTags:function(){var e=this;this.value.length=0;var t=this.settings.classNames,n=[t.tagNotAllowed.split(" ")[0],t.tagHide];[].forEach.call(this.getTagElms(),function(r){vt(r.classList).some(function(i){return n.includes(i)})||e.value.push(le(r))}),this.update(),this.dropdown.refilter()},injectAtCaret:function(e,t){var n;if(t=t||((n=this.state.selection)===null||n===void 0?void 0:n.range),typeof e=="string"&&(e=document.createTextNode(e)),!t&&e)return this.appendMixTags(e),this;var r=Rr(e,t);return this.setRangeAtStartEnd(!1,r),this.updateValueByDOMTags(),this.update(),this},input:{set:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",t=!(arguments.length>1&&arguments[1]!==void 0)||arguments[1],n=this.settings,r=n.dropdown.closeOnSelect;this.state.inputText=e,t&&(this.DOM.input.innerHTML=He(""+e),e&&this.toggleClass(n.classNames.empty,!this.DOM.input.innerHTML)),!e&&r&&this.dropdown.hide.bind(this),this.input.autocomplete.suggest.call(this),this.input.validate.call(this)},raw:function(){return this.DOM.input.textContent},validate:function(){var e=!this.state.inputText||this.validateTag({value:this.state.inputText})===!0;return this.DOM.input.classList.toggle(this.settings.classNames.inputInvalid,!e),e},normalize:function(e,t){var n=e||this.DOM.input,r=[];n.childNodes.forEach(function(i){return i.nodeType==3&&r.push(i.nodeValue)}),r=r.join(`
`);try{r=r.replace(/(?:\r\n|\r|\n)/g,this.settings.delimiters.source.charAt(0))}catch{}return r=r.replace(/\s/g," "),t!=null&&t.trim?this.trim(r):r},autocomplete:{suggest:function(e){if(this.settings.autoComplete.enabled){typeof(e=e||{value:""})!="object"&&(e={value:e});var t=this.dropdown.getMappedValue(e);if(typeof t!="number"){var n=this.state.inputText.toLowerCase(),r=t.substr(0,this.state.inputText.length).toLowerCase(),i=t.substring(this.state.inputText.length);t&&this.state.inputText&&r==n?(this.DOM.input.setAttribute("data-suggest",i),this.state.inputSuggestion=e):(this.DOM.input.removeAttribute("data-suggest"),delete this.state.inputSuggestion)}}},set:function(e){var t=this.DOM.input.getAttribute("data-suggest"),n=e||(t?this.state.inputText+t:null);return!!n&&(this.settings.mode=="mix"?this.replaceTextWithNode(document.createTextNode(this.state.tag.prefix+n)):(this.input.set.call(this,n),this.setRangeAtStartEnd(!1,this.DOM.input)),this.input.autocomplete.suggest.call(this),this.dropdown.hide(),!0)}}},getTagIdx:function(e){return this.value.findIndex(function(t){return t.__tagId==(e||{}).__tagId})},getNodeIndex:function(e){var t=0;if(e)for(;e=e.previousElementSibling;)t++;return t},getTagElms:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r="."+vt(this.settings.classNames.tag.split(" ")).concat(vt(t)).join(".");return[].slice.call(this.DOM.scope.querySelectorAll(r))},getLastTag:function(){var e=this.settings.classNames,t=this.DOM.scope.querySelectorAll("".concat(e.tagSelector,":not(.").concat(e.tagHide,"):not([readonly])"));return t[t.length-1]},isTagDuplicate:function(e,t,n){var r=0,i=!0,a=!1,s=void 0;try{for(var d,c=this.value[Symbol.iterator]();!(i=(d=c.next()).done);i=!0){var y=d.value;ft(this.trim(""+e),y.value,t)&&n!=y.__tagId&&r++}}catch(f){a=!0,s=f}finally{try{i||c.return==null||c.return()}finally{if(a)throw s}}return r},getTagIndexByValue:function(e){var t=this,n=[],r=this.settings.dropdown.caseSensitive;return this.getTagElms().forEach(function(i,a){i.__tagifyTagData&&ft(t.trim(i.__tagifyTagData.value),e,r)&&n.push(a)}),n},getTagElmByValue:function(e){var t=this.getTagIndexByValue(e)[0];return this.getTagElms()[t]},flashTag:function(e){var t=this;e&&(e.classList.add(this.settings.classNames.tagFlash),setTimeout(function(){e.classList.remove(t.settings.classNames.tagFlash)},100))},isTagBlacklisted:function(e){return e=this.trim(e.toLowerCase()),this.settings.blacklist.filter(function(t){return(""+t).toLowerCase()==e}).length},isTagWhitelisted:function(e){return!!this.getWhitelistItem(e)},getWhitelistItem:function(e,t,n){t=t||"value";var r,i=this.settings;return(n=n||i.whitelist).some(function(a){var s=typeof a=="object"?a[t]||a.value:a;if(ft(s,e,i.dropdown.caseSensitive,i.trim))return r=typeof a=="object"?a:{value:a},!0}),r||t!="value"||i.tagTextProp=="value"||(r=this.getWhitelistItem(e,i.tagTextProp,n)),r},validateTag:function(e){var t=this.settings,n="value"in e?"value":t.tagTextProp,r=this.trim(e[n]+"");return(e[n]+"").trim()?t.mode!="mix"&&t.pattern&&Ce(t.pattern,RegExp)&&!t.pattern.test(r)?this.TEXTS.pattern:!t.duplicates&&this.isTagDuplicate(r,t.dropdown.caseSensitive,e.__tagId)?this.TEXTS.duplicate:this.isTagBlacklisted(r)||t.enforceWhitelist&&!this.isTagWhitelisted(r)?this.TEXTS.notAllowed:!t.validate||t.validate(e):this.TEXTS.empty},getInvalidTagAttrs:function(e,t){return{"aria-invalid":!0,class:"".concat(e.class||""," ").concat(this.settings.classNames.tagNotAllowed).trim(),title:t}},hasMaxTags:function(){return this.value.length>=this.settings.maxTags&&this.TEXTS.exceed},setReadonly:function(e,t){var n=this.settings;this.DOM.scope.contains(document.activeElement)&&document.activeElement.blur(),n[t||"readonly"]=e,this.DOM.scope[(e?"set":"remove")+"Attribute"](t||"readonly",!0),this.settings.userInput=!0,this.setContentEditable(!e)},setContentEditable:function(e){this.DOM.scope.querySelectorAll("[contenteditable]").forEach(function(t){t.contentEditable=e,t.tabIndex=e?0:-1})},setDisabled:function(e){this.setReadonly(e,"disabled")},normalizeTags:function(e){var t=this,n=this.settings,r=n.whitelist,i=n.delimiters,a=n.mode,s=n.tagTextProp,d=[],c=!!r&&Ce(r[0],Object),y=Array.isArray(e),f=y&&e[0].value,u=function(o){return(o+"").split(i).reduce(function(l,h){var v,E=t.trim(h);return E&&l.push((Jt(v={},s,E),Jt(v,"value",E),v)),l},[])};if(typeof e=="number"&&(e=e.toString()),typeof e=="string"){if(!e.trim())return[];e=u(e)}else y&&(e=e.reduce(function(o,l){if(Pe(l)){var h=me({},l);s in h||(s="value"),h[s]=t.trim(h[s]),(h[s]||h[s]===0)&&o.push(h)}else if(l!=null&&l!==""&&l!==void 0){var v;(v=o).push.apply(v,vt(u(l)))}return o},[]));return c&&!f&&(e.forEach(function(o){var l=d.map(function(E){return E.value}),h=t.dropdown.filterListItems.call(t,o[s],{exact:!0});t.settings.duplicates||(h=h.filter(function(E){return!l.includes(E.value)}));var v=h.length>1?t.getWhitelistItem(o[s],s,h):h[0];v&&Ce(v,Object)?d.push(v):a!="mix"&&(o.value==null&&(o.value=o[s]),d.push(o))}),d.length&&(e=d)),e},parseMixTags:function(e){var t=this,n=this.settings,r=n.mixTagsInterpolator,i=n.duplicates,a=n.transformTag,s=n.enforceWhitelist,d=n.maxTags,c=n.tagTextProp,y=[];e=e.split(r[0]).map(function(u,o){var l,h,v,E=u.split(r[1]),p=E[0],m=y.length==d;try{if(p==+p)throw Error;h=JSON.parse(p)}catch{h=t.normalizeTags(p)[0]||{value:p}}if(a.call(t,h),m||!(E.length>1)||s&&!t.isTagWhitelisted(h.value)||!i&&t.isTagDuplicate(h.value)){if(u)return o?r[0]+u:u}else h[l=h[c]?c:"value"]=t.trim(h[l]),v=t.createTagElem(h),y.push(h),v.classList.add(t.settings.classNames.tagNoAnimation),E[0]=v.outerHTML,t.value.push(h);return E.join("")}).join(""),this.DOM.input.innerHTML=e,this.DOM.input.appendChild(document.createTextNode("")),this.DOM.input.normalize();var f=this.getTagElms();return f.forEach(function(u,o){return le(u,y[o])}),this.update({withoutChangeEvent:!0}),Dr(f,this.state.hasFocus),e},replaceTextWithNode:function(e,t){if(this.state.tag||t){t=t||this.state.tag.prefix+this.state.tag.value;var n,r,i=this.state.selection||window.getSelection(),a=i.anchorNode,s=this.state.tag.delimiters?this.state.tag.delimiters.length:0;return a.splitText(i.anchorOffset-s),(n=a.nodeValue.lastIndexOf(t))==-1||(r=a.splitText(n),e&&a.parentNode.replaceChild(e,r)),!0}},prepareNewTagNode:function(e,t){t=t||{};var n=this.settings,r=[],i={},a=Object.assign({},e,{value:e.value+""});if(e=Object.assign({},a),n.transformTag.call(this,e),e.__isValid=this.hasMaxTags()||this.validateTag(e),e.__isValid!==!0){if(t.skipInvalid)return;if(me(i,this.getInvalidTagAttrs(e,e.__isValid),{__preInvalidData:a}),e.__isValid==this.TEXTS.duplicate&&this.flashTag(this.getTagElmByValue(e.value)),!n.createInvalidTags)return void r.push(e.value)}return"readonly"in e&&(e.readonly?i["aria-readonly"]=!0:delete e.readonly),{tagElm:this.createTagElem(e,i),tagData:e,aggregatedInvalidInput:r}},postProcessNewTagNode:function(e,t){var n=this,r=this.settings,i=t.__isValid;i&&i===!0?this.value.push(t):(this.trigger("invalid",{data:t,index:this.value.length,tag:e,message:i}),r.keepInvalidTags||setTimeout(function(){return n.removeTags(e,!0)},1e3)),this.dropdown.position()},selectTag:function(e,t){var n=this;if(!this.settings.enforceWhitelist||this.isTagWhitelisted(t.value)){this.state.actions.selectOption&&setTimeout(function(){return n.setRangeAtStartEnd(!1,n.DOM.input)});var r=this.getLastTag();return r?this.replaceTag(r,t):this.appendTag(e),this.value[0]=t,this.update(),this.trigger("add",{tag:e,data:t}),[e]}},addEmptyTag:function(e){var t=me({value:""},e||{}),n=this.createTagElem(t);le(n,t),this.appendTag(n),this.editTag(n,{skipValidation:!0}),this.toggleFocusClass(!0)},addTags:function(e,t,n){var r=this,i=[],a=this.settings,s=[],d=document.createDocumentFragment(),c=[];if(!e||e.length==0)return i;switch(e=this.normalizeTags(e),a.mode){case"mix":return this.addMixTags(e);case"select":t=!1,this.removeAllTags()}return this.DOM.input.removeAttribute("style"),e.forEach(function(y){var f=r.prepareNewTagNode(y,{skipInvalid:n||a.skipInvalid});if(f){var u=f.tagElm;if(y=f.tagData,s=f.aggregatedInvalidInput,i.push(u),a.mode=="select")return r.selectTag(u,y);d.appendChild(u),r.postProcessNewTagNode(u,y),c.push({tagElm:u,tagData:y})}}),this.appendTag(d),c.forEach(function(y){var f=y.tagElm,u=y.tagData;return r.trigger("add",{tag:f,index:r.getTagIdx(u),data:u})}),this.update(),e.length&&t&&(this.input.set.call(this,a.createInvalidTags?"":s.join(a._delimiters)),this.setRangeAtStartEnd(!1,this.DOM.input)),this.dropdown.refilter(),i},addMixTags:function(e){var t=this;if((e=this.normalizeTags(e))[0].prefix||this.state.tag)return this.prefixedTextToTag(e[0]);var n=document.createDocumentFragment();return e.forEach(function(r){var i=t.prepareNewTagNode(r);n.appendChild(i.tagElm),t.insertAfterTag(i.tagElm),t.postProcessNewTagNode(i.tagElm,i.tagData)}),this.appendMixTags(n),n.children},appendMixTags:function(e){var t=!!this.state.selection;t?this.injectAtCaret(e):(this.DOM.input.focus(),(t=this.setStateSelection()).range.setStart(this.DOM.input,t.range.endOffset),t.range.setEnd(this.DOM.input,t.range.endOffset),this.DOM.input.appendChild(e),this.updateValueByDOMTags(),this.update())},prefixedTextToTag:function(e){var t,n,r,i=this,a=this.settings,s=(t=this.state.tag)===null||t===void 0?void 0:t.delimiters;if(e.prefix=e.prefix||this.state.tag?this.state.tag.prefix:(a.pattern.source||a.pattern)[0],r=this.prepareNewTagNode(e),n=r.tagElm,this.replaceTextWithNode(n)||this.DOM.input.appendChild(n),setTimeout(function(){return n.classList.add(i.settings.classNames.tagNoAnimation)},300),this.update(),!s){var d=this.insertAfterTag(n)||n;setTimeout(je,0,d)}return this.state.tag=null,this.postProcessNewTagNode(n,r.tagData),n},appendTag:function(e){var t=this.DOM,n=t.input;t.scope.insertBefore(e,n)},createTagElem:function(e,t){e.__tagId=kn();var n,r=me({},e,Un({value:He(e.value+"")},t));return function(i){for(var a,s=document.createNodeIterator(i,NodeFilter.SHOW_TEXT,null,!1);a=s.nextNode();)a.textContent.trim()||a.parentNode.removeChild(a)}(n=this.parseTemplate("tag",[r,this])),le(n,e),n},reCheckInvalidTags:function(){var e=this,t=this.settings;this.getTagElms(t.classNames.tagNotAllowed).forEach(function(n,r){var i=le(n),a=e.hasMaxTags(),s=e.validateTag(i),d=s===!0&&!a;if(t.mode=="select"&&e.toggleScopeValidation(s),d)return i=i.__preInvalidData?i.__preInvalidData:{value:i.value},e.replaceTag(n,i);n.title=a||s})},removeTags:function(e,t,n){var r,i=this,a=this.settings;if(e=e&&Ce(e,HTMLElement)?[e]:Ce(e,Array)?e:e?[e]:[this.getLastTag()].filter(function(s){return s}),r=e.reduce(function(s,d){d&&typeof d=="string"&&(d=i.getTagElmByValue(d));var c=le(d);return d&&c&&!c.readonly&&s.push({node:d,idx:i.getTagIdx(c),data:le(d,{__removed:!0})}),s},[]),n=typeof n=="number"?n:this.CSSVars.tagHideTransition,a.mode=="select"&&(n=0,this.input.set.call(this)),r.length==1&&a.mode!="select"&&r[0].node.classList.contains(a.classNames.tagNotAllowed)&&(t=!0),r.length)return a.hooks.beforeRemoveTag(r,{tagify:this}).then(function(){var s=function(d){d.node.parentNode&&(d.node.parentNode.removeChild(d.node),t?a.keepInvalidTags&&this.trigger("remove",{tag:d.node,index:d.idx}):(this.trigger("remove",{tag:d.node,index:d.idx,data:d.data}),this.dropdown.refilter(),this.dropdown.position(),this.DOM.input.normalize(),a.keepInvalidTags&&this.reCheckInvalidTags()))};n&&n>10&&r.length==1?(function(d){d.node.style.width=parseFloat(window.getComputedStyle(d.node).width)+"px",document.body.clientTop,d.node.classList.add(a.classNames.tagHide),setTimeout(s.bind(this),n,d)}).call(i,r[0]):r.forEach(s.bind(i)),t||(i.removeTagsFromValue(r.map(function(d){return d.node})),i.update(),a.mode=="select"&&a.userInput&&i.setContentEditable(!0))}).catch(function(s){})},removeTagsFromDOM:function(){this.getTagElms().forEach(function(e){return e.remove()})},removeTagsFromValue:function(e){var t=this;(e=Array.isArray(e)?e:[e]).forEach(function(n){var r=le(n),i=t.getTagIdx(r);i>-1&&t.value.splice(i,1)})},removeAllTags:function(e){var t=this;e=e||{},this.value=[],this.settings.mode=="mix"?this.DOM.input.innerHTML="":this.removeTagsFromDOM(),this.dropdown.refilter(),this.dropdown.position(),this.state.dropdown.visible&&setTimeout(function(){t.DOM.input.focus()}),this.settings.mode=="select"&&(this.input.set.call(this),this.settings.userInput&&this.setContentEditable(!0)),this.update(e)},postUpdate:function(){this.state.blockChangeEvent=!1;var e,t,n=this.settings,r=n.classNames,i=n.mode=="mix"?n.mixMode.integrated?this.DOM.input.textContent:this.DOM.originalInput.value.trim():this.value.length+this.input.raw.call(this).length;this.toggleClass(r.hasMaxTags,this.value.length>=n.maxTags),this.toggleClass(r.hasNoTags,!this.value.length),this.toggleClass(r.empty,!i),n.mode=="select"&&this.toggleScopeValidation((t=this.value)===null||t===void 0||(e=t[0])===null||e===void 0?void 0:e.__isValid)},setOriginalInputValue:function(e){var t=this.DOM.originalInput;this.settings.mixMode.integrated||(t.value=e,t.tagifyValue=t.value,this.setPersistedData(e,"value"))},update:function(e){clearTimeout(this.debouncedUpdateTimeout),this.debouncedUpdateTimeout=setTimeout((function(){var t=this.getInputValue();this.setOriginalInputValue(t),this.settings.onChangeAfterBlur&&(e||{}).withoutChangeEvent||this.state.blockChangeEvent||this.triggerChangeEvent(),this.postUpdate()}).bind(this),100),this.events.bindOriginaInputListener.call(this,100)},getInputValue:function(){var e=this.getCleanValue();return this.settings.mode=="mix"?this.getMixedTagsAsString(e):e.length?this.settings.originalInputValueFormat?this.settings.originalInputValueFormat(e):JSON.stringify(e):""},getCleanValue:function(e){return Pn(e||this.value,this.dataProps)},getMixedTagsAsString:function(){var e="",t=this,n=this.settings,r=n.originalInputValueFormat||JSON.stringify,i=n.mixTagsInterpolator;return function a(s){s.childNodes.forEach(function(d){if(d.nodeType==1){var c=le(d);if(d.tagName=="BR"&&(e+=`\r
`),c&&ze.call(t,d)){if(c.__removed)return;e+=i[0]+r(Zt(c,t.dataProps))+i[1]}else d.getAttribute("style")||["B","I","U"].includes(d.tagName)?e+=d.textContent:d.tagName!="DIV"&&d.tagName!="P"||(e+=`\r
`,a(d))}else e+=d.textContent})}(this.DOM.input),e}},zt.prototype.removeTag=zt.prototype.removeTags;/*!
 * FilePond 4.32.7
 * Licensed under MIT, https://opensource.org/licenses/MIT/
 * Please visit https://pqina.nl/filepond/ for details.
 */const xi=e=>e instanceof HTMLElement,Ci=(e,t=[],n=[])=>{const r={...e},i=[],a=[],s=()=>({...r}),d=()=>{const h=[...i];return i.length=0,h},c=()=>{const h=[...a];a.length=0,h.forEach(({type:v,data:E})=>{y(v,E)})},y=(h,v,E)=>{if(E&&!document.hidden){a.push({type:h,data:v});return}l[h]&&l[h](v),i.push({type:h,data:v})},f=(h,...v)=>o[h]?o[h](...v):null,u={getState:s,processActionQueue:d,processDispatchQueue:c,dispatch:y,query:f};let o={};t.forEach(h=>{o={...h(r),...o}});let l={};return n.forEach(h=>{l={...h(y,f,r),...l}}),u},ki=(e,t,n)=>{if(typeof n=="function"){e[t]=n;return}Object.defineProperty(e,t,{...n})},ce=(e,t)=>{for(const n in e)e.hasOwnProperty(n)&&t(n,e[n])},Ge=e=>{const t={};return ce(e,n=>{ki(t,n,e[n])}),t},ye=(e,t,n=null)=>{if(n===null)return e.getAttribute(t)||e.hasAttribute(t);e.setAttribute(t,n)},Bi="http://www.w3.org/2000/svg",qi=["svg","path"],Gn=e=>qi.includes(e),At=(e,t,n={})=>{typeof t=="object"&&(n=t,t=null);const r=Gn(e)?document.createElementNS(Bi,e):document.createElement(e);return t&&(Gn(e)?ye(r,"class",t):r.className=t),ce(n,(i,a)=>{ye(r,i,a)}),r},Fi=e=>(t,n)=>{typeof n<"u"&&e.children[n]?e.insertBefore(t,e.children[n]):e.appendChild(t)},ji=(e,t)=>(n,r)=>(typeof r<"u"?t.splice(r,0,n):t.push(n),n),Ui=(e,t)=>n=>(t.splice(t.indexOf(n),1),n.element.parentNode&&e.removeChild(n.element),n),Gi=typeof window<"u"&&typeof window.document<"u",Nr=()=>Gi,Vi=Nr()?At("svg"):{},Hi="children"in Vi?e=>e.children.length:e=>e.childNodes.length,Lr=(e,t,n,r)=>{const i=n[0]||e.left,a=n[1]||e.top,s=i+e.width,d=a+e.height*(r[1]||1),c={element:{...e},inner:{left:e.left,top:e.top,right:e.right,bottom:e.bottom},outer:{left:i,top:a,right:s,bottom:d}};return t.filter(y=>!y.isRectIgnored()).map(y=>y.rect).forEach(y=>{Vn(c.inner,{...y.inner}),Vn(c.outer,{...y.outer})}),Hn(c.inner),c.outer.bottom+=c.element.marginBottom,c.outer.right+=c.element.marginRight,Hn(c.outer),c},Vn=(e,t)=>{t.top+=e.top,t.right+=e.left,t.bottom+=e.top,t.left+=e.left,t.bottom>e.bottom&&(e.bottom=t.bottom),t.right>e.right&&(e.right=t.right)},Hn=e=>{e.width=e.right-e.left,e.height=e.bottom-e.top},We=e=>typeof e=="number",zi=(e,t,n,r=.001)=>Math.abs(e-t)<r&&Math.abs(n)<r,Wi=({stiffness:e=.5,damping:t=.75,mass:n=10}={})=>{let r=null,i=null,a=0,s=!1;const y=Ge({interpolate:(f,u)=>{if(s)return;if(!(We(r)&&We(i))){s=!0,a=0;return}const o=-(i-r)*e;a+=o/n,i+=a,a*=t,zi(i,r,a)||u?(i=r,a=0,s=!0,y.onupdate(i),y.oncomplete(i)):y.onupdate(i)},target:{set:f=>{if(We(f)&&!We(i)&&(i=f),r===null&&(r=f,i=f),r=f,i===r||typeof r>"u"){s=!0,a=0,y.onupdate(i),y.oncomplete(i);return}s=!1},get:()=>r},resting:{get:()=>s},onupdate:f=>{},oncomplete:f=>{}});return y},Yi=e=>e<.5?2*e*e:-1+(4-2*e)*e,$i=({duration:e=500,easing:t=Yi,delay:n=0}={})=>{let r=null,i,a,s=!0,d=!1,c=null;const f=Ge({interpolate:(u,o)=>{s||c===null||(r===null&&(r=u),!(u-r<n)&&(i=u-r-n,i>=e||o?(i=1,a=d?0:1,f.onupdate(a*c),f.oncomplete(a*c),s=!0):(a=i/e,f.onupdate((i>=0?t(d?1-a:a):0)*c))))},target:{get:()=>d?0:c,set:u=>{if(c===null){c=u,f.onupdate(u),f.oncomplete(u);return}u<c?(c=1,d=!0):(d=!1,c=u),s=!1,r=null}},resting:{get:()=>s},onupdate:u=>{},oncomplete:u=>{}});return f},zn={spring:Wi,tween:$i},Ki=(e,t,n)=>{const r=e[t]&&typeof e[t][n]=="object"?e[t][n]:e[t]||e,i=typeof r=="string"?r:r.type,a=typeof r=="object"?{...r}:{};return zn[i]?zn[i](a):null},vn=(e,t,n,r=!1)=>{t=Array.isArray(t)?t:[t],t.forEach(i=>{e.forEach(a=>{let s=a,d=()=>n[a],c=y=>n[a]=y;typeof a=="object"&&(s=a.key,d=a.getter||d,c=a.setter||c),!(i[s]&&!r)&&(i[s]={get:d,set:c})})})},Xi=({mixinConfig:e,viewProps:t,viewInternalAPI:n,viewExternalAPI:r})=>{const i={...t},a=[];return ce(e,(s,d)=>{const c=Ki(d);if(!c)return;c.onupdate=f=>{t[s]=f},c.target=i[s],vn([{key:s,setter:f=>{c.target!==f&&(c.target=f)},getter:()=>t[s]}],[n,r],t,!0),a.push(c)}),{write:s=>{let d=document.hidden,c=!0;return a.forEach(y=>{y.resting||(c=!1),y.interpolate(s,d)}),c},destroy:()=>{}}},Qi=e=>(t,n)=>{e.addEventListener(t,n)},Zi=e=>(t,n)=>{e.removeEventListener(t,n)},Ji=({mixinConfig:e,viewProps:t,viewInternalAPI:n,viewExternalAPI:r,viewState:i,view:a})=>{const s=[],d=Qi(a.element),c=Zi(a.element);return r.on=(y,f)=>{s.push({type:y,fn:f}),d(y,f)},r.off=(y,f)=>{s.splice(s.findIndex(u=>u.type===y&&u.fn===f),1),c(y,f)},{write:()=>!0,destroy:()=>{s.forEach(y=>{c(y.type,y.fn)})}}},eo=({mixinConfig:e,viewProps:t,viewExternalAPI:n})=>{vn(e,n,t)},_e=e=>e!=null,to={opacity:1,scaleX:1,scaleY:1,translateX:0,translateY:0,rotateX:0,rotateY:0,rotateZ:0,originX:0,originY:0},no=({mixinConfig:e,viewProps:t,viewInternalAPI:n,viewExternalAPI:r,view:i})=>{const a={...t},s={};vn(e,[n,r],t);const d=()=>[t.translateX||0,t.translateY||0],c=()=>[t.scaleX||0,t.scaleY||0],y=()=>i.rect?Lr(i.rect,i.childViews,d(),c()):null;return n.rect={get:y},r.rect={get:y},e.forEach(f=>{t[f]=typeof a[f]>"u"?to[f]:a[f]}),{write:()=>{if(ro(s,t))return io(i.element,t),Object.assign(s,{...t}),!0},destroy:()=>{}}},ro=(e,t)=>{if(Object.keys(e).length!==Object.keys(t).length)return!0;for(const n in t)if(t[n]!==e[n])return!0;return!1},io=(e,{opacity:t,perspective:n,translateX:r,translateY:i,scaleX:a,scaleY:s,rotateX:d,rotateY:c,rotateZ:y,originX:f,originY:u,width:o,height:l})=>{let h="",v="";(_e(f)||_e(u))&&(v+=`transform-origin: ${f||0}px ${u||0}px;`),_e(n)&&(h+=`perspective(${n}px) `),(_e(r)||_e(i))&&(h+=`translate3d(${r||0}px, ${i||0}px, 0) `),(_e(a)||_e(s))&&(h+=`scale3d(${_e(a)?a:1}, ${_e(s)?s:1}, 1) `),_e(y)&&(h+=`rotateZ(${y}rad) `),_e(d)&&(h+=`rotateX(${d}rad) `),_e(c)&&(h+=`rotateY(${c}rad) `),h.length&&(v+=`transform:${h};`),_e(t)&&(v+=`opacity:${t};`,t===0&&(v+="visibility:hidden;"),t<1&&(v+="pointer-events:none;")),_e(l)&&(v+=`height:${l}px;`),_e(o)&&(v+=`width:${o}px;`);const E=e.elementCurrentStyle||"";(v.length!==E.length||v!==E)&&(e.style.cssText=v,e.elementCurrentStyle=v)},oo={styles:no,listeners:Ji,animations:Xi,apis:eo},Wn=(e={},t={},n={})=>(t.layoutCalculated||(e.paddingTop=parseInt(n.paddingTop,10)||0,e.marginTop=parseInt(n.marginTop,10)||0,e.marginRight=parseInt(n.marginRight,10)||0,e.marginBottom=parseInt(n.marginBottom,10)||0,e.marginLeft=parseInt(n.marginLeft,10)||0,t.layoutCalculated=!0),e.left=t.offsetLeft||0,e.top=t.offsetTop||0,e.width=t.offsetWidth||0,e.height=t.offsetHeight||0,e.right=e.left+e.width,e.bottom=e.top+e.height,e.scrollTop=t.scrollTop,e.hidden=t.offsetParent===null,e),pe=({tag:e="div",name:t=null,attributes:n={},read:r=()=>{},write:i=()=>{},create:a=()=>{},destroy:s=()=>{},filterFrameActionsForChild:d=(l,h)=>h,didCreateView:c=()=>{},didWriteView:y=()=>{},ignoreRect:f=!1,ignoreRectUpdate:u=!1,mixins:o=[]}={})=>(l,h={})=>{const v=At(e,`filepond--${t}`,n),E=window.getComputedStyle(v,null),p=Wn();let m=null,_=!1;const g=[],b=[],A={},S={},I=[i],N=[r],w=[s],T=()=>v,O=()=>g.concat(),L=()=>A,D=P=>(j,H)=>j(P,H),M=()=>m||(m=Lr(p,g,[0,0],[1,1]),m),x=()=>E,B=()=>{m=null,g.forEach(H=>H._read()),!(u&&p.width&&p.height)&&Wn(p,v,E);const j={root:G,props:h,rect:p};N.forEach(H=>H(j))},V=(P,j,H)=>{let z=j.length===0;return I.forEach(Y=>{Y({props:h,root:G,actions:j,timestamp:P,shouldOptimize:H})===!1&&(z=!1)}),b.forEach(Y=>{Y.write(P)===!1&&(z=!1)}),g.filter(Y=>!!Y.element.parentNode).forEach(Y=>{Y._write(P,d(Y,j),H)||(z=!1)}),g.forEach((Y,Z)=>{Y.element.parentNode||(G.appendChild(Y.element,Z),Y._read(),Y._write(P,d(Y,j),H),z=!1)}),_=z,y({props:h,root:G,actions:j,timestamp:P}),z},F=()=>{b.forEach(P=>P.destroy()),w.forEach(P=>{P({root:G,props:h})}),g.forEach(P=>P._destroy())},k={element:{get:T},style:{get:x},childViews:{get:O}},R={...k,rect:{get:M},ref:{get:L},is:P=>t===P,appendChild:Fi(v),createChildView:D(l),linkView:P=>(g.push(P),P),unlinkView:P=>{g.splice(g.indexOf(P),1)},appendChildView:ji(v,g),removeChildView:Ui(v,g),registerWriter:P=>I.push(P),registerReader:P=>N.push(P),registerDestroyer:P=>w.push(P),invalidateLayout:()=>v.layoutCalculated=!1,dispatch:l.dispatch,query:l.query},C={element:{get:T},childViews:{get:O},rect:{get:M},resting:{get:()=>_},isRectIgnored:()=>f,_read:B,_write:V,_destroy:F},q={...k,rect:{get:()=>p}};Object.keys(o).sort((P,j)=>P==="styles"?1:j==="styles"?-1:0).forEach(P=>{const j=oo[P]({mixinConfig:o[P],viewProps:h,viewState:S,viewInternalAPI:R,viewExternalAPI:C,view:Ge(q)});j&&b.push(j)});const G=Ge(R);a({root:G,props:h});const U=Hi(v);return g.forEach((P,j)=>{G.appendChild(P.element,U+j)}),c(G),Ge(C)},ao=(e,t,n=60)=>{const r="__framePainter";if(window[r]){window[r].readers.push(e),window[r].writers.push(t);return}window[r]={readers:[e],writers:[t]};const i=window[r],a=1e3/n;let s=null,d=null,c=null,y=null;const f=()=>{document.hidden?(c=()=>window.setTimeout(()=>u(performance.now()),a),y=()=>window.clearTimeout(d)):(c=()=>window.requestAnimationFrame(u),y=()=>window.cancelAnimationFrame(d))};document.addEventListener("visibilitychange",()=>{y&&y(),f(),u(performance.now())});const u=o=>{d=c(u),s||(s=o);const l=o-s;l<=a||(s=o-l%a,i.readers.forEach(h=>h()),i.writers.forEach(h=>h(o)))};return f(),u(performance.now()),{pause:()=>{y(d)}}},Te=(e,t)=>({root:n,props:r,actions:i=[],timestamp:a,shouldOptimize:s})=>{i.filter(d=>e[d.type]).forEach(d=>e[d.type]({root:n,props:r,action:d.data,timestamp:a,shouldOptimize:s})),t&&t({root:n,props:r,actions:i,timestamp:a,shouldOptimize:s})},Yn=(e,t)=>t.parentNode.insertBefore(e,t),$n=(e,t)=>t.parentNode.insertBefore(e,t.nextSibling),Lt=e=>Array.isArray(e),ke=e=>e==null,so=e=>e.trim(),Pt=e=>""+e,lo=(e,t=",")=>ke(e)?[]:Lt(e)?e:Pt(e).split(t).map(so).filter(n=>n.length),Pr=e=>typeof e=="boolean",Mr=e=>Pr(e)?e:e==="true",be=e=>typeof e=="string",xr=e=>We(e)?e:be(e)?Pt(e).replace(/[a-z]+/gi,""):0,It=e=>parseInt(xr(e),10),Kn=e=>parseFloat(xr(e)),at=e=>We(e)&&isFinite(e)&&Math.floor(e)===e,Xn=(e,t=1e3)=>{if(at(e))return e;let n=Pt(e).trim();return/MB$/i.test(n)?(n=n.replace(/MB$i/,"").trim(),It(n)*t*t):/KB/i.test(n)?(n=n.replace(/KB$i/,"").trim(),It(n)*t):It(n)},Ye=e=>typeof e=="function",uo=e=>{let t=self,n=e.split("."),r=null;for(;r=n.shift();)if(t=t[r],!t)return null;return t},Qn={process:"POST",patch:"PATCH",revert:"DELETE",fetch:"GET",restore:"GET",load:"GET"},co=e=>{const t={};return t.url=be(e)?e:e.url||"",t.timeout=e.timeout?parseInt(e.timeout,10):0,t.headers=e.headers?e.headers:{},ce(Qn,n=>{t[n]=fo(n,e[n],Qn[n],t.timeout,t.headers)}),t.process=e.process||be(e)||e.url?t.process:null,t.remove=e.remove||null,delete t.headers,t},fo=(e,t,n,r,i)=>{if(t===null)return null;if(typeof t=="function")return t;const a={url:n==="GET"||n==="PATCH"?`?${e}=`:"",method:n,headers:i,withCredentials:!1,timeout:r,onload:null,ondata:null,onerror:null};if(be(t))return a.url=t,a;if(Object.assign(a,t),be(a.headers)){const s=a.headers.split(/:(.+)/);a.headers={header:s[0],value:s[1]}}return a.withCredentials=Mr(a.withCredentials),a},ho=e=>co(e),po=e=>e===null,Ee=e=>typeof e=="object"&&e!==null,go=e=>Ee(e)&&be(e.url)&&Ee(e.process)&&Ee(e.revert)&&Ee(e.restore)&&Ee(e.fetch),en=e=>Lt(e)?"array":po(e)?"null":at(e)?"int":/^[0-9]+ ?(?:GB|MB|KB)$/gi.test(e)?"bytes":go(e)?"api":typeof e,vo=e=>e.replace(/{\s*'/g,'{"').replace(/'\s*}/g,'"}').replace(/'\s*:/g,'":').replace(/:\s*'/g,':"').replace(/,\s*'/g,',"').replace(/'\s*,/g,'",'),mo={array:lo,boolean:Mr,int:e=>en(e)==="bytes"?Xn(e):It(e),number:Kn,float:Kn,bytes:Xn,string:e=>Ye(e)?e:Pt(e),function:e=>uo(e),serverapi:ho,object:e=>{try{return JSON.parse(vo(e))}catch{return null}}},yo=(e,t)=>mo[t](e),Cr=(e,t,n)=>{if(e===t)return e;let r=en(e);if(r!==n){const i=yo(e,n);if(r=en(i),i===null)throw`Trying to assign value with incorrect type to "${option}", allowed type: "${n}"`;e=i}return e},Eo=(e,t)=>{let n=e;return{enumerable:!0,get:()=>n,set:r=>{n=Cr(r,e,t)}}},_o=e=>{const t={};return ce(e,n=>{const r=e[n];t[n]=Eo(r[0],r[1])}),Ge(t)},bo=e=>({items:[],listUpdateTimeout:null,itemUpdateTimeout:null,processingQueue:[],options:_o(e)}),Mt=(e,t="-")=>e.split(/(?=[A-Z])/).map(n=>n.toLowerCase()).join(t),To=(e,t)=>{const n={};return ce(t,r=>{n[r]={get:()=>e.getState().options[r],set:i=>{e.dispatch(`SET_${Mt(r,"_").toUpperCase()}`,{value:i})}}}),n},Oo=e=>(t,n,r)=>{const i={};return ce(e,a=>{const s=Mt(a,"_").toUpperCase();i[`SET_${s}`]=d=>{try{r.options[a]=d.value}catch{}t(`DID_SET_${s}`,{value:r.options[a]})}}),i},Io=e=>t=>{const n={};return ce(e,r=>{n[`GET_${Mt(r,"_").toUpperCase()}`]=i=>t.options[r]}),n},De={API:1,DROP:2,BROWSE:3,PASTE:4,NONE:5},mn=()=>Math.random().toString(36).substring(2,11),yn=(e,t)=>e.splice(t,1),wo=(e,t)=>{t?e():document.hidden?Promise.resolve(1).then(e):setTimeout(e,0)},xt=()=>{const e=[],t=(r,i)=>{yn(e,e.findIndex(a=>a.event===r&&(a.cb===i||!i)))},n=(r,i,a)=>{e.filter(s=>s.event===r).map(s=>s.cb).forEach(s=>wo(()=>s(...i),a))};return{fireSync:(r,...i)=>{n(r,i,!0)},fire:(r,...i)=>{n(r,i,!1)},on:(r,i)=>{e.push({event:r,cb:i})},onOnce:(r,i)=>{e.push({event:r,cb:(...a)=>{t(r,i),i(...a)}})},off:t}},kr=(e,t,n)=>{Object.getOwnPropertyNames(e).filter(r=>!n.includes(r)).forEach(r=>Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(e,r)))},So=["fire","process","revert","load","on","off","onOnce","retryLoad","extend","archive","archived","release","released","requestProcessing","freeze"],Ie=e=>{const t={};return kr(e,t,So),t},Ao=e=>{e.forEach((t,n)=>{t.released&&yn(e,n)})},te={INIT:1,IDLE:2,PROCESSING_QUEUED:9,PROCESSING:3,PROCESSING_COMPLETE:5,PROCESSING_ERROR:6,PROCESSING_REVERT_ERROR:10,LOADING:7,LOAD_ERROR:8},ve={INPUT:1,LIMBO:2,LOCAL:3},Br=e=>/[^0-9]+/.exec(e),qr=()=>Br(1.1.toLocaleString())[0],Ro=()=>{const e=qr(),t=1e3.toLocaleString();return t!=="1000"?Br(t)[0]:e==="."?",":"."},K={BOOLEAN:"boolean",INT:"int",NUMBER:"number",STRING:"string",ARRAY:"array",OBJECT:"object",FUNCTION:"function",ACTION:"action",SERVER_API:"serverapi",REGEX:"regex"},En=[],Ne=(e,t,n)=>new Promise((r,i)=>{const a=En.filter(d=>d.key===e).map(d=>d.cb);if(a.length===0){r(t);return}const s=a.shift();a.reduce((d,c)=>d.then(y=>c(y,n)),s(t,n)).then(d=>r(d)).catch(d=>i(d))}),Je=(e,t,n)=>En.filter(r=>r.key===e).map(r=>r.cb(t,n)),Do=(e,t)=>En.push({key:e,cb:t}),No=e=>Object.assign(nt,e),Rt=()=>({...nt}),Lo=e=>{ce(e,(t,n)=>{nt[t]&&(nt[t][0]=Cr(n,nt[t][0],nt[t][1]))})},nt={id:[null,K.STRING],name:["filepond",K.STRING],disabled:[!1,K.BOOLEAN],className:[null,K.STRING],required:[!1,K.BOOLEAN],captureMethod:[null,K.STRING],allowSyncAcceptAttribute:[!0,K.BOOLEAN],allowDrop:[!0,K.BOOLEAN],allowBrowse:[!0,K.BOOLEAN],allowPaste:[!0,K.BOOLEAN],allowMultiple:[!1,K.BOOLEAN],allowReplace:[!0,K.BOOLEAN],allowRevert:[!0,K.BOOLEAN],allowRemove:[!0,K.BOOLEAN],allowProcess:[!0,K.BOOLEAN],allowReorder:[!1,K.BOOLEAN],allowDirectoriesOnly:[!1,K.BOOLEAN],storeAsFile:[!1,K.BOOLEAN],forceRevert:[!1,K.BOOLEAN],maxFiles:[null,K.INT],checkValidity:[!1,K.BOOLEAN],itemInsertLocationFreedom:[!0,K.BOOLEAN],itemInsertLocation:["before",K.STRING],itemInsertInterval:[75,K.INT],dropOnPage:[!1,K.BOOLEAN],dropOnElement:[!0,K.BOOLEAN],dropValidation:[!1,K.BOOLEAN],ignoredFiles:[[".ds_store","thumbs.db","desktop.ini"],K.ARRAY],instantUpload:[!0,K.BOOLEAN],maxParallelUploads:[2,K.INT],allowMinimumUploadDuration:[!0,K.BOOLEAN],chunkUploads:[!1,K.BOOLEAN],chunkForce:[!1,K.BOOLEAN],chunkSize:[5e6,K.INT],chunkRetryDelays:[[500,1e3,3e3],K.ARRAY],server:[null,K.SERVER_API],fileSizeBase:[1e3,K.INT],labelFileSizeBytes:["bytes",K.STRING],labelFileSizeKilobytes:["KB",K.STRING],labelFileSizeMegabytes:["MB",K.STRING],labelFileSizeGigabytes:["GB",K.STRING],labelDecimalSeparator:[qr(),K.STRING],labelThousandsSeparator:[Ro(),K.STRING],labelIdle:['Drag & Drop your files or <span class="filepond--label-action">Browse</span>',K.STRING],labelInvalidField:["Field contains invalid files",K.STRING],labelFileWaitingForSize:["Waiting for size",K.STRING],labelFileSizeNotAvailable:["Size not available",K.STRING],labelFileCountSingular:["file in list",K.STRING],labelFileCountPlural:["files in list",K.STRING],labelFileLoading:["Loading",K.STRING],labelFileAdded:["Added",K.STRING],labelFileLoadError:["Error during load",K.STRING],labelFileRemoved:["Removed",K.STRING],labelFileRemoveError:["Error during remove",K.STRING],labelFileProcessing:["Uploading",K.STRING],labelFileProcessingComplete:["Upload complete",K.STRING],labelFileProcessingAborted:["Upload cancelled",K.STRING],labelFileProcessingError:["Error during upload",K.STRING],labelFileProcessingRevertError:["Error during revert",K.STRING],labelTapToCancel:["tap to cancel",K.STRING],labelTapToRetry:["tap to retry",K.STRING],labelTapToUndo:["tap to undo",K.STRING],labelButtonRemoveItem:["Remove",K.STRING],labelButtonAbortItemLoad:["Abort",K.STRING],labelButtonRetryItemLoad:["Retry",K.STRING],labelButtonAbortItemProcessing:["Cancel",K.STRING],labelButtonUndoItemProcessing:["Undo",K.STRING],labelButtonRetryItemProcessing:["Retry",K.STRING],labelButtonProcessItem:["Upload",K.STRING],iconRemove:['<svg width="26" height="26" viewBox="0 0 26 26" xmlns="http://www.w3.org/2000/svg"><path d="M11.586 13l-2.293 2.293a1 1 0 0 0 1.414 1.414L13 14.414l2.293 2.293a1 1 0 0 0 1.414-1.414L14.414 13l2.293-2.293a1 1 0 0 0-1.414-1.414L13 11.586l-2.293-2.293a1 1 0 0 0-1.414 1.414L11.586 13z" fill="currentColor" fill-rule="nonzero"/></svg>',K.STRING],iconProcess:['<svg width="26" height="26" viewBox="0 0 26 26" xmlns="http://www.w3.org/2000/svg"><path d="M14 10.414v3.585a1 1 0 0 1-2 0v-3.585l-1.293 1.293a1 1 0 0 1-1.414-1.415l3-3a1 1 0 0 1 1.414 0l3 3a1 1 0 0 1-1.414 1.415L14 10.414zM9 18a1 1 0 0 1 0-2h8a1 1 0 0 1 0 2H9z" fill="currentColor" fill-rule="evenodd"/></svg>',K.STRING],iconRetry:['<svg width="26" height="26" viewBox="0 0 26 26" xmlns="http://www.w3.org/2000/svg"><path d="M10.81 9.185l-.038.02A4.997 4.997 0 0 0 8 13.683a5 5 0 0 0 5 5 5 5 0 0 0 5-5 1 1 0 0 1 2 0A7 7 0 1 1 9.722 7.496l-.842-.21a.999.999 0 1 1 .484-1.94l3.23.806c.535.133.86.675.73 1.21l-.804 3.233a.997.997 0 0 1-1.21.73.997.997 0 0 1-.73-1.21l.23-.928v-.002z" fill="currentColor" fill-rule="nonzero"/></svg>',K.STRING],iconUndo:['<svg width="26" height="26" viewBox="0 0 26 26" xmlns="http://www.w3.org/2000/svg"><path d="M9.185 10.81l.02-.038A4.997 4.997 0 0 1 13.683 8a5 5 0 0 1 5 5 5 5 0 0 1-5 5 1 1 0 0 0 0 2A7 7 0 1 0 7.496 9.722l-.21-.842a.999.999 0 1 0-1.94.484l.806 3.23c.133.535.675.86 1.21.73l3.233-.803a.997.997 0 0 0 .73-1.21.997.997 0 0 0-1.21-.73l-.928.23-.002-.001z" fill="currentColor" fill-rule="nonzero"/></svg>',K.STRING],iconDone:['<svg width="26" height="26" viewBox="0 0 26 26" xmlns="http://www.w3.org/2000/svg"><path d="M18.293 9.293a1 1 0 0 1 1.414 1.414l-7.002 7a1 1 0 0 1-1.414 0l-3.998-4a1 1 0 1 1 1.414-1.414L12 15.586l6.294-6.293z" fill="currentColor" fill-rule="nonzero"/></svg>',K.STRING],oninit:[null,K.FUNCTION],onwarning:[null,K.FUNCTION],onerror:[null,K.FUNCTION],onactivatefile:[null,K.FUNCTION],oninitfile:[null,K.FUNCTION],onaddfilestart:[null,K.FUNCTION],onaddfileprogress:[null,K.FUNCTION],onaddfile:[null,K.FUNCTION],onprocessfilestart:[null,K.FUNCTION],onprocessfileprogress:[null,K.FUNCTION],onprocessfileabort:[null,K.FUNCTION],onprocessfilerevert:[null,K.FUNCTION],onprocessfile:[null,K.FUNCTION],onprocessfiles:[null,K.FUNCTION],onremovefile:[null,K.FUNCTION],onpreparefile:[null,K.FUNCTION],onupdatefiles:[null,K.FUNCTION],onreorderfiles:[null,K.FUNCTION],beforeDropFile:[null,K.FUNCTION],beforeAddFile:[null,K.FUNCTION],beforeRemoveFile:[null,K.FUNCTION],beforePrepareFile:[null,K.FUNCTION],stylePanelLayout:[null,K.STRING],stylePanelAspectRatio:[null,K.STRING],styleItemPanelAspectRatio:[null,K.STRING],styleButtonRemoveItemPosition:["left",K.STRING],styleButtonProcessItemPosition:["right",K.STRING],styleLoadIndicatorPosition:["right",K.STRING],styleProgressIndicatorPosition:["right",K.STRING],styleButtonRemoveItemAlign:[!1,K.BOOLEAN],files:[[],K.ARRAY],credits:[["https://pqina.nl/","Powered by PQINA"],K.ARRAY]},$e=(e,t)=>ke(t)?e[0]||null:at(t)?e[t]||null:(typeof t=="object"&&(t=t.id),e.find(n=>n.id===t)||null),Fr=e=>{if(ke(e))return e;if(/:/.test(e)){const t=e.split(":");return t[1]/t[0]}return parseFloat(e)},Le=e=>e.filter(t=>!t.archived),jr={EMPTY:0,IDLE:1,ERROR:2,BUSY:3,READY:4};let mt=null;const Po=()=>{if(mt===null)try{const e=new DataTransfer;e.items.add(new File(["hello world"],"This_Works.txt"));const t=document.createElement("input");t.setAttribute("type","file"),t.files=e.files,mt=t.files.length===1}catch{mt=!1}return mt},Mo=[te.LOAD_ERROR,te.PROCESSING_ERROR,te.PROCESSING_REVERT_ERROR],xo=[te.LOADING,te.PROCESSING,te.PROCESSING_QUEUED,te.INIT],Co=[te.PROCESSING_COMPLETE],ko=e=>Mo.includes(e.status),Bo=e=>xo.includes(e.status),qo=e=>Co.includes(e.status),Zn=e=>Ee(e.options.server)&&(Ee(e.options.server.process)||Ye(e.options.server.process)),Fo=e=>({GET_STATUS:()=>{const t=Le(e.items),{EMPTY:n,ERROR:r,BUSY:i,IDLE:a,READY:s}=jr;return t.length===0?n:t.some(ko)?r:t.some(Bo)?i:t.some(qo)?s:a},GET_ITEM:t=>$e(e.items,t),GET_ACTIVE_ITEM:t=>$e(Le(e.items),t),GET_ACTIVE_ITEMS:()=>Le(e.items),GET_ITEMS:()=>e.items,GET_ITEM_NAME:t=>{const n=$e(e.items,t);return n?n.filename:null},GET_ITEM_SIZE:t=>{const n=$e(e.items,t);return n?n.fileSize:null},GET_STYLES:()=>Object.keys(e.options).filter(t=>/^style/.test(t)).map(t=>({name:t,value:e.options[t]})),GET_PANEL_ASPECT_RATIO:()=>/circle/.test(e.options.stylePanelLayout)?1:Fr(e.options.stylePanelAspectRatio),GET_ITEM_PANEL_ASPECT_RATIO:()=>e.options.styleItemPanelAspectRatio,GET_ITEMS_BY_STATUS:t=>Le(e.items).filter(n=>n.status===t),GET_TOTAL_ITEMS:()=>Le(e.items).length,SHOULD_UPDATE_FILE_INPUT:()=>e.options.storeAsFile&&Po()&&!Zn(e),IS_ASYNC:()=>Zn(e),GET_FILE_SIZE_LABELS:t=>({labelBytes:t("GET_LABEL_FILE_SIZE_BYTES")||void 0,labelKilobytes:t("GET_LABEL_FILE_SIZE_KILOBYTES")||void 0,labelMegabytes:t("GET_LABEL_FILE_SIZE_MEGABYTES")||void 0,labelGigabytes:t("GET_LABEL_FILE_SIZE_GIGABYTES")||void 0})}),jo=e=>{const t=Le(e.items).length;if(!e.options.allowMultiple)return t===0;const n=e.options.maxFiles;return n===null||t<n},Ur=(e,t,n)=>Math.max(Math.min(n,e),t),Uo=(e,t,n)=>e.splice(t,0,n),Go=(e,t,n)=>ke(t)?null:typeof n>"u"?(e.push(t),t):(n=Ur(n,0,e.length),Uo(e,n,t),t),tn=e=>/^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*)\s*$/i.test(e),ht=e=>`${e}`.split("/").pop().split("?").shift(),Ct=e=>e.split(".").pop(),Vo=e=>{if(typeof e!="string")return"";const t=e.split("/").pop();return/svg/.test(t)?"svg":/zip|compressed/.test(t)?"zip":/plain/.test(t)?"txt":/msword/.test(t)?"doc":/[a-z]+/.test(t)?t==="jpeg"?"jpg":t:""},lt=(e,t="")=>(t+e).slice(-t.length),Gr=(e=new Date)=>`${e.getFullYear()}-${lt(e.getMonth()+1,"00")}-${lt(e.getDate(),"00")}_${lt(e.getHours(),"00")}-${lt(e.getMinutes(),"00")}-${lt(e.getSeconds(),"00")}`,ot=(e,t,n=null,r=null)=>{const i=typeof n=="string"?e.slice(0,e.size,n):e.slice(0,e.size,e.type);return i.lastModifiedDate=new Date,e._relativePath&&(i._relativePath=e._relativePath),be(t)||(t=Gr()),t&&r===null&&Ct(t)?i.name=t:(r=r||Vo(i.type),i.name=t+(r?"."+r:"")),i},Ho=()=>window.BlobBuilder=window.BlobBuilder||window.WebKitBlobBuilder||window.MozBlobBuilder||window.MSBlobBuilder,Vr=(e,t)=>{const n=Ho();if(n){const r=new n;return r.append(e),r.getBlob(t)}return new Blob([e],{type:t})},zo=(e,t)=>{const n=new ArrayBuffer(e.length),r=new Uint8Array(n);for(let i=0;i<e.length;i++)r[i]=e.charCodeAt(i);return Vr(n,t)},Hr=e=>(/^data:(.+);/.exec(e)||[])[1]||null,Wo=e=>e.split(",")[1].replace(/\s/g,""),Yo=e=>atob(Wo(e)),$o=e=>{const t=Hr(e),n=Yo(e);return zo(n,t)},Ko=(e,t,n)=>ot($o(e),t,null,n),Xo=e=>{if(!/^content-disposition:/i.test(e))return null;const t=e.split(/filename=|filename\*=.+''/).splice(1).map(n=>n.trim().replace(/^["']|[;"']{0,2}$/g,"")).filter(n=>n.length);return t.length?decodeURI(t[t.length-1]):null},Qo=e=>{if(/content-length:/i.test(e)){const t=e.match(/[0-9]+/)[0];return t?parseInt(t,10):null}return null},Zo=e=>/x-content-transfer-id:/i.test(e)&&(e.split(":")[1]||"").trim()||null,_n=e=>{const t={source:null,name:null,size:null},n=e.split(`
`);for(let r of n){const i=Xo(r);if(i){t.name=i;continue}const a=Qo(r);if(a){t.size=a;continue}const s=Zo(r);if(s){t.source=s;continue}}return t},Jo=e=>{const t={source:null,complete:!1,progress:0,size:null,timestamp:null,duration:0,request:null},n=()=>t.progress,r=()=>{t.request&&t.request.abort&&t.request.abort()},i=()=>{const d=t.source;s.fire("init",d),d instanceof File?s.fire("load",d):d instanceof Blob?s.fire("load",ot(d,d.name)):tn(d)?s.fire("load",Ko(d)):a(d)},a=d=>{if(!e){s.fire("error",{type:"error",body:"Can't load URL",code:400});return}t.timestamp=Date.now(),t.request=e(d,c=>{t.duration=Date.now()-t.timestamp,t.complete=!0,c instanceof Blob&&(c=ot(c,c.name||ht(d))),s.fire("load",c instanceof Blob?c:c?c.body:null)},c=>{s.fire("error",typeof c=="string"?{type:"error",code:0,body:c}:c)},(c,y,f)=>{if(f&&(t.size=f),t.duration=Date.now()-t.timestamp,!c){t.progress=null;return}t.progress=y/f,s.fire("progress",t.progress)},()=>{s.fire("abort")},c=>{const y=_n(typeof c=="string"?c:c.headers);s.fire("meta",{size:t.size||y.size,filename:y.name,source:y.source})})},s={...xt(),setSource:d=>t.source=d,getProgress:n,abort:r,load:i};return s},Jn=e=>/GET|HEAD/.test(e),Ke=(e,t,n)=>{const r={onheaders:()=>{},onprogress:()=>{},onload:()=>{},ontimeout:()=>{},onerror:()=>{},onabort:()=>{},abort:()=>{i=!0,s.abort()}};let i=!1,a=!1;n={method:"POST",headers:{},withCredentials:!1,...n},t=encodeURI(t),Jn(n.method)&&e&&(t=`${t}${encodeURIComponent(typeof e=="string"?e:JSON.stringify(e))}`);const s=new XMLHttpRequest,d=Jn(n.method)?s:s.upload;return d.onprogress=c=>{i||r.onprogress(c.lengthComputable,c.loaded,c.total)},s.onreadystatechange=()=>{s.readyState<2||s.readyState===4&&s.status===0||a||(a=!0,r.onheaders(s))},s.onload=()=>{s.status>=200&&s.status<300?r.onload(s):r.onerror(s)},s.onerror=()=>r.onerror(s),s.onabort=()=>{i=!0,r.onabort()},s.ontimeout=()=>r.ontimeout(s),s.open(n.method,t,!0),at(n.timeout)&&(s.timeout=n.timeout),Object.keys(n.headers).forEach(c=>{const y=unescape(encodeURIComponent(n.headers[c]));s.setRequestHeader(c,y)}),n.responseType&&(s.responseType=n.responseType),n.withCredentials&&(s.withCredentials=!0),s.send(e),r},fe=(e,t,n,r)=>({type:e,code:t,body:n,headers:r}),Xe=e=>t=>{e(fe("error",0,"Timeout",t.getAllResponseHeaders()))},er=e=>/\?/.test(e),dt=(...e)=>{let t="";return e.forEach(n=>{t+=er(t)&&er(n)?n.replace(/\?/,"&"):n}),t},Wt=(e="",t)=>{if(typeof t=="function")return t;if(!t||!be(t.url))return null;const n=t.onload||(i=>i),r=t.onerror||(i=>null);return(i,a,s,d,c,y)=>{const f=Ke(i,dt(e,t.url),{...t,responseType:"blob"});return f.onload=u=>{const o=u.getAllResponseHeaders(),l=_n(o).name||ht(i);a(fe("load",u.status,t.method==="HEAD"?null:ot(n(u.response),l),o))},f.onerror=u=>{s(fe("error",u.status,r(u.response)||u.statusText,u.getAllResponseHeaders()))},f.onheaders=u=>{y(fe("headers",u.status,null,u.getAllResponseHeaders()))},f.ontimeout=Xe(s),f.onprogress=d,f.onabort=c,f}},Ae={QUEUED:0,COMPLETE:1,PROCESSING:2,ERROR:3,WAITING:4},ea=(e,t,n,r,i,a,s,d,c,y,f)=>{const u=[],{chunkTransferId:o,chunkServer:l,chunkSize:h,chunkRetryDelays:v}=f,E={serverId:o,aborted:!1},p=t.ondata||(D=>D),m=t.onload||((D,M)=>M==="HEAD"?D.getResponseHeader("Upload-Offset"):D.response),_=t.onerror||(D=>null),g=D=>{const M=new FormData;Ee(i)&&M.append(n,JSON.stringify(i));const x=typeof t.headers=="function"?t.headers(r,i):{...t.headers,"Upload-Length":r.size},B={...t,headers:x},V=Ke(p(M),dt(e,t.url),B);V.onload=F=>D(m(F,B.method)),V.onerror=F=>s(fe("error",F.status,_(F.response)||F.statusText,F.getAllResponseHeaders())),V.ontimeout=Xe(s)},b=D=>{const M=dt(e,l.url,E.serverId),B={headers:typeof t.headers=="function"?t.headers(E.serverId):{...t.headers},method:"HEAD"},V=Ke(null,M,B);V.onload=F=>D(m(F,B.method)),V.onerror=F=>s(fe("error",F.status,_(F.response)||F.statusText,F.getAllResponseHeaders())),V.ontimeout=Xe(s)},A=Math.floor(r.size/h);for(let D=0;D<=A;D++){const M=D*h,x=r.slice(M,M+h,"application/offset+octet-stream");u[D]={index:D,size:x.size,offset:M,data:x,file:r,progress:0,retries:[...v],status:Ae.QUEUED,error:null,request:null,timeout:null}}const S=()=>a(E.serverId),I=D=>D.status===Ae.QUEUED||D.status===Ae.ERROR,N=D=>{if(E.aborted)return;if(D=D||u.find(I),!D){u.every(R=>R.status===Ae.COMPLETE)&&S();return}D.status=Ae.PROCESSING,D.progress=null;const M=l.ondata||(R=>R),x=l.onerror||(R=>null),B=l.onload||(()=>{}),V=dt(e,l.url,E.serverId),F=typeof l.headers=="function"?l.headers(D):{...l.headers,"Content-Type":"application/offset+octet-stream","Upload-Offset":D.offset,"Upload-Length":r.size,"Upload-Name":r.name},k=D.request=Ke(M(D.data),V,{...l,headers:F});k.onload=R=>{B(R,D.index,u.length),D.status=Ae.COMPLETE,D.request=null,O()},k.onprogress=(R,C,q)=>{D.progress=R?C:null,T()},k.onerror=R=>{D.status=Ae.ERROR,D.request=null,D.error=x(R.response)||R.statusText,w(D)||s(fe("error",R.status,x(R.response)||R.statusText,R.getAllResponseHeaders()))},k.ontimeout=R=>{D.status=Ae.ERROR,D.request=null,w(D)||Xe(s)(R)},k.onabort=()=>{D.status=Ae.QUEUED,D.request=null,c()}},w=D=>D.retries.length===0?!1:(D.status=Ae.WAITING,clearTimeout(D.timeout),D.timeout=setTimeout(()=>{N(D)},D.retries.shift()),!0),T=()=>{const D=u.reduce((x,B)=>x===null||B.progress===null?null:x+B.progress,0);if(D===null)return d(!1,0,0);const M=u.reduce((x,B)=>x+B.size,0);d(!0,D,M)},O=()=>{u.filter(M=>M.status===Ae.PROCESSING).length>=1||N()},L=()=>{u.forEach(D=>{clearTimeout(D.timeout),D.request&&D.request.abort()})};return E.serverId?b(D=>{E.aborted||(u.filter(M=>M.offset<D).forEach(M=>{M.status=Ae.COMPLETE,M.progress=M.size}),O())}):g(D=>{E.aborted||(y(D),E.serverId=D,O())}),{abort:()=>{E.aborted=!0,L()}}},ta=(e,t,n,r)=>(i,a,s,d,c,y,f)=>{if(!i)return;const u=r.chunkUploads,o=u&&i.size>r.chunkSize,l=u&&(o||r.chunkForce);if(i instanceof Blob&&l)return ea(e,t,n,i,a,s,d,c,y,f,r);const h=t.ondata||(b=>b),v=t.onload||(b=>b),E=t.onerror||(b=>null),p=typeof t.headers=="function"?t.headers(i,a)||{}:{...t.headers},m={...t,headers:p};var _=new FormData;Ee(a)&&_.append(n,JSON.stringify(a)),(i instanceof Blob?[{name:null,file:i}]:i).forEach(b=>{_.append(n,b.file,b.name===null?b.file.name:`${b.name}${b.file.name}`)});const g=Ke(h(_),dt(e,t.url),m);return g.onload=b=>{s(fe("load",b.status,v(b.response),b.getAllResponseHeaders()))},g.onerror=b=>{d(fe("error",b.status,E(b.response)||b.statusText,b.getAllResponseHeaders()))},g.ontimeout=Xe(d),g.onprogress=c,g.onabort=y,g},na=(e="",t,n,r)=>typeof t=="function"?(...i)=>t(n,...i,r):!t||!be(t.url)?null:ta(e,t,n,r),ut=(e="",t)=>{if(typeof t=="function")return t;if(!t||!be(t.url))return(i,a)=>a();const n=t.onload||(i=>i),r=t.onerror||(i=>null);return(i,a,s)=>{const d=Ke(i,e+t.url,t);return d.onload=c=>{a(fe("load",c.status,n(c.response),c.getAllResponseHeaders()))},d.onerror=c=>{s(fe("error",c.status,r(c.response)||c.statusText,c.getAllResponseHeaders()))},d.ontimeout=Xe(s),d}},zr=(e=0,t=1)=>e+Math.random()*(t-e),ra=(e,t=1e3,n=0,r=25,i=250)=>{let a=null;const s=Date.now(),d=()=>{let c=Date.now()-s,y=zr(r,i);c+y>t&&(y=c+y-t);let f=c/t;if(f>=1||document.hidden){e(1);return}e(f),a=setTimeout(d,y)};return t>0&&d(),{clear:()=>{clearTimeout(a)}}},ia=(e,t)=>{const n={complete:!1,perceivedProgress:0,perceivedPerformanceUpdater:null,progress:null,timestamp:null,perceivedDuration:0,duration:0,request:null,response:null},{allowMinimumUploadDuration:r}=t,i=(f,u)=>{const o=()=>{n.duration===0||n.progress===null||y.fire("progress",y.getProgress())},l=()=>{n.complete=!0,y.fire("load-perceived",n.response.body)};y.fire("start"),n.timestamp=Date.now(),n.perceivedPerformanceUpdater=ra(h=>{n.perceivedProgress=h,n.perceivedDuration=Date.now()-n.timestamp,o(),n.response&&n.perceivedProgress===1&&!n.complete&&l()},r?zr(750,1500):0),n.request=e(f,u,h=>{n.response=Ee(h)?h:{type:"load",code:200,body:`${h}`,headers:{}},n.duration=Date.now()-n.timestamp,n.progress=1,y.fire("load",n.response.body),(!r||r&&n.perceivedProgress===1)&&l()},h=>{n.perceivedPerformanceUpdater.clear(),y.fire("error",Ee(h)?h:{type:"error",code:0,body:`${h}`})},(h,v,E)=>{n.duration=Date.now()-n.timestamp,n.progress=h?v/E:null,o()},()=>{n.perceivedPerformanceUpdater.clear(),y.fire("abort",n.response?n.response.body:null)},h=>{y.fire("transfer",h)})},a=()=>{n.request&&(n.perceivedPerformanceUpdater.clear(),n.request.abort&&n.request.abort(),n.complete=!0)},s=()=>{a(),n.complete=!1,n.perceivedProgress=0,n.progress=0,n.timestamp=null,n.perceivedDuration=0,n.duration=0,n.request=null,n.response=null},d=r?()=>n.progress?Math.min(n.progress,n.perceivedProgress):null:()=>n.progress||null,c=r?()=>Math.min(n.duration,n.perceivedDuration):()=>n.duration,y={...xt(),process:i,abort:a,getProgress:d,getDuration:c,reset:s};return y},Wr=e=>e.substring(0,e.lastIndexOf("."))||e,oa=e=>{let t=[e.name,e.size,e.type];return e instanceof Blob||tn(e)?t[0]=e.name||Gr():tn(e)?(t[1]=e.length,t[2]=Hr(e)):be(e)&&(t[0]=ht(e),t[1]=0,t[2]="application/octet-stream"),{name:t[0],size:t[1],type:t[2]}},Qe=e=>!!(e instanceof File||e instanceof Blob&&e.name),Yr=e=>{if(!Ee(e))return e;const t=Lt(e)?[]:{};for(const n in e){if(!e.hasOwnProperty(n))continue;const r=e[n];t[n]=r&&Ee(r)?Yr(r):r}return t},aa=(e=null,t=null,n=null)=>{const r=mn(),i={archived:!1,frozen:!1,released:!1,source:null,file:n,serverFileReference:t,transferId:null,processingAborted:!1,status:t?te.PROCESSING_COMPLETE:te.INIT,activeLoader:null,activeProcessor:null};let a=null;const s={},d=I=>i.status=I,c=(I,...N)=>{i.released||i.frozen||A.fire(I,...N)},y=()=>Ct(i.file.name),f=()=>i.file.type,u=()=>i.file.size,o=()=>i.file,l=(I,N,w)=>{if(i.source=I,A.fireSync("init"),i.file){A.fireSync("load-skip");return}i.file=oa(I),N.on("init",()=>{c("load-init")}),N.on("meta",T=>{i.file.size=T.size,i.file.filename=T.filename,T.source&&(e=ve.LIMBO,i.serverFileReference=T.source,i.status=te.PROCESSING_COMPLETE),c("load-meta")}),N.on("progress",T=>{d(te.LOADING),c("load-progress",T)}),N.on("error",T=>{d(te.LOAD_ERROR),c("load-request-error",T)}),N.on("abort",()=>{d(te.INIT),c("load-abort")}),N.on("load",T=>{i.activeLoader=null;const O=D=>{i.file=Qe(D)?D:i.file,e===ve.LIMBO&&i.serverFileReference?d(te.PROCESSING_COMPLETE):d(te.IDLE),c("load")},L=D=>{i.file=T,c("load-meta"),d(te.LOAD_ERROR),c("load-file-error",D)};if(i.serverFileReference){O(T);return}w(T,O,L)}),N.setSource(I),i.activeLoader=N,N.load()},h=()=>{i.activeLoader&&i.activeLoader.load()},v=()=>{if(i.activeLoader){i.activeLoader.abort();return}d(te.INIT),c("load-abort")},E=(I,N)=>{if(i.processingAborted){i.processingAborted=!1;return}if(d(te.PROCESSING),a=null,!(i.file instanceof Blob)){A.on("load",()=>{E(I,N)});return}I.on("load",O=>{i.transferId=null,i.serverFileReference=O}),I.on("transfer",O=>{i.transferId=O}),I.on("load-perceived",O=>{i.activeProcessor=null,i.transferId=null,i.serverFileReference=O,d(te.PROCESSING_COMPLETE),c("process-complete",O)}),I.on("start",()=>{c("process-start")}),I.on("error",O=>{i.activeProcessor=null,d(te.PROCESSING_ERROR),c("process-error",O)}),I.on("abort",O=>{i.activeProcessor=null,i.serverFileReference=O,d(te.IDLE),c("process-abort"),a&&a()}),I.on("progress",O=>{c("process-progress",O)});const w=O=>{i.archived||I.process(O,{...s})},T=console.error;N(i.file,w,T),i.activeProcessor=I},p=()=>{i.processingAborted=!1,d(te.PROCESSING_QUEUED)},m=()=>new Promise(I=>{if(!i.activeProcessor){i.processingAborted=!0,d(te.IDLE),c("process-abort"),I();return}a=()=>{I()},i.activeProcessor.abort()}),_=(I,N)=>new Promise((w,T)=>{const O=i.serverFileReference!==null?i.serverFileReference:i.transferId;if(O===null){w();return}I(O,()=>{i.serverFileReference=null,i.transferId=null,w()},L=>{if(!N){w();return}d(te.PROCESSING_REVERT_ERROR),c("process-revert-error"),T(L)}),d(te.IDLE),c("process-revert")}),g=(I,N,w)=>{const T=I.split("."),O=T[0],L=T.pop();let D=s;T.forEach(M=>D=D[M]),JSON.stringify(D[L])!==JSON.stringify(N)&&(D[L]=N,c("metadata-update",{key:O,value:s[O],silent:w}))},A={id:{get:()=>r},origin:{get:()=>e,set:I=>e=I},serverId:{get:()=>i.serverFileReference},transferId:{get:()=>i.transferId},status:{get:()=>i.status},filename:{get:()=>i.file.name},filenameWithoutExtension:{get:()=>Wr(i.file.name)},fileExtension:{get:y},fileType:{get:f},fileSize:{get:u},file:{get:o},relativePath:{get:()=>i.file._relativePath},source:{get:()=>i.source},getMetadata:I=>Yr(I?s[I]:s),setMetadata:(I,N,w)=>{if(Ee(I)){const T=I;return Object.keys(T).forEach(O=>{g(O,T[O],N)}),I}return g(I,N,w),N},extend:(I,N)=>S[I]=N,abortLoad:v,retryLoad:h,requestProcessing:p,abortProcessing:m,load:l,process:E,revert:_,...xt(),freeze:()=>i.frozen=!0,release:()=>i.released=!0,released:{get:()=>i.released},archive:()=>i.archived=!0,archived:{get:()=>i.archived},setFile:I=>i.file=I},S=Ge(A);return S},sa=(e,t)=>ke(t)?0:be(t)?e.findIndex(n=>n.id===t):-1,tr=(e,t)=>{const n=sa(e,t);if(!(n<0))return e[n]||null},nr=(e,t,n,r,i,a)=>{const s=Ke(null,e,{method:"GET",responseType:"blob"});return s.onload=d=>{const c=d.getAllResponseHeaders(),y=_n(c).name||ht(e);t(fe("load",d.status,ot(d.response,y),c))},s.onerror=d=>{n(fe("error",d.status,d.statusText,d.getAllResponseHeaders()))},s.onheaders=d=>{a(fe("headers",d.status,null,d.getAllResponseHeaders()))},s.ontimeout=Xe(n),s.onprogress=r,s.onabort=i,s},rr=e=>(e.indexOf("//")===0&&(e=location.protocol+e),e.toLowerCase().replace("blob:","").replace(/([a-z])?:\/\//,"$1").split("/")[0]),la=e=>(e.indexOf(":")>-1||e.indexOf("//")>-1)&&rr(location.href)!==rr(e),yt=e=>(...t)=>Ye(e)?e(...t):e,ua=e=>!Qe(e.file),Yt=(e,t)=>{clearTimeout(t.listUpdateTimeout),t.listUpdateTimeout=setTimeout(()=>{e("DID_UPDATE_ITEMS",{items:Le(t.items)})},0)},ir=(e,...t)=>new Promise(n=>{if(!e)return n(!0);const r=e(...t);if(r==null)return n(!0);if(typeof r=="boolean")return n(r);typeof r.then=="function"&&r.then(n)}),$t=(e,t)=>{e.items.sort((n,r)=>t(Ie(n),Ie(r)))},Re=(e,t)=>({query:n,success:r=()=>{},failure:i=()=>{},...a}={})=>{const s=$e(e.items,n);if(!s){i({error:fe("error",0,"Item not found"),file:null});return}t(s,r,i,a||{})},ca=(e,t,n)=>({ABORT_ALL:()=>{Le(n.items).forEach(r=>{r.freeze(),r.abortLoad(),r.abortProcessing()})},DID_SET_FILES:({value:r=[]})=>{const i=r.map(s=>({source:s.source?s.source:s,options:s.options}));let a=Le(n.items);a.forEach(s=>{i.find(d=>d.source===s.source||d.source===s.file)||e("REMOVE_ITEM",{query:s,remove:!1})}),a=Le(n.items),i.forEach((s,d)=>{a.find(c=>c.source===s.source||c.file===s.source)||e("ADD_ITEM",{...s,interactionMethod:De.NONE,index:d})})},DID_UPDATE_ITEM_METADATA:({id:r,action:i,change:a})=>{a.silent||(clearTimeout(n.itemUpdateTimeout),n.itemUpdateTimeout=setTimeout(()=>{const s=tr(n.items,r);if(!t("IS_ASYNC")){Ne("SHOULD_PREPARE_OUTPUT",!1,{item:s,query:t,action:i,change:a}).then(f=>{const u=t("GET_BEFORE_PREPARE_FILE");u&&(f=u(s,f)),f&&e("REQUEST_PREPARE_OUTPUT",{query:r,item:s,success:o=>{e("DID_PREPARE_OUTPUT",{id:r,file:o})}},!0)});return}s.origin===ve.LOCAL&&e("DID_LOAD_ITEM",{id:s.id,error:null,serverFileReference:s.source});const d=()=>{setTimeout(()=>{e("REQUEST_ITEM_PROCESSING",{query:r})},32)},c=f=>{s.revert(ut(n.options.server.url,n.options.server.revert),t("GET_FORCE_REVERT")).then(f?d:()=>{}).catch(()=>{})},y=f=>{s.abortProcessing().then(f?d:()=>{})};if(s.status===te.PROCESSING_COMPLETE)return c(n.options.instantUpload);if(s.status===te.PROCESSING)return y(n.options.instantUpload);n.options.instantUpload&&d()},0))},MOVE_ITEM:({query:r,index:i})=>{const a=$e(n.items,r);if(!a)return;const s=n.items.indexOf(a);i=Ur(i,0,n.items.length-1),s!==i&&n.items.splice(i,0,n.items.splice(s,1)[0])},SORT:({compare:r})=>{$t(n,r),e("DID_SORT_ITEMS",{items:t("GET_ACTIVE_ITEMS")})},ADD_ITEMS:({items:r,index:i,interactionMethod:a,success:s=()=>{},failure:d=()=>{}})=>{let c=i;if(i===-1||typeof i>"u"){const l=t("GET_ITEM_INSERT_LOCATION"),h=t("GET_TOTAL_ITEMS");c=l==="before"?0:h}const y=t("GET_IGNORED_FILES"),f=l=>Qe(l)?!y.includes(l.name.toLowerCase()):!ke(l),o=r.filter(f).map(l=>new Promise((h,v)=>{e("ADD_ITEM",{interactionMethod:a,source:l.source||l,success:h,failure:v,index:c++,options:l.options||{}})}));Promise.all(o).then(s).catch(d)},ADD_ITEM:({source:r,index:i=-1,interactionMethod:a,success:s=()=>{},failure:d=()=>{},options:c={}})=>{if(ke(r)){d({error:fe("error",0,"No source"),file:null});return}if(Qe(r)&&n.options.ignoredFiles.includes(r.name.toLowerCase()))return;if(!jo(n)){if(n.options.allowMultiple||!n.options.allowMultiple&&!n.options.allowReplace){const m=fe("warning",0,"Max files");e("DID_THROW_MAX_FILES",{source:r,error:m}),d({error:m,file:null});return}const p=Le(n.items)[0];if(p.status===te.PROCESSING_COMPLETE||p.status===te.PROCESSING_REVERT_ERROR){const m=t("GET_FORCE_REVERT");if(p.revert(ut(n.options.server.url,n.options.server.revert),m).then(()=>{m&&e("ADD_ITEM",{source:r,index:i,interactionMethod:a,success:s,failure:d,options:c})}).catch(()=>{}),m)return}e("REMOVE_ITEM",{query:p.id})}const y=c.type==="local"?ve.LOCAL:c.type==="limbo"?ve.LIMBO:ve.INPUT,f=aa(y,y===ve.INPUT?null:r,c.file);Object.keys(c.metadata||{}).forEach(p=>{f.setMetadata(p,c.metadata[p])}),Je("DID_CREATE_ITEM",f,{query:t,dispatch:e});const u=t("GET_ITEM_INSERT_LOCATION");n.options.itemInsertLocationFreedom||(i=u==="before"?-1:n.items.length),Go(n.items,f,i),Ye(u)&&r&&$t(n,u);const o=f.id;f.on("init",()=>{e("DID_INIT_ITEM",{id:o})}),f.on("load-init",()=>{e("DID_START_ITEM_LOAD",{id:o})}),f.on("load-meta",()=>{e("DID_UPDATE_ITEM_META",{id:o})}),f.on("load-progress",p=>{e("DID_UPDATE_ITEM_LOAD_PROGRESS",{id:o,progress:p})}),f.on("load-request-error",p=>{const m=yt(n.options.labelFileLoadError)(p);if(p.code>=400&&p.code<500){e("DID_THROW_ITEM_INVALID",{id:o,error:p,status:{main:m,sub:`${p.code} (${p.body})`}}),d({error:p,file:Ie(f)});return}e("DID_THROW_ITEM_LOAD_ERROR",{id:o,error:p,status:{main:m,sub:n.options.labelTapToRetry}})}),f.on("load-file-error",p=>{e("DID_THROW_ITEM_INVALID",{id:o,error:p.status,status:p.status}),d({error:p.status,file:Ie(f)})}),f.on("load-abort",()=>{e("REMOVE_ITEM",{query:o})}),f.on("load-skip",()=>{f.on("metadata-update",p=>{Qe(f.file)&&e("DID_UPDATE_ITEM_METADATA",{id:o,change:p})}),e("COMPLETE_LOAD_ITEM",{query:o,item:f,data:{source:r,success:s}})}),f.on("load",()=>{const p=m=>{if(!m){e("REMOVE_ITEM",{query:o});return}f.on("metadata-update",_=>{e("DID_UPDATE_ITEM_METADATA",{id:o,change:_})}),Ne("SHOULD_PREPARE_OUTPUT",!1,{item:f,query:t}).then(_=>{const g=t("GET_BEFORE_PREPARE_FILE");g&&(_=g(f,_));const b=()=>{e("COMPLETE_LOAD_ITEM",{query:o,item:f,data:{source:r,success:s}}),Yt(e,n)};if(_){e("REQUEST_PREPARE_OUTPUT",{query:o,item:f,success:A=>{e("DID_PREPARE_OUTPUT",{id:o,file:A}),b()}},!0);return}b()})};Ne("DID_LOAD_ITEM",f,{query:t,dispatch:e}).then(()=>{ir(t("GET_BEFORE_ADD_FILE"),Ie(f)).then(p)}).catch(m=>{if(!m||!m.error||!m.status)return p(!1);e("DID_THROW_ITEM_INVALID",{id:o,error:m.error,status:m.status})})}),f.on("process-start",()=>{e("DID_START_ITEM_PROCESSING",{id:o})}),f.on("process-progress",p=>{e("DID_UPDATE_ITEM_PROCESS_PROGRESS",{id:o,progress:p})}),f.on("process-error",p=>{e("DID_THROW_ITEM_PROCESSING_ERROR",{id:o,error:p,status:{main:yt(n.options.labelFileProcessingError)(p),sub:n.options.labelTapToRetry}})}),f.on("process-revert-error",p=>{e("DID_THROW_ITEM_PROCESSING_REVERT_ERROR",{id:o,error:p,status:{main:yt(n.options.labelFileProcessingRevertError)(p),sub:n.options.labelTapToRetry}})}),f.on("process-complete",p=>{e("DID_COMPLETE_ITEM_PROCESSING",{id:o,error:null,serverFileReference:p}),e("DID_DEFINE_VALUE",{id:o,value:p})}),f.on("process-abort",()=>{e("DID_ABORT_ITEM_PROCESSING",{id:o})}),f.on("process-revert",()=>{e("DID_REVERT_ITEM_PROCESSING",{id:o}),e("DID_DEFINE_VALUE",{id:o,value:null})}),e("DID_ADD_ITEM",{id:o,index:i,interactionMethod:a}),Yt(e,n);const{url:l,load:h,restore:v,fetch:E}=n.options.server||{};f.load(r,Jo(y===ve.INPUT?be(r)&&la(r)&&E?Wt(l,E):nr:y===ve.LIMBO?Wt(l,v):Wt(l,h)),(p,m,_)=>{Ne("LOAD_FILE",p,{query:t}).then(m).catch(_)})},REQUEST_PREPARE_OUTPUT:({item:r,success:i,failure:a=()=>{}})=>{const s={error:fe("error",0,"Item not found"),file:null};if(r.archived)return a(s);Ne("PREPARE_OUTPUT",r.file,{query:t,item:r}).then(d=>{Ne("COMPLETE_PREPARE_OUTPUT",d,{query:t,item:r}).then(c=>{if(r.archived)return a(s);i(c)})})},COMPLETE_LOAD_ITEM:({item:r,data:i})=>{const{success:a,source:s}=i,d=t("GET_ITEM_INSERT_LOCATION");if(Ye(d)&&s&&$t(n,d),e("DID_LOAD_ITEM",{id:r.id,error:null,serverFileReference:r.origin===ve.INPUT?null:s}),a(Ie(r)),r.origin===ve.LOCAL){e("DID_LOAD_LOCAL_ITEM",{id:r.id});return}if(r.origin===ve.LIMBO){e("DID_COMPLETE_ITEM_PROCESSING",{id:r.id,error:null,serverFileReference:s}),e("DID_DEFINE_VALUE",{id:r.id,value:r.serverId||s});return}t("IS_ASYNC")&&n.options.instantUpload&&e("REQUEST_ITEM_PROCESSING",{query:r.id})},RETRY_ITEM_LOAD:Re(n,r=>{r.retryLoad()}),REQUEST_ITEM_PREPARE:Re(n,(r,i,a)=>{e("REQUEST_PREPARE_OUTPUT",{query:r.id,item:r,success:s=>{e("DID_PREPARE_OUTPUT",{id:r.id,file:s}),i({file:r,output:s})},failure:a},!0)}),REQUEST_ITEM_PROCESSING:Re(n,(r,i,a)=>{if(!(r.status===te.IDLE||r.status===te.PROCESSING_ERROR)){const d=()=>e("REQUEST_ITEM_PROCESSING",{query:r,success:i,failure:a}),c=()=>document.hidden?d():setTimeout(d,32);r.status===te.PROCESSING_COMPLETE||r.status===te.PROCESSING_REVERT_ERROR?r.revert(ut(n.options.server.url,n.options.server.revert),t("GET_FORCE_REVERT")).then(c).catch(()=>{}):r.status===te.PROCESSING&&r.abortProcessing().then(c);return}r.status!==te.PROCESSING_QUEUED&&(r.requestProcessing(),e("DID_REQUEST_ITEM_PROCESSING",{id:r.id}),e("PROCESS_ITEM",{query:r,success:i,failure:a},!0))}),PROCESS_ITEM:Re(n,(r,i,a)=>{const s=t("GET_MAX_PARALLEL_UPLOADS");if(t("GET_ITEMS_BY_STATUS",te.PROCESSING).length===s){n.processingQueue.push({id:r.id,success:i,failure:a});return}if(r.status===te.PROCESSING)return;const c=()=>{const f=n.processingQueue.shift();if(!f)return;const{id:u,success:o,failure:l}=f,h=$e(n.items,u);if(!h||h.archived){c();return}e("PROCESS_ITEM",{query:u,success:o,failure:l},!0)};r.onOnce("process-complete",()=>{i(Ie(r)),c();const f=n.options.server;if(n.options.instantUpload&&r.origin===ve.LOCAL&&Ye(f.remove)){const l=()=>{};r.origin=ve.LIMBO,n.options.server.remove(r.source,l,l)}t("GET_ITEMS_BY_STATUS",te.PROCESSING_COMPLETE).length===n.items.length&&e("DID_COMPLETE_ITEM_PROCESSING_ALL")}),r.onOnce("process-error",f=>{a({error:f,file:Ie(r)}),c()});const y=n.options;r.process(ia(na(y.server.url,y.server.process,y.name,{chunkTransferId:r.transferId,chunkServer:y.server.patch,chunkUploads:y.chunkUploads,chunkForce:y.chunkForce,chunkSize:y.chunkSize,chunkRetryDelays:y.chunkRetryDelays}),{allowMinimumUploadDuration:t("GET_ALLOW_MINIMUM_UPLOAD_DURATION")}),(f,u,o)=>{Ne("PREPARE_OUTPUT",f,{query:t,item:r}).then(l=>{e("DID_PREPARE_OUTPUT",{id:r.id,file:l}),u(l)}).catch(o)})}),RETRY_ITEM_PROCESSING:Re(n,r=>{e("REQUEST_ITEM_PROCESSING",{query:r})}),REQUEST_REMOVE_ITEM:Re(n,r=>{ir(t("GET_BEFORE_REMOVE_FILE"),Ie(r)).then(i=>{i&&e("REMOVE_ITEM",{query:r})})}),RELEASE_ITEM:Re(n,r=>{r.release()}),REMOVE_ITEM:Re(n,(r,i,a,s)=>{const d=()=>{const y=r.id;tr(n.items,y).archive(),e("DID_REMOVE_ITEM",{error:null,id:y,item:r}),Yt(e,n),i(Ie(r))},c=n.options.server;r.origin===ve.LOCAL&&c&&Ye(c.remove)&&s.remove!==!1?(e("DID_START_ITEM_REMOVE",{id:r.id}),c.remove(r.source,()=>d(),y=>{e("DID_THROW_ITEM_REMOVE_ERROR",{id:r.id,error:fe("error",0,y,null),status:{main:yt(n.options.labelFileRemoveError)(y),sub:n.options.labelTapToRetry}})})):((s.revert&&r.origin!==ve.LOCAL&&r.serverId!==null||n.options.chunkUploads&&r.file.size>n.options.chunkSize||n.options.chunkUploads&&n.options.chunkForce)&&r.revert(ut(n.options.server.url,n.options.server.revert),t("GET_FORCE_REVERT")),d())}),ABORT_ITEM_LOAD:Re(n,r=>{r.abortLoad()}),ABORT_ITEM_PROCESSING:Re(n,r=>{if(r.serverId){e("REVERT_ITEM_PROCESSING",{id:r.id});return}r.abortProcessing().then(()=>{n.options.instantUpload&&e("REMOVE_ITEM",{query:r.id})})}),REQUEST_REVERT_ITEM_PROCESSING:Re(n,r=>{if(!n.options.instantUpload){e("REVERT_ITEM_PROCESSING",{query:r});return}const i=d=>{d&&e("REVERT_ITEM_PROCESSING",{query:r})},a=t("GET_BEFORE_REMOVE_FILE");if(!a)return i(!0);const s=a(Ie(r));if(s==null)return i(!0);if(typeof s=="boolean")return i(s);typeof s.then=="function"&&s.then(i)}),REVERT_ITEM_PROCESSING:Re(n,r=>{r.revert(ut(n.options.server.url,n.options.server.revert),t("GET_FORCE_REVERT")).then(()=>{(n.options.instantUpload||ua(r))&&e("REMOVE_ITEM",{query:r.id})}).catch(()=>{})}),SET_OPTIONS:({options:r})=>{const i=Object.keys(r),a=fa.filter(d=>i.includes(d));[...a,...Object.keys(r).filter(d=>!a.includes(d))].forEach(d=>{e(`SET_${Mt(d,"_").toUpperCase()}`,{value:r[d]})})}}),fa=["server"],bn=e=>e,Be=e=>document.createElement(e),de=(e,t)=>{let n=e.childNodes[0];n?t!==n.nodeValue&&(n.nodeValue=t):(n=document.createTextNode(t),e.appendChild(n))},or=(e,t,n,r)=>{const i=(r%360-90)*Math.PI/180;return{x:e+n*Math.cos(i),y:t+n*Math.sin(i)}},da=(e,t,n,r,i,a)=>{const s=or(e,t,n,i),d=or(e,t,n,r);return["M",s.x,s.y,"A",n,n,0,a,0,d.x,d.y].join(" ")},ha=(e,t,n,r,i)=>{let a=1;return i>r&&i-r<=.5&&(a=0),r>i&&r-i>=.5&&(a=0),da(e,t,n,Math.min(.9999,r)*360,Math.min(.9999,i)*360,a)},pa=({root:e,props:t})=>{t.spin=!1,t.progress=0,t.opacity=0;const n=At("svg");e.ref.path=At("path",{"stroke-width":2,"stroke-linecap":"round"}),n.appendChild(e.ref.path),e.ref.svg=n,e.appendChild(n)},ga=({root:e,props:t})=>{if(t.opacity===0)return;t.align&&(e.element.dataset.align=t.align);const n=parseInt(ye(e.ref.path,"stroke-width"),10),r=e.rect.element.width*.5;let i=0,a=0;t.spin?(i=0,a=.5):(i=0,a=t.progress);const s=ha(r,r,r-n,i,a);ye(e.ref.path,"d",s),ye(e.ref.path,"stroke-opacity",t.spin||t.progress>0?1:0)},ar=pe({tag:"div",name:"progress-indicator",ignoreRectUpdate:!0,ignoreRect:!0,create:pa,write:ga,mixins:{apis:["progress","spin","align"],styles:["opacity"],animations:{opacity:{type:"tween",duration:500},progress:{type:"spring",stiffness:.95,damping:.65,mass:10}}}}),va=({root:e,props:t})=>{e.element.innerHTML=(t.icon||"")+`<span>${t.label}</span>`,t.isDisabled=!1},ma=({root:e,props:t})=>{const{isDisabled:n}=t,r=e.query("GET_DISABLED")||t.opacity===0;r&&!n?(t.isDisabled=!0,ye(e.element,"disabled","disabled")):!r&&n&&(t.isDisabled=!1,e.element.removeAttribute("disabled"))},$r=pe({tag:"button",attributes:{type:"button"},ignoreRect:!0,ignoreRectUpdate:!0,name:"file-action-button",mixins:{apis:["label"],styles:["translateX","translateY","scaleX","scaleY","opacity"],animations:{scaleX:"spring",scaleY:"spring",translateX:"spring",translateY:"spring",opacity:{type:"tween",duration:250}},listeners:!0},create:va,write:ma}),Kr=(e,t=".",n=1e3,r={})=>{const{labelBytes:i="bytes",labelKilobytes:a="KB",labelMegabytes:s="MB",labelGigabytes:d="GB"}=r;e=Math.round(Math.abs(e));const c=n,y=n*n,f=n*n*n;return e<c?`${e} ${i}`:e<y?`${Math.floor(e/c)} ${a}`:e<f?`${sr(e/y,1,t)} ${s}`:`${sr(e/f,2,t)} ${d}`},sr=(e,t,n)=>e.toFixed(t).split(".").filter(r=>r!=="0").join(n),ya=({root:e,props:t})=>{const n=Be("span");n.className="filepond--file-info-main",ye(n,"aria-hidden","true"),e.appendChild(n),e.ref.fileName=n;const r=Be("span");r.className="filepond--file-info-sub",e.appendChild(r),e.ref.fileSize=r,de(r,e.query("GET_LABEL_FILE_WAITING_FOR_SIZE")),de(n,bn(e.query("GET_ITEM_NAME",t.id)))},nn=({root:e,props:t})=>{de(e.ref.fileSize,Kr(e.query("GET_ITEM_SIZE",t.id),".",e.query("GET_FILE_SIZE_BASE"),e.query("GET_FILE_SIZE_LABELS",e.query))),de(e.ref.fileName,bn(e.query("GET_ITEM_NAME",t.id)))},lr=({root:e,props:t})=>{if(at(e.query("GET_ITEM_SIZE",t.id))){nn({root:e,props:t});return}de(e.ref.fileSize,e.query("GET_LABEL_FILE_SIZE_NOT_AVAILABLE"))},Ea=pe({name:"file-info",ignoreRect:!0,ignoreRectUpdate:!0,write:Te({DID_LOAD_ITEM:nn,DID_UPDATE_ITEM_META:nn,DID_THROW_ITEM_LOAD_ERROR:lr,DID_THROW_ITEM_INVALID:lr}),didCreateView:e=>{Je("CREATE_VIEW",{...e,view:e})},create:ya,mixins:{styles:["translateX","translateY"],animations:{translateX:"spring",translateY:"spring"}}}),Xr=e=>Math.round(e*100),_a=({root:e})=>{const t=Be("span");t.className="filepond--file-status-main",e.appendChild(t),e.ref.main=t;const n=Be("span");n.className="filepond--file-status-sub",e.appendChild(n),e.ref.sub=n,Qr({root:e,action:{progress:null}})},Qr=({root:e,action:t})=>{const n=t.progress===null?e.query("GET_LABEL_FILE_LOADING"):`${e.query("GET_LABEL_FILE_LOADING")} ${Xr(t.progress)}%`;de(e.ref.main,n),de(e.ref.sub,e.query("GET_LABEL_TAP_TO_CANCEL"))},ba=({root:e,action:t})=>{const n=t.progress===null?e.query("GET_LABEL_FILE_PROCESSING"):`${e.query("GET_LABEL_FILE_PROCESSING")} ${Xr(t.progress)}%`;de(e.ref.main,n),de(e.ref.sub,e.query("GET_LABEL_TAP_TO_CANCEL"))},Ta=({root:e})=>{de(e.ref.main,e.query("GET_LABEL_FILE_PROCESSING")),de(e.ref.sub,e.query("GET_LABEL_TAP_TO_CANCEL"))},Oa=({root:e})=>{de(e.ref.main,e.query("GET_LABEL_FILE_PROCESSING_ABORTED")),de(e.ref.sub,e.query("GET_LABEL_TAP_TO_RETRY"))},Ia=({root:e})=>{de(e.ref.main,e.query("GET_LABEL_FILE_PROCESSING_COMPLETE")),de(e.ref.sub,e.query("GET_LABEL_TAP_TO_UNDO"))},ur=({root:e})=>{de(e.ref.main,""),de(e.ref.sub,"")},ct=({root:e,action:t})=>{de(e.ref.main,t.status.main),de(e.ref.sub,t.status.sub)},wa=pe({name:"file-status",ignoreRect:!0,ignoreRectUpdate:!0,write:Te({DID_LOAD_ITEM:ur,DID_REVERT_ITEM_PROCESSING:ur,DID_REQUEST_ITEM_PROCESSING:Ta,DID_ABORT_ITEM_PROCESSING:Oa,DID_COMPLETE_ITEM_PROCESSING:Ia,DID_UPDATE_ITEM_PROCESS_PROGRESS:ba,DID_UPDATE_ITEM_LOAD_PROGRESS:Qr,DID_THROW_ITEM_LOAD_ERROR:ct,DID_THROW_ITEM_INVALID:ct,DID_THROW_ITEM_PROCESSING_ERROR:ct,DID_THROW_ITEM_PROCESSING_REVERT_ERROR:ct,DID_THROW_ITEM_REMOVE_ERROR:ct}),didCreateView:e=>{Je("CREATE_VIEW",{...e,view:e})},create:_a,mixins:{styles:["translateX","translateY","opacity"],animations:{opacity:{type:"tween",duration:250},translateX:"spring",translateY:"spring"}}}),rn={AbortItemLoad:{label:"GET_LABEL_BUTTON_ABORT_ITEM_LOAD",action:"ABORT_ITEM_LOAD",className:"filepond--action-abort-item-load",align:"LOAD_INDICATOR_POSITION"},RetryItemLoad:{label:"GET_LABEL_BUTTON_RETRY_ITEM_LOAD",action:"RETRY_ITEM_LOAD",icon:"GET_ICON_RETRY",className:"filepond--action-retry-item-load",align:"BUTTON_PROCESS_ITEM_POSITION"},RemoveItem:{label:"GET_LABEL_BUTTON_REMOVE_ITEM",action:"REQUEST_REMOVE_ITEM",icon:"GET_ICON_REMOVE",className:"filepond--action-remove-item",align:"BUTTON_REMOVE_ITEM_POSITION"},ProcessItem:{label:"GET_LABEL_BUTTON_PROCESS_ITEM",action:"REQUEST_ITEM_PROCESSING",icon:"GET_ICON_PROCESS",className:"filepond--action-process-item",align:"BUTTON_PROCESS_ITEM_POSITION"},AbortItemProcessing:{label:"GET_LABEL_BUTTON_ABORT_ITEM_PROCESSING",action:"ABORT_ITEM_PROCESSING",className:"filepond--action-abort-item-processing",align:"BUTTON_PROCESS_ITEM_POSITION"},RetryItemProcessing:{label:"GET_LABEL_BUTTON_RETRY_ITEM_PROCESSING",action:"RETRY_ITEM_PROCESSING",icon:"GET_ICON_RETRY",className:"filepond--action-retry-item-processing",align:"BUTTON_PROCESS_ITEM_POSITION"},RevertItemProcessing:{label:"GET_LABEL_BUTTON_UNDO_ITEM_PROCESSING",action:"REQUEST_REVERT_ITEM_PROCESSING",icon:"GET_ICON_UNDO",className:"filepond--action-revert-item-processing",align:"BUTTON_PROCESS_ITEM_POSITION"}},on=[];ce(rn,e=>{on.push(e)});const we=e=>{if(an(e)==="right")return 0;const t=e.ref.buttonRemoveItem.rect.element;return t.hidden?null:t.width+t.left},Sa=e=>e.ref.buttonAbortItemLoad.rect.element.width,Et=e=>Math.floor(e.ref.buttonRemoveItem.rect.element.height/4),Aa=e=>Math.floor(e.ref.buttonRemoveItem.rect.element.left/2),Ra=e=>e.query("GET_STYLE_LOAD_INDICATOR_POSITION"),Da=e=>e.query("GET_STYLE_PROGRESS_INDICATOR_POSITION"),an=e=>e.query("GET_STYLE_BUTTON_REMOVE_ITEM_POSITION"),Na={buttonAbortItemLoad:{opacity:0},buttonRetryItemLoad:{opacity:0},buttonRemoveItem:{opacity:0},buttonProcessItem:{opacity:0},buttonAbortItemProcessing:{opacity:0},buttonRetryItemProcessing:{opacity:0},buttonRevertItemProcessing:{opacity:0},loadProgressIndicator:{opacity:0,align:Ra},processProgressIndicator:{opacity:0,align:Da},processingCompleteIndicator:{opacity:0,scaleX:.75,scaleY:.75},info:{translateX:0,translateY:0,opacity:0},status:{translateX:0,translateY:0,opacity:0}},cr={buttonRemoveItem:{opacity:1},buttonProcessItem:{opacity:1},info:{translateX:we},status:{translateX:we}},Kt={buttonAbortItemProcessing:{opacity:1},processProgressIndicator:{opacity:1},status:{opacity:1}},rt={DID_THROW_ITEM_INVALID:{buttonRemoveItem:{opacity:1},info:{translateX:we},status:{translateX:we,opacity:1}},DID_START_ITEM_LOAD:{buttonAbortItemLoad:{opacity:1},loadProgressIndicator:{opacity:1},status:{opacity:1}},DID_THROW_ITEM_LOAD_ERROR:{buttonRetryItemLoad:{opacity:1},buttonRemoveItem:{opacity:1},info:{translateX:we},status:{opacity:1}},DID_START_ITEM_REMOVE:{processProgressIndicator:{opacity:1,align:an},info:{translateX:we},status:{opacity:0}},DID_THROW_ITEM_REMOVE_ERROR:{processProgressIndicator:{opacity:0,align:an},buttonRemoveItem:{opacity:1},info:{translateX:we},status:{opacity:1,translateX:we}},DID_LOAD_ITEM:cr,DID_LOAD_LOCAL_ITEM:{buttonRemoveItem:{opacity:1},info:{translateX:we},status:{translateX:we}},DID_START_ITEM_PROCESSING:Kt,DID_REQUEST_ITEM_PROCESSING:Kt,DID_UPDATE_ITEM_PROCESS_PROGRESS:Kt,DID_COMPLETE_ITEM_PROCESSING:{buttonRevertItemProcessing:{opacity:1},info:{opacity:1},status:{opacity:1}},DID_THROW_ITEM_PROCESSING_ERROR:{buttonRemoveItem:{opacity:1},buttonRetryItemProcessing:{opacity:1},status:{opacity:1},info:{translateX:we}},DID_THROW_ITEM_PROCESSING_REVERT_ERROR:{buttonRevertItemProcessing:{opacity:1},status:{opacity:1},info:{opacity:1}},DID_ABORT_ITEM_PROCESSING:{buttonRemoveItem:{opacity:1},buttonProcessItem:{opacity:1},info:{translateX:we},status:{opacity:1}},DID_REVERT_ITEM_PROCESSING:cr},La=pe({create:({root:e})=>{e.element.innerHTML=e.query("GET_ICON_DONE")},name:"processing-complete-indicator",ignoreRect:!0,mixins:{styles:["scaleX","scaleY","opacity"],animations:{scaleX:"spring",scaleY:"spring",opacity:{type:"tween",duration:250}}}}),Pa=({root:e,props:t})=>{const n=Object.keys(rn).reduce((h,v)=>(h[v]={...rn[v]},h),{}),{id:r}=t,i=e.query("GET_ALLOW_REVERT"),a=e.query("GET_ALLOW_REMOVE"),s=e.query("GET_ALLOW_PROCESS"),d=e.query("GET_INSTANT_UPLOAD"),c=e.query("IS_ASYNC"),y=e.query("GET_STYLE_BUTTON_REMOVE_ITEM_ALIGN");let f;c?s&&!i?f=h=>!/RevertItemProcessing/.test(h):!s&&i?f=h=>!/ProcessItem|RetryItemProcessing|AbortItemProcessing/.test(h):!s&&!i&&(f=h=>!/Process/.test(h)):f=h=>!/Process/.test(h);const u=f?on.filter(f):on.concat();if(d&&i&&(n.RevertItemProcessing.label="GET_LABEL_BUTTON_REMOVE_ITEM",n.RevertItemProcessing.icon="GET_ICON_REMOVE"),c&&!i){const h=rt.DID_COMPLETE_ITEM_PROCESSING;h.info.translateX=Aa,h.info.translateY=Et,h.status.translateY=Et,h.processingCompleteIndicator={opacity:1,scaleX:1,scaleY:1}}if(c&&!s&&(["DID_START_ITEM_PROCESSING","DID_REQUEST_ITEM_PROCESSING","DID_UPDATE_ITEM_PROCESS_PROGRESS","DID_THROW_ITEM_PROCESSING_ERROR"].forEach(h=>{rt[h].status.translateY=Et}),rt.DID_THROW_ITEM_PROCESSING_ERROR.status.translateX=Sa),y&&i){n.RevertItemProcessing.align="BUTTON_REMOVE_ITEM_POSITION";const h=rt.DID_COMPLETE_ITEM_PROCESSING;h.info.translateX=we,h.status.translateY=Et,h.processingCompleteIndicator={opacity:1,scaleX:1,scaleY:1}}a||(n.RemoveItem.disabled=!0),ce(n,(h,v)=>{const E=e.createChildView($r,{label:e.query(v.label),icon:e.query(v.icon),opacity:0});u.includes(h)&&e.appendChildView(E),v.disabled&&(E.element.setAttribute("disabled","disabled"),E.element.setAttribute("hidden","hidden")),E.element.dataset.align=e.query(`GET_STYLE_${v.align}`),E.element.classList.add(v.className),E.on("click",p=>{p.stopPropagation(),!v.disabled&&e.dispatch(v.action,{query:r})}),e.ref[`button${h}`]=E}),e.ref.processingCompleteIndicator=e.appendChildView(e.createChildView(La)),e.ref.processingCompleteIndicator.element.dataset.align=e.query("GET_STYLE_BUTTON_PROCESS_ITEM_POSITION"),e.ref.info=e.appendChildView(e.createChildView(Ea,{id:r})),e.ref.status=e.appendChildView(e.createChildView(wa,{id:r}));const o=e.appendChildView(e.createChildView(ar,{opacity:0,align:e.query("GET_STYLE_LOAD_INDICATOR_POSITION")}));o.element.classList.add("filepond--load-indicator"),e.ref.loadProgressIndicator=o;const l=e.appendChildView(e.createChildView(ar,{opacity:0,align:e.query("GET_STYLE_PROGRESS_INDICATOR_POSITION")}));l.element.classList.add("filepond--process-indicator"),e.ref.processProgressIndicator=l,e.ref.activeStyles=[]},Ma=({root:e,actions:t,props:n})=>{xa({root:e,actions:t,props:n});let r=t.concat().filter(i=>/^DID_/.test(i.type)).reverse().find(i=>rt[i.type]);if(r){e.ref.activeStyles=[];const i=rt[r.type];ce(Na,(a,s)=>{const d=e.ref[a];ce(s,(c,y)=>{const f=i[a]&&typeof i[a][c]<"u"?i[a][c]:y;e.ref.activeStyles.push({control:d,key:c,value:f})})})}e.ref.activeStyles.forEach(({control:i,key:a,value:s})=>{i[a]=typeof s=="function"?s(e):s})},xa=Te({DID_SET_LABEL_BUTTON_ABORT_ITEM_PROCESSING:({root:e,action:t})=>{e.ref.buttonAbortItemProcessing.label=t.value},DID_SET_LABEL_BUTTON_ABORT_ITEM_LOAD:({root:e,action:t})=>{e.ref.buttonAbortItemLoad.label=t.value},DID_SET_LABEL_BUTTON_ABORT_ITEM_REMOVAL:({root:e,action:t})=>{e.ref.buttonAbortItemRemoval.label=t.value},DID_REQUEST_ITEM_PROCESSING:({root:e})=>{e.ref.processProgressIndicator.spin=!0,e.ref.processProgressIndicator.progress=0},DID_START_ITEM_LOAD:({root:e})=>{e.ref.loadProgressIndicator.spin=!0,e.ref.loadProgressIndicator.progress=0},DID_START_ITEM_REMOVE:({root:e})=>{e.ref.processProgressIndicator.spin=!0,e.ref.processProgressIndicator.progress=0},DID_UPDATE_ITEM_LOAD_PROGRESS:({root:e,action:t})=>{e.ref.loadProgressIndicator.spin=!1,e.ref.loadProgressIndicator.progress=t.progress},DID_UPDATE_ITEM_PROCESS_PROGRESS:({root:e,action:t})=>{e.ref.processProgressIndicator.spin=!1,e.ref.processProgressIndicator.progress=t.progress}}),Ca=pe({create:Pa,write:Ma,didCreateView:e=>{Je("CREATE_VIEW",{...e,view:e})},name:"file"}),ka=({root:e,props:t})=>{e.ref.fileName=Be("legend"),e.appendChild(e.ref.fileName),e.ref.file=e.appendChildView(e.createChildView(Ca,{id:t.id})),e.ref.data=!1},Ba=({root:e,props:t})=>{de(e.ref.fileName,bn(e.query("GET_ITEM_NAME",t.id)))},qa=pe({create:ka,ignoreRect:!0,write:Te({DID_LOAD_ITEM:Ba}),didCreateView:e=>{Je("CREATE_VIEW",{...e,view:e})},tag:"fieldset",name:"file-wrapper"}),fr={type:"spring",damping:.6,mass:7},Fa=({root:e,props:t})=>{[{name:"top"},{name:"center",props:{translateY:null,scaleY:null},mixins:{animations:{scaleY:fr},styles:["translateY","scaleY"]}},{name:"bottom",props:{translateY:null},mixins:{animations:{translateY:fr},styles:["translateY"]}}].forEach(n=>{ja(e,n,t.name)}),e.element.classList.add(`filepond--${t.name}`),e.ref.scalable=null},ja=(e,t,n)=>{const r=pe({name:`panel-${t.name} filepond--${n}`,mixins:t.mixins,ignoreRectUpdate:!0}),i=e.createChildView(r,t.props);e.ref[t.name]=e.appendChildView(i)},Ua=({root:e,props:t})=>{if((e.ref.scalable===null||t.scalable!==e.ref.scalable)&&(e.ref.scalable=Pr(t.scalable)?t.scalable:!0,e.element.dataset.scalable=e.ref.scalable),!t.height)return;const n=e.ref.top.rect.element,r=e.ref.bottom.rect.element,i=Math.max(n.height+r.height,t.height);e.ref.center.translateY=n.height,e.ref.center.scaleY=(i-n.height-r.height)/100,e.ref.bottom.translateY=i-r.height},Zr=pe({name:"panel",read:({root:e,props:t})=>t.heightCurrent=e.ref.bottom.translateY,write:Ua,create:Fa,ignoreRect:!0,mixins:{apis:["height","heightCurrent","scalable"]}}),Ga=e=>{const t=e.map(r=>r.id);let n;return{setIndex:r=>{n=r},getIndex:()=>n,getItemIndex:r=>t.indexOf(r.id)}},dr={type:"spring",stiffness:.75,damping:.45,mass:10},hr="spring",pr={DID_START_ITEM_LOAD:"busy",DID_UPDATE_ITEM_LOAD_PROGRESS:"loading",DID_THROW_ITEM_INVALID:"load-invalid",DID_THROW_ITEM_LOAD_ERROR:"load-error",DID_LOAD_ITEM:"idle",DID_THROW_ITEM_REMOVE_ERROR:"remove-error",DID_START_ITEM_REMOVE:"busy",DID_START_ITEM_PROCESSING:"busy processing",DID_REQUEST_ITEM_PROCESSING:"busy processing",DID_UPDATE_ITEM_PROCESS_PROGRESS:"processing",DID_COMPLETE_ITEM_PROCESSING:"processing-complete",DID_THROW_ITEM_PROCESSING_ERROR:"processing-error",DID_THROW_ITEM_PROCESSING_REVERT_ERROR:"processing-revert-error",DID_ABORT_ITEM_PROCESSING:"cancelled",DID_REVERT_ITEM_PROCESSING:"idle"},Va=({root:e,props:t})=>{if(e.ref.handleClick=r=>e.dispatch("DID_ACTIVATE_ITEM",{id:t.id}),e.element.id=`filepond--item-${t.id}`,e.element.addEventListener("click",e.ref.handleClick),e.ref.container=e.appendChildView(e.createChildView(qa,{id:t.id})),e.ref.panel=e.appendChildView(e.createChildView(Zr,{name:"item-panel"})),e.ref.panel.height=null,t.markedForRemoval=!1,!e.query("GET_ALLOW_REORDER"))return;e.element.dataset.dragState="idle";const n=r=>{if(!r.isPrimary)return;let i=!1;const a={x:r.pageX,y:r.pageY};t.dragOrigin={x:e.translateX,y:e.translateY},t.dragCenter={x:r.offsetX,y:r.offsetY};const s=Ga(e.query("GET_ACTIVE_ITEMS"));e.dispatch("DID_GRAB_ITEM",{id:t.id,dragState:s});const d=u=>{if(!u.isPrimary)return;u.stopPropagation(),u.preventDefault(),t.dragOffset={x:u.pageX-a.x,y:u.pageY-a.y},t.dragOffset.x*t.dragOffset.x+t.dragOffset.y*t.dragOffset.y>16&&!i&&(i=!0,e.element.removeEventListener("click",e.ref.handleClick)),e.dispatch("DID_DRAG_ITEM",{id:t.id,dragState:s})},c=u=>{u.isPrimary&&(t.dragOffset={x:u.pageX-a.x,y:u.pageY-a.y},f())},y=()=>{f()},f=()=>{document.removeEventListener("pointercancel",y),document.removeEventListener("pointermove",d),document.removeEventListener("pointerup",c),e.dispatch("DID_DROP_ITEM",{id:t.id,dragState:s}),i&&setTimeout(()=>e.element.addEventListener("click",e.ref.handleClick),0)};document.addEventListener("pointercancel",y),document.addEventListener("pointermove",d),document.addEventListener("pointerup",c)};e.element.addEventListener("pointerdown",n)},Ha=Te({DID_UPDATE_PANEL_HEIGHT:({root:e,action:t})=>{e.height=t.height}}),za=Te({DID_GRAB_ITEM:({root:e,props:t})=>{t.dragOrigin={x:e.translateX,y:e.translateY}},DID_DRAG_ITEM:({root:e})=>{e.element.dataset.dragState="drag"},DID_DROP_ITEM:({root:e,props:t})=>{t.dragOffset=null,t.dragOrigin=null,e.element.dataset.dragState="drop"}},({root:e,actions:t,props:n,shouldOptimize:r})=>{e.element.dataset.dragState==="drop"&&e.scaleX<=1&&(e.element.dataset.dragState="idle");let i=t.concat().filter(s=>/^DID_/.test(s.type)).reverse().find(s=>pr[s.type]);i&&i.type!==n.currentState&&(n.currentState=i.type,e.element.dataset.filepondItemState=pr[n.currentState]||"");const a=e.query("GET_ITEM_PANEL_ASPECT_RATIO")||e.query("GET_PANEL_ASPECT_RATIO");a?r||(e.height=e.rect.element.width*a):(Ha({root:e,actions:t,props:n}),!e.height&&e.ref.container.rect.element.height>0&&(e.height=e.ref.container.rect.element.height)),r&&(e.ref.panel.height=null),e.ref.panel.height=e.height}),Wa=pe({create:Va,write:za,destroy:({root:e,props:t})=>{e.element.removeEventListener("click",e.ref.handleClick),e.dispatch("RELEASE_ITEM",{query:t.id})},tag:"li",name:"item",mixins:{apis:["id","interactionMethod","markedForRemoval","spawnDate","dragCenter","dragOrigin","dragOffset"],styles:["translateX","translateY","scaleX","scaleY","opacity","height"],animations:{scaleX:hr,scaleY:hr,translateX:dr,translateY:dr,opacity:{type:"tween",duration:150}}}});var Tn=(e,t)=>Math.max(1,Math.floor((e+1)/t));const On=(e,t,n)=>{if(!n)return;const r=e.rect.element.width,i=t.length;let a=null;if(i===0||n.top<t[0].rect.element.top)return-1;const d=t[0].rect.element,c=d.marginLeft+d.marginRight,y=d.width+c,f=Tn(r,y);if(f===1){for(let l=0;l<i;l++){const h=t[l],v=h.rect.outer.top+h.rect.element.height*.5;if(n.top<v)return l}return i}const u=d.marginTop+d.marginBottom,o=d.height+u;for(let l=0;l<i;l++){const h=l%f,v=Math.floor(l/f),E=h*y,p=v*o,m=p-d.marginTop,_=E+y,g=p+o+d.marginBottom;if(n.top<g&&n.top>m){if(n.left<_)return l;l!==i-1?a=l:a=null}}return a!==null?a:i},_t={height:0,width:0,get getHeight(){return this.height},set setHeight(e){(this.height===0||e===0)&&(this.height=e)},get getWidth(){return this.width},set setWidth(e){(this.width===0||e===0)&&(this.width=e)}},Ya=({root:e})=>{ye(e.element,"role","list"),e.ref.lastItemSpanwDate=Date.now()},$a=({root:e,action:t})=>{const{id:n,index:r,interactionMethod:i}=t;e.ref.addIndex=r;const a=Date.now();let s=a,d=1;if(i!==De.NONE){d=0;const c=e.query("GET_ITEM_INSERT_INTERVAL"),y=a-e.ref.lastItemSpanwDate;s=y<c?a+(c-y):a}e.ref.lastItemSpanwDate=s,e.appendChildView(e.createChildView(Wa,{spawnDate:s,id:n,opacity:d,interactionMethod:i}),r)},gr=(e,t,n,r=0,i=1)=>{e.dragOffset?(e.translateX=null,e.translateY=null,e.translateX=e.dragOrigin.x+e.dragOffset.x,e.translateY=e.dragOrigin.y+e.dragOffset.y,e.scaleX=1.025,e.scaleY=1.025):(e.translateX=t,e.translateY=n,Date.now()>e.spawnDate&&(e.opacity===0&&Ka(e,t,n,r,i),e.scaleX=1,e.scaleY=1,e.opacity=1))},Ka=(e,t,n,r,i)=>{e.interactionMethod===De.NONE?(e.translateX=null,e.translateX=t,e.translateY=null,e.translateY=n):e.interactionMethod===De.DROP?(e.translateX=null,e.translateX=t-r*20,e.translateY=null,e.translateY=n-i*10,e.scaleX=.8,e.scaleY=.8):e.interactionMethod===De.BROWSE?(e.translateY=null,e.translateY=n-30):e.interactionMethod===De.API&&(e.translateX=null,e.translateX=t-30,e.translateY=null)},Xa=({root:e,action:t})=>{const{id:n}=t,r=e.childViews.find(i=>i.id===n);r&&(r.scaleX=.9,r.scaleY=.9,r.opacity=0,r.markedForRemoval=!0)},Xt=e=>e.rect.element.height+e.rect.element.marginBottom*.5+e.rect.element.marginTop*.5,Qa=e=>e.rect.element.width+e.rect.element.marginLeft*.5+e.rect.element.marginRight*.5,Za=({root:e,action:t})=>{const{id:n,dragState:r}=t,i=e.query("GET_ITEM",{id:n}),a=e.childViews.find(E=>E.id===n),s=e.childViews.length,d=r.getItemIndex(i);if(!a)return;const c={x:a.dragOrigin.x+a.dragOffset.x+a.dragCenter.x,y:a.dragOrigin.y+a.dragOffset.y+a.dragCenter.y},y=Xt(a),f=Qa(a);let u=Math.floor(e.rect.outer.width/f);u>s&&(u=s);const o=Math.floor(s/u+1);_t.setHeight=y*o,_t.setWidth=f*u;var l={y:Math.floor(c.y/y),x:Math.floor(c.x/f),getGridIndex:function(){return c.y>_t.getHeight||c.y<0||c.x>_t.getWidth||c.x<0?d:this.y*u+this.x},getColIndex:function(){const p=e.query("GET_ACTIVE_ITEMS"),m=e.childViews.filter(T=>T.rect.element.height),_=p.map(T=>m.find(O=>O.id===T.id)),g=_.findIndex(T=>T===a),b=Xt(a),A=_.length;let S=A,I=0,N=0,w=0;for(let T=0;T<A;T++)if(I=Xt(_[T]),w=N,N=w+I,c.y<N){if(g>T){if(c.y<w+b){S=T;break}continue}S=T;break}return S}};const h=u>1?l.getGridIndex():l.getColIndex();e.dispatch("MOVE_ITEM",{query:a,index:h});const v=r.getIndex();if(v===void 0||v!==h){if(r.setIndex(h),v===void 0)return;e.dispatch("DID_REORDER_ITEMS",{items:e.query("GET_ACTIVE_ITEMS"),origin:d,target:h})}},Ja=Te({DID_ADD_ITEM:$a,DID_REMOVE_ITEM:Xa,DID_DRAG_ITEM:Za}),es=({root:e,props:t,actions:n,shouldOptimize:r})=>{Ja({root:e,props:t,actions:n});const{dragCoordinates:i}=t,a=e.rect.element.width,s=e.childViews.filter(_=>_.rect.element.height),d=e.query("GET_ACTIVE_ITEMS").map(_=>s.find(g=>g.id===_.id)).filter(_=>_),c=i?On(e,d,i):null,y=e.ref.addIndex||null;e.ref.addIndex=null;let f=0,u=0,o=0;if(d.length===0)return;const l=d[0].rect.element,h=l.marginTop+l.marginBottom,v=l.marginLeft+l.marginRight,E=l.width+v,p=l.height+h,m=Tn(a,E);if(m===1){let _=0,g=0;d.forEach((b,A)=>{if(c){let N=A-c;N===-2?g=-h*.25:N===-1?g=-h*.75:N===0?g=h*.75:N===1?g=h*.25:g=0}r&&(b.translateX=null,b.translateY=null),b.markedForRemoval||gr(b,0,_+g);let I=(b.rect.element.height+h)*(b.markedForRemoval?b.opacity:1);_+=I})}else{let _=0,g=0;d.forEach((b,A)=>{A===c&&(f=1),A===y&&(o+=1),b.markedForRemoval&&b.opacity<.5&&(u-=1);const S=A+o+f+u,I=S%m,N=Math.floor(S/m),w=I*E,T=N*p,O=Math.sign(w-_),L=Math.sign(T-g);_=w,g=T,!b.markedForRemoval&&(r&&(b.translateX=null,b.translateY=null),gr(b,w,T,O,L))})}},ts=(e,t)=>t.filter(n=>n.data&&n.data.id?e.id===n.data.id:!0),ns=pe({create:Ya,write:es,tag:"ul",name:"list",didWriteView:({root:e})=>{e.childViews.filter(t=>t.markedForRemoval&&t.opacity===0&&t.resting).forEach(t=>{t._destroy(),e.removeChildView(t)})},filterFrameActionsForChild:ts,mixins:{apis:["dragCoordinates"]}}),rs=({root:e,props:t})=>{e.ref.list=e.appendChildView(e.createChildView(ns)),t.dragCoordinates=null,t.overflowing=!1},is=({root:e,props:t,action:n})=>{e.query("GET_ITEM_INSERT_LOCATION_FREEDOM")&&(t.dragCoordinates={left:n.position.scopeLeft-e.ref.list.rect.element.left,top:n.position.scopeTop-(e.rect.outer.top+e.rect.element.marginTop+e.rect.element.scrollTop)})},os=({props:e})=>{e.dragCoordinates=null},as=Te({DID_DRAG:is,DID_END_DRAG:os}),ss=({root:e,props:t,actions:n})=>{if(as({root:e,props:t,actions:n}),e.ref.list.dragCoordinates=t.dragCoordinates,t.overflowing&&!t.overflow&&(t.overflowing=!1,e.element.dataset.state="",e.height=null),t.overflow){const r=Math.round(t.overflow);r!==e.height&&(t.overflowing=!0,e.element.dataset.state="overflow",e.height=r)}},ls=pe({create:rs,write:ss,name:"list-scroller",mixins:{apis:["overflow","dragCoordinates"],styles:["height","translateY"],animations:{translateY:"spring"}}}),Me=(e,t,n,r="")=>{n?ye(e,t,r):e.removeAttribute(t)},us=e=>{if(!(!e||e.value==="")){try{e.value=""}catch{}if(e.value){const t=Be("form"),n=e.parentNode,r=e.nextSibling;t.appendChild(e),t.reset(),r?n.insertBefore(e,r):n.appendChild(e)}}},cs=({root:e,props:t})=>{e.element.id=`filepond--browser-${t.id}`,ye(e.element,"name",e.query("GET_NAME")),ye(e.element,"aria-controls",`filepond--assistant-${t.id}`),ye(e.element,"aria-labelledby",`filepond--drop-label-${t.id}`),Jr({root:e,action:{value:e.query("GET_ACCEPTED_FILE_TYPES")}}),ei({root:e,action:{value:e.query("GET_ALLOW_MULTIPLE")}}),ti({root:e,action:{value:e.query("GET_ALLOW_DIRECTORIES_ONLY")}}),sn({root:e}),ni({root:e,action:{value:e.query("GET_REQUIRED")}}),ri({root:e,action:{value:e.query("GET_CAPTURE_METHOD")}}),e.ref.handleChange=n=>{if(!e.element.value)return;const r=Array.from(e.element.files).map(i=>(i._relativePath=i.webkitRelativePath,i));setTimeout(()=>{t.onload(r),us(e.element)},250)},e.element.addEventListener("change",e.ref.handleChange)},Jr=({root:e,action:t})=>{e.query("GET_ALLOW_SYNC_ACCEPT_ATTRIBUTE")&&Me(e.element,"accept",!!t.value,t.value?t.value.join(","):"")},ei=({root:e,action:t})=>{Me(e.element,"multiple",t.value)},ti=({root:e,action:t})=>{Me(e.element,"webkitdirectory",t.value)},sn=({root:e})=>{const t=e.query("GET_DISABLED"),n=e.query("GET_ALLOW_BROWSE"),r=t||!n;Me(e.element,"disabled",r)},ni=({root:e,action:t})=>{t.value?e.query("GET_TOTAL_ITEMS")===0&&Me(e.element,"required",!0):Me(e.element,"required",!1)},ri=({root:e,action:t})=>{Me(e.element,"capture",!!t.value,t.value===!0?"":t.value)},vr=({root:e})=>{const{element:t}=e;if(e.query("GET_TOTAL_ITEMS")>0){Me(t,"required",!1),Me(t,"name",!1);const n=e.query("GET_ACTIVE_ITEMS");let r=!1;for(let i=0;i<n.length;i++)n[i].status===te.LOAD_ERROR&&(r=!0);e.element.setCustomValidity(r?e.query("GET_LABEL_INVALID_FIELD"):"")}else Me(t,"name",!0,e.query("GET_NAME")),e.query("GET_CHECK_VALIDITY")&&t.setCustomValidity(""),e.query("GET_REQUIRED")&&Me(t,"required",!0)},fs=({root:e})=>{e.query("GET_CHECK_VALIDITY")&&e.element.setCustomValidity(e.query("GET_LABEL_INVALID_FIELD"))},ds=pe({tag:"input",name:"browser",ignoreRect:!0,ignoreRectUpdate:!0,attributes:{type:"file"},create:cs,destroy:({root:e})=>{e.element.removeEventListener("change",e.ref.handleChange)},write:Te({DID_LOAD_ITEM:vr,DID_REMOVE_ITEM:vr,DID_THROW_ITEM_INVALID:fs,DID_SET_DISABLED:sn,DID_SET_ALLOW_BROWSE:sn,DID_SET_ALLOW_DIRECTORIES_ONLY:ti,DID_SET_ALLOW_MULTIPLE:ei,DID_SET_ACCEPTED_FILE_TYPES:Jr,DID_SET_CAPTURE_METHOD:ri,DID_SET_REQUIRED:ni})}),mr={ENTER:13,SPACE:32},hs=({root:e,props:t})=>{const n=Be("label");ye(n,"for",`filepond--browser-${t.id}`),ye(n,"id",`filepond--drop-label-${t.id}`),e.ref.handleKeyDown=r=>{(r.keyCode===mr.ENTER||r.keyCode===mr.SPACE)&&(r.preventDefault(),e.ref.label.click())},e.ref.handleClick=r=>{r.target===n||n.contains(r.target)||e.ref.label.click()},n.addEventListener("keydown",e.ref.handleKeyDown),e.element.addEventListener("click",e.ref.handleClick),ii(n,t.caption),e.appendChild(n),e.ref.label=n},ii=(e,t)=>{e.innerHTML=t;const n=e.querySelector(".filepond--label-action");return n&&ye(n,"tabindex","0"),t},ps=pe({name:"drop-label",ignoreRect:!0,create:hs,destroy:({root:e})=>{e.ref.label.addEventListener("keydown",e.ref.handleKeyDown),e.element.removeEventListener("click",e.ref.handleClick)},write:Te({DID_SET_LABEL_IDLE:({root:e,action:t})=>{ii(e.ref.label,t.value)}}),mixins:{styles:["opacity","translateX","translateY"],animations:{opacity:{type:"tween",duration:150},translateX:"spring",translateY:"spring"}}}),gs=pe({name:"drip-blob",ignoreRect:!0,mixins:{styles:["translateX","translateY","scaleX","scaleY","opacity"],animations:{scaleX:"spring",scaleY:"spring",translateX:"spring",translateY:"spring",opacity:{type:"tween",duration:250}}}}),vs=({root:e})=>{const t=e.rect.element.width*.5,n=e.rect.element.height*.5;e.ref.blob=e.appendChildView(e.createChildView(gs,{opacity:0,scaleX:2.5,scaleY:2.5,translateX:t,translateY:n}))},ms=({root:e,action:t})=>{if(!e.ref.blob){vs({root:e});return}e.ref.blob.translateX=t.position.scopeLeft,e.ref.blob.translateY=t.position.scopeTop,e.ref.blob.scaleX=1,e.ref.blob.scaleY=1,e.ref.blob.opacity=1},ys=({root:e})=>{e.ref.blob&&(e.ref.blob.opacity=0)},Es=({root:e})=>{e.ref.blob&&(e.ref.blob.scaleX=2.5,e.ref.blob.scaleY=2.5,e.ref.blob.opacity=0)},_s=({root:e,props:t,actions:n})=>{bs({root:e,props:t,actions:n});const{blob:r}=e.ref;n.length===0&&r&&r.opacity===0&&(e.removeChildView(r),e.ref.blob=null)},bs=Te({DID_DRAG:ms,DID_DROP:Es,DID_END_DRAG:ys}),Ts=pe({ignoreRect:!0,ignoreRectUpdate:!0,name:"drip",write:_s}),oi=(e,t)=>{try{const n=new DataTransfer;t.forEach(r=>{r instanceof File?n.items.add(r):n.items.add(new File([r],r.name,{type:r.type}))}),e.files=n.files}catch{return!1}return!0},Os=({root:e})=>{e.ref.fields={};const t=document.createElement("legend");t.textContent="Files",e.element.appendChild(t)},kt=(e,t)=>e.ref.fields[t],In=e=>{e.query("GET_ACTIVE_ITEMS").forEach(t=>{e.ref.fields[t.id]&&e.element.appendChild(e.ref.fields[t.id])})},yr=({root:e})=>In(e),Is=({root:e,action:t})=>{const i=!(e.query("GET_ITEM",t.id).origin===ve.LOCAL)&&e.query("SHOULD_UPDATE_FILE_INPUT"),a=Be("input");a.type=i?"file":"hidden",a.name=e.query("GET_NAME"),e.ref.fields[t.id]=a,In(e)},ws=({root:e,action:t})=>{const n=kt(e,t.id);if(!n||(t.serverFileReference!==null&&(n.value=t.serverFileReference),!e.query("SHOULD_UPDATE_FILE_INPUT")))return;const r=e.query("GET_ITEM",t.id);oi(n,[r.file])},Ss=({root:e,action:t})=>{e.query("SHOULD_UPDATE_FILE_INPUT")&&setTimeout(()=>{const n=kt(e,t.id);n&&oi(n,[t.file])},0)},As=({root:e})=>{e.element.disabled=e.query("GET_DISABLED")},Rs=({root:e,action:t})=>{const n=kt(e,t.id);n&&(n.parentNode&&n.parentNode.removeChild(n),delete e.ref.fields[t.id])},Ds=({root:e,action:t})=>{const n=kt(e,t.id);n&&(t.value===null?n.removeAttribute("value"):n.type!="file"&&(n.value=t.value),In(e))},Ns=Te({DID_SET_DISABLED:As,DID_ADD_ITEM:Is,DID_LOAD_ITEM:ws,DID_REMOVE_ITEM:Rs,DID_DEFINE_VALUE:Ds,DID_PREPARE_OUTPUT:Ss,DID_REORDER_ITEMS:yr,DID_SORT_ITEMS:yr}),Ls=pe({tag:"fieldset",name:"data",create:Os,write:Ns,ignoreRect:!0}),Ps=e=>"getRootNode"in e?e.getRootNode():document,Ms=["jpg","jpeg","png","gif","bmp","webp","svg","tiff"],xs=["css","csv","html","txt"],Cs={zip:"zip|compressed",epub:"application/epub+zip"},ai=(e="")=>(e=e.toLowerCase(),Ms.includes(e)?"image/"+(e==="jpg"?"jpeg":e==="svg"?"svg+xml":e):xs.includes(e)?"text/"+e:Cs[e]||""),wn=e=>new Promise((t,n)=>{const r=Vs(e);if(r.length&&!ks(e))return t(r);Bs(e).then(t)}),ks=e=>e.files?e.files.length>0:!1,Bs=e=>new Promise((t,n)=>{const r=(e.items?Array.from(e.items):[]).filter(i=>qs(i)).map(i=>Fs(i));if(!r.length){t(e.files?Array.from(e.files):[]);return}Promise.all(r).then(i=>{const a=[];i.forEach(s=>{a.push.apply(a,s)}),t(a.filter(s=>s).map(s=>(s._relativePath||(s._relativePath=s.webkitRelativePath),s)))}).catch(console.error)}),qs=e=>{if(si(e)){const t=Sn(e);if(t)return t.isFile||t.isDirectory}return e.kind==="file"},Fs=e=>new Promise((t,n)=>{if(Gs(e)){js(Sn(e)).then(t).catch(n);return}t([e.getAsFile()])}),js=e=>new Promise((t,n)=>{const r=[];let i=0,a=0;const s=()=>{a===0&&i===0&&t(r)},d=c=>{i++;const y=c.createReader(),f=()=>{y.readEntries(u=>{if(u.length===0){i--,s();return}u.forEach(o=>{o.isDirectory?d(o):(a++,o.file(l=>{const h=Us(l);o.fullPath&&(h._relativePath=o.fullPath),r.push(h),a--,s()}))}),f()},n)};f()};d(e)}),Us=e=>{if(e.type.length)return e;const t=e.lastModifiedDate,n=e.name,r=ai(Ct(e.name));return r.length&&(e=e.slice(0,e.size,r),e.name=n,e.lastModifiedDate=t),e},Gs=e=>si(e)&&(Sn(e)||{}).isDirectory,si=e=>"webkitGetAsEntry"in e,Sn=e=>e.webkitGetAsEntry(),Vs=e=>{let t=[];try{if(t=zs(e),t.length)return t;t=Hs(e)}catch{}return t},Hs=e=>{let t=e.getData("url");return typeof t=="string"&&t.length?[t]:[]},zs=e=>{let t=e.getData("text/html");if(typeof t=="string"&&t.length){const n=t.match(/src\s*=\s*"(.+?)"/);if(n)return[n[1]]}return[]},Dt=[],Ze=e=>({pageLeft:e.pageX,pageTop:e.pageY,scopeLeft:e.offsetX||e.layerX,scopeTop:e.offsetY||e.layerY}),Ws=(e,t,n)=>{const r=Ys(t),i={element:e,filterElement:n,state:null,ondrop:()=>{},onenter:()=>{},ondrag:()=>{},onexit:()=>{},onload:()=>{},allowdrop:()=>{}};return i.destroy=r.addListener(i),i},Ys=e=>{const t=Dt.find(r=>r.element===e);if(t)return t;const n=$s(e);return Dt.push(n),n},$s=e=>{const t=[],n={dragenter:Xs,dragover:Qs,dragleave:Js,drop:Zs},r={};ce(n,(a,s)=>{r[a]=s(e,t),e.addEventListener(a,r[a],!1)});const i={element:e,addListener:a=>(t.push(a),()=>{t.splice(t.indexOf(a),1),t.length===0&&(Dt.splice(Dt.indexOf(i),1),ce(n,s=>{e.removeEventListener(s,r[s],!1)}))})};return i},Ks=(e,t)=>("elementFromPoint"in e||(e=document),e.elementFromPoint(t.x,t.y)),An=(e,t)=>{const n=Ps(t),r=Ks(n,{x:e.pageX-window.pageXOffset,y:e.pageY-window.pageYOffset});return r===t||t.contains(r)};let li=null;const bt=(e,t)=>{try{e.dropEffect=t}catch{}},Xs=(e,t)=>n=>{n.preventDefault(),li=n.target,t.forEach(r=>{const{element:i,onenter:a}=r;An(n,i)&&(r.state="enter",a(Ze(n)))})},Qs=(e,t)=>n=>{n.preventDefault();const r=n.dataTransfer;wn(r).then(i=>{let a=!1;t.some(s=>{const{filterElement:d,element:c,onenter:y,onexit:f,ondrag:u,allowdrop:o}=s;bt(r,"copy");const l=o(i);if(!l){bt(r,"none");return}if(An(n,c)){if(a=!0,s.state===null){s.state="enter",y(Ze(n));return}if(s.state="over",d&&!l){bt(r,"none");return}u(Ze(n))}else d&&!a&&bt(r,"none"),s.state&&(s.state=null,f(Ze(n)))})})},Zs=(e,t)=>n=>{n.preventDefault();const r=n.dataTransfer;wn(r).then(i=>{t.forEach(a=>{const{filterElement:s,element:d,ondrop:c,onexit:y,allowdrop:f}=a;if(a.state=null,!(s&&!An(n,d))){if(!f(i))return y(Ze(n));c(Ze(n),i)}})})},Js=(e,t)=>n=>{li===n.target&&t.forEach(r=>{const{onexit:i}=r;r.state=null,i(Ze(n))})},el=(e,t,n)=>{e.classList.add("filepond--hopper");const{catchesDropsOnPage:r,requiresDropOnElement:i,filterItems:a=f=>f}=n,s=Ws(e,r?document.documentElement:e,i);let d="",c="";s.allowdrop=f=>t(a(f)),s.ondrop=(f,u)=>{const o=a(u);if(!t(o)){y.ondragend(f);return}c="drag-drop",y.onload(o,f)},s.ondrag=f=>{y.ondrag(f)},s.onenter=f=>{c="drag-over",y.ondragstart(f)},s.onexit=f=>{c="drag-exit",y.ondragend(f)};const y={updateHopperState:()=>{d!==c&&(e.dataset.hopperState=c,d=c)},onload:()=>{},ondragstart:()=>{},ondrag:()=>{},ondragend:()=>{},destroy:()=>{s.destroy()}};return y};let ln=!1;const it=[],ui=e=>{const t=document.activeElement;if(t&&(/textarea|input/i.test(t.nodeName)||t.getAttribute("contenteditable")==="true")){let r=!1,i=t;for(;i!==document.body;){if(i.classList.contains("filepond--root")){r=!0;break}i=i.parentNode}if(!r)return}wn(e.clipboardData).then(r=>{r.length&&it.forEach(i=>i(r))})},tl=e=>{it.includes(e)||(it.push(e),!ln&&(ln=!0,document.addEventListener("paste",ui)))},nl=e=>{yn(it,it.indexOf(e)),it.length===0&&(document.removeEventListener("paste",ui),ln=!1)},rl=()=>{const e=n=>{t.onload(n)},t={destroy:()=>{nl(e)},onload:()=>{}};return tl(e),t},il=({root:e,props:t})=>{e.element.id=`filepond--assistant-${t.id}`,ye(e.element,"role","alert"),ye(e.element,"aria-live","polite"),ye(e.element,"aria-relevant","additions")};let Er=null,_r=null;const Qt=[],Bt=(e,t)=>{e.element.textContent=t},ol=e=>{e.element.textContent=""},ci=(e,t,n)=>{const r=e.query("GET_TOTAL_ITEMS");Bt(e,`${n} ${t}, ${r} ${r===1?e.query("GET_LABEL_FILE_COUNT_SINGULAR"):e.query("GET_LABEL_FILE_COUNT_PLURAL")}`),clearTimeout(_r),_r=setTimeout(()=>{ol(e)},1500)},fi=e=>e.element.parentNode.contains(document.activeElement),al=({root:e,action:t})=>{if(!fi(e))return;e.element.textContent="";const n=e.query("GET_ITEM",t.id);Qt.push(n.filename),clearTimeout(Er),Er=setTimeout(()=>{ci(e,Qt.join(", "),e.query("GET_LABEL_FILE_ADDED")),Qt.length=0},750)},sl=({root:e,action:t})=>{if(!fi(e))return;const n=t.item;ci(e,n.filename,e.query("GET_LABEL_FILE_REMOVED"))},ll=({root:e,action:t})=>{const r=e.query("GET_ITEM",t.id).filename,i=e.query("GET_LABEL_FILE_PROCESSING_COMPLETE");Bt(e,`${r} ${i}`)},br=({root:e,action:t})=>{const r=e.query("GET_ITEM",t.id).filename,i=e.query("GET_LABEL_FILE_PROCESSING_ABORTED");Bt(e,`${r} ${i}`)},Tt=({root:e,action:t})=>{const r=e.query("GET_ITEM",t.id).filename;Bt(e,`${t.status.main} ${r} ${t.status.sub}`)},ul=pe({create:il,ignoreRect:!0,ignoreRectUpdate:!0,write:Te({DID_LOAD_ITEM:al,DID_REMOVE_ITEM:sl,DID_COMPLETE_ITEM_PROCESSING:ll,DID_ABORT_ITEM_PROCESSING:br,DID_REVERT_ITEM_PROCESSING:br,DID_THROW_ITEM_REMOVE_ERROR:Tt,DID_THROW_ITEM_LOAD_ERROR:Tt,DID_THROW_ITEM_INVALID:Tt,DID_THROW_ITEM_PROCESSING_ERROR:Tt}),tag:"span",name:"assistant"}),di=(e,t="-")=>e.replace(new RegExp(`${t}.`,"g"),n=>n.charAt(1).toUpperCase()),hi=(e,t=16,n=!0)=>{let r=Date.now(),i=null;return(...a)=>{clearTimeout(i);const s=Date.now()-r,d=()=>{r=Date.now(),e(...a)};s<t?n||(i=setTimeout(d,t-s)):d()}},cl=1e6,Nt=e=>e.preventDefault(),fl=({root:e,props:t})=>{const n=e.query("GET_ID");n&&(e.element.id=n);const r=e.query("GET_CLASS_NAME");r&&r.split(" ").filter(c=>c.length).forEach(c=>{e.element.classList.add(c)}),e.ref.label=e.appendChildView(e.createChildView(ps,{...t,translateY:null,caption:e.query("GET_LABEL_IDLE")})),e.ref.list=e.appendChildView(e.createChildView(ls,{translateY:null})),e.ref.panel=e.appendChildView(e.createChildView(Zr,{name:"panel-root"})),e.ref.assistant=e.appendChildView(e.createChildView(ul,{...t})),e.ref.data=e.appendChildView(e.createChildView(Ls,{...t})),e.ref.measure=Be("div"),e.ref.measure.style.height="100%",e.element.appendChild(e.ref.measure),e.ref.bounds=null,e.query("GET_STYLES").filter(c=>!ke(c.value)).map(({name:c,value:y})=>{e.element.dataset[c]=y}),e.ref.widthPrevious=null,e.ref.widthUpdated=hi(()=>{e.ref.updateHistory=[],e.dispatch("DID_RESIZE_ROOT")},250),e.ref.previousAspectRatio=null,e.ref.updateHistory=[];const i=window.matchMedia("(pointer: fine) and (hover: hover)").matches,a="PointerEvent"in window;e.query("GET_ALLOW_REORDER")&&a&&!i&&(e.element.addEventListener("touchmove",Nt,{passive:!1}),e.element.addEventListener("gesturestart",Nt));const s=e.query("GET_CREDITS");if(s.length===2){const c=document.createElement("a");c.className="filepond--credits",c.href=s[0],c.tabIndex=-1,c.target="_blank",c.rel="noopener noreferrer nofollow",c.textContent=s[1],e.element.appendChild(c),e.ref.credits=c}},dl=({root:e,props:t,actions:n})=>{if(ml({root:e,props:t,actions:n}),n.filter(A=>/^DID_SET_STYLE_/.test(A.type)).filter(A=>!ke(A.data.value)).map(({type:A,data:S})=>{const I=di(A.substring(8).toLowerCase(),"_");e.element.dataset[I]=S.value,e.invalidateLayout()}),e.rect.element.hidden)return;e.rect.element.width!==e.ref.widthPrevious&&(e.ref.widthPrevious=e.rect.element.width,e.ref.widthUpdated());let r=e.ref.bounds;r||(r=e.ref.bounds=gl(e),e.element.removeChild(e.ref.measure),e.ref.measure=null);const{hopper:i,label:a,list:s,panel:d}=e.ref;i&&i.updateHopperState();const c=e.query("GET_PANEL_ASPECT_RATIO"),y=e.query("GET_ALLOW_MULTIPLE"),f=e.query("GET_TOTAL_ITEMS"),u=y?e.query("GET_MAX_FILES")||cl:1,o=f===u,l=n.find(A=>A.type==="DID_ADD_ITEM");if(o&&l){const A=l.data.interactionMethod;a.opacity=0,y?a.translateY=-40:A===De.API?a.translateX=40:A===De.BROWSE?a.translateY=40:a.translateY=30}else o||(a.opacity=1,a.translateX=0,a.translateY=0);const h=hl(e),v=pl(e),E=a.rect.element.height,p=!y||o?0:E,m=o?s.rect.element.marginTop:0,_=f===0?0:s.rect.element.marginBottom,g=p+m+v.visual+_,b=p+m+v.bounds+_;if(s.translateY=Math.max(0,p-s.rect.element.marginTop)-h.top,c){const A=e.rect.element.width,S=A*c;c!==e.ref.previousAspectRatio&&(e.ref.previousAspectRatio=c,e.ref.updateHistory=[]);const I=e.ref.updateHistory;I.push(A);const N=2;if(I.length>N*2){const T=I.length,O=T-10;let L=0;for(let D=T;D>=O;D--)if(I[D]===I[D-2]&&L++,L>=N)return}d.scalable=!1,d.height=S;const w=S-p-(_-h.bottom)-(o?m:0);v.visual>w?s.overflow=w:s.overflow=null,e.height=S}else if(r.fixedHeight){d.scalable=!1;const A=r.fixedHeight-p-(_-h.bottom)-(o?m:0);v.visual>A?s.overflow=A:s.overflow=null}else if(r.cappedHeight){const A=g>=r.cappedHeight,S=Math.min(r.cappedHeight,g);d.scalable=!0,d.height=A?S:S-h.top-h.bottom;const I=S-p-(_-h.bottom)-(o?m:0);g>r.cappedHeight&&v.visual>I?s.overflow=I:s.overflow=null,e.height=Math.min(r.cappedHeight,b-h.top-h.bottom)}else{const A=f>0?h.top+h.bottom:0;d.scalable=!0,d.height=Math.max(E,g-A),e.height=Math.max(E,b-A)}e.ref.credits&&d.heightCurrent&&(e.ref.credits.style.transform=`translateY(${d.heightCurrent}px)`)},hl=e=>{const t=e.ref.list.childViews[0].childViews[0];return t?{top:t.rect.element.marginTop,bottom:t.rect.element.marginBottom}:{top:0,bottom:0}},pl=e=>{let t=0,n=0;const r=e.ref.list,i=r.childViews[0],a=i.childViews.filter(m=>m.rect.element.height),s=e.query("GET_ACTIVE_ITEMS").map(m=>a.find(_=>_.id===m.id)).filter(m=>m);if(s.length===0)return{visual:t,bounds:n};const d=i.rect.element.width,c=On(i,s,r.dragCoordinates),y=s[0].rect.element,f=y.marginTop+y.marginBottom,u=y.marginLeft+y.marginRight,o=y.width+u,l=y.height+f,h=typeof c<"u"&&c>=0?1:0,v=s.find(m=>m.markedForRemoval&&m.opacity<.45)?-1:0,E=s.length+h+v,p=Tn(d,o);return p===1?s.forEach(m=>{const _=m.rect.element.height+f;n+=_,t+=_*m.opacity}):(n=Math.ceil(E/p)*l,t=n),{visual:t,bounds:n}},gl=e=>{const t=e.ref.measureHeight||null;return{cappedHeight:parseInt(e.style.maxHeight,10)||null,fixedHeight:t===0?null:t}},Rn=(e,t)=>{const n=e.query("GET_ALLOW_REPLACE"),r=e.query("GET_ALLOW_MULTIPLE"),i=e.query("GET_TOTAL_ITEMS");let a=e.query("GET_MAX_FILES");const s=t.length;return!r&&s>1?(e.dispatch("DID_THROW_MAX_FILES",{source:t,error:fe("warning",0,"Max files")}),!0):(a=r?a:1,!r&&n?!1:at(a)&&i+s>a?(e.dispatch("DID_THROW_MAX_FILES",{source:t,error:fe("warning",0,"Max files")}),!0):!1)},vl=(e,t,n)=>{const r=e.childViews[0];return On(r,t,{left:n.scopeLeft-r.rect.element.left,top:n.scopeTop-(e.rect.outer.top+e.rect.element.marginTop+e.rect.element.scrollTop)})},Tr=e=>{const t=e.query("GET_ALLOW_DROP"),n=e.query("GET_DISABLED"),r=t&&!n;if(r&&!e.ref.hopper){const i=el(e.element,a=>{const s=e.query("GET_BEFORE_DROP_FILE")||(()=>!0);return e.query("GET_DROP_VALIDATION")?a.every(c=>Je("ALLOW_HOPPER_ITEM",c,{query:e.query}).every(y=>y===!0)&&s(c)):!0},{filterItems:a=>{const s=e.query("GET_IGNORED_FILES");return a.filter(d=>Qe(d)?!s.includes(d.name.toLowerCase()):!0)},catchesDropsOnPage:e.query("GET_DROP_ON_PAGE"),requiresDropOnElement:e.query("GET_DROP_ON_ELEMENT")});i.onload=(a,s)=>{const c=e.ref.list.childViews[0].childViews.filter(f=>f.rect.element.height),y=e.query("GET_ACTIVE_ITEMS").map(f=>c.find(u=>u.id===f.id)).filter(f=>f);Ne("ADD_ITEMS",a,{dispatch:e.dispatch}).then(f=>{if(Rn(e,f))return!1;e.dispatch("ADD_ITEMS",{items:f,index:vl(e.ref.list,y,s),interactionMethod:De.DROP})}),e.dispatch("DID_DROP",{position:s}),e.dispatch("DID_END_DRAG",{position:s})},i.ondragstart=a=>{e.dispatch("DID_START_DRAG",{position:a})},i.ondrag=hi(a=>{e.dispatch("DID_DRAG",{position:a})}),i.ondragend=a=>{e.dispatch("DID_END_DRAG",{position:a})},e.ref.hopper=i,e.ref.drip=e.appendChildView(e.createChildView(Ts))}else!r&&e.ref.hopper&&(e.ref.hopper.destroy(),e.ref.hopper=null,e.removeChildView(e.ref.drip))},Or=(e,t)=>{const n=e.query("GET_ALLOW_BROWSE"),r=e.query("GET_DISABLED"),i=n&&!r;i&&!e.ref.browser?e.ref.browser=e.appendChildView(e.createChildView(ds,{...t,onload:a=>{Ne("ADD_ITEMS",a,{dispatch:e.dispatch}).then(s=>{if(Rn(e,s))return!1;e.dispatch("ADD_ITEMS",{items:s,index:-1,interactionMethod:De.BROWSE})})}}),0):!i&&e.ref.browser&&(e.removeChildView(e.ref.browser),e.ref.browser=null)},Ir=e=>{const t=e.query("GET_ALLOW_PASTE"),n=e.query("GET_DISABLED"),r=t&&!n;r&&!e.ref.paster?(e.ref.paster=rl(),e.ref.paster.onload=i=>{Ne("ADD_ITEMS",i,{dispatch:e.dispatch}).then(a=>{if(Rn(e,a))return!1;e.dispatch("ADD_ITEMS",{items:a,index:-1,interactionMethod:De.PASTE})})}):!r&&e.ref.paster&&(e.ref.paster.destroy(),e.ref.paster=null)},ml=Te({DID_SET_ALLOW_BROWSE:({root:e,props:t})=>{Or(e,t)},DID_SET_ALLOW_DROP:({root:e})=>{Tr(e)},DID_SET_ALLOW_PASTE:({root:e})=>{Ir(e)},DID_SET_DISABLED:({root:e,props:t})=>{Tr(e),Ir(e),Or(e,t),e.query("GET_DISABLED")?e.element.dataset.disabled="disabled":e.element.removeAttribute("data-disabled")}}),yl=pe({name:"root",read:({root:e})=>{e.ref.measure&&(e.ref.measureHeight=e.ref.measure.offsetHeight)},create:fl,write:dl,destroy:({root:e})=>{e.ref.paster&&e.ref.paster.destroy(),e.ref.hopper&&e.ref.hopper.destroy(),e.element.removeEventListener("touchmove",Nt),e.element.removeEventListener("gesturestart",Nt)},mixins:{styles:["height"]}}),El=(e={})=>{let t=null;const n=Rt(),r=Ci(bo(n),[Fo,Io(n)],[ca,Oo(n)]);r.dispatch("SET_OPTIONS",{options:e});const i=()=>{document.hidden||r.dispatch("KICK")};document.addEventListener("visibilitychange",i);let a=null,s=!1,d=!1,c=null,y=null;const f=()=>{s||(s=!0),clearTimeout(a),a=setTimeout(()=>{s=!1,c=null,y=null,d&&(d=!1,r.dispatch("DID_STOP_RESIZE"))},500)};window.addEventListener("resize",f);const u=yl(r,{id:mn()});let o=!1,l=!1;const h={_read:()=>{s&&(y=window.innerWidth,c||(c=y),!d&&y!==c&&(r.dispatch("DID_START_RESIZE"),d=!0)),l&&o&&(o=u.element.offsetParent===null),!o&&(u._read(),l=u.rect.element.hidden)},_write:x=>{const B=r.processActionQueue().filter(V=>!/^SET_/.test(V.type));o&&!B.length||(m(B),o=u._write(x,B,d),Ao(r.query("GET_ITEMS")),o&&r.processDispatchQueue())}},v=x=>B=>{const V={type:x};if(!B)return V;if(B.hasOwnProperty("error")&&(V.error=B.error?{...B.error}:null),B.status&&(V.status={...B.status}),B.file&&(V.output=B.file),B.source)V.file=B.source;else if(B.item||B.id){const F=B.item?B.item:r.query("GET_ITEM",B.id);V.file=F?Ie(F):null}return B.items&&(V.items=B.items.map(Ie)),/progress/.test(x)&&(V.progress=B.progress),B.hasOwnProperty("origin")&&B.hasOwnProperty("target")&&(V.origin=B.origin,V.target=B.target),V},E={DID_DESTROY:v("destroy"),DID_INIT:v("init"),DID_THROW_MAX_FILES:v("warning"),DID_INIT_ITEM:v("initfile"),DID_START_ITEM_LOAD:v("addfilestart"),DID_UPDATE_ITEM_LOAD_PROGRESS:v("addfileprogress"),DID_LOAD_ITEM:v("addfile"),DID_THROW_ITEM_INVALID:[v("error"),v("addfile")],DID_THROW_ITEM_LOAD_ERROR:[v("error"),v("addfile")],DID_THROW_ITEM_REMOVE_ERROR:[v("error"),v("removefile")],DID_PREPARE_OUTPUT:v("preparefile"),DID_START_ITEM_PROCESSING:v("processfilestart"),DID_UPDATE_ITEM_PROCESS_PROGRESS:v("processfileprogress"),DID_ABORT_ITEM_PROCESSING:v("processfileabort"),DID_COMPLETE_ITEM_PROCESSING:v("processfile"),DID_COMPLETE_ITEM_PROCESSING_ALL:v("processfiles"),DID_REVERT_ITEM_PROCESSING:v("processfilerevert"),DID_THROW_ITEM_PROCESSING_ERROR:[v("error"),v("processfile")],DID_REMOVE_ITEM:v("removefile"),DID_UPDATE_ITEMS:v("updatefiles"),DID_ACTIVATE_ITEM:v("activatefile"),DID_REORDER_ITEMS:v("reorderfiles")},p=x=>{const B={pond:M,...x};delete B.type,u.element.dispatchEvent(new CustomEvent(`FilePond:${x.type}`,{detail:B,bubbles:!0,cancelable:!0,composed:!0}));const V=[];x.hasOwnProperty("error")&&V.push(x.error),x.hasOwnProperty("file")&&V.push(x.file);const F=["type","error","file"];Object.keys(x).filter(R=>!F.includes(R)).forEach(R=>V.push(x[R])),M.fire(x.type,...V);const k=r.query(`GET_ON${x.type.toUpperCase()}`);k&&k(...V)},m=x=>{x.length&&x.filter(B=>E[B.type]).forEach(B=>{const V=E[B.type];(Array.isArray(V)?V:[V]).forEach(F=>{B.type==="DID_INIT_ITEM"?p(F(B.data)):setTimeout(()=>{p(F(B.data))},0)})})},_=x=>r.dispatch("SET_OPTIONS",{options:x}),g=x=>r.query("GET_ACTIVE_ITEM",x),b=x=>new Promise((B,V)=>{r.dispatch("REQUEST_ITEM_PREPARE",{query:x,success:F=>{B(F)},failure:F=>{V(F)}})}),A=(x,B={})=>new Promise((V,F)=>{N([{source:x,options:B}],{index:B.index}).then(k=>V(k&&k[0])).catch(F)}),S=x=>x.file&&x.id,I=(x,B)=>(typeof x=="object"&&!S(x)&&!B&&(B=x,x=void 0),r.dispatch("REMOVE_ITEM",{...B,query:x}),r.query("GET_ACTIVE_ITEM",x)===null),N=(...x)=>new Promise((B,V)=>{const F=[],k={};if(Lt(x[0]))F.push.apply(F,x[0]),Object.assign(k,x[1]||{});else{const R=x[x.length-1];typeof R=="object"&&!(R instanceof Blob)&&Object.assign(k,x.pop()),F.push(...x)}r.dispatch("ADD_ITEMS",{items:F,index:k.index,interactionMethod:De.API,success:B,failure:V})}),w=()=>r.query("GET_ACTIVE_ITEMS"),T=x=>new Promise((B,V)=>{r.dispatch("REQUEST_ITEM_PROCESSING",{query:x,success:F=>{B(F)},failure:F=>{V(F)}})}),O=(...x)=>{const B=Array.isArray(x[0])?x[0]:x,V=B.length?B:w();return Promise.all(V.map(b))},L=(...x)=>{const B=Array.isArray(x[0])?x[0]:x;if(!B.length){const V=w().filter(F=>!(F.status===te.IDLE&&F.origin===ve.LOCAL)&&F.status!==te.PROCESSING&&F.status!==te.PROCESSING_COMPLETE&&F.status!==te.PROCESSING_REVERT_ERROR);return Promise.all(V.map(T))}return Promise.all(B.map(T))},D=(...x)=>{const B=Array.isArray(x[0])?x[0]:x;let V;typeof B[B.length-1]=="object"?V=B.pop():Array.isArray(x[0])&&(V=x[1]);const F=w();return B.length?B.map(R=>We(R)?F[R]?F[R].id:null:R).filter(R=>R).map(R=>I(R,V)):Promise.all(F.map(R=>I(R,V)))},M={...xt(),...h,...To(r,n),setOptions:_,addFile:A,addFiles:N,getFile:g,processFile:T,prepareFile:b,removeFile:I,moveFile:(x,B)=>r.dispatch("MOVE_ITEM",{query:x,index:B}),getFiles:w,processFiles:L,removeFiles:D,prepareFiles:O,sort:x=>r.dispatch("SORT",{compare:x}),browse:()=>{var x=u.element.querySelector("input[type=file]");x&&x.click()},destroy:()=>{M.fire("destroy",u.element),r.dispatch("ABORT_ALL"),u._destroy(),window.removeEventListener("resize",f),document.removeEventListener("visibilitychange",i),r.dispatch("DID_DESTROY")},insertBefore:x=>Yn(u.element,x),insertAfter:x=>$n(u.element,x),appendTo:x=>x.appendChild(u.element),replaceElement:x=>{Yn(u.element,x),x.parentNode.removeChild(x),t=x},restoreElement:()=>{t&&($n(t,u.element),u.element.parentNode.removeChild(u.element),t=null)},isAttachedTo:x=>u.element===x||t===x,element:{get:()=>u.element},status:{get:()=>r.query("GET_STATUS")}};return r.dispatch("DID_INIT"),Ge(M)},pi=(e={})=>{const t={};return ce(Rt(),(r,i)=>{t[r]=i[0]}),El({...t,...e})},_l=e=>e.charAt(0).toLowerCase()+e.slice(1),bl=e=>di(e.replace(/^data-/,"")),gi=(e,t)=>{ce(t,(n,r)=>{ce(e,(i,a)=>{const s=new RegExp(n);if(!s.test(i)||(delete e[i],r===!1))return;if(be(r)){e[r]=a;return}const c=r.group;Ee(r)&&!e[c]&&(e[c]={}),e[c][_l(i.replace(s,""))]=a}),r.mapping&&gi(e[r.group],r.mapping)})},Tl=(e,t={})=>{const n=[];ce(e.attributes,i=>{n.push(e.attributes[i])});const r=n.filter(i=>i.name).reduce((i,a)=>{const s=ye(e,a.name);return i[bl(a.name)]=s===a.name?!0:s,i},{});return gi(r,t),r},Ol=(e,t={})=>{const n={"^class$":"className","^multiple$":"allowMultiple","^capture$":"captureMethod","^webkitdirectory$":"allowDirectoriesOnly","^server":{group:"server",mapping:{"^process":{group:"process"},"^revert":{group:"revert"},"^fetch":{group:"fetch"},"^restore":{group:"restore"},"^load":{group:"load"}}},"^type$":!1,"^files$":!1};Je("SET_ATTRIBUTE_TO_OPTION_MAP",n);const r={...t},i=Tl(e.nodeName==="FIELDSET"?e.querySelector("input[type=file]"):e,n);Object.keys(i).forEach(s=>{Ee(i[s])?(Ee(r[s])||(r[s]={}),Object.assign(r[s],i[s])):r[s]=i[s]}),r.files=(t.files||[]).concat(Array.from(e.querySelectorAll("input:not([type=file])")).map(s=>({source:s.value,options:{type:s.dataset.type}})));const a=pi(r);return e.files&&Array.from(e.files).forEach(s=>{a.addFile(s)}),a.replaceElement(e),a},Il=(...e)=>xi(e[0])?Ol(...e):pi(...e),wl=["fire","_read","_write"],wr=e=>{const t={};return kr(e,t,wl),t},Sl=(e,t)=>e.replace(/(?:{([a-zA-Z]+)})/g,(n,r)=>t[r]),Al=e=>{const t=new Blob(["(",e.toString(),")()"],{type:"application/javascript"}),n=URL.createObjectURL(t),r=new Worker(n);return{transfer:(i,a)=>{},post:(i,a,s)=>{const d=mn();r.onmessage=c=>{c.data.id===d&&a(c.data.message)},r.postMessage({id:d,message:i},s)},terminate:()=>{r.terminate(),URL.revokeObjectURL(n)}}},Rl=e=>new Promise((t,n)=>{const r=new Image;r.onload=()=>{t(r)},r.onerror=i=>{n(i)},r.src=e}),vi=(e,t)=>{const n=e.slice(0,e.size,e.type);return n.lastModifiedDate=e.lastModifiedDate,n.name=t,n},Dl=e=>vi(e,e.name),Sr=[],Nl=e=>{if(Sr.includes(e))return;Sr.push(e);const t=e({addFilter:Do,utils:{Type:K,forin:ce,isString:be,isFile:Qe,toNaturalFileSize:Kr,replaceInString:Sl,getExtensionFromFilename:Ct,getFilenameWithoutExtension:Wr,guesstimateMimeType:ai,getFileFromBlob:ot,getFilenameFromURL:ht,createRoute:Te,createWorker:Al,createView:pe,createItemAPI:Ie,loadImage:Rl,copyFile:Dl,renameFile:vi,createBlob:Vr,applyFilterChain:Ne,text:de,getNumericAspectRatioFromString:Fr},views:{fileActionButton:$r}});No(t.options)},Ll=()=>Object.prototype.toString.call(window.operamini)==="[object OperaMini]",Pl=()=>"Promise"in window,Ml=()=>"slice"in Blob.prototype,xl=()=>"URL"in window&&"createObjectURL"in window.URL,Cl=()=>"visibilityState"in document,kl=()=>"performance"in window,Bl=()=>"supports"in(window.CSS||{}),ql=()=>/MSIE|Trident/.test(window.navigator.userAgent),un=(()=>{const e=Nr()&&!Ll()&&Cl()&&Pl()&&Ml()&&xl()&&kl()&&(Bl()||ql());return()=>e})(),Fe={apps:[]},Fl="filepond",et=()=>{};let mi={},yi={},Ei={},cn={},wt=et,St=et,fn=et,dn=et,hn=et,pn=et,gn=et;if(un()){ao(()=>{Fe.apps.forEach(n=>n._read())},n=>{Fe.apps.forEach(r=>r._write(n))});const e=()=>{document.dispatchEvent(new CustomEvent("FilePond:loaded",{detail:{supported:un,create:wt,destroy:St,parse:fn,find:dn,registerPlugin:hn,setOptions:gn}})),document.removeEventListener("DOMContentLoaded",e)};document.readyState!=="loading"?setTimeout(()=>e(),0):document.addEventListener("DOMContentLoaded",e);const t=()=>ce(Rt(),(n,r)=>{cn[n]=r[1]});mi={...jr},Ei={...ve},yi={...te},cn={},t(),wt=(...n)=>{const r=Il(...n);return r.on("destroy",St),Fe.apps.push(r),wr(r)},St=n=>{const r=Fe.apps.findIndex(i=>i.isAttachedTo(n));return r>=0?(Fe.apps.splice(r,1)[0].restoreElement(),!0):!1},fn=n=>Array.from(n.querySelectorAll(`.${Fl}`)).filter(a=>!Fe.apps.find(s=>s.isAttachedTo(a))).map(a=>wt(a)),dn=n=>{const r=Fe.apps.find(i=>i.isAttachedTo(n));return r?wr(r):null},hn=(...n)=>{n.forEach(Nl),t()},pn=()=>{const n={};return ce(Rt(),(r,i)=>{n[r]=i[0]}),n},gn=n=>(Ee(n)&&(Fe.apps.forEach(r=>{r.setOptions(n)}),Lo(n)),pn())}const Ul=Object.freeze(Object.defineProperty({__proto__:null,get FileOrigin(){return Ei},get FileStatus(){return yi},get OptionTypes(){return cn},get Status(){return mi},get create(){return wt},get destroy(){return St},get find(){return dn},get getOptions(){return pn},get parse(){return fn},get registerPlugin(){return hn},get setOptions(){return gn},supported:un},Symbol.toStringTag,{value:"Module"}));export{zt as Q,Ul as f,jl as r};

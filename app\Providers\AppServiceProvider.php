<?php

namespace App\Providers;

use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\View;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Share the current locale with all views
        View::composer('*', function ($view) {
            $view->with('currentLocale', App::getLocale());
        });
        
        // Set the default locale from session if available
        if (session()->has('locale')) {
            App::setLocale(session('locale'));
        }
    }
}

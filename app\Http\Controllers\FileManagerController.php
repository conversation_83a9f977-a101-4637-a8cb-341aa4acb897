<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use App\Models\File;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Services\FileUploadService;

class FileManagerController extends Controller
{
    /**
     * Display a listing of files.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $search = $request->input('search', '');
        $perPage = 12; // Files per page

        $query = File::query();

        // Apply search filter if provided
        if ($search) {
            $query->where('title', 'like', "%{$search}%");
        }

        // Get paginated results
        $files = $query->orderBy('created_at', 'desc')->paginate($perPage);

        // Debug: Log file details to check what's being returned
        if (count($files) > 0) {
            Log::info('Sample file details:', [
                'id' => $files[0]->id,
                'title' => $files[0]->title,
                'url' => $files[0]->url,
                'fileType' => $files[0]->fileType
            ]);
        } else {
            Log::info('No files found');
        }

        // Format for API response
        $response = [
            'files' => $files->items(),
            'pagination' => [
                'current_page' => $files->currentPage(),
                'last_page' => $files->lastPage(),
                'per_page' => $files->perPage(),
                'total' => $files->total()
            ]
        ];

        return response()->json($response);
    }

    /**
     * Store multiple uploaded files.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    /**
     * @var FileUploadService
     */
    protected $fileUploadService;

    /**
     * Constructor
     *
     * @param FileUploadService $fileUploadService
     */
    public function __construct(FileUploadService $fileUploadService)
    {
        $this->fileUploadService = $fileUploadService;
    }

    /**
     * Store multiple uploaded files.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        \Log::info('File upload request received', [
            'has_files' => $request->hasFile('files'),
            'has_files_array' => $request->hasFile('files[]'),
            'token_present' => $request->has('_token'),
            'auth_check' => auth()->check(),
            'files_count' => $request->file('files') ? count($request->file('files')) : 0,
            'request_all' => $request->all()
        ]);

        // Validate the request for multiple files
        $request->validate([
            'files' => 'required|array',
            'files.*' => 'file|max:10240|mimes:jpg,jpeg,png,gif,webp,pdf,doc,docx,xls,xlsx'
        ]);

        try {
            $uploadedFiles = [];

            // Use a database transaction to ensure all files are processed
            DB::beginTransaction();

            // Process each uploaded file
            foreach ($request->file('files') as $file) {
                // Use the FileUploadService to handle the upload
                $fileModel = $this->fileUploadService->uploadFile($file);
                $uploadedFiles[] = $fileModel;

                // Debug - log the file details to check what was saved
                Log::info('File uploaded:', [
                    'filename' => $fileModel->filename,
                    'original_name' => $fileModel->title,
                    'url' => $fileModel->url,
                    'fileType' => $fileModel->fileType,
                    'fileSize' => $fileModel->fileSize
                ]);
            }

            // Commit the transaction
            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Files uploaded successfully',
                'files' => $uploadedFiles
            ]);

        } catch (\Exception $e) {
            // Rollback the transaction in case of any error
            DB::rollBack();

            // Log the error for debugging
            Log::error('File upload failed: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);

            // Remove any partially uploaded files
            foreach ($uploadedFiles ?? [] as $file) {
                // Try to delete from both locations
                if (file_exists(public_path('uploads/' . $file->filename))) {
                    unlink(public_path('uploads/' . $file->filename));
                }

                if (Storage::disk('public')->exists('uploads/' . $file->filename)) {
                    Storage::disk('public')->delete('uploads/' . $file->filename);
                }
            }

            return response()->json([
                'success' => false,
                'message' => 'Upload failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified file.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            $file = File::findOrFail($id);

            // Use the FileUploadService to delete the file
            $this->fileUploadService->deleteFile($file);

            return response()->json([
                'success' => true,
                'message' => 'File deleted successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('File deletion failed: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Deletion failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get file details for a specific file.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        try {
            $file = File::findOrFail($id);

            return response()->json([
                'success' => true,
                'file' => $file
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'File not found: ' . $e->getMessage()
            ], 404);
        }
    }

    /**
     * Check if a file exists and is accessible.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function checkFile($id)
    {
        try {
            $file = File::findOrFail($id);

            // Check if the file exists in public/uploads
            $uploadsPath = public_path('uploads/' . $file->filename);
            $existsInUploads = file_exists($uploadsPath);

            // Check if the file exists in storage/app/public
            $existsInStorage = Storage::disk('public')->exists('uploads/' . $file->filename);

            // Get the full URL using our improved method
            $url = $file->getFullUrl();

            return response()->json([
                'success' => true,
                'exists' => $existsInUploads || $existsInStorage,
                'existsInUploads' => $existsInUploads,
                'existsInStorage' => $existsInStorage,
                'file' => $file,
                'url' => $url
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'File check failed: ' . $e->getMessage()
            ], 404);
        }
    }

    /**
     * Get multiple files by their IDs.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function batch(Request $request)
    {
        try {
            $ids = $request->query('ids');

            if (!$ids) {
                return response()->json([
                    'success' => false,
                    'message' => 'No file IDs provided'
                ], 400);
            }

            $idArray = explode(',', $ids);
            $files = File::whereIn('id', $idArray)->get();

            // Add full URL to each file
            $files->each(function($file) {
                $file->fullUrl = $file->getFullUrl();

                // Check if file exists in public/uploads
                $uploadsPath = public_path('uploads/' . $file->filename);
                $file->existsInUploads = file_exists($uploadsPath);

                // Check if file exists in storage/app/public
                $file->existsInStorage = Storage::disk('public')->exists('uploads/' . $file->filename);
            });

            // Log for debugging
            Log::info('Batch file retrieval successful', [
                'count' => $files->count(),
                'ids' => $idArray
            ]);

            return response()->json([
                'success' => true,
                'files' => $files
            ]);

        } catch (\Exception $e) {
            Log::error('Batch file retrieval failed: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve files: ' . $e->getMessage()
            ], 500);
        }
    }
}
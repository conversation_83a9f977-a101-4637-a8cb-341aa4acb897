<x-app-layout>
<!-- Hero Section -->
<section class="relative h-[700px] overflow-hidden">
    <div class="absolute inset-0">
        <div class="absolute inset-0 bg-gradient-to-r from-black/70 to-black/40 z-10"></div>
        <img src="https://images.unsplash.com/photo-1449965408869-eaa3f722e40d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
             alt="Car sharing" class="w-full h-full object-cover">
    </div>
    <div class="relative z-20 container mx-auto px-4 h-full">
        <div class="h-full flex flex-col md:flex-row items-center justify-between">
            <!-- Left side content -->
            <div class="md:w-1/2 pt-20 md:pt-0">
                <div class="max-w-xl">
                    <h1 class="text-5xl md:text-7xl font-extrabold text-white mb-6 leading-tight animate-fade-in" data-lang-key="hero_title">{{ frontend_trans('hero_title') }}</h1>
                    <p class="text-xl md:text-2xl text-white/90 mb-10 max-w-xl animate-fade-in-delay" data-lang-key="hero_subtitle">{{ frontend_trans('hero_subtitle') }}</p>
                    <div class="flex flex-col sm:flex-row gap-4 animate-fade-in-delay-2">
                        <a href="{{ route('cars.listing') }}" class="bg-primary hover:bg-red-600 text-white font-bold py-4 px-8 rounded-xl text-center transition-all transform hover:scale-105 shadow-lg" data-lang-key="rent_car_btn">{{ frontend_trans('rent_car_btn') }}</a>
                        <a href="{{ route('list-car') }}" class="bg-white hover:bg-gray-100 text-primary font-bold py-4 px-8 rounded-xl text-center transition-all transform hover:scale-105 shadow-lg" data-lang-key="list_car_btn">{{ frontend_trans('list_car_btn') }}</a>
                    </div>
                </div>
            </div>

            <!-- Right side search form -->
            <div class="md:w-5/12 mt-12 md:mt-0">
                <form action="{{ route('cars.listing') }}" method="GET" class="bg-white p-8 rounded-2xl shadow-2xl backdrop-blur-sm border border-white/10 animate-slide-up">
                    <h3 class="text-2xl font-bold text-gray-800 mb-6">Find Your Perfect Car</h3>

                    <!-- Location field -->
                    <div class="mb-6">
                        <label class="block text-gray-700 font-medium mb-2" data-lang-key="location_label">Location</label>
                        <div class="relative">
                            <select id="city-dropdown" name="city_id" class="w-full p-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:outline-none shadow-sm">
                                <option value="" data-lang-key="select_city">Select City</option>
                                @foreach($cities as $city)
                                    <option value="{{ $city->id }}">{{ $city->name }}</option>
                                @endforeach
                            </select>
                            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                <i class="fas fa-map-marker-alt text-primary"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Date fields -->
                    <div class="grid grid-cols-2 gap-4 mb-6">
                        <div>
                            <label class="block text-gray-700 font-medium mb-2" data-lang-key="from_label">From</label>
                            <div class="relative">
                                <input type="date" name="pickup_date" class="w-full p-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:outline-none shadow-sm" min="{{ date('Y-m-d') }}">
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    <i class="fas fa-calendar-alt text-primary"></i>
                                </div>
                            </div>
                        </div>
                        <div>
                            <label class="block text-gray-700 font-medium mb-2" data-lang-key="to_label">To</label>
                            <div class="relative">
                                <input type="date" name="return_date" class="w-full p-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:outline-none shadow-sm" min="{{ date('Y-m-d', strtotime('+1 day')) }}">
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    <i class="fas fa-calendar-alt text-primary"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="w-full bg-primary text-white py-4 rounded-xl hover:bg-red-600 transition-all transform hover:scale-[1.02] font-bold text-lg shadow-md animate-pulse-subtle" data-lang-key="find_cars_btn">Find Available Cars</button>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Stats Section -->
<section class="py-16 bg-white">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div class="p-6 rounded-xl hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 border border-gray-100">
                <div class="w-16 h-16 bg-red-50 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-car-side text-2xl text-primary"></i>
                </div>
                <p class="text-4xl font-extrabold text-primary mb-2 counter">10,000+</p>
                <p class="text-gray-600 font-medium" data-lang-key="cars_listed">Cars Listed</p>
            </div>
            <div class="p-6 rounded-xl hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 border border-gray-100">
                <div class="w-16 h-16 bg-red-50 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-users text-2xl text-primary"></i>
                </div>
                <p class="text-4xl font-extrabold text-primary mb-2 counter">50,000+</p>
                <p class="text-gray-600 font-medium" data-lang-key="happy_users">Happy Users</p>
            </div>
            <div class="p-6 rounded-xl hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 border border-gray-100">
                <div class="w-16 h-16 bg-red-50 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-map-marker-alt text-2xl text-primary"></i>
                </div>
                <p class="text-4xl font-extrabold text-primary mb-2 counter">500+</p>
                <p class="text-gray-600 font-medium" data-lang-key="cities">Cities</p>
            </div>
            <div class="p-6 rounded-xl hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 border border-gray-100">
                <div class="w-16 h-16 bg-red-50 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-euro-sign text-2xl text-primary"></i>
                </div>
                <p class="text-4xl font-extrabold text-primary mb-2 counter">€5M+</p>
                <p class="text-gray-600 font-medium" data-lang-key="owner_earnings">Owner Earnings</p>
            </div>
        </div>
    </div>
</section>

<!-- How It Works Section -->
<section id="how-it-works" class="py-24 bg-gradient-to-b from-white to-gray-50">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-extrabold text-gray-900 mb-4 relative inline-block" data-lang-key="how_carbnb_works">
                How CARBNB Works
                <div class="w-full h-1 mt-2 bg-primary opacity-70 transform -translate-y-2"></div>
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto" data-lang-key="join_thousands">Join thousands who are already saving money and making extra income.</p>
        </div>

        <!-- Video Section -->
        <div class="max-w-4xl mx-auto mb-16 rounded-2xl overflow-hidden shadow-2xl video-section">
            <div class="relative pb-[56.25%] h-0">
                <video class="absolute inset-0 w-full h-full object-cover" controls poster="{{ asset('video/videoPoster.png') }}">
                    <source src="{{ asset('video/carbnbVideo.mp4') }}" type="video/mp4">
                    Your browser does not support the video tag.
                </video>
                <div class="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center group transition-opacity duration-500">
                    <button class="w-20 h-20 bg-primary bg-opacity-80 rounded-full flex items-center justify-center transition-transform transform group-hover:scale-110 focus:outline-none">
                        <i class="fas fa-play text-white text-3xl pl-1"></i>
                    </button>
                </div>
            </div>
        </div>

        <div class="flex flex-col md:flex-row gap-12 mb-16">
            <div class="md:w-1/2 bg-white p-8 rounded-2xl shadow-xl transform transition-all duration-300 hover:shadow-2xl hover:-translate-y-1">
                <div class="inline-block bg-primary text-white text-lg font-bold px-6 py-2 rounded-full mb-6">
                    <i class="fas fa-car-side mr-2"></i>
                    <span data-lang-key="rent_a_car_title">Rent a Car</span>
                </div>
                <div class="space-y-8">
                    <div class="flex items-start">
                        <div class="w-14 h-14 bg-red-100 rounded-full flex items-center justify-center mr-5 flex-shrink-0 shadow-md">
                            <span class="text-primary text-xl font-bold">1</span>
                        </div>
                        <div>
                            <h4 class="text-xl font-bold mb-3" data-lang-key="search_compare">Search & Compare</h4>
                            <p class="text-gray-600 leading-relaxed" data-lang-key="search_compare_desc">Browse thousands of vehicles by location, date, and price to find your ideal ride.</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <div class="w-14 h-14 bg-red-100 rounded-full flex items-center justify-center mr-5 flex-shrink-0 shadow-md">
                            <span class="text-primary text-xl font-bold">2</span>
                        </div>
                        <div>
                            <h4 class="text-xl font-bold mb-3" data-lang-key="book_instantly">Book Instantly</h4>
                            <p class="text-gray-600 leading-relaxed" data-lang-key="book_instantly_desc">Reserve your chosen car with our secure booking system. No waiting for approval.</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <div class="w-14 h-14 bg-red-100 rounded-full flex items-center justify-center mr-5 flex-shrink-0 shadow-md">
                            <span class="text-primary text-xl font-bold">3</span>
                        </div>
                        <div>
                            <h4 class="text-xl font-bold mb-3" data-lang-key="pickup_go">Pick Up & Go</h4>
                            <p class="text-gray-600 leading-relaxed" data-lang-key="pickup_go_desc">Meet the owner to collect the keys or use our contactless pickup option where available.</p>
                        </div>
                    </div>
                </div>
                <a href="{{ route('cars.listing') }}" class="inline-block mt-8 px-8 py-4 bg-primary text-white font-bold rounded-xl hover:bg-red-600 transition-all transform hover:scale-105 shadow-lg" data-lang-key="find_car_btn">Find a Car</a>
            </div>

            <div class="md:w-1/2 bg-white p-8 rounded-2xl shadow-xl transform transition-all duration-300 hover:shadow-2xl hover:-translate-y-1">
                <div class="inline-block bg-primary text-white text-lg font-bold px-6 py-2 rounded-full mb-6">
                    <i class="fas fa-key mr-2"></i>
                    <span data-lang-key="list_your_car_title">List Your Car</span>
                </div>
                <div class="space-y-8">
                    <div class="flex items-start">
                        <div class="w-14 h-14 bg-red-100 rounded-full flex items-center justify-center mr-5 flex-shrink-0 shadow-md">
                            <span class="text-primary text-xl font-bold">1</span>
                        </div>
                        <div>
                            <h4 class="text-xl font-bold mb-3" data-lang-key="list_for_free">List for Free</h4>
                            <p class="text-gray-600 leading-relaxed" data-lang-key="list_for_free_desc">Create a listing with photos, set your price and availability. It takes just 10 minutes.</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <div class="w-14 h-14 bg-red-100 rounded-full flex items-center justify-center mr-5 flex-shrink-0 shadow-md">
                            <span class="text-primary text-xl font-bold">2</span>
                        </div>
                        <div>
                            <h4 class="text-xl font-bold mb-3" data-lang-key="receive_bookings">Receive Bookings</h4>
                            <p class="text-gray-600 leading-relaxed" data-lang-key="receive_bookings_desc">Get notified when someone books your car. Accept or decline based on your schedule.</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <div class="w-14 h-14 bg-red-100 rounded-full flex items-center justify-center mr-5 flex-shrink-0 shadow-md">
                            <span class="text-primary text-xl font-bold">3</span>
                        </div>
                        <div>
                            <h4 class="text-xl font-bold mb-3" data-lang-key="get_paid">Get Paid</h4>
                            <p class="text-gray-600 leading-relaxed" data-lang-key="get_paid_desc">Earnings are automatically deposited to your account after each completed trip.</p>
                        </div>
                    </div>
                </div>
                <a href="{{ route('list-car') }}" id="list-your-car" class="inline-block mt-8 px-8 py-4 bg-primary text-white font-bold rounded-xl hover:bg-red-600 transition-all transform hover:scale-105 shadow-lg" data-lang-key="list_car_btn2">List Your Car</a>
            </div>
        </div>
    </div>
</section>

<!-- Popular Car Categories -->
<section id="rent-a-car" class="py-24 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-extrabold text-gray-900 mb-4 relative inline-block" data-lang-key="browse_car_type">
                Browse by Car Type
                <div class="w-full h-1 mt-2 bg-primary opacity-70 transform -translate-y-2"></div>
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto" data-lang-key="find_perfect_vehicle">Find the perfect vehicle for any occasion, from compact city cars to spacious SUVs.</p>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
            @foreach($vehicleTypes as $type)
                <div class="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 group border border-gray-100">
                    <div class="h-52 overflow-hidden relative">
                        @if($type->icon_file)
                            <img src="{{ $type->icon_file->url }}" alt="{{ $type->name }}" class="w-full h-full object-cover group-hover:scale-110 transition-all duration-700 ease-in-out">
                        @else
                            <img src="https://via.placeholder.com/400x320?text={{ urlencode($type->name) }}" alt="{{ $type->name }}" class="w-full h-full object-cover group-hover:scale-110 transition-all duration-700 ease-in-out">
                        @endif
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300"></div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-2xl font-bold mb-3 group-hover:text-primary transition-colors">{{ $type->name }}</h3>
                        <p class="text-gray-600 mb-4 line-clamp-2">{{ $type->description ?? 'Explore our selection of ' . $type->name . ' vehicles.' }}</p>

                        @php
                            // Get the lowest daily rate for this vehicle type
                            $lowestPrice = \App\Models\Vehicle::where('vehicle_type_id', $type->id)
                                ->where('status', 'active')
                                ->min('daily_rate') ?? 0;
                        @endphp

                        <div class="flex justify-between items-center">
                            <p class="text-primary font-bold text-lg">
                                @if($lowestPrice > 0)
                                    From €{{ number_format($lowestPrice, 2) }}/day
                                @else
                                    Check availability
                                @endif
                            </p>
                            <a href="{{ route('cars.listing', ['vehicle_type' => $type->id]) }}" class="text-primary font-semibold hover:text-red-700 transition flex items-center">
                                View <i class="fas fa-arrow-right ml-2"></i>
                            </a>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</section>

<!-- Featured Cars -->
<section class="py-24 bg-gradient-to-b from-gray-50 to-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-extrabold text-gray-900 mb-4 relative inline-block" data-lang-key="top_rated_cars">
                Top Rated Cars
                <div class="w-full h-1 mt-2 bg-primary opacity-70 transform -translate-y-2"></div>
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto" data-lang-key="discover_highest_rated">Discover our highest-rated vehicles from trusted owners in our community.</p>
        </div>

        <div id="featured-cars" class="carousel relative">
            <div class="carousel-container flex overflow-x-auto snap-x pb-12 -mx-4 px-4 space-x-8 scrollbar-hide">
                @forelse($featuredVehicles as $vehicle)
                    <div class="carousel-item flex-none w-full sm:w-1/2 lg:w-1/3 snap-start bg-white rounded-2xl shadow-xl overflow-hidden transform transition-all duration-300 hover:shadow-2xl hover:-translate-y-2 border border-gray-100">
                        <div class="relative h-64">
                            @if($vehicle->primaryImage)
                                <img src="{{ $vehicle->primaryImage->url }}" alt="{{ $vehicle->make }} {{ $vehicle->model }}" class="w-full h-full object-cover">
                            @else
                                <img src="https://via.placeholder.com/400x320?text={{ urlencode($vehicle->make . ' ' . $vehicle->model) }}" alt="{{ $vehicle->make }} {{ $vehicle->model }}" class="w-full h-full object-cover">
                            @endif
                            <div class="absolute top-4 right-4 bg-primary text-white text-sm font-bold py-1.5 px-4 rounded-full shadow-md" data-lang-key="featured">
                                Featured
                            </div>
                            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent text-white p-4">
                                <h3 class="text-2xl font-bold">{{ $vehicle->year }} {{ $vehicle->make }} {{ $vehicle->model }}</h3>
                                <div class="flex items-center mt-1">
                                    <i class="fas fa-map-marker-alt text-primary mr-2"></i>
                                    <span>{{ $vehicle->city->name }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="flex justify-between items-center mb-4 pb-4 border-b border-gray-100">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-user text-gray-500"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium">{{ $vehicle->user->name }}</p>
                                        <div class="flex items-center">
                                            <i class="fas fa-star text-yellow-400 text-sm"></i>
                                            <span class="ml-1 text-sm text-gray-600">
                                                @php
                                                    // This would be replaced with actual rating logic
                                                    $rating = rand(40, 50) / 10;
                                                    $trips = rand(10, 60);
                                                @endphp
                                                {{ $rating }} ({{ $trips }} trips)
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <p class="text-primary font-bold text-xl">€{{ number_format($vehicle->daily_rate, 2) }}<span class="text-sm font-normal">/day</span></p>
                            </div>

                            <div class="grid grid-cols-2 gap-4 mb-6">
                                <div class="flex items-center bg-gray-50 p-2 rounded-lg">
                                    @if($vehicle->fuel_type == 'electric')
                                        <i class="fas fa-bolt text-green-500 mr-2"></i>
                                        <span class="text-sm font-medium" data-lang-key="electric">Electric</span>
                                    @elseif($vehicle->fuel_type == 'hybrid' || $vehicle->fuel_type == 'plugin_hybrid')
                                        <i class="fas fa-leaf text-green-500 mr-2"></i>
                                        <span class="text-sm font-medium" data-lang-key="{{ $vehicle->fuel_type }}">{{ ucfirst(str_replace('_', ' ', $vehicle->fuel_type)) }}</span>
                                    @else
                                        <i class="fas fa-gas-pump text-blue-500 mr-2"></i>
                                        <span class="text-sm font-medium" data-lang-key="{{ $vehicle->fuel_type }}">{{ ucfirst($vehicle->fuel_type) }}</span>
                                    @endif
                                </div>
                                <div class="flex items-center bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-cog text-gray-500 mr-2"></i>
                                    <span class="text-sm font-medium" data-lang-key="{{ $vehicle->transmission }}">{{ ucfirst($vehicle->transmission) }}</span>
                                </div>
                                <div class="flex items-center bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-user-friends text-gray-500 mr-2"></i>
                                    <span class="text-sm font-medium">{{ $vehicle->seats }} {{ __('Seats') }}</span>
                                </div>
                                <div class="flex items-center bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-door-open text-gray-500 mr-2"></i>
                                    <span class="text-sm font-medium">{{ $vehicle->doors }} {{ __('Doors') }}</span>
                                </div>
                            </div>
                            <a href="{{ route('vehicle.details', $vehicle->id) }}" class="block w-full bg-primary text-white py-3.5 text-center rounded-xl hover:bg-red-600 transition-all transform hover:scale-[1.02] font-bold shadow-md" data-lang-key="view_details">View Details</a>
                        </div>
                    </div>
                @empty
                    <div class="carousel-item flex-none w-full snap-start bg-white rounded-2xl shadow-xl overflow-hidden p-12 text-center">
                        <div class="w-20 h-20 bg-red-50 rounded-full flex items-center justify-center mx-auto mb-6">
                            <i class="fas fa-car-side text-4xl text-primary"></i>
                        </div>
                        <h3 class="text-2xl font-bold mb-4" data-lang-key="no_featured_cars">No Featured Cars Available</h3>
                        <p class="text-gray-600 mb-8 max-w-md mx-auto" data-lang-key="check_back">Check back soon for featured vehicles or browse all available cars.</p>
                        <a href="{{ route('cars.listing') }}" class="inline-block bg-primary text-white px-8 py-3 rounded-xl hover:bg-red-600 transition-all transform hover:scale-105 font-bold shadow-md" data-lang-key="browse_all_cars">Browse All Cars</a>
                    </div>
                @endforelse
            </div>

            <button id="prev-btn" class="absolute top-1/2 left-4 -translate-y-1/2 bg-white p-4 rounded-full shadow-lg text-primary hover:bg-primary hover:text-white focus:outline-none z-10 transition-all">
                <i class="fas fa-chevron-left"></i>
            </button>
            <button id="next-btn" class="absolute top-1/2 right-4 -translate-y-1/2 bg-white p-4 rounded-full shadow-lg text-primary hover:bg-primary hover:text-white focus:outline-none z-10 transition-all">
                <i class="fas fa-chevron-right"></i>
            </button>
        </div>

        <div class="text-center mt-12">
            <a href="{{ route('cars.listing') }}" class="inline-block text-primary font-bold text-lg hover:text-red-700 transition-all hover:translate-x-1" data-lang-key="browse_all">{{ frontend_trans('browse_all') }} <i class="fas fa-arrow-right ml-2"></i></a>
        </div>
    </div>
</section>

<!-- Benefits Section -->
<section class="py-24 bg-gradient-to-b from-white to-gray-50">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-extrabold text-gray-900 mb-4 relative inline-block" data-lang-key="why_choose">
                Why Choose CARBNB
                <div class="w-full h-1 mt-2 bg-primary opacity-70 transform -translate-y-2"></div>
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto" data-lang-key="benefits_desc">Our peer-to-peer marketplace offers benefits for both car owners and renters.</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-10">
            <div class="bg-white p-8 rounded-2xl shadow-xl border border-gray-100 transform transition-all duration-300 hover:shadow-2xl hover:-translate-y-2">
                <div class="w-20 h-20 bg-red-50 rounded-full flex items-center justify-center mb-8 mx-auto">
                    <i class="fas fa-shield-alt text-3xl text-primary"></i>
                </div>
                <h3 class="text-2xl font-bold mb-4 text-center" data-lang-key="insurance_included">Insurance Included</h3>
                <p class="text-gray-600 text-center leading-relaxed" data-lang-key="insurance_desc">Every booking comes with comprehensive insurance coverage for peace of mind.</p>
                <div class="mt-6 pt-6 border-t border-gray-100">
                    <ul class="space-y-3">
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-700">Liability coverage up to €1M</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-700">Collision protection</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-700">24/7 roadside assistance</span>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="bg-white p-8 rounded-2xl shadow-xl border border-gray-100 transform transition-all duration-300 hover:shadow-2xl hover:-translate-y-2">
                <div class="w-20 h-20 bg-red-50 rounded-full flex items-center justify-center mb-8 mx-auto">
                    <i class="fas fa-hand-holding-euro text-3xl text-primary"></i>
                </div>
                <h3 class="text-2xl font-bold mb-4 text-center" data-lang-key="earn_money">Earn Money Sharing</h3>
                <p class="text-gray-600 text-center leading-relaxed" data-lang-key="earn_money_desc">Car owners can earn an average of €500+ per month sharing their vehicle.</p>
                <div class="mt-6 pt-6 border-t border-gray-100">
                    <ul class="space-y-3">
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-700">Set your own availability</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-700">Choose your own pricing</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-700">Fast, secure payments</span>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="bg-white p-8 rounded-2xl shadow-xl border border-gray-100 transform transition-all duration-300 hover:shadow-2xl hover:-translate-y-2">
                <div class="w-20 h-20 bg-red-50 rounded-full flex items-center justify-center mb-8 mx-auto">
                    <i class="fas fa-wallet text-3xl text-primary"></i>
                </div>
                <h3 class="text-2xl font-bold mb-4 text-center" data-lang-key="save_rentals">Save on Rentals</h3>
                <p class="text-gray-600 text-center leading-relaxed" data-lang-key="save_rentals_desc">Renters typically save 25% compared to traditional car rental agencies.</p>
                <div class="mt-6 pt-6 border-t border-gray-100">
                    <ul class="space-y-3">
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-700">No hidden fees</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-700">Flexible pickup & return</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-700">Wide selection of vehicles</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Testimonials -->
<section class="py-24 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-extrabold text-gray-900 mb-4 relative inline-block" data-lang-key="community_says">
                What Our Community Says
                <div class="w-full h-1 mt-2 bg-primary opacity-70 transform -translate-y-2"></div>
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto" data-lang-key="real_stories">Real stories from car owners and renters in our growing community.</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-10">
            <div class="bg-white p-8 rounded-2xl shadow-xl border border-gray-100 transform transition-all duration-300 hover:shadow-2xl hover:-translate-y-2 relative">
                <div class="absolute -top-6 left-1/2 transform -translate-x-1/2">
                    <div class="w-16 h-16 rounded-full border-4 border-white shadow-md overflow-hidden">
                        <img src="https://via.placeholder.com/64x64" alt="Customer" class="w-full h-full object-cover">
                    </div>
                </div>

                <div class="text-center pt-12 mb-6">
                    <h4 class="text-xl font-bold">Raquel T.</h4>
                    <p class="text-gray-500 text-sm" data-lang-key="car_owner">Car Owner</p>
                    <div class="flex text-yellow-400 justify-center mt-2">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                </div>

                <div class="relative">
                    <i class="fas fa-quote-left text-4xl text-red-100 absolute -top-4 -left-2"></i>
                    <p class="text-gray-700 leading-relaxed relative z-10" data-lang-key="testimonial_1">"My car was just sitting in my driveway most days. Now it makes me over €600 a month on CARBNB! The platform is easy to use and the support team is always helpful."</p>
                    <i class="fas fa-quote-right text-4xl text-red-100 absolute -bottom-4 -right-2"></i>
                </div>

                <div class="mt-8 pt-6 border-t border-gray-100 text-center">
                    <span class="inline-block bg-red-50 text-primary px-4 py-2 rounded-full text-sm font-medium">
                        <i class="fas fa-euro-sign mr-1"></i> Earns €600+/month
                    </span>
                </div>
            </div>

            <div class="bg-white p-8 rounded-2xl shadow-xl border border-gray-100 transform transition-all duration-300 hover:shadow-2xl hover:-translate-y-2 relative">
                <div class="absolute -top-6 left-1/2 transform -translate-x-1/2">
                    <div class="w-16 h-16 rounded-full border-4 border-white shadow-md overflow-hidden">
                        <img src="https://via.placeholder.com/64x64" alt="Customer" class="w-full h-full object-cover">
                    </div>
                </div>

                <div class="text-center pt-12 mb-6">
                    <h4 class="text-xl font-bold">André M.</h4>
                    <p class="text-gray-500 text-sm" data-lang-key="car_renter">Car Renter</p>
                    <div class="flex text-yellow-400 justify-center mt-2">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star-half-alt"></i>
                    </div>
                </div>

                <div class="relative">
                    <i class="fas fa-quote-left text-4xl text-red-100 absolute -top-4 -left-2"></i>
                    <p class="text-gray-700 leading-relaxed relative z-10" data-lang-key="testimonial_2">"I needed a truck for a weekend move and found the perfect vehicle on CARBNB. Way cheaper than rental companies and the owner was super flexible with pickup and drop-off times."</p>
                    <i class="fas fa-quote-right text-4xl text-red-100 absolute -bottom-4 -right-2"></i>
                </div>

                <div class="mt-8 pt-6 border-t border-gray-100 text-center">
                    <span class="inline-block bg-red-50 text-primary px-4 py-2 rounded-full text-sm font-medium">
                        <i class="fas fa-percentage mr-1"></i> Saved 30% vs. rental companies
                    </span>
                </div>
            </div>

            <div class="bg-white p-8 rounded-2xl shadow-xl border border-gray-100 transform transition-all duration-300 hover:shadow-2xl hover:-translate-y-2 relative">
                <div class="absolute -top-6 left-1/2 transform -translate-x-1/2">
                    <div class="w-16 h-16 rounded-full border-4 border-white shadow-md overflow-hidden">
                        <img src="https://via.placeholder.com/64x64" alt="Customer" class="w-full h-full object-cover">
                    </div>
                </div>

                <div class="text-center pt-12 mb-6">
                    <h4 class="text-xl font-bold">Maria L.</h4>
                    <p class="text-gray-500 text-sm" data-lang-key="owner_renter">Car Owner & Renter</p>
                    <div class="flex text-yellow-400 justify-center mt-2">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                </div>

                <div class="relative">
                    <i class="fas fa-quote-left text-4xl text-red-100 absolute -top-4 -left-2"></i>
                    <p class="text-gray-700 leading-relaxed relative z-10" data-lang-key="testimonial_3">"I list my sedan during the week and rent SUVs for weekend trips. CARBNB has completely changed how I think about car ownership. The community is respectful and the process is seamless."</p>
                    <i class="fas fa-quote-right text-4xl text-red-100 absolute -bottom-4 -right-2"></i>
                </div>

                <div class="mt-8 pt-6 border-t border-gray-100 text-center">
                    <span class="inline-block bg-red-50 text-primary px-4 py-2 rounded-full text-sm font-medium">
                        <i class="fas fa-exchange-alt mr-1"></i> Both owner & renter
                    </span>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Trust & Safety Section -->
<section class="py-24 bg-gradient-to-r from-gray-900 to-secondary text-white relative overflow-hidden">
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0 bg-pattern-dots"></div>
    </div>

    <div class="container mx-auto px-4 relative z-10">
        <div class="flex flex-col lg:flex-row items-center">
            <div class="lg:w-1/2 mb-12 lg:mb-0">
                <div class="bg-black/30 p-8 rounded-3xl backdrop-blur-sm border border-white/10">
                    <h2 class="text-4xl font-extrabold mb-6 text-white" data-lang-key="trust_safety">Trust & Safety Guaranteed</h2>
                    <p class="mb-8 text-white/80 text-lg leading-relaxed" data-lang-key="trust_safety_desc">Your safety is our top priority. Every trip is protected by our comprehensive insurance and 24/7 roadside assistance.</p>

                    <div class="space-y-6 mb-8">
                        <div class="flex items-start bg-white/5 p-4 rounded-xl hover:bg-white/10 transition-all">
                            <div class="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                                <i class="fas fa-shield-alt text-xl text-primary"></i>
                            </div>
                            <div>
                                <h4 class="text-xl font-bold mb-2" data-lang-key="insurance_protection">€1M Insurance Protection</h4>
                                <p class="text-white/70" data-lang-key="insurance_protection_desc">Every trip includes liability insurance up to €1 million, giving both owners and renters complete peace of mind.</p>
                            </div>
                        </div>

                        <div class="flex items-start bg-white/5 p-4 rounded-xl hover:bg-white/10 transition-all">
                            <div class="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                                <i class="fas fa-phone-alt text-xl text-primary"></i>
                            </div>
                            <div>
                                <h4 class="text-xl font-bold mb-2" data-lang-key="roadside">24/7 Roadside Assistance</h4>
                                <p class="text-white/70" data-lang-key="roadside_desc">Help is always available if you need it on the road, with emergency support just a phone call away.</p>
                            </div>
                        </div>

                        <div class="flex items-start bg-white/5 p-4 rounded-xl hover:bg-white/10 transition-all">
                            <div class="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                                <i class="fas fa-lock text-xl text-primary"></i>
                            </div>
                            <div>
                                <h4 class="text-xl font-bold mb-2" data-lang-key="secure_payments">Secure Payments</h4>
                                <p class="text-white/70" data-lang-key="secure_payments_desc">All transactions are processed securely through our platform with bank-level encryption and fraud protection.</p>
                            </div>
                        </div>
                    </div>

                    <a href="#" class="inline-block px-8 py-4 bg-primary text-white font-bold rounded-xl hover:bg-red-600 transition-all transform hover:scale-105 shadow-lg" data-lang-key="learn_more">Learn More About Safety</a>
                </div>
            </div>

            <div class="lg:w-1/2 lg:pl-16">
                <div class="relative">
                    <img src="https://via.placeholder.com/600x400?text=Trust+and+Safety" alt="Trust and Safety" class="w-full rounded-3xl shadow-2xl transform hover:scale-[1.02] transition-all duration-500">
                    <div class="absolute -bottom-6 -right-6 bg-primary text-white p-6 rounded-2xl shadow-xl">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-white rounded-full flex items-center justify-center mr-4">
                                <i class="fas fa-user-shield text-2xl text-primary"></i>
                            </div>
                            <div>
                                <p class="font-bold text-lg">100% Secure</p>
                                <p class="text-white/80 text-sm">Verified & Protected</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Cities Section -->
<section class="py-24 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-extrabold text-gray-900 mb-4 relative inline-block" data-lang-key="available_cities">
                Available in Your City
                <div class="w-full h-1 mt-2 bg-primary opacity-70 transform -translate-y-2"></div>
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto" data-lang-key="expanding_cities">We're rapidly expanding to cities across Portugal. Find cars to share near you.</p>
        </div>

        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-6 mb-12">
            <a href="#" class="group">
                <div class="bg-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform group-hover:-translate-y-2 text-center border border-gray-100 h-full">
                    <div class="w-16 h-16 bg-red-50 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary group-hover:text-white transition-all">
                        <i class="fas fa-city text-2xl text-primary group-hover:text-white"></i>
                    </div>
                    <p class="font-bold text-lg group-hover:text-primary transition-colors" data-lang-key="lisbon">Lisbon</p>
                    <p class="text-gray-500 text-sm mt-1">320+ cars</p>
                </div>
            </a>

            <a href="#" class="group">
                <div class="bg-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform group-hover:-translate-y-2 text-center border border-gray-100 h-full">
                    <div class="w-16 h-16 bg-red-50 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary group-hover:text-white transition-all">
                        <i class="fas fa-city text-2xl text-primary group-hover:text-white"></i>
                    </div>
                    <p class="font-bold text-lg group-hover:text-primary transition-colors" data-lang-key="porto">Porto</p>
                    <p class="text-gray-500 text-sm mt-1">280+ cars</p>
                </div>
            </a>

            <a href="#" class="group">
                <div class="bg-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform group-hover:-translate-y-2 text-center border border-gray-100 h-full">
                    <div class="w-16 h-16 bg-red-50 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary group-hover:text-white transition-all">
                        <i class="fas fa-city text-2xl text-primary group-hover:text-white"></i>
                    </div>
                    <p class="font-bold text-lg group-hover:text-primary transition-colors" data-lang-key="faro">Faro</p>
                    <p class="text-gray-500 text-sm mt-1">150+ cars</p>
                </div>
            </a>

            <a href="#" class="group">
                <div class="bg-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform group-hover:-translate-y-2 text-center border border-gray-100 h-full">
                    <div class="w-16 h-16 bg-red-50 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary group-hover:text-white transition-all">
                        <i class="fas fa-city text-2xl text-primary group-hover:text-white"></i>
                    </div>
                    <p class="font-bold text-lg group-hover:text-primary transition-colors" data-lang-key="braga">Braga</p>
                    <p class="text-gray-500 text-sm mt-1">120+ cars</p>
                </div>
            </a>

            <a href="#" class="group">
                <div class="bg-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform group-hover:-translate-y-2 text-center border border-gray-100 h-full">
                    <div class="w-16 h-16 bg-red-50 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary group-hover:text-white transition-all">
                        <i class="fas fa-city text-2xl text-primary group-hover:text-white"></i>
                    </div>
                    <p class="font-bold text-lg group-hover:text-primary transition-colors" data-lang-key="coimbra">Coimbra</p>
                    <p class="text-gray-500 text-sm mt-1">90+ cars</p>
                </div>
            </a>

            <a href="#" class="group">
                <div class="bg-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform group-hover:-translate-y-2 text-center border border-gray-100 h-full">
                    <div class="w-16 h-16 bg-red-50 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary group-hover:text-white transition-all">
                        <i class="fas fa-city text-2xl text-primary group-hover:text-white"></i>
                    </div>
                    <p class="font-bold text-lg group-hover:text-primary transition-colors" data-lang-key="aveiro">Aveiro</p>
                    <p class="text-gray-500 text-sm mt-1">75+ cars</p>
                </div>
            </a>
        </div>

        <div class="text-center">
            <a href="#" class="inline-block text-primary font-bold text-lg hover:text-red-700 transition-all hover:translate-x-1" data-lang-key="view_locations">View all locations <i class="fas fa-arrow-right ml-2"></i></a>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-24 bg-gradient-to-r from-primary to-red-700 text-white relative overflow-hidden">
    <!-- Background decorative elements -->
    <div class="absolute inset-0 overflow-hidden opacity-10">
        <div class="absolute -top-24 -left-24 w-96 h-96 rounded-full bg-white/20"></div>
        <div class="absolute -bottom-32 -right-32 w-96 h-96 rounded-full bg-white/20"></div>
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] rounded-full border-2 border-white/10"></div>
    </div>

    <div class="container mx-auto px-4 text-center relative z-10">
        <div class="max-w-4xl mx-auto">
            <h2 class="text-4xl md:text-5xl font-extrabold mb-6 leading-tight" data-lang-key="ready_join">Ready to Join Our Community?</h2>
            <p class="text-xl md:text-2xl mb-10 text-white/90" data-lang-key="ready_join_desc">Whether you want to rent a car or share your own, it's free to join CARBNB and start exploring.</p>

            <div class="flex flex-col sm:flex-row justify-center gap-6">
                <a href="{{ route('cars.listing') }}" class="px-10 py-5 bg-white text-primary font-bold rounded-xl hover:bg-gray-100 transition-all transform hover:scale-105 shadow-lg text-lg" data-lang-key="rent_car_cta">Rent a Car</a>
                <a href="{{ route('list-car') }}" class="px-10 py-5 bg-secondary text-white font-bold rounded-xl hover:bg-gray-800 transition-all transform hover:scale-105 shadow-lg text-lg" data-lang-key="list_car_cta">List Your Car</a>
                <a href="{{ route('quick-driver-registration') }}" class="px-10 py-5 bg-blue-600 text-white font-bold rounded-xl hover:bg-blue-700 transition-all transform hover:scale-105 shadow-lg text-lg">Become a Driver</a>
            </div>

            <div class="mt-12 pt-12 border-t border-white/20 flex flex-wrap justify-center gap-8">
                <div class="flex items-center">
                    <i class="fas fa-check-circle text-2xl mr-3"></i>
                    <span class="text-lg">Free to join</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-check-circle text-2xl mr-3"></i>
                    <span class="text-lg">Cancel anytime</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-check-circle text-2xl mr-3"></i>
                    <span class="text-lg">24/7 customer support</span>
                </div>
            </div>
        </div>
    </div>
</section>

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize carousel
            const carouselContainer = document.querySelector('.carousel-container');
            const prevBtn = document.getElementById('prev-btn');
            const nextBtn = document.getElementById('next-btn');

            if (carouselContainer && prevBtn && nextBtn) {
                const scrollAmount = carouselContainer.clientWidth;

                prevBtn.addEventListener('click', function() {
                    carouselContainer.scrollBy({
                        left: -scrollAmount,
                        behavior: 'smooth'
                    });
                });

                nextBtn.addEventListener('click', function() {
                    carouselContainer.scrollBy({
                        left: scrollAmount,
                        behavior: 'smooth'
                    });
                });
            }

            // Animate counters
            const counters = document.querySelectorAll('.counter');
            const speed = 200;

            counters.forEach(counter => {
                const animate = () => {
                    const value = +counter.innerText.replace(/,/g, '').replace(/\+/g, '');
                    const data = +counter.getAttribute('data-target');
                    const time = data / speed;

                    if (value < data) {
                        counter.innerText = Math.ceil(value + time);
                        setTimeout(animate, 1);
                    } else {
                        counter.innerText = data.toLocaleString() + (counter.innerText.includes('+') ? '+' : '');
                    }
                };

                counter.setAttribute('data-target', parseInt(counter.innerText.replace(/,/g, '').replace(/\+/g, '')));
                counter.innerText = '0';
                animate();
            });

            // Video player functionality
            const videoContainer = document.querySelector('.video-section');
            const video = document.querySelector('.video-section video');
            const playButton = document.querySelector('.video-section .group button');
            const overlay = document.querySelector('.video-section .absolute.inset-0.bg-black');

            if (video && playButton) {
                // Make sure video is loaded
                video.addEventListener('loadedmetadata', function() {
                    console.log('Video metadata loaded');
                });

                // Play button click handler
                playButton.addEventListener('click', function() {
                    console.log('Play button clicked');
                    try {
                        const playPromise = video.play();

                        if (playPromise !== undefined) {
                            playPromise.then(_ => {
                                // Video playback started successfully
                                console.log('Video playback started');
                                this.parentElement.classList.add('opacity-0');
                                setTimeout(() => {
                                    this.parentElement.style.display = 'none';
                                }, 500);
                            })
                            .catch(error => {
                                // Auto-play was prevented
                                console.log('Playback error:', error);
                            });
                        }
                    } catch (e) {
                        console.error('Error playing video:', e);
                    }
                });

                // Handle video pause
                video.addEventListener('pause', function() {
                    console.log('Video paused');
                    playButton.parentElement.style.display = 'flex';
                    setTimeout(() => {
                        playButton.parentElement.classList.remove('opacity-0');
                    }, 10);
                });

                // Handle video end
                video.addEventListener('ended', function() {
                    console.log('Video ended');
                    playButton.parentElement.style.display = 'flex';
                    setTimeout(() => {
                        playButton.parentElement.classList.remove('opacity-0');
                    }, 10);
                });
            }
        });
    </script>
@endpush

</x-app-layout>

# File Upload System Documentation

This document explains how to use the file upload system in the application.

## Overview

The file upload system allows you to:
- Upload files to the server
- Store file metadata in the database
- Associate files with other models
- Manage files through a file manager interface

## File Storage

All uploaded files are stored in the `storage/app/public/uploads` directory. The file paths are stored in the `files` table in the database.

## Database Structure

The `files` table has the following structure:

| Column | Type | Description |
|--------|------|-------------|
| id | bigint | Primary key |
| title | string | Original filename |
| filename | string | Generated unique filename |
| url | string | Public URL to access the file |
| fileSize | integer | File size in bytes |
| fileType | string | MIME type of the file |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

## Using the File Upload Component

The application provides a reusable file upload component that can be used in any form.

### Basic Usage

```blade
<x-file-upload 
    name="document" 
    label="Upload Document" 
    :multiple="false" 
    accept="image/*,.pdf" 
    :required="true"
/>
```

### Component Properties

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| name | string | 'file' | Form field name |
| label | string | 'Upload File' | Label text |
| multiple | boolean | false | Allow multiple file selection |
| accept | string | '*/*' | Accepted file types (MIME types or extensions) |
| required | boolean | false | Whether the field is required |
| fileManagerEnabled | boolean | true | Enable file manager button |
| previewEnabled | boolean | true | Show file previews |
| fileIds | string | '' | Comma-separated list of pre-selected file IDs |
| primaryFileId | string | '' | ID of the primary file (for multiple files) |

### Form Handling

When the form is submitted, the component will send the following data:

- For single file upload: `name="file_id"`
- For multiple file upload: `name="file_ids"` (comma-separated list) and `name="file_ids_primary"` (primary file ID)

Example controller method:

```php
public function store(Request $request)
{
    $request->validate([
        'document' => 'required|string', // File ID
    ]);
    
    $fileId = $request->input('document');
    
    // Associate the file with your model
    $yourModel->file_id = $fileId;
    $yourModel->save();
    
    return redirect()->back()->with('success', 'Document uploaded successfully!');
}
```

## File Manager

The file manager provides a user interface for browsing and selecting files. It can be accessed by clicking the "Browse Files" button in the file upload component.

### Features

- Upload new files
- Browse existing files
- Search for files
- Select files for use in forms
- Delete files

### Including the File Manager

Make sure to include the file manager modal in your layout or view:

```blade
@include('components.file-management.file-management-modal')
```

## Programmatic File Upload

You can also upload files programmatically using the `FileManagerController`:

```php
use App\Models\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

// Generate a unique filename
$filename = time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();

// Store the file
$path = $file->storeAs('uploads', $filename, 'public');

// Create a database record
$fileModel = new File();
$fileModel->title = $file->getClientOriginalName();
$fileModel->filename = $filename;
$fileModel->url = Storage::disk('public')->url($path);
$fileModel->fileSize = $file->getSize();
$fileModel->fileType = $file->getMimeType();
$fileModel->save();

// Now you can use $fileModel->id to associate with other models
```

## Retrieving Files

To retrieve a file by ID:

```php
$file = File::find($fileId);
$fileUrl = $file->url;
```

To retrieve multiple files by IDs:

```php
$fileIds = explode(',', $fileIdsString);
$files = File::whereIn('id', $fileIds)->get();
```

## File Relationships

You can define relationships between your models and files:

```php
// In your model
public function document()
{
    return $this->belongsTo(File::class, 'file_id');
}

// For multiple files
public function attachments()
{
    return $this->belongsToMany(File::class, 'model_files', 'model_id', 'file_id')
        ->withTimestamps();
}
```

## Example Usage

Here's a complete example of how to use the file upload system in a form:

```blade
<form action="{{ route('your.route') }}" method="POST">
    @csrf
    
    <x-file-upload 
        name="profile_image" 
        label="Profile Image" 
        :multiple="false" 
        accept="image/*" 
        :required="true"
        :fileIds="$user->profile_image_id ?? ''"
    />
    
    <x-file-upload 
        name="documents" 
        label="Supporting Documents" 
        :multiple="true" 
        accept=".pdf,.doc,.docx" 
        :required="false"
        :fileIds="$documentIds ?? ''"
        :primaryFileId="$primaryDocumentId ?? ''"
    />
    
    <button type="submit">Submit</button>
</form>
```

Controller:

```php
public function update(Request $request, User $user)
{
    $request->validate([
        'profile_image' => 'required|string',
        'documents' => 'nullable|string',
        'documents_primary' => 'nullable|string',
    ]);
    
    // Update profile image
    $user->profile_image_id = $request->input('profile_image');
    $user->save();
    
    // Update documents
    $documentIds = $request->input('documents') ? explode(',', $request->input('documents')) : [];
    
    // Clear existing documents
    $user->documents()->detach();
    
    // Attach new documents
    foreach ($documentIds as $documentId) {
        $user->documents()->attach($documentId, [
            'is_primary' => $documentId == $request->input('documents_primary')
        ]);
    }
    
    return redirect()->back()->with('success', 'Profile updated successfully!');
}
```

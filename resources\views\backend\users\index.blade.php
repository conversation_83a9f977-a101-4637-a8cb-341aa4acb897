<x-app-backend-layout>
    <div class="container mx-auto " x-data='userApp(@json($users))'>
        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <h1 class="text-xl font-bold text-gray-900">Users</h1>
            <button @click="openCreateModal" class="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-lg shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition">
                + Create User
            </button>
        </div>

        <!-- Users Table -->
        <table class="min-w-full bg-white shadow-sm rounded-lg">
            <thead>
            <tr class="bg-gray-100 border-b">
                <th class="px-6 py-3 text-left text-sm font-medium text-gray-600 uppercase">Name</th>
                <th class="px-6 py-3 text-left text-sm font-medium text-gray-600 uppercase">Email</th>
                <th class="px-6 py-3 text-left text-sm font-medium text-gray-600 uppercase">Role</th>
                <th class="px-6 py-3 text-right text-sm font-medium text-gray-600 uppercase">Actions</th>
            </tr>
            </thead>
            <tbody>
            @foreach($users as $user)
                <tr class="border-b hover:bg-gray-50 transition duration-150">
                    <td class="px-6 py-3">{{ $user->first_name }} {{ $user->last_name }}</td>
                    <td class="px-6 py-3">{{ $user->email }}</td>
                    <td class="px-6 py-3">{{ $user->role }}</td>
                    <td class="px-6 py-3">
                        <button @click="openEditModal({{ $user }})" class="text-blue-500 hover:text-blue-700">Edit</button>
                        <button @click="deleteUser({{ $user->id }})" class="text-red-500 hover:text-red-700">Delete</button>
                    </td>
                </tr>
            @endforeach
            </tbody>
        </table>

        <!-- Modal -->
        <div x-show="isModalOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4" x-cloak>
            <div class="bg-white p-6 rounded-lg shadow-xl w-full max-w-md relative">
                <button @click="closeModal" class="absolute top-3 right-3 text-gray-500 hover:text-gray-800">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
                <h2 class="text-2xl font-semibold mb-4 text-gray-800" x-text="isEditMode ? 'Edit User' : 'Create User'"></h2>
                <form @submit.prevent="isEditMode ? updateUser() : createUser()">
                    <div class="mb-4">
                        <label for="first_name" class="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                        <input type="text" id="first_name" x-model="form.first_name" class="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-800 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div class="mb-4">
                        <label for="last_name" class="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                        <input type="text" id="last_name" x-model="form.last_name" class="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-800 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div class="mb-4">
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <input type="email" id="email" x-model="form.email" class="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-800 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div class="mb-4">
                        <label for="role" class="block text-sm font-medium text-gray-700 mb-1">Role</label>
                        <select id="role" x-model="form.role" class="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-800 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Select Role</option>
                            <option value="staff">Staff</option>
                        </select>
                    </div>
                    <div class="flex justify-end">
                        <button type="button" @click="closeModal" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg shadow-sm transition mr-3">
                            Cancel
                        </button>
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg shadow-sm transition">
                            <span x-text="isEditMode ? 'Update' : 'Create'"></span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function userApp(users) {
            return {
                isModalOpen: false,
                isEditMode: false,
                form: { id: '', first_name: '', last_name: '', email: '', role: '' },
                users: users,
                openCreateModal() {
                    this.isEditMode = false;
                    this.form = { id: null, first_name: '', last_name: '', email: '', role: '' };
                    this.isModalOpen = true;
                },
                openEditModal(user) {
                    this.isEditMode = true;
                    this.form = { id: user.id, first_name: user.first_name, last_name: user.last_name, email: user.email, role: user.role };
                    this.isModalOpen = true;
                },
                closeModal() {
                    this.isModalOpen = false;
                },
                async createUser() {
                    try {
                        await fetch('{{ route('users.store') }}', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json', 'X-CSRF-TOKEN': '{{ csrf_token() }}' },
                            body: JSON.stringify(this.form)
                        });
                        window.location.reload();
                    } catch (error) {
                        Swal.fire('Error', error.message, 'error');
                    }
                },
                async updateUser() {
                    try {
                        await fetch(`{{ url('/users') }}/${this.form.id}`, {
                            method: 'PATCH',
                            headers: { 'Content-Type': 'application/json', 'X-CSRF-TOKEN': '{{ csrf_token() }}' },
                            body: JSON.stringify(this.form)
                        });
                        window.location.reload();
                    } catch (error) {
                        Swal.fire('Error', error.message, 'error');
                    }
                },
                async deleteUser(id) {
                    Swal.fire({
                        title: 'Are you sure?',
                        text: "This action cannot be undone!",
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonText: 'Yes, delete it!'
                    }).then(async (result) => {
                        if (result.isConfirmed) {
                            await fetch(`{{ url('/users') }}/${id}`, {
                                method: 'DELETE',
                                headers: { 'X-CSRF-TOKEN': '{{ csrf_token() }}' }
                            });
                            window.location.reload();
                        }
                    });
                }
            };
        }
    </script>
</x-app-backend-layout>

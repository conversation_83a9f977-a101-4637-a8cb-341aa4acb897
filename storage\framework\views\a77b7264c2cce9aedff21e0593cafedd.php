<?php if (isset($component)) { $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742 = $attributes; } ?>
<?php $component = App\View\Components\AppBackendLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-backend-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppBackendLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container bg-white p-5 mx-auto" x-data="stateApp()">
        <div class="flex justify-between mb-4">
            <h1 class="text-2xl">States</h1>
            <button @click="openCreateModal" class="bg-blue-500 text-white px-4 py-2 rounded">Create State</button>
        </div>

        <table class="table-auto w-full mb-4">
            <thead>
            <tr>
                <th class="px-4 py-2">Name</th>
                <th class="px-4 py-2">Slug</th>
                <th class="px-4 py-2">Description</th>
                <th class="px-4 py-2">Country</th>
                <th class="px-4 py-2">Actions</th>
            </tr>
            </thead>
            <tbody>
            <?php $__currentLoopData = $states; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $state): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr>
                    <td class="border px-4 py-2"><?php echo e($state->name); ?></td>
                    <td class="border px-4 py-2"><?php echo e($state->slug); ?></td>
                    <td class="border px-4 py-2"><?php echo e($state->description); ?></td>
                    <td class="border px-4 py-2"><?php echo e($state->country->name); ?></td>
                    <td class="border px-4 py-2">
                        <button @click="openEditModal(<?php echo e($state); ?>)" class="text-blue-500">Edit</button>
                        <button @click="deleteState(<?php echo e($state->id); ?>)" class="text-red-500 ml-2">Delete</button>
                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tbody>
        </table>

        <!-- Create/Edit Modal -->
        <div x-show="isModalOpen" class="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50" x-cloak>
            <div class="bg-white p-6 rounded-lg w-1/3 relative">
                <button @click="closeModal" class="absolute top-2 right-2 text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>

                <h2 class="text-xl mb-4" x-text="isEditMode ? 'Edit State' : 'Create State'"></h2>

                <form @submit.prevent="isEditMode ? updateState() : createState()">
                    <div class="mb-4">
                        <label for="name" class="block text-sm font-bold">Name</label>
                        <input type="text" name="name" id="name" x-model="form.name" class="mt-1 block w-full border border-gray-300 rounded p-2" required>
                    </div>

                    <div class="mb-4">
                        <label for="slug" class="block text-sm font-bold">Slug</label>
                        <input type="text" name="slug" id="slug" x-model="form.slug" class="mt-1 block w-full border border-gray-300 rounded p-2" required>
                    </div>

                    <div class="mb-4">
                        <label for="description" class="block text-sm font-bold">Description</label>
                        <textarea name="description" id="description" x-model="form.description" class="mt-1 block w-full border border-gray-300 rounded p-2" required></textarea>
                    </div>

                    <div class="mb-4">
                        <label for="country_id" class="block text-sm font-bold">Country</label>
                        <select name="country_id" id="country_id" x-model="form.country_id" class="mt-1 block w-full border border-gray-300 rounded p-2" required>
                            <option value="" disabled>Select a country</option>
                            <?php $__currentLoopData = $countries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $country): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($country->id); ?>"><?php echo e($country->name); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>

                    <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded" x-text="isEditMode ? 'Update' : 'Create'"></button>
                    <button @click="closeModal" type="button" class="bg-gray-500 text-white px-4 py-2 rounded ml-2">Cancel</button>
                </form>
            </div>
        </div>
    </div>

    <script>
        function stateApp() {
            return {
                isModalOpen: false,
                isEditMode: false,
                form: {
                    id: null,
                    name: '',
                    slug: '',
                    description: '',
                    country_id: null // Include country_id in the form
                },

                openCreateModal() {
                    this.isEditMode = false;
                    this.form = { id: null, name: '', slug: '', description: '', country_id: null };
                    this.isModalOpen = true;
                },

                openEditModal(state) {
                    this.isEditMode = true;
                    this.form = { id: state.id, name: state.name, slug: state.slug, description: state.description, country_id: state.country_id };
                    this.isModalOpen = true;
                },

                closeModal() {
                    this.isModalOpen = false;
                },

                async createState() {
                    try {
                        const response = await fetch('<?php echo e(route('admin.states.store')); ?>', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                            },
                            body: JSON.stringify(this.form)
                        });

                        if (!response.ok) throw new Error('Error creating state');
                        window.location.reload();
                    } catch (error) {
                        Swal.fire('Error', error.message, 'error');
                    }
                },

                async updateState() {
                    try {
                        const response = await fetch(`<?php echo e(url('admin/states')); ?>/${this.form.id}`, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                            },
                            body: JSON.stringify(this.form)
                        });

                        if (!response.ok) throw new Error('Error updating state');
                        window.location.reload();
                    } catch (error) {
                        Swal.fire('Error', error.message, 'error');
                    }
                },

                async deleteState(id) {
                    Swal.fire({
                        title: 'Are you sure?',
                        text: "This action cannot be undone.",
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#3085d6',
                        cancelButtonColor: '#d33',
                        confirmButtonText: 'Yes, delete it!'
                    }).then(async (result) => {
                        if (result.isConfirmed) {
                            try {
                                const response = await fetch(`<?php echo e(url('admin/states')); ?>/${id}`, {
                                    method: 'DELETE',
                                    headers: {
                                        'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                                    }
                                });

                                if (!response.ok) throw new Error('Error deleting state');
                                window.location.reload();
                            } catch (error) {
                                Swal.fire('Error', error.message, 'error');
                            }
                        }
                    });
                }
            }
        }
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $attributes = $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $component = $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\carbnb\resources\views\backend\location\states.blade.php ENDPATH**/ ?>
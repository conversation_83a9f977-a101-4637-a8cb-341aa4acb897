<x-app-backend-layout>
    <div class="container bg-white p-5 mx-auto">
        <h1 class="text-2xl mb-4">Edit Category</h1>

        <form action="{{ route('admin.categories.update', $category) }}" method="POST">
            @csrf
            @method('PUT')

            <div class="mb-4">
                <label for="name" class="block text-sm font-bold">Name</label>
                <input type="text" name="name" id="name" value="{{ old('name', $category->name) }}" class="mt-1 block w-full border border-gray-300 rounded p-2" required>
            </div>

            <div class="mb-4">
                <label for="description" class="block text-sm font-bold">Description</label>
                <input type="text" name="description" id="description" value="{{ old('description', $category->description) }}" class="mt-1 block w-full border border-gray-300 rounded p-2">
            </div>

            <div class="mb-4">
                <label for="status" class="block text-sm font-bold">Status</label>
                <select name="status" id="status" class="mt-1 block w-full border border-gray-300 rounded">
                    <option value="1" {{ $category->status ? 'selected' : '' }}>Active</option>
                    <option value="0" {{ !$category->status ? 'selected' : '' }}>Inactive</option>
                </select>
            </div>

            <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded">Update Category</button>
        </form>
    </div>
</x-app-backend-layout>

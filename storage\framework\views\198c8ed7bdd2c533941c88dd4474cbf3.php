<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<div class="container py-8">
    <div class="mb-6">
        <?php if(Auth::id() === $booking->user_id): ?>
            <a href="<?php echo e(route('booking.index')); ?>" class="text-primary hover:text-primary-dark flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                </svg>
                Back to My Bookings
            </a>
        <?php else: ?>
            <a href="<?php echo e(route('booking.owner')); ?>" class="text-primary hover:text-primary-dark flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                </svg>
                Back to Booking Requests
            </a>
        <?php endif; ?>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
                <div class="bg-gray-50 px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h1 class="text-2xl font-bold text-gray-800">Booking Details</h1>
                    <div>
                        <?php
                            $statusColors = [
                                'pending' => 'bg-yellow-100 text-yellow-800',
                                'confirmed' => 'bg-blue-100 text-blue-800',
                                'in_progress' => 'bg-purple-100 text-purple-800',
                                'completed' => 'bg-green-100 text-green-800',
                                'cancelled' => 'bg-red-100 text-red-800',
                                'rejected' => 'bg-gray-100 text-gray-800',
                            ];
                            $statusColor = $statusColors[$booking->status] ?? 'bg-gray-100 text-gray-800';
                        ?>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium <?php echo e($statusColor); ?>">
                            <?php echo e(ucfirst($booking->status)); ?>

                        </span>
                    </div>
                </div>

                <div class="p-6">
                    <div class="mb-8">
                        <div class="flex items-center mb-4">
                            <?php if($booking->vehicle->primaryImage): ?>
                                <img src="<?php echo e(asset('storage/' . $booking->vehicle->primaryImage->file->path)); ?>" alt="<?php echo e($booking->vehicle->make); ?> <?php echo e($booking->vehicle->model); ?>" class="w-24 h-24 object-cover rounded-md mr-4">
                            <?php else: ?>
                                <div class="w-24 h-24 bg-gray-200 rounded-md mr-4 flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                </div>
                            <?php endif; ?>
                            <div>
                                <h3 class="font-bold text-gray-800 text-lg"><?php echo e($booking->vehicle->make); ?> <?php echo e($booking->vehicle->model); ?></h3>
                                <p class="text-gray-600"><?php echo e($booking->vehicle->year); ?> · <?php echo e($booking->vehicle->vehicleType->name); ?></p>
                                <p class="text-gray-600 mt-1">Hosted by <?php echo e($booking->vehicle->user->first_name); ?> <?php echo e($booking->vehicle->user->last_name); ?></p>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                            <div>
                                <h3 class="font-medium text-gray-700 mb-2">Booking Information</h3>
                                <p class="text-gray-800 mb-1"><span class="text-gray-600">Booking Number:</span> <?php echo e($booking->booking_number); ?></p>
                                <p class="text-gray-800 mb-1"><span class="text-gray-600">Booked by:</span> <?php echo e($booking->user->name); ?></p>
                                <p class="text-gray-800 mb-1"><span class="text-gray-600">Booked on:</span> <?php echo e($booking->created_at->format('M d, Y')); ?></p>
                            </div>

                            <div>
                                <h3 class="font-medium text-gray-700 mb-2">Trip Details</h3>
                                <p class="text-gray-800 mb-1"><span class="text-gray-600">Pickup:</span> <?php echo e($booking->start_date->format('M d, Y')); ?></p>
                                <p class="text-gray-800 mb-1"><span class="text-gray-600">Return:</span> <?php echo e($booking->end_date->format('M d, Y')); ?></p>
                                <p class="text-gray-800 mb-1"><span class="text-gray-600">Duration:</span> <?php echo e($booking->duration_in_days); ?> days</p>
                            </div>

                            <div>
                                <h3 class="font-medium text-gray-700 mb-2">Pickup Location</h3>
                                <p class="text-gray-800"><?php echo e($booking->pickup_location); ?></p>
                            </div>

                            <div>
                                <h3 class="font-medium text-gray-700 mb-2">Return Location</h3>
                                <p class="text-gray-800"><?php echo e($booking->return_location); ?></p>
                            </div>
                        </div>
                    </div>

                    <?php if($booking->driver): ?>
                    <div class="mb-8">
                        <h2 class="text-xl font-semibold mb-4 text-gray-700">Driver Details</h2>

                        <div class="flex items-center p-4 bg-gray-50 rounded-lg">
                            <div class="w-12 h-12 bg-gray-200 rounded-full mr-4 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                            </div>
                            <div>
                                <h3 class="font-bold text-gray-800"><?php echo e($booking->driver->user->name); ?></h3>
                                <p class="text-gray-600 text-sm"><?php echo e($booking->driver->experience_years); ?> years experience</p>
                                <p class="text-gray-600 text-sm">Daily Rate: $<?php echo e(number_format($booking->driver->daily_rate, 2)); ?></p>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if(!empty($booking->special_requests)): ?>
                    <div class="mb-8">
                        <h2 class="text-xl font-semibold mb-4 text-gray-700">Special Requests</h2>

                        <div class="bg-gray-50 p-4 rounded-md">
                            <p class="text-gray-700"><?php echo e($booking->special_requests); ?></p>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if($booking->status === 'cancelled' || $booking->status === 'rejected'): ?>
                    <div class="mb-8">
                        <h2 class="text-xl font-semibold mb-4 text-gray-700">Cancellation Reason</h2>

                        <div class="bg-red-50 p-4 rounded-md">
                            <p class="text-red-700"><?php echo e($booking->cancellation_reason); ?></p>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if($booking->statusHistory->count() > 0): ?>
                    <div class="mb-8">
                        <h2 class="text-xl font-semibold mb-4 text-gray-700">Status History</h2>

                        <div class="bg-gray-50 p-4 rounded-md">
                            <ul class="space-y-4">
                                <?php $__currentLoopData = $booking->statusHistory->sortByDesc('created_at'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li class="flex items-start">
                                    <div class="flex-shrink-0 h-5 w-5 rounded-full bg-blue-500 flex items-center justify-center mt-1">
                                        <div class="h-2 w-2 rounded-full bg-white"></div>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-900">
                                            Status changed to <span class="font-bold"><?php echo e(ucfirst($status->status)); ?></span>
                                        </p>
                                        <p class="text-sm text-gray-500">
                                            <?php echo e($status->created_at->format('M d, Y h:i A')); ?> by <?php echo e($status->updatedBy->name); ?>

                                        </p>
                                        <?php if($status->notes): ?>
                                        <p class="text-sm text-gray-700 mt-1">
                                            <?php echo e($status->notes); ?>

                                        </p>
                                        <?php endif; ?>
                                    </div>
                                </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Action Buttons -->
                    <div class="mt-8 border-t border-gray-200 pt-6">
                        <h2 class="text-xl font-semibold mb-4 text-gray-700">Actions</h2>

                        <div class="flex flex-wrap gap-4">
                            <?php if(Auth::id() === $booking->user_id && in_array($booking->status, ['confirmed', 'in_progress'])): ?>
                                <!-- Message the vehicle owner -->
                                <a href="<?php echo e(route('messages.create', ['recipient_id' => $booking->vehicle->user_id, 'booking_id' => $booking->id])); ?>" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <i class="fas fa-envelope mr-2"></i> Message Owner
                                </a>
                            <?php endif; ?>

                            <?php if(Auth::id() === $booking->vehicle->user_id && in_array($booking->status, ['confirmed', 'in_progress'])): ?>
                                <!-- Message the renter -->
                                <a href="<?php echo e(route('messages.create', ['recipient_id' => $booking->user_id, 'booking_id' => $booking->id])); ?>" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <i class="fas fa-envelope mr-2"></i> Message Renter
                                </a>
                            <?php endif; ?>

                            <?php if(Auth::id() === $booking->vehicle->user_id): ?>
                                <!-- Owner Actions -->
                                <?php if($booking->status === 'pending'): ?>
                                <form action="<?php echo e(route('booking.update-status', $booking->id)); ?>" method="POST" class="confirm-action" data-message="Are you sure you want to confirm this booking?">
                                    <?php echo csrf_field(); ?>
                                    <input type="hidden" name="status" value="confirmed">
                                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                        Confirm Booking
                                    </button>
                                </form>

                                <button type="button" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500" onclick="showRejectModal()">
                                    Reject Booking
                                </button>
                                <?php endif; ?>

                                <?php if($booking->status === 'confirmed'): ?>
                                <form action="<?php echo e(route('booking.update-status', $booking->id)); ?>" method="POST" class="confirm-action" data-message="Are you sure you want to mark this booking as in progress?">
                                    <?php echo csrf_field(); ?>
                                    <input type="hidden" name="status" value="in_progress">
                                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                        Mark as In Progress
                                    </button>
                                </form>
                                <?php endif; ?>

                                <?php if($booking->status === 'in_progress'): ?>
                                <form action="<?php echo e(route('booking.update-status', $booking->id)); ?>" method="POST" class="confirm-action" data-message="Are you sure you want to mark this booking as completed?">
                                    <?php echo csrf_field(); ?>
                                    <input type="hidden" name="status" value="completed">
                                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                        Mark as Completed
                                    </button>
                                </form>
                                <?php endif; ?>
                            <?php endif; ?>

                            <?php if(Auth::id() === $booking->user_id): ?>
                                <!-- Renter Actions -->
                                <?php if($booking->status === 'pending' || $booking->status === 'confirmed'): ?>
                                <button type="button" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500" onclick="showCancelModal()">
                                    Cancel Booking
                                </button>
                                <?php endif; ?>

                                <?php if($booking->status === 'pending' && $booking->payments->isEmpty()): ?>
                                <a href="<?php echo e(route('booking.payment', $booking->id)); ?>" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                    Make Payment
                                </a>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-md overflow-hidden sticky top-24">
                <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                    <h2 class="text-xl font-bold text-gray-800">Price Details</h2>
                </div>

                <div class="p-6">
                    <div class="mb-4">
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-600">$<?php echo e(number_format($booking->vehicle_price / $booking->duration_in_days, 2)); ?> × <?php echo e($booking->duration_in_days); ?> days</span>
                            <span class="font-medium">$<?php echo e(number_format($booking->vehicle_price, 2)); ?></span>
                        </div>

                        <?php if($booking->discount_amount > 0): ?>
                        <div class="flex justify-between mb-2 text-green-600">
                            <span>Discount</span>
                            <span>-$<?php echo e(number_format($booking->discount_amount, 2)); ?></span>
                        </div>
                        <?php endif; ?>

                        <?php if($booking->driver_price > 0): ?>
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-600">Driver</span>
                            <span class="font-medium">$<?php echo e(number_format($booking->driver_price, 2)); ?></span>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="border-t border-gray-200 pt-4 mb-4">
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-600">Subtotal</span>
                            <span class="font-medium">$<?php echo e(number_format($booking->subtotal, 2)); ?></span>
                        </div>
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-600">Tax (10%)</span>
                            <span class="font-medium">$<?php echo e(number_format($booking->tax_amount, 2)); ?></span>
                        </div>
                    </div>

                    <div class="border-t border-gray-200 pt-4 mb-4">
                        <div class="flex justify-between">
                            <span class="text-lg font-bold">Total</span>
                            <span class="text-lg font-bold">$<?php echo e(number_format($booking->total_amount, 2)); ?></span>
                        </div>
                    </div>

                    <?php if($booking->security_deposit > 0): ?>
                    <div class="border-t border-gray-200 pt-4 mb-4">
                        <div class="flex justify-between text-gray-600">
                            <span>Security deposit</span>
                            <span>$<?php echo e(number_format($booking->security_deposit, 2)); ?></span>
                        </div>
                        <p class="text-sm text-gray-500 mt-1">Refundable if no damage occurs</p>
                    </div>
                    <?php endif; ?>

                    <div class="border-t border-gray-200 pt-4">
                        <h3 class="font-medium text-gray-700 mb-2">Payment Status</h3>

                        <?php if($booking->payments->isNotEmpty()): ?>
                            <?php
                                $payment = $booking->payments->first();
                                $paymentStatusColors = [
                                    'pending' => 'bg-yellow-100 text-yellow-800',
                                    'completed' => 'bg-green-100 text-green-800',
                                    'failed' => 'bg-red-100 text-red-800',
                                    'refunded' => 'bg-blue-100 text-blue-800',
                                ];
                                $paymentStatusColor = $paymentStatusColors[$payment->status] ?? 'bg-gray-100 text-gray-800';
                            ?>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600"><?php echo e(ucfirst($payment->payment_method)); ?></span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($paymentStatusColor); ?>">
                                    <?php echo e(ucfirst($payment->status)); ?>

                                </span>
                            </div>
                            <p class="text-sm text-gray-500 mt-1">Transaction ID: <?php echo e($payment->transaction_id); ?></p>
                            <p class="text-sm text-gray-500">Date: <?php echo e($payment->created_at->format('M d, Y')); ?></p>
                        <?php else: ?>
                            <p class="text-sm text-yellow-600">Payment pending</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reject Booking Modal -->
<div id="reject-modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Reject Booking</h3>
        </div>
        <form action="<?php echo e(route('booking.update-status', $booking->id)); ?>" method="POST">
            <?php echo csrf_field(); ?>
            <input type="hidden" name="status" value="rejected">
            <div class="px-6 py-4">
                <div class="mb-4">
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Reason for rejection</label>
                    <textarea name="notes" id="notes" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50" required></textarea>
                </div>
            </div>
            <div class="px-6 py-3 bg-gray-50 flex justify-end space-x-3 rounded-b-lg">
                <button type="button" onclick="hideRejectModal()" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    Cancel
                </button>
                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                    Reject Booking
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Cancel Booking Modal -->
<div id="cancel-modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Cancel Booking</h3>
        </div>
        <form action="<?php echo e(route('booking.update-status', $booking->id)); ?>" method="POST">
            <?php echo csrf_field(); ?>
            <input type="hidden" name="status" value="cancelled">
            <div class="px-6 py-4">
                <div class="mb-4">
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Reason for cancellation</label>
                    <textarea name="notes" id="notes" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50" required></textarea>
                </div>
                <div class="bg-yellow-50 p-4 rounded-md">
                    <div class="flex">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                        </svg>
                        <div>
                            <p class="text-sm text-yellow-700">Cancellation may be subject to fees depending on the timing. Please review the cancellation policy.</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="px-6 py-3 bg-gray-50 flex justify-end space-x-3 rounded-b-lg">
                <button type="button" onclick="hideCancelModal()" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    Go Back
                </button>
                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                    Cancel Booking
                </button>
            </div>
        </form>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
    // Modal functions
    function showRejectModal() {
        document.getElementById('reject-modal').classList.remove('hidden');
    }

    function hideRejectModal() {
        document.getElementById('reject-modal').classList.add('hidden');
    }

    function showCancelModal() {
        document.getElementById('cancel-modal').classList.remove('hidden');
    }

    function hideCancelModal() {
        document.getElementById('cancel-modal').classList.add('hidden');
    }

    // Confirmation for actions
    document.addEventListener('DOMContentLoaded', function() {
        const confirmForms = document.querySelectorAll('.confirm-action');

        confirmForms.forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();

                const message = this.getAttribute('data-message') || 'Are you sure you want to perform this action?';

                if (confirm(message)) {
                    this.submit();
                }
            });
        });

        // Close modals when clicking outside
        window.addEventListener('click', function(e) {
            const rejectModal = document.getElementById('reject-modal');
            const cancelModal = document.getElementById('cancel-modal');

            if (e.target === rejectModal) {
                hideRejectModal();
            }

            if (e.target === cancelModal) {
                hideCancelModal();
            }
        });
    });
</script>
<?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\carbnb\resources\views\front\bookings\show.blade.php ENDPATH**/ ?>
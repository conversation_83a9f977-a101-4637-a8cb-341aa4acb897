<?php if (isset($component)) { $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742 = $attributes; } ?>
<?php $component = App\View\Components\AppBackendLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-backend-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppBackendLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div x-data="categoryManager" class="p-6">
        <div class="mb-4">
            <div class="flex justify-between items-center mb-3">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4">Category Management</h2>
                <button @click="openModal()"
                    class="inline-flex items-center justify-center px-4 py-2 rounded-lg bg-blue-600 text-white font-medium transition-all duration-200 hover:bg-blue-700 hover:shadow-lg focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                    <i class="fas fa-plus mr-2"></i>
                    Add Category
                </button>
            </div>

            <!-- Success Message (Fallback if notification system fails) -->
            <div x-show="successMessage" x-transition x-cloak class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4">
                <span x-text="successMessage"></span>
                <button @click="successMessage = ''" class="absolute top-0 right-0 mr-2 mt-2">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Search and filters section -->
            <div class="mb-4 bg-white p-4 rounded-lg shadow">
                <div class="flex flex-col md:flex-row gap-4">
                    <div class="flex-grow">
                        <div class="relative">
                            <input 
                                type="text" 
                                x-model="search" 
                                @keyup.enter="searchItems"
                                placeholder="Search categories by name..."
                                class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                            >
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-search text-gray-400"></i>
                            </div>
                            <button 
                                @click="searchItems"
                                class="absolute inset-y-0 right-0 px-4 text-gray-700 bg-gray-100 rounded-r-lg hover:bg-gray-200 focus:outline-none"
                            >
                                Search
                            </button>
                        </div>
                    </div>
                    <div>
                        <button 
                            @click="resetFilters"
                            class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-100"
                        >
                            Clear
                        </button>
                    </div>
                </div>
            </div>

            <!-- Loading Indicator (Fallback if notification loading fails) -->
            <div x-show="loading" class="flex justify-center my-4">
                <i class="fas fa-spinner fa-spin text-blue-600 text-3xl"></i>
            </div>

            <div class="bg-white rounded-lg shadow" x-show="!loading">
                <div class="overflow-x-auto">
                    <table class="w-full table-auto border-collapse">
                        <thead>
                            <tr class="bg-gray-200 text-gray-600 uppercase text-sm leading-normal">
                                <th class="py-3 px-6 text-left">#</th>
                                <th class="py-3 px-6 text-left">Name</th>
                                <th class="py-3 px-6 text-left">Slug</th>
                                <th class="py-3 px-6 text-center">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <template x-if="items.length === 0">
                                <tr class="border-b border-gray-200">
                                    <td colspan="4" class="py-8 px-6 text-center text-gray-500">
                                        No categories found
                                    </td>
                                </tr>
                            </template>
                            <template x-for="(category, index) in items" :key="category.id">
                                <tr class="border-b border-gray-200 hover:bg-gray-100">
                                    <td class="py-3 px-6 text-left" x-text="category.id"></td>
                                    <td class="py-3 px-6 text-left" x-text="category.name"></td>
                                    <td class="py-3 px-6 text-left" x-text="category.slug"></td>
                                    <td class="py-3 px-6 text-center flex justify-center items-center space-x-2">
                                        <!-- View Button -->
                                        <a :href="'/category/' + category.slug" 
                                           target="_blank"
                                           class="inline-flex items-center justify-center px-3 py-1.5 rounded-lg bg-emerald-500 text-white font-medium text-sm transition-all duration-200 hover:bg-emerald-600 hover:shadow-md focus:ring-2 focus:ring-emerald-400 focus:ring-opacity-50">
                                           <i class="fas fa-eye mr-1"></i>
                                           View
                                        </a>
                                        
                                        <!-- Edit Button -->
                                        <button @click="openModal(category)"
                                           class="inline-flex items-center justify-center px-3 py-1.5 rounded-lg bg-amber-500 text-white font-medium text-sm transition-all duration-200 hover:bg-amber-600 hover:shadow-md focus:ring-2 focus:ring-amber-400 focus:ring-opacity-50">
                                           <i class="fas fa-edit mr-1"></i>
                                           Edit
                                        </button>
                                        
                                        <!-- Delete Button -->
                                        <button @click="deleteCategory(category.id)"
                                           class="inline-flex items-center justify-center px-3 py-1.5 rounded-lg bg-rose-500 text-white font-medium text-sm transition-all duration-200 hover:bg-rose-600 hover:shadow-md focus:ring-2 focus:ring-rose-400 focus:ring-opacity-50">
                                           <i class="fas fa-trash-alt mr-1"></i>
                                           Delete
                                        </button>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination controls -->
                <template x-if="totalPages > 1">
                    <div class="px-4 py-3 bg-white border-t border-gray-200 sm:px-6">
                        <div class="flex items-center justify-between">
                            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                <div>
                                    <p class="text-sm text-gray-700">
                                        Showing
                                        <span class="font-medium" x-text="(currentPage - 1) * perPage + 1"></span>
                                        to
                                        <span class="font-medium" x-text="Math.min(currentPage * perPage, totalRecords)"></span>
                                        of
                                        <span class="font-medium" x-text="totalRecords"></span>
                                        results
                                    </p>
                                </div>
                                <div>
                                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                        <!-- Previous Page -->
                                        <button 
                                            @click="prevPage" 
                                            :disabled="currentPage === 1"
                                            :class="{'opacity-50 cursor-not-allowed': currentPage === 1}"
                                            class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                                        >
                                            <span class="sr-only">Previous</span>
                                            <i class="fas fa-chevron-left h-5 w-5"></i>
                                        </button>
                                        
                                        <!-- Page Numbers -->
                                        <template x-for="page in paginationArray" :key="page">
                                            <button 
                                                @click="page !== '...' ? goToPage(page) : null" 
                                                :class="{
                                                    'bg-blue-50 border-blue-500 text-blue-600': page === currentPage, 
                                                    'bg-white border-gray-300 text-gray-500 hover:bg-gray-50': page !== currentPage && page !== '...',
                                                    'bg-white border-gray-300 text-gray-500 cursor-default': page === '...'
                                                }"
                                                class="relative inline-flex items-center px-4 py-2 border text-sm font-medium"
                                                x-text="page"
                                            ></button>
                                        </template>
                                        
                                        <!-- Next Page -->
                                        <button 
                                            @click="nextPage" 
                                            :disabled="currentPage === totalPages"
                                            :class="{'opacity-50 cursor-not-allowed': currentPage === totalPages}"
                                            class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                                        >
                                            <span class="sr-only">Next</span>
                                            <i class="fas fa-chevron-right h-5 w-5"></i>
                                        </button>
                                    </nav>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </div>
        </div>

        <!-- Category Modal -->
        <div x-show="showModal" class="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50 z-50" x-cloak>
            <div class="bg-white p-6 rounded-lg shadow-lg w-[400px] max-w-full">
                <h3 class="text-lg font-semibold text-gray-800 mb-4" x-text="form.id ? 'Edit Category' : 'Add Category'"></h3>
                
                <div class="space-y-3">
                    <div>
                        <label class="text-gray-700 text-sm font-medium">Category Name</label>
                        <input type="text" placeholder="Name" x-model="form.name" @input="generateSlug"
                            class="border p-2 w-full rounded-lg shadow-sm focus:ring focus:ring-blue-300">
                    </div>
                    <div>
                        <label class="text-gray-700 text-sm font-medium">Slug</label>
                        <input type="text" placeholder="Slug" x-model="form.slug"
                            class="border p-2 w-full rounded-lg shadow-sm bg-gray-100 text-gray-600" readonly>
                    </div>
                </div>

                <div class="flex justify-end space-x-2 mt-6">
                    <button @click="showModal = false"
                        class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-100 text-gray-700 font-medium">
                        Cancel
                    </button>
                    <button @click="saveCategory()"
                        class="inline-flex items-center justify-center px-4 py-2 rounded-lg bg-blue-600 text-white font-medium transition-all duration-200 hover:bg-blue-700 hover:shadow-lg focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                        <span x-text="form.id ? 'Update' : 'Save'"></span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('alpine:init', () => {
            Alpine.data('categoryManager', () => {
                // Import the shared pagination functionality
                const pagination = window.createPagination({
                    fetchUrl: '/categories',
                    initialPage: <?php echo e(isset($categories->current_page) ? $categories->current_page : 1); ?>,
                    perPage: <?php echo e(isset($categories->per_page) ? $categories->per_page : 10); ?>,
                    autoInit: false, // We'll manually initialize
                    successMessage: '<?php echo e(session('success')); ?>'
                });
                
                // Extend with category-specific functionality
                return {
                    // Include all pagination functionality
                    ...pagination,
                    
                    // Category-specific state
                    showModal: false,
                    form: { 
                        id: null, 
                        name: '', 
                        slug: '' 
                    },
                    
                    // Initialize data
                    init() {
                        // Get categories data
                        const categoriesData = <?php echo json_encode($categories, 15, 512) ?>;
                        
                        // Check if the data is already paginated
                        if (categoriesData.data) {
                            // Already paginated
                            this.items = categoriesData.data;
                            this.totalPages = categoriesData.last_page || 1;
                            this.currentPage = categoriesData.current_page || 1;
                            this.totalRecords = categoriesData.total || 0;
                            this.perPage = categoriesData.per_page || 10;
                        } else {
                            // Not paginated, convert to paginated format
                            this.items = Array.isArray(categoriesData) ? categoriesData : [];
                            this.totalPages = 1;
                            this.currentPage = 1;
                            this.totalRecords = this.items.length;
                            this.perPage = this.items.length;
                        }
                        
                        // Call the parent init (setup URL params, success message etc.)
                        pagination.init.call(this);
                    },
                    
                    // Generate slug from name
                    generateSlug() {
                        this.form.slug = this.form.name.toLowerCase()
                            .replace(/\s+/g, '-')           // Replace spaces with hyphens
                            .replace(/[^\w\-]+/g, '')       // Remove all non-word characters
                            .replace(/\-\-+/g, '-')         // Replace multiple hyphens with single hyphen
                            .replace(/^-+/, '')             // Trim hyphens from start
                            .replace(/-+$/, '');            // Trim hyphens from end
                    },
                    
                    // Open the category form modal
                    openModal(category = null) {
                        if (category) {
                            // Make a copy to avoid directly modifying the items array
                            this.form = {
                                id: category.id,
                                name: category.name,
                                slug: category.slug
                            };
                        } else {
                            this.form = { 
                                id: null, 
                                name: '', 
                                slug: '' 
                            };
                        }
                        
                        this.showModal = true;
                    },
                    
                    // Save or update category
                    saveCategory() {
                        // Validate form
                        if (!this.form.name.trim()) {
                            if (window.notification) {
                                window.notification.error('Category name is required');
                            } else {
                                alert('Category name is required');
                            }
                            return;
                        }

                        // Show loading indicator
                        let loadingDialog = null;
                        if (window.notification) {
                            loadingDialog = window.notification.loading(this.form.id ? 'Updating category...' : 'Creating category...');
                        }
                        
                        let url = this.form.id ? `/categories/${this.form.id}` : '/categories';
                        let method = this.form.id ? 'PUT' : 'POST';

                        fetch(url, {
                            method: method,
                            headers: { 
                                'Content-Type': 'application/json', 
                                'Accept': 'application/json',
                                'X-Requested-With': 'XMLHttpRequest',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content 
                            },
                            credentials: 'same-origin',
                            body: JSON.stringify(this.form),
                        })
                        .then(res => {
                            if (!res.ok) {
                                throw new Error(`HTTP error! Status: ${res.status}`);
                            }
                            return res.json();
                        })
                        .then((data) => {
                            // Close loading indicator
                            if (loadingDialog && window.notification) {
                                window.notification.close();
                            }
                            
                            // Show success message
                            if (window.notification) {
                                window.notification.success(data.message || 'Category saved successfully.');
                            } else {
                                this.successMessage = data.message || 'Category saved successfully.';
                                setTimeout(() => {
                                    this.successMessage = '';
                                }, 3000);
                            }
                            
                            // Update local data
                            this.fetchData(); // Refresh the list
                            
                            // Close the modal
                            this.showModal = false;
                        })
                        .catch(error => {
                            console.error('Error saving category:', error);
                            
                            // Close loading indicator
                            if (loadingDialog && window.notification) {
                                window.notification.close();
                            }
                            
                            // Show error message
                            if (window.notification) {
                                window.notification.error('Failed to save category. Please try again.');
                            } else {
                                alert('Failed to save category. Please try again.');
                            }
                        });
                    },
                    
                    // Delete a category
                    deleteCategory(id) {
                        // Use notification system if available
                        if (window.notification) {
                            window.notification.confirmDelete('category')
                                .then((result) => {
                                    if (result.isConfirmed) {
                                        this.processDeleteCategory(id);
                                    }
                                });
                        } else {
                            // Fallback to confirm
                            if (confirm('Are you sure you want to delete this category?')) {
                                this.processDeleteCategory(id);
                            }
                        }
                    },
                    
                    // Process the category deletion
                    processDeleteCategory(id) {
                        // Show loading indicator
                        let loadingDialog = null;
                        if (window.notification) {
                            loadingDialog = window.notification.loading('Deleting category...');
                        }
                        
                        fetch(`/categories/${id}`, {
                            method: 'DELETE',
                            headers: { 
                                'Content-Type': 'application/json', 
                                'Accept': 'application/json',
                                'X-Requested-With': 'XMLHttpRequest',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content 
                            },
                            credentials: 'same-origin'
                        })
                        .then(res => {
                            if (!res.ok) {
                                throw new Error(`HTTP error! Status: ${res.status}`);
                            }
                            return res.json();
                        })
                        .then(() => {
                            // Close loading indicator
                            if (loadingDialog && window.notification) {
                                window.notification.close();
                            }
                            
                            // Show success message
                            if (window.notification) {
                                window.notification.success('Category has been deleted.');
                            } else {
                                this.successMessage = 'Category has been deleted.';
                                setTimeout(() => {
                                    this.successMessage = '';
                                }, 3000);
                            }
                            
                            // Refresh the list
                            this.fetchData();
                        })
                        .catch(error => {
                            console.error('Error deleting category:', error);
                            
                            // Close loading indicator
                            if (loadingDialog && window.notification) {
                                window.notification.close();
                            }
                            
                            // Show error message
                            if (window.notification) {
                                window.notification.error('Failed to delete category. Please try again.');
                            } else {
                                alert('Failed to delete category. Please try again.');
                            }
                        });
                    }
                };
            });
        });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $attributes = $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $component = $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?><?php /**PATH C:\laragon\www\carbnb\resources\views\backend\categories\index.blade.php ENDPATH**/ ?>
<x-app-layout>
<div class="container py-8">
    <div class="max-w-3xl mx-auto">
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="bg-green-50 px-6 py-8 border-b border-gray-200 text-center">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                </div>
                <h1 class="text-3xl font-bold text-gray-800 mb-2">Payment Successful!</h1>
                <p class="text-gray-600 text-lg">Your booking has been confirmed.</p>
            </div>

            <div class="p-6">
                <div class="mb-8">
                    <h2 class="text-xl font-semibold mb-4 text-gray-700">Booking Details</h2>

                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-4">
                            @if($booking->vehicle->primaryImage)
                                <img src="{{ asset('storage/' . $booking->vehicle->primaryImage->file->path) }}" alt="{{ $booking->vehicle->make }} {{ $booking->vehicle->model }}" class="w-20 h-20 object-cover rounded-md mr-4">
                            @else
                                <div class="w-20 h-20 bg-gray-200 rounded-md mr-4 flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                </div>
                            @endif
                            <div>
                                <h3 class="font-bold text-gray-800">{{ $booking->vehicle->make }} {{ $booking->vehicle->model }}</h3>
                                <p class="text-gray-600 text-sm">{{ $booking->vehicle->year }} · {{ $booking->vehicle->vehicleType->name }}</p>
                                <p class="text-gray-600 text-sm mt-1">Hosted by {{ $booking->vehicle->user->name }}</p>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <p class="text-gray-600 text-sm">Booking Number</p>
                                <p class="font-medium">{{ $booking->booking_number }}</p>
                            </div>

                            <div>
                                <p class="text-gray-600 text-sm">Status</p>
                                <p class="font-medium">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        Pending Approval
                                    </span>
                                </p>
                            </div>

                            <div>
                                <p class="text-gray-600 text-sm">Pickup Date</p>
                                <p class="font-medium">{{ $booking->start_date->format('M d, Y') }}</p>
                            </div>

                            <div>
                                <p class="text-gray-600 text-sm">Return Date</p>
                                <p class="font-medium">{{ $booking->end_date->format('M d, Y') }}</p>
                            </div>

                            <div>
                                <p class="text-gray-600 text-sm">Pickup Location</p>
                                <p class="font-medium">{{ $booking->pickup_location }}</p>
                            </div>

                            <div>
                                <p class="text-gray-600 text-sm">Return Location</p>
                                <p class="font-medium">{{ $booking->return_location }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-8">
                    <h2 class="text-xl font-semibold mb-4 text-gray-700">Payment Details</h2>

                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <p class="text-gray-600 text-sm">Payment Method</p>
                                <p class="font-medium">
                                    @if($booking->payments->isNotEmpty())
                                        {{ ucfirst($booking->payments->first()->payment_method) }}
                                    @else
                                        Not available
                                    @endif
                                </p>
                            </div>

                            <div>
                                <p class="text-gray-600 text-sm">Payment Status</p>
                                <p class="font-medium">
                                    @if($booking->payments->isNotEmpty())
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            {{ ucfirst($booking->payments->first()->status) }}
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                            Not available
                                        </span>
                                    @endif
                                </p>
                            </div>
                        </div>

                        <div class="border-t border-gray-200 pt-4">
                            <div class="flex justify-between mb-2">
                                <span class="text-gray-600">Vehicle:</span>
                                <span class="font-medium">${{ number_format($booking->vehicle_price, 2) }}</span>
                            </div>

                            @if($booking->driver_price > 0)
                            <div class="flex justify-between mb-2">
                                <span class="text-gray-600">Driver:</span>
                                <span class="font-medium">${{ number_format($booking->driver_price, 2) }}</span>
                            </div>
                            @endif

                            @if($booking->discount_amount > 0)
                            <div class="flex justify-between mb-2 text-green-600">
                                <span>Discount:</span>
                                <span>-${{ number_format($booking->discount_amount, 2) }}</span>
                            </div>
                            @endif

                            <div class="flex justify-between mb-2">
                                <span class="text-gray-600">Subtotal:</span>
                                <span class="font-medium">${{ number_format($booking->subtotal, 2) }}</span>
                            </div>

                            <div class="flex justify-between mb-2">
                                <span class="text-gray-600">Tax (10%):</span>
                                <span class="font-medium">${{ number_format($booking->tax_amount, 2) }}</span>
                            </div>

                            <div class="flex justify-between pt-2 border-t border-gray-200">
                                <span class="font-bold">Total:</span>
                                <span class="font-bold">${{ number_format($booking->total_amount, 2) }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-8">
                    <h2 class="text-xl font-semibold mb-4 text-gray-700">What's Next?</h2>

                    <div class="bg-blue-50 p-4 rounded-md">
                        <div class="flex">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                            </svg>
                            <div>
                                <p class="text-sm text-blue-700 mb-2">The vehicle owner will review your booking request and confirm it shortly. You'll receive an email notification once your booking is confirmed.</p>
                                <p class="text-sm text-blue-700">You can view your booking details and status anytime in your <a href="{{ route('booking.show', $booking->id) }}" class="font-medium underline">booking dashboard</a>.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex justify-between mt-8">
                    <a href="{{ route('booking.show', $booking->id) }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                        View Booking Details
                    </a>

                    <a href="{{ route('booking.index') }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                        Go to My Bookings
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
</x-app-layout>

import{g as U,s as Ie,e as se,c as _,a as ae,b as ce,d as be,n as te,f as Ee,m as B,h as Xe,i as Pe,j as ie,k as Ae,l as he,o as Oe,p as re,q as xe}from"./vendor-media-DYhAATBa.js";function We(L){let{swiper:e,extendParams:S,on:m,emit:b}=L;S({virtual:{enabled:!1,slides:[],cache:!0,renderSlide:null,renderExternal:null,renderExternalUpdate:!0,addSlidesBefore:0,addSlidesAfter:0}});let g;const v=U();e.virtual={cache:{},from:void 0,to:void 0,slides:[],offset:0,slidesGrid:[]};const w=v.createElement("div");function d(a,t){const r=e.params.virtual;if(r.cache&&e.virtual.cache[t])return e.virtual.cache[t];let c;return r.renderSlide?(c=r.renderSlide.call(e,a,t),typeof c=="string"&&(w.innerHTML=c,c=w.children[0])):e.isElement?c=_("swiper-slide"):c=_("div",e.params.slideClass),c.setAttribute("data-swiper-slide-index",t),r.renderSlide||(c.innerHTML=a),r.cache&&(e.virtual.cache[t]=c),c}function l(a,t,r){const{slidesPerView:c,slidesPerGroup:i,centeredSlides:s,loop:p,initialSlide:C}=e.params;if(t&&!p&&C>0)return;const{addSlidesBefore:X,addSlidesAfter:A}=e.params.virtual,{from:O,to:E,slides:I,slidesGrid:H,offset:P}=e.virtual;e.params.cssMode||e.updateActiveIndex();const M=typeof r>"u"?e.activeIndex||0:r;let x;e.rtlTranslate?x="right":x=e.isHorizontal()?"left":"top";let z,k;s?(z=Math.floor(c/2)+i+A,k=Math.floor(c/2)+i+X):(z=c+(i-1)+A,k=(p?c:i)+X);let h=M-k,y=M+z;p||(h=Math.max(h,0),y=Math.min(y,I.length-1));let Y=(e.slidesGrid[h]||0)-(e.slidesGrid[0]||0);p&&M>=k?(h-=k,s||(Y+=e.slidesGrid[0])):p&&M<k&&(h=-k,s&&(Y+=e.slidesGrid[0])),Object.assign(e.virtual,{from:h,to:y,offset:Y,slidesGrid:e.slidesGrid,slidesBefore:k,slidesAfter:z});function R(){e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),b("virtualUpdate")}if(O===h&&E===y&&!a){e.slidesGrid!==H&&Y!==P&&e.slides.forEach(T=>{T.style[x]=`${Y-Math.abs(e.cssOverflowAdjustment())}px`}),e.updateProgress(),b("virtualUpdate");return}if(e.params.virtual.renderExternal){e.params.virtual.renderExternal.call(e,{offset:Y,from:h,to:y,slides:function(){const D=[];for(let q=h;q<=y;q+=1)D.push(I[q]);return D}()}),e.params.virtual.renderExternalUpdate?R():b("virtualUpdate");return}const W=[],N=[],V=T=>{let D=T;return T<0?D=I.length+T:D>=I.length&&(D=D-I.length),D};if(a)e.slides.filter(T=>T.matches(`.${e.params.slideClass}, swiper-slide`)).forEach(T=>{T.remove()});else for(let T=O;T<=E;T+=1)if(T<h||T>y){const D=V(T);e.slides.filter(q=>q.matches(`.${e.params.slideClass}[data-swiper-slide-index="${D}"], swiper-slide[data-swiper-slide-index="${D}"]`)).forEach(q=>{q.remove()})}const fe=p?-I.length:0,$=p?I.length*2:I.length;for(let T=fe;T<$;T+=1)if(T>=h&&T<=y){const D=V(T);typeof E>"u"||a?N.push(D):(T>E&&N.push(D),T<O&&W.push(D))}if(N.forEach(T=>{e.slidesEl.append(d(I[T],T))}),p)for(let T=W.length-1;T>=0;T-=1){const D=W[T];e.slidesEl.prepend(d(I[D],D))}else W.sort((T,D)=>D-T),W.forEach(T=>{e.slidesEl.prepend(d(I[T],T))});se(e.slidesEl,".swiper-slide, swiper-slide").forEach(T=>{T.style[x]=`${Y-Math.abs(e.cssOverflowAdjustment())}px`}),R()}function n(a){if(typeof a=="object"&&"length"in a)for(let t=0;t<a.length;t+=1)a[t]&&e.virtual.slides.push(a[t]);else e.virtual.slides.push(a);l(!0)}function f(a){const t=e.activeIndex;let r=t+1,c=1;if(Array.isArray(a)){for(let i=0;i<a.length;i+=1)a[i]&&e.virtual.slides.unshift(a[i]);r=t+a.length,c=a.length}else e.virtual.slides.unshift(a);if(e.params.virtual.cache){const i=e.virtual.cache,s={};Object.keys(i).forEach(p=>{const C=i[p],X=C.getAttribute("data-swiper-slide-index");X&&C.setAttribute("data-swiper-slide-index",parseInt(X,10)+c),s[parseInt(p,10)+c]=C}),e.virtual.cache=s}l(!0),e.slideTo(r,0)}function u(a){if(typeof a>"u"||a===null)return;let t=e.activeIndex;if(Array.isArray(a))for(let r=a.length-1;r>=0;r-=1)e.params.virtual.cache&&(delete e.virtual.cache[a[r]],Object.keys(e.virtual.cache).forEach(c=>{c>a&&(e.virtual.cache[c-1]=e.virtual.cache[c],e.virtual.cache[c-1].setAttribute("data-swiper-slide-index",c-1),delete e.virtual.cache[c])})),e.virtual.slides.splice(a[r],1),a[r]<t&&(t-=1),t=Math.max(t,0);else e.params.virtual.cache&&(delete e.virtual.cache[a],Object.keys(e.virtual.cache).forEach(r=>{r>a&&(e.virtual.cache[r-1]=e.virtual.cache[r],e.virtual.cache[r-1].setAttribute("data-swiper-slide-index",r-1),delete e.virtual.cache[r])})),e.virtual.slides.splice(a,1),a<t&&(t-=1),t=Math.max(t,0);l(!0),e.slideTo(t,0)}function o(){e.virtual.slides=[],e.params.virtual.cache&&(e.virtual.cache={}),l(!0),e.slideTo(0,0)}m("beforeInit",()=>{if(!e.params.virtual.enabled)return;let a;if(typeof e.passedParams.virtual.slides>"u"){const t=[...e.slidesEl.children].filter(r=>r.matches(`.${e.params.slideClass}, swiper-slide`));t&&t.length&&(e.virtual.slides=[...t],a=!0,t.forEach((r,c)=>{r.setAttribute("data-swiper-slide-index",c),e.virtual.cache[c]=r,r.remove()}))}a||(e.virtual.slides=e.params.virtual.slides),e.classNames.push(`${e.params.containerModifierClass}virtual`),e.params.watchSlidesProgress=!0,e.originalParams.watchSlidesProgress=!0,l(!1,!0)}),m("setTranslate",()=>{e.params.virtual.enabled&&(e.params.cssMode&&!e._immediateVirtual?(clearTimeout(g),g=setTimeout(()=>{l()},100)):l())}),m("init update resize",()=>{e.params.virtual.enabled&&e.params.cssMode&&Ie(e.wrapperEl,"--swiper-virtual-size",`${e.virtualSize}px`)}),Object.assign(e.virtual,{appendSlide:n,prependSlide:f,removeSlide:u,removeAllSlides:o,update:l})}function Ne(L){let{swiper:e,extendParams:S,on:m,emit:b}=L;const g=U(),v=ae();e.keyboard={enabled:!1},S({keyboard:{enabled:!1,onlyInViewport:!0,pageUpDown:!0}});function w(n){if(!e.enabled)return;const{rtlTranslate:f}=e;let u=n;u.originalEvent&&(u=u.originalEvent);const o=u.keyCode||u.charCode,a=e.params.keyboard.pageUpDown,t=a&&o===33,r=a&&o===34,c=o===37,i=o===39,s=o===38,p=o===40;if(!e.allowSlideNext&&(e.isHorizontal()&&i||e.isVertical()&&p||r)||!e.allowSlidePrev&&(e.isHorizontal()&&c||e.isVertical()&&s||t))return!1;if(!(u.shiftKey||u.altKey||u.ctrlKey||u.metaKey)&&!(g.activeElement&&g.activeElement.nodeName&&(g.activeElement.nodeName.toLowerCase()==="input"||g.activeElement.nodeName.toLowerCase()==="textarea"))){if(e.params.keyboard.onlyInViewport&&(t||r||c||i||s||p)){let C=!1;if(ce(e.el,`.${e.params.slideClass}, swiper-slide`).length>0&&ce(e.el,`.${e.params.slideActiveClass}`).length===0)return;const X=e.el,A=X.clientWidth,O=X.clientHeight,E=v.innerWidth,I=v.innerHeight,H=be(X);f&&(H.left-=X.scrollLeft);const P=[[H.left,H.top],[H.left+A,H.top],[H.left,H.top+O],[H.left+A,H.top+O]];for(let M=0;M<P.length;M+=1){const x=P[M];if(x[0]>=0&&x[0]<=E&&x[1]>=0&&x[1]<=I){if(x[0]===0&&x[1]===0)continue;C=!0}}if(!C)return}e.isHorizontal()?((t||r||c||i)&&(u.preventDefault?u.preventDefault():u.returnValue=!1),((r||i)&&!f||(t||c)&&f)&&e.slideNext(),((t||c)&&!f||(r||i)&&f)&&e.slidePrev()):((t||r||s||p)&&(u.preventDefault?u.preventDefault():u.returnValue=!1),(r||p)&&e.slideNext(),(t||s)&&e.slidePrev()),b("keyPress",o)}}function d(){e.keyboard.enabled||(g.addEventListener("keydown",w),e.keyboard.enabled=!0)}function l(){e.keyboard.enabled&&(g.removeEventListener("keydown",w),e.keyboard.enabled=!1)}m("init",()=>{e.params.keyboard.enabled&&d()}),m("destroy",()=>{e.keyboard.enabled&&l()}),Object.assign(e.keyboard,{enable:d,disable:l})}function Fe(L){let{swiper:e,extendParams:S,on:m,emit:b}=L;const g=ae();S({mousewheel:{enabled:!1,releaseOnEdges:!1,invert:!1,forceToAxis:!1,sensitivity:1,eventsTarget:"container",thresholdDelta:null,thresholdTime:null,noMousewheelClass:"swiper-no-mousewheel"}}),e.mousewheel={enabled:!1};let v,w=te(),d;const l=[];function n(s){let A=0,O=0,E=0,I=0;return"detail"in s&&(O=s.detail),"wheelDelta"in s&&(O=-s.wheelDelta/120),"wheelDeltaY"in s&&(O=-s.wheelDeltaY/120),"wheelDeltaX"in s&&(A=-s.wheelDeltaX/120),"axis"in s&&s.axis===s.HORIZONTAL_AXIS&&(A=O,O=0),E=A*10,I=O*10,"deltaY"in s&&(I=s.deltaY),"deltaX"in s&&(E=s.deltaX),s.shiftKey&&!E&&(E=I,I=0),(E||I)&&s.deltaMode&&(s.deltaMode===1?(E*=40,I*=40):(E*=800,I*=800)),E&&!A&&(A=E<1?-1:1),I&&!O&&(O=I<1?-1:1),{spinX:A,spinY:O,pixelX:E,pixelY:I}}function f(){e.enabled&&(e.mouseEntered=!0)}function u(){e.enabled&&(e.mouseEntered=!1)}function o(s){return e.params.mousewheel.thresholdDelta&&s.delta<e.params.mousewheel.thresholdDelta||e.params.mousewheel.thresholdTime&&te()-w<e.params.mousewheel.thresholdTime?!1:s.delta>=6&&te()-w<60?!0:(s.direction<0?(!e.isEnd||e.params.loop)&&!e.animating&&(e.slideNext(),b("scroll",s.raw)):(!e.isBeginning||e.params.loop)&&!e.animating&&(e.slidePrev(),b("scroll",s.raw)),w=new g.Date().getTime(),!1)}function a(s){const p=e.params.mousewheel;if(s.direction<0){if(e.isEnd&&!e.params.loop&&p.releaseOnEdges)return!0}else if(e.isBeginning&&!e.params.loop&&p.releaseOnEdges)return!0;return!1}function t(s){let p=s,C=!0;if(!e.enabled||s.target.closest(`.${e.params.mousewheel.noMousewheelClass}`))return;const X=e.params.mousewheel;e.params.cssMode&&p.preventDefault();let A=e.el;e.params.mousewheel.eventsTarget!=="container"&&(A=document.querySelector(e.params.mousewheel.eventsTarget));const O=A&&A.contains(p.target);if(!e.mouseEntered&&!O&&!X.releaseOnEdges)return!0;p.originalEvent&&(p=p.originalEvent);let E=0;const I=e.rtlTranslate?-1:1,H=n(p);if(X.forceToAxis)if(e.isHorizontal())if(Math.abs(H.pixelX)>Math.abs(H.pixelY))E=-H.pixelX*I;else return!0;else if(Math.abs(H.pixelY)>Math.abs(H.pixelX))E=-H.pixelY;else return!0;else E=Math.abs(H.pixelX)>Math.abs(H.pixelY)?-H.pixelX*I:-H.pixelY;if(E===0)return!0;X.invert&&(E=-E);let P=e.getTranslate()+E*X.sensitivity;if(P>=e.minTranslate()&&(P=e.minTranslate()),P<=e.maxTranslate()&&(P=e.maxTranslate()),C=e.params.loop?!0:!(P===e.minTranslate()||P===e.maxTranslate()),C&&e.params.nested&&p.stopPropagation(),!e.params.freeMode||!e.params.freeMode.enabled){const M={time:te(),delta:Math.abs(E),direction:Math.sign(E),raw:s};l.length>=2&&l.shift();const x=l.length?l[l.length-1]:void 0;if(l.push(M),x?(M.direction!==x.direction||M.delta>x.delta||M.time>x.time+150)&&o(M):o(M),a(M))return!0}else{const M={time:te(),delta:Math.abs(E),direction:Math.sign(E)},x=d&&M.time<d.time+500&&M.delta<=d.delta&&M.direction===d.direction;if(!x){d=void 0;let z=e.getTranslate()+E*X.sensitivity;const k=e.isBeginning,h=e.isEnd;if(z>=e.minTranslate()&&(z=e.minTranslate()),z<=e.maxTranslate()&&(z=e.maxTranslate()),e.setTransition(0),e.setTranslate(z),e.updateProgress(),e.updateActiveIndex(),e.updateSlidesClasses(),(!k&&e.isBeginning||!h&&e.isEnd)&&e.updateSlidesClasses(),e.params.loop&&e.loopFix({direction:M.direction<0?"next":"prev",byMousewheel:!0}),e.params.freeMode.sticky){clearTimeout(v),v=void 0,l.length>=15&&l.shift();const y=l.length?l[l.length-1]:void 0,Y=l[0];if(l.push(M),y&&(M.delta>y.delta||M.direction!==y.direction))l.splice(0);else if(l.length>=15&&M.time-Y.time<500&&Y.delta-M.delta>=1&&M.delta<=6){const R=E>0?.8:.2;d=M,l.splice(0),v=Ee(()=>{e.destroyed||!e.params||e.slideToClosest(e.params.speed,!0,void 0,R)},0)}v||(v=Ee(()=>{if(e.destroyed||!e.params)return;const R=.5;d=M,l.splice(0),e.slideToClosest(e.params.speed,!0,void 0,R)},500))}if(x||b("scroll",p),e.params.autoplay&&e.params.autoplay.disableOnInteraction&&e.autoplay.stop(),X.releaseOnEdges&&(z===e.minTranslate()||z===e.maxTranslate()))return!0}}return p.preventDefault?p.preventDefault():p.returnValue=!1,!1}function r(s){let p=e.el;e.params.mousewheel.eventsTarget!=="container"&&(p=document.querySelector(e.params.mousewheel.eventsTarget)),p[s]("mouseenter",f),p[s]("mouseleave",u),p[s]("wheel",t)}function c(){return e.params.cssMode?(e.wrapperEl.removeEventListener("wheel",t),!0):e.mousewheel.enabled?!1:(r("addEventListener"),e.mousewheel.enabled=!0,!0)}function i(){return e.params.cssMode?(e.wrapperEl.addEventListener(event,t),!0):e.mousewheel.enabled?(r("removeEventListener"),e.mousewheel.enabled=!1,!0):!1}m("init",()=>{!e.params.mousewheel.enabled&&e.params.cssMode&&i(),e.params.mousewheel.enabled&&c()}),m("destroy",()=>{e.params.cssMode&&c(),e.mousewheel.enabled&&i()}),Object.assign(e.mousewheel,{enable:c,disable:i})}function ze(L,e,S,m){return L.params.createElements&&Object.keys(m).forEach(b=>{if(!S[b]&&S.auto===!0){let g=se(L.el,`.${m[b]}`)[0];g||(g=_("div",m[b]),g.className=m[b],L.el.append(g)),S[b]=g,e[b]=g}}),S}function Ge(L){let{swiper:e,extendParams:S,on:m,emit:b}=L;S({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),e.navigation={nextEl:null,prevEl:null};function g(a){let t;return a&&typeof a=="string"&&e.isElement&&(t=e.el.querySelector(a)||e.hostEl.querySelector(a),t)?t:(a&&(typeof a=="string"&&(t=[...document.querySelectorAll(a)]),e.params.uniqueNavElements&&typeof a=="string"&&t&&t.length>1&&e.el.querySelectorAll(a).length===1?t=e.el.querySelector(a):t&&t.length===1&&(t=t[0])),a&&!t?a:t)}function v(a,t){const r=e.params.navigation;a=B(a),a.forEach(c=>{c&&(c.classList[t?"add":"remove"](...r.disabledClass.split(" ")),c.tagName==="BUTTON"&&(c.disabled=t),e.params.watchOverflow&&e.enabled&&c.classList[e.isLocked?"add":"remove"](r.lockClass))})}function w(){const{nextEl:a,prevEl:t}=e.navigation;if(e.params.loop){v(t,!1),v(a,!1);return}v(t,e.isBeginning&&!e.params.rewind),v(a,e.isEnd&&!e.params.rewind)}function d(a){a.preventDefault(),!(e.isBeginning&&!e.params.loop&&!e.params.rewind)&&(e.slidePrev(),b("navigationPrev"))}function l(a){a.preventDefault(),!(e.isEnd&&!e.params.loop&&!e.params.rewind)&&(e.slideNext(),b("navigationNext"))}function n(){const a=e.params.navigation;if(e.params.navigation=ze(e,e.originalParams.navigation,e.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),!(a.nextEl||a.prevEl))return;let t=g(a.nextEl),r=g(a.prevEl);Object.assign(e.navigation,{nextEl:t,prevEl:r}),t=B(t),r=B(r);const c=(i,s)=>{i&&i.addEventListener("click",s==="next"?l:d),!e.enabled&&i&&i.classList.add(...a.lockClass.split(" "))};t.forEach(i=>c(i,"next")),r.forEach(i=>c(i,"prev"))}function f(){let{nextEl:a,prevEl:t}=e.navigation;a=B(a),t=B(t);const r=(c,i)=>{c.removeEventListener("click",i==="next"?l:d),c.classList.remove(...e.params.navigation.disabledClass.split(" "))};a.forEach(c=>r(c,"next")),t.forEach(c=>r(c,"prev"))}m("init",()=>{e.params.navigation.enabled===!1?o():(n(),w())}),m("toEdge fromEdge lock unlock",()=>{w()}),m("destroy",()=>{f()}),m("enable disable",()=>{let{nextEl:a,prevEl:t}=e.navigation;if(a=B(a),t=B(t),e.enabled){w();return}[...a,...t].filter(r=>!!r).forEach(r=>r.classList.add(e.params.navigation.lockClass))}),m("click",(a,t)=>{let{nextEl:r,prevEl:c}=e.navigation;r=B(r),c=B(c);const i=t.target;let s=c.includes(i)||r.includes(i);if(e.isElement&&!s){const p=t.path||t.composedPath&&t.composedPath();p&&(s=p.find(C=>r.includes(C)||c.includes(C)))}if(e.params.navigation.hideOnClick&&!s){if(e.pagination&&e.params.pagination&&e.params.pagination.clickable&&(e.pagination.el===i||e.pagination.el.contains(i)))return;let p;r.length?p=r[0].classList.contains(e.params.navigation.hiddenClass):c.length&&(p=c[0].classList.contains(e.params.navigation.hiddenClass)),b(p===!0?"navigationShow":"navigationHide"),[...r,...c].filter(C=>!!C).forEach(C=>C.classList.toggle(e.params.navigation.hiddenClass))}});const u=()=>{e.el.classList.remove(...e.params.navigation.navigationDisabledClass.split(" ")),n(),w()},o=()=>{e.el.classList.add(...e.params.navigation.navigationDisabledClass.split(" ")),f()};Object.assign(e.navigation,{enable:u,disable:o,update:w,init:n,destroy:f})}function K(L){return L===void 0&&(L=""),`.${L.trim().replace(/([\.:!+\/])/g,"\\$1").replace(/ /g,".")}`}function je(L){let{swiper:e,extendParams:S,on:m,emit:b}=L;const g="swiper-pagination";S({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:i=>i,formatFractionTotal:i=>i,bulletClass:`${g}-bullet`,bulletActiveClass:`${g}-bullet-active`,modifierClass:`${g}-`,currentClass:`${g}-current`,totalClass:`${g}-total`,hiddenClass:`${g}-hidden`,progressbarFillClass:`${g}-progressbar-fill`,progressbarOppositeClass:`${g}-progressbar-opposite`,clickableClass:`${g}-clickable`,lockClass:`${g}-lock`,horizontalClass:`${g}-horizontal`,verticalClass:`${g}-vertical`,paginationDisabledClass:`${g}-disabled`}}),e.pagination={el:null,bullets:[]};let v,w=0;function d(){return!e.params.pagination.el||!e.pagination.el||Array.isArray(e.pagination.el)&&e.pagination.el.length===0}function l(i,s){const{bulletActiveClass:p}=e.params.pagination;i&&(i=i[`${s==="prev"?"previous":"next"}ElementSibling`],i&&(i.classList.add(`${p}-${s}`),i=i[`${s==="prev"?"previous":"next"}ElementSibling`],i&&i.classList.add(`${p}-${s}-${s}`)))}function n(i,s,p){if(i=i%p,s=s%p,s===i+1)return"next";if(s===i-1)return"previous"}function f(i){const s=i.target.closest(K(e.params.pagination.bulletClass));if(!s)return;i.preventDefault();const p=Pe(s)*e.params.slidesPerGroup;if(e.params.loop){if(e.realIndex===p)return;const C=n(e.realIndex,p,e.slides.length);C==="next"?e.slideNext():C==="previous"?e.slidePrev():e.slideToLoop(p)}else e.slideTo(p)}function u(){const i=e.rtl,s=e.params.pagination;if(d())return;let p=e.pagination.el;p=B(p);let C,X;const A=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:e.slides.length,O=e.params.loop?Math.ceil(A/e.params.slidesPerGroup):e.snapGrid.length;if(e.params.loop?(X=e.previousRealIndex||0,C=e.params.slidesPerGroup>1?Math.floor(e.realIndex/e.params.slidesPerGroup):e.realIndex):typeof e.snapIndex<"u"?(C=e.snapIndex,X=e.previousSnapIndex):(X=e.previousIndex||0,C=e.activeIndex||0),s.type==="bullets"&&e.pagination.bullets&&e.pagination.bullets.length>0){const E=e.pagination.bullets;let I,H,P;if(s.dynamicBullets&&(v=Xe(E[0],e.isHorizontal()?"width":"height"),p.forEach(M=>{M.style[e.isHorizontal()?"width":"height"]=`${v*(s.dynamicMainBullets+4)}px`}),s.dynamicMainBullets>1&&X!==void 0&&(w+=C-(X||0),w>s.dynamicMainBullets-1?w=s.dynamicMainBullets-1:w<0&&(w=0)),I=Math.max(C-w,0),H=I+(Math.min(E.length,s.dynamicMainBullets)-1),P=(H+I)/2),E.forEach(M=>{const x=[...["","-next","-next-next","-prev","-prev-prev","-main"].map(z=>`${s.bulletActiveClass}${z}`)].map(z=>typeof z=="string"&&z.includes(" ")?z.split(" "):z).flat();M.classList.remove(...x)}),p.length>1)E.forEach(M=>{const x=Pe(M);x===C?M.classList.add(...s.bulletActiveClass.split(" ")):e.isElement&&M.setAttribute("part","bullet"),s.dynamicBullets&&(x>=I&&x<=H&&M.classList.add(...`${s.bulletActiveClass}-main`.split(" ")),x===I&&l(M,"prev"),x===H&&l(M,"next"))});else{const M=E[C];if(M&&M.classList.add(...s.bulletActiveClass.split(" ")),e.isElement&&E.forEach((x,z)=>{x.setAttribute("part",z===C?"bullet-active":"bullet")}),s.dynamicBullets){const x=E[I],z=E[H];for(let k=I;k<=H;k+=1)E[k]&&E[k].classList.add(...`${s.bulletActiveClass}-main`.split(" "));l(x,"prev"),l(z,"next")}}if(s.dynamicBullets){const M=Math.min(E.length,s.dynamicMainBullets+4),x=(v*M-v)/2-P*v,z=i?"right":"left";E.forEach(k=>{k.style[e.isHorizontal()?z:"top"]=`${x}px`})}}p.forEach((E,I)=>{if(s.type==="fraction"&&(E.querySelectorAll(K(s.currentClass)).forEach(H=>{H.textContent=s.formatFractionCurrent(C+1)}),E.querySelectorAll(K(s.totalClass)).forEach(H=>{H.textContent=s.formatFractionTotal(O)})),s.type==="progressbar"){let H;s.progressbarOpposite?H=e.isHorizontal()?"vertical":"horizontal":H=e.isHorizontal()?"horizontal":"vertical";const P=(C+1)/O;let M=1,x=1;H==="horizontal"?M=P:x=P,E.querySelectorAll(K(s.progressbarFillClass)).forEach(z=>{z.style.transform=`translate3d(0,0,0) scaleX(${M}) scaleY(${x})`,z.style.transitionDuration=`${e.params.speed}ms`})}s.type==="custom"&&s.renderCustom?(E.innerHTML=s.renderCustom(e,C+1,O),I===0&&b("paginationRender",E)):(I===0&&b("paginationRender",E),b("paginationUpdate",E)),e.params.watchOverflow&&e.enabled&&E.classList[e.isLocked?"add":"remove"](s.lockClass)})}function o(){const i=e.params.pagination;if(d())return;const s=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:e.grid&&e.params.grid.rows>1?e.slides.length/Math.ceil(e.params.grid.rows):e.slides.length;let p=e.pagination.el;p=B(p);let C="";if(i.type==="bullets"){let X=e.params.loop?Math.ceil(s/e.params.slidesPerGroup):e.snapGrid.length;e.params.freeMode&&e.params.freeMode.enabled&&X>s&&(X=s);for(let A=0;A<X;A+=1)i.renderBullet?C+=i.renderBullet.call(e,A,i.bulletClass):C+=`<${i.bulletElement} ${e.isElement?'part="bullet"':""} class="${i.bulletClass}"></${i.bulletElement}>`}i.type==="fraction"&&(i.renderFraction?C=i.renderFraction.call(e,i.currentClass,i.totalClass):C=`<span class="${i.currentClass}"></span> / <span class="${i.totalClass}"></span>`),i.type==="progressbar"&&(i.renderProgressbar?C=i.renderProgressbar.call(e,i.progressbarFillClass):C=`<span class="${i.progressbarFillClass}"></span>`),e.pagination.bullets=[],p.forEach(X=>{i.type!=="custom"&&(X.innerHTML=C||""),i.type==="bullets"&&e.pagination.bullets.push(...X.querySelectorAll(K(i.bulletClass)))}),i.type!=="custom"&&b("paginationRender",p[0])}function a(){e.params.pagination=ze(e,e.originalParams.pagination,e.params.pagination,{el:"swiper-pagination"});const i=e.params.pagination;if(!i.el)return;let s;typeof i.el=="string"&&e.isElement&&(s=e.el.querySelector(i.el)),!s&&typeof i.el=="string"&&(s=[...document.querySelectorAll(i.el)]),s||(s=i.el),!(!s||s.length===0)&&(e.params.uniqueNavElements&&typeof i.el=="string"&&Array.isArray(s)&&s.length>1&&(s=[...e.el.querySelectorAll(i.el)],s.length>1&&(s=s.find(p=>ce(p,".swiper")[0]===e.el))),Array.isArray(s)&&s.length===1&&(s=s[0]),Object.assign(e.pagination,{el:s}),s=B(s),s.forEach(p=>{i.type==="bullets"&&i.clickable&&p.classList.add(...(i.clickableClass||"").split(" ")),p.classList.add(i.modifierClass+i.type),p.classList.add(e.isHorizontal()?i.horizontalClass:i.verticalClass),i.type==="bullets"&&i.dynamicBullets&&(p.classList.add(`${i.modifierClass}${i.type}-dynamic`),w=0,i.dynamicMainBullets<1&&(i.dynamicMainBullets=1)),i.type==="progressbar"&&i.progressbarOpposite&&p.classList.add(i.progressbarOppositeClass),i.clickable&&p.addEventListener("click",f),e.enabled||p.classList.add(i.lockClass)}))}function t(){const i=e.params.pagination;if(d())return;let s=e.pagination.el;s&&(s=B(s),s.forEach(p=>{p.classList.remove(i.hiddenClass),p.classList.remove(i.modifierClass+i.type),p.classList.remove(e.isHorizontal()?i.horizontalClass:i.verticalClass),i.clickable&&(p.classList.remove(...(i.clickableClass||"").split(" ")),p.removeEventListener("click",f))})),e.pagination.bullets&&e.pagination.bullets.forEach(p=>p.classList.remove(...i.bulletActiveClass.split(" ")))}m("changeDirection",()=>{if(!e.pagination||!e.pagination.el)return;const i=e.params.pagination;let{el:s}=e.pagination;s=B(s),s.forEach(p=>{p.classList.remove(i.horizontalClass,i.verticalClass),p.classList.add(e.isHorizontal()?i.horizontalClass:i.verticalClass)})}),m("init",()=>{e.params.pagination.enabled===!1?c():(a(),o(),u())}),m("activeIndexChange",()=>{typeof e.snapIndex>"u"&&u()}),m("snapIndexChange",()=>{u()}),m("snapGridLengthChange",()=>{o(),u()}),m("destroy",()=>{t()}),m("enable disable",()=>{let{el:i}=e.pagination;i&&(i=B(i),i.forEach(s=>s.classList[e.enabled?"remove":"add"](e.params.pagination.lockClass)))}),m("lock unlock",()=>{u()}),m("click",(i,s)=>{const p=s.target,C=B(e.pagination.el);if(e.params.pagination.el&&e.params.pagination.hideOnClick&&C&&C.length>0&&!p.classList.contains(e.params.pagination.bulletClass)){if(e.navigation&&(e.navigation.nextEl&&p===e.navigation.nextEl||e.navigation.prevEl&&p===e.navigation.prevEl))return;const X=C[0].classList.contains(e.params.pagination.hiddenClass);b(X===!0?"paginationShow":"paginationHide"),C.forEach(A=>A.classList.toggle(e.params.pagination.hiddenClass))}});const r=()=>{e.el.classList.remove(e.params.pagination.paginationDisabledClass);let{el:i}=e.pagination;i&&(i=B(i),i.forEach(s=>s.classList.remove(e.params.pagination.paginationDisabledClass))),a(),o(),u()},c=()=>{e.el.classList.add(e.params.pagination.paginationDisabledClass);let{el:i}=e.pagination;i&&(i=B(i),i.forEach(s=>s.classList.add(e.params.pagination.paginationDisabledClass))),t()};Object.assign(e.pagination,{enable:r,disable:c,render:o,update:u,init:a,destroy:t})}function Ve(L){let{swiper:e,extendParams:S,on:m,emit:b}=L;const g=U();let v=!1,w=null,d=null,l,n,f,u;S({scrollbar:{el:null,dragSize:"auto",hide:!1,draggable:!1,snapOnRelease:!0,lockClass:"swiper-scrollbar-lock",dragClass:"swiper-scrollbar-drag",scrollbarDisabledClass:"swiper-scrollbar-disabled",horizontalClass:"swiper-scrollbar-horizontal",verticalClass:"swiper-scrollbar-vertical"}}),e.scrollbar={el:null,dragEl:null};function o(){if(!e.params.scrollbar.el||!e.scrollbar.el)return;const{scrollbar:P,rtlTranslate:M}=e,{dragEl:x,el:z}=P,k=e.params.scrollbar,h=e.params.loop?e.progressLoop:e.progress;let y=n,Y=(f-n)*h;M?(Y=-Y,Y>0?(y=n-Y,Y=0):-Y+n>f&&(y=f+Y)):Y<0?(y=n+Y,Y=0):Y+n>f&&(y=f-Y),e.isHorizontal()?(x.style.transform=`translate3d(${Y}px, 0, 0)`,x.style.width=`${y}px`):(x.style.transform=`translate3d(0px, ${Y}px, 0)`,x.style.height=`${y}px`),k.hide&&(clearTimeout(w),z.style.opacity=1,w=setTimeout(()=>{z.style.opacity=0,z.style.transitionDuration="400ms"},1e3))}function a(P){!e.params.scrollbar.el||!e.scrollbar.el||(e.scrollbar.dragEl.style.transitionDuration=`${P}ms`)}function t(){if(!e.params.scrollbar.el||!e.scrollbar.el)return;const{scrollbar:P}=e,{dragEl:M,el:x}=P;M.style.width="",M.style.height="",f=e.isHorizontal()?x.offsetWidth:x.offsetHeight,u=e.size/(e.virtualSize+e.params.slidesOffsetBefore-(e.params.centeredSlides?e.snapGrid[0]:0)),e.params.scrollbar.dragSize==="auto"?n=f*u:n=parseInt(e.params.scrollbar.dragSize,10),e.isHorizontal()?M.style.width=`${n}px`:M.style.height=`${n}px`,u>=1?x.style.display="none":x.style.display="",e.params.scrollbar.hide&&(x.style.opacity=0),e.params.watchOverflow&&e.enabled&&P.el.classList[e.isLocked?"add":"remove"](e.params.scrollbar.lockClass)}function r(P){return e.isHorizontal()?P.clientX:P.clientY}function c(P){const{scrollbar:M,rtlTranslate:x}=e,{el:z}=M;let k;k=(r(P)-be(z)[e.isHorizontal()?"left":"top"]-(l!==null?l:n/2))/(f-n),k=Math.max(Math.min(k,1),0),x&&(k=1-k);const h=e.minTranslate()+(e.maxTranslate()-e.minTranslate())*k;e.updateProgress(h),e.setTranslate(h),e.updateActiveIndex(),e.updateSlidesClasses()}function i(P){const M=e.params.scrollbar,{scrollbar:x,wrapperEl:z}=e,{el:k,dragEl:h}=x;v=!0,l=P.target===h?r(P)-P.target.getBoundingClientRect()[e.isHorizontal()?"left":"top"]:null,P.preventDefault(),P.stopPropagation(),z.style.transitionDuration="100ms",h.style.transitionDuration="100ms",c(P),clearTimeout(d),k.style.transitionDuration="0ms",M.hide&&(k.style.opacity=1),e.params.cssMode&&(e.wrapperEl.style["scroll-snap-type"]="none"),b("scrollbarDragStart",P)}function s(P){const{scrollbar:M,wrapperEl:x}=e,{el:z,dragEl:k}=M;v&&(P.preventDefault&&P.cancelable?P.preventDefault():P.returnValue=!1,c(P),x.style.transitionDuration="0ms",z.style.transitionDuration="0ms",k.style.transitionDuration="0ms",b("scrollbarDragMove",P))}function p(P){const M=e.params.scrollbar,{scrollbar:x,wrapperEl:z}=e,{el:k}=x;v&&(v=!1,e.params.cssMode&&(e.wrapperEl.style["scroll-snap-type"]="",z.style.transitionDuration=""),M.hide&&(clearTimeout(d),d=Ee(()=>{k.style.opacity=0,k.style.transitionDuration="400ms"},1e3)),b("scrollbarDragEnd",P),M.snapOnRelease&&e.slideToClosest())}function C(P){const{scrollbar:M,params:x}=e,z=M.el;if(!z)return;const k=z,h=x.passiveListeners?{passive:!1,capture:!1}:!1,y=x.passiveListeners?{passive:!0,capture:!1}:!1;if(!k)return;const Y=P==="on"?"addEventListener":"removeEventListener";k[Y]("pointerdown",i,h),g[Y]("pointermove",s,h),g[Y]("pointerup",p,y)}function X(){!e.params.scrollbar.el||!e.scrollbar.el||C("on")}function A(){!e.params.scrollbar.el||!e.scrollbar.el||C("off")}function O(){const{scrollbar:P,el:M}=e;e.params.scrollbar=ze(e,e.originalParams.scrollbar,e.params.scrollbar,{el:"swiper-scrollbar"});const x=e.params.scrollbar;if(!x.el)return;let z;if(typeof x.el=="string"&&e.isElement&&(z=e.el.querySelector(x.el)),!z&&typeof x.el=="string"){if(z=g.querySelectorAll(x.el),!z.length)return}else z||(z=x.el);e.params.uniqueNavElements&&typeof x.el=="string"&&z.length>1&&M.querySelectorAll(x.el).length===1&&(z=M.querySelector(x.el)),z.length>0&&(z=z[0]),z.classList.add(e.isHorizontal()?x.horizontalClass:x.verticalClass);let k;z&&(k=z.querySelector(K(e.params.scrollbar.dragClass)),k||(k=_("div",e.params.scrollbar.dragClass),z.append(k))),Object.assign(P,{el:z,dragEl:k}),x.draggable&&X(),z&&z.classList[e.enabled?"remove":"add"](...ie(e.params.scrollbar.lockClass))}function E(){const P=e.params.scrollbar,M=e.scrollbar.el;M&&M.classList.remove(...ie(e.isHorizontal()?P.horizontalClass:P.verticalClass)),A()}m("changeDirection",()=>{if(!e.scrollbar||!e.scrollbar.el)return;const P=e.params.scrollbar;let{el:M}=e.scrollbar;M=B(M),M.forEach(x=>{x.classList.remove(P.horizontalClass,P.verticalClass),x.classList.add(e.isHorizontal()?P.horizontalClass:P.verticalClass)})}),m("init",()=>{e.params.scrollbar.enabled===!1?H():(O(),t(),o())}),m("update resize observerUpdate lock unlock changeDirection",()=>{t()}),m("setTranslate",()=>{o()}),m("setTransition",(P,M)=>{a(M)}),m("enable disable",()=>{const{el:P}=e.scrollbar;P&&P.classList[e.enabled?"remove":"add"](...ie(e.params.scrollbar.lockClass))}),m("destroy",()=>{E()});const I=()=>{e.el.classList.remove(...ie(e.params.scrollbar.scrollbarDisabledClass)),e.scrollbar.el&&e.scrollbar.el.classList.remove(...ie(e.params.scrollbar.scrollbarDisabledClass)),O(),t(),o()},H=()=>{e.el.classList.add(...ie(e.params.scrollbar.scrollbarDisabledClass)),e.scrollbar.el&&e.scrollbar.el.classList.add(...ie(e.params.scrollbar.scrollbarDisabledClass)),E()};Object.assign(e.scrollbar,{enable:I,disable:H,updateSize:t,setTranslate:o,init:O,destroy:E})}function _e(L){let{swiper:e,extendParams:S,on:m}=L;S({parallax:{enabled:!1}});const b="[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]",g=(d,l)=>{const{rtl:n}=e,f=n?-1:1,u=d.getAttribute("data-swiper-parallax")||"0";let o=d.getAttribute("data-swiper-parallax-x"),a=d.getAttribute("data-swiper-parallax-y");const t=d.getAttribute("data-swiper-parallax-scale"),r=d.getAttribute("data-swiper-parallax-opacity"),c=d.getAttribute("data-swiper-parallax-rotate");if(o||a?(o=o||"0",a=a||"0"):e.isHorizontal()?(o=u,a="0"):(a=u,o="0"),o.indexOf("%")>=0?o=`${parseInt(o,10)*l*f}%`:o=`${o*l*f}px`,a.indexOf("%")>=0?a=`${parseInt(a,10)*l}%`:a=`${a*l}px`,typeof r<"u"&&r!==null){const s=r-(r-1)*(1-Math.abs(l));d.style.opacity=s}let i=`translate3d(${o}, ${a}, 0px)`;if(typeof t<"u"&&t!==null){const s=t-(t-1)*(1-Math.abs(l));i+=` scale(${s})`}if(c&&typeof c<"u"&&c!==null){const s=c*l*-1;i+=` rotate(${s}deg)`}d.style.transform=i},v=()=>{const{el:d,slides:l,progress:n,snapGrid:f,isElement:u}=e,o=se(d,b);e.isElement&&o.push(...se(e.hostEl,b)),o.forEach(a=>{g(a,n)}),l.forEach((a,t)=>{let r=a.progress;e.params.slidesPerGroup>1&&e.params.slidesPerView!=="auto"&&(r+=Math.ceil(t/2)-n*(f.length-1)),r=Math.min(Math.max(r,-1),1),a.querySelectorAll(`${b}, [data-swiper-parallax-rotate]`).forEach(c=>{g(c,r)})})},w=function(d){d===void 0&&(d=e.params.speed);const{el:l,hostEl:n}=e,f=[...l.querySelectorAll(b)];e.isElement&&f.push(...n.querySelectorAll(b)),f.forEach(u=>{let o=parseInt(u.getAttribute("data-swiper-parallax-duration"),10)||d;d===0&&(o=0),u.style.transitionDuration=`${o}ms`})};m("beforeInit",()=>{e.params.parallax.enabled&&(e.params.watchSlidesProgress=!0,e.originalParams.watchSlidesProgress=!0)}),m("init",()=>{e.params.parallax.enabled&&v()}),m("setTranslate",()=>{e.params.parallax.enabled&&v()}),m("setTransition",(d,l)=>{e.params.parallax.enabled&&w(l)})}function Ue(L){let{swiper:e,extendParams:S,on:m,emit:b}=L;const g=ae();S({zoom:{enabled:!1,limitToOriginalSize:!1,maxRatio:3,minRatio:1,panOnMouseMove:!1,toggle:!0,containerClass:"swiper-zoom-container",zoomedSlideClass:"swiper-slide-zoomed"}}),e.zoom={enabled:!1};let v=1,w=!1,d=!1,l={x:0,y:0};const n=-3;let f,u;const o=[],a={originX:0,originY:0,slideEl:void 0,slideWidth:void 0,slideHeight:void 0,imageEl:void 0,imageWrapEl:void 0,maxRatio:3},t={isTouched:void 0,isMoved:void 0,currentX:void 0,currentY:void 0,minX:void 0,minY:void 0,maxX:void 0,maxY:void 0,width:void 0,height:void 0,startX:void 0,startY:void 0,touchesStart:{},touchesCurrent:{}},r={x:void 0,y:void 0,prevPositionX:void 0,prevPositionY:void 0,prevTime:void 0};let c=1;Object.defineProperty(e.zoom,"scale",{get(){return c},set($){if(c!==$){const T=a.imageEl,D=a.slideEl;b("zoomChange",$,T,D)}c=$}});function i(){if(o.length<2)return 1;const $=o[0].pageX,T=o[0].pageY,D=o[1].pageX,q=o[1].pageY;return Math.sqrt((D-$)**2+(q-T)**2)}function s(){const $=e.params.zoom,T=a.imageWrapEl.getAttribute("data-swiper-zoom")||$.maxRatio;if($.limitToOriginalSize&&a.imageEl&&a.imageEl.naturalWidth){const D=a.imageEl.naturalWidth/a.imageEl.offsetWidth;return Math.min(D,T)}return T}function p(){if(o.length<2)return{x:null,y:null};const $=a.imageEl.getBoundingClientRect();return[(o[0].pageX+(o[1].pageX-o[0].pageX)/2-$.x-g.scrollX)/v,(o[0].pageY+(o[1].pageY-o[0].pageY)/2-$.y-g.scrollY)/v]}function C(){return e.isElement?"swiper-slide":`.${e.params.slideClass}`}function X($){const T=C();return!!($.target.matches(T)||e.slides.filter(D=>D.contains($.target)).length>0)}function A($){const T=`.${e.params.zoom.containerClass}`;return!!($.target.matches(T)||[...e.hostEl.querySelectorAll(T)].filter(D=>D.contains($.target)).length>0)}function O($){if($.pointerType==="mouse"&&o.splice(0,o.length),!X($))return;const T=e.params.zoom;if(f=!1,u=!1,o.push($),!(o.length<2)){if(f=!0,a.scaleStart=i(),!a.slideEl){a.slideEl=$.target.closest(`.${e.params.slideClass}, swiper-slide`),a.slideEl||(a.slideEl=e.slides[e.activeIndex]);let D=a.slideEl.querySelector(`.${T.containerClass}`);if(D&&(D=D.querySelectorAll("picture, img, svg, canvas, .swiper-zoom-target")[0]),a.imageEl=D,D?a.imageWrapEl=ce(a.imageEl,`.${T.containerClass}`)[0]:a.imageWrapEl=void 0,!a.imageWrapEl){a.imageEl=void 0;return}a.maxRatio=s()}if(a.imageEl){const[D,q]=p();a.originX=D,a.originY=q,a.imageEl.style.transitionDuration="0ms"}w=!0}}function E($){if(!X($))return;const T=e.params.zoom,D=e.zoom,q=o.findIndex(F=>F.pointerId===$.pointerId);q>=0&&(o[q]=$),!(o.length<2)&&(u=!0,a.scaleMove=i(),a.imageEl&&(D.scale=a.scaleMove/a.scaleStart*v,D.scale>a.maxRatio&&(D.scale=a.maxRatio-1+(D.scale-a.maxRatio+1)**.5),D.scale<T.minRatio&&(D.scale=T.minRatio+1-(T.minRatio-D.scale+1)**.5),a.imageEl.style.transform=`translate3d(0,0,0) scale(${D.scale})`))}function I($){if(!X($)||$.pointerType==="mouse"&&$.type==="pointerout")return;const T=e.params.zoom,D=e.zoom,q=o.findIndex(F=>F.pointerId===$.pointerId);q>=0&&o.splice(q,1),!(!f||!u)&&(f=!1,u=!1,a.imageEl&&(D.scale=Math.max(Math.min(D.scale,a.maxRatio),T.minRatio),a.imageEl.style.transitionDuration=`${e.params.speed}ms`,a.imageEl.style.transform=`translate3d(0,0,0) scale(${D.scale})`,v=D.scale,w=!1,D.scale>1&&a.slideEl?a.slideEl.classList.add(`${T.zoomedSlideClass}`):D.scale<=1&&a.slideEl&&a.slideEl.classList.remove(`${T.zoomedSlideClass}`),D.scale===1&&(a.originX=0,a.originY=0,a.slideEl=void 0)))}let H;function P(){e.touchEventsData.preventTouchMoveFromPointerMove=!1}function M(){clearTimeout(H),e.touchEventsData.preventTouchMoveFromPointerMove=!0,H=setTimeout(()=>{e.destroyed||P()})}function x($){const T=e.device;if(!a.imageEl||t.isTouched)return;T.android&&$.cancelable&&$.preventDefault(),t.isTouched=!0;const D=o.length>0?o[0]:$;t.touchesStart.x=D.pageX,t.touchesStart.y=D.pageY}function z($){const D=$.pointerType==="mouse"&&e.params.zoom.panOnMouseMove;if(!X($)||!A($))return;const q=e.zoom;if(!a.imageEl)return;if(!t.isTouched||!a.slideEl){D&&y($);return}if(D){y($);return}t.isMoved||(t.width=a.imageEl.offsetWidth||a.imageEl.clientWidth,t.height=a.imageEl.offsetHeight||a.imageEl.clientHeight,t.startX=Ae(a.imageWrapEl,"x")||0,t.startY=Ae(a.imageWrapEl,"y")||0,a.slideWidth=a.slideEl.offsetWidth,a.slideHeight=a.slideEl.offsetHeight,a.imageWrapEl.style.transitionDuration="0ms");const F=t.width*q.scale,Q=t.height*q.scale;if(t.minX=Math.min(a.slideWidth/2-F/2,0),t.maxX=-t.minX,t.minY=Math.min(a.slideHeight/2-Q/2,0),t.maxY=-t.minY,t.touchesCurrent.x=o.length>0?o[0].pageX:$.pageX,t.touchesCurrent.y=o.length>0?o[0].pageY:$.pageY,Math.max(Math.abs(t.touchesCurrent.x-t.touchesStart.x),Math.abs(t.touchesCurrent.y-t.touchesStart.y))>5&&(e.allowClick=!1),!t.isMoved&&!w){if(e.isHorizontal()&&(Math.floor(t.minX)===Math.floor(t.startX)&&t.touchesCurrent.x<t.touchesStart.x||Math.floor(t.maxX)===Math.floor(t.startX)&&t.touchesCurrent.x>t.touchesStart.x)){t.isTouched=!1,P();return}if(!e.isHorizontal()&&(Math.floor(t.minY)===Math.floor(t.startY)&&t.touchesCurrent.y<t.touchesStart.y||Math.floor(t.maxY)===Math.floor(t.startY)&&t.touchesCurrent.y>t.touchesStart.y)){t.isTouched=!1,P();return}}$.cancelable&&$.preventDefault(),$.stopPropagation(),M(),t.isMoved=!0;const Z=(q.scale-v)/(a.maxRatio-e.params.zoom.minRatio),{originX:ee,originY:G}=a;t.currentX=t.touchesCurrent.x-t.touchesStart.x+t.startX+Z*(t.width-ee*2),t.currentY=t.touchesCurrent.y-t.touchesStart.y+t.startY+Z*(t.height-G*2),t.currentX<t.minX&&(t.currentX=t.minX+1-(t.minX-t.currentX+1)**.8),t.currentX>t.maxX&&(t.currentX=t.maxX-1+(t.currentX-t.maxX+1)**.8),t.currentY<t.minY&&(t.currentY=t.minY+1-(t.minY-t.currentY+1)**.8),t.currentY>t.maxY&&(t.currentY=t.maxY-1+(t.currentY-t.maxY+1)**.8),r.prevPositionX||(r.prevPositionX=t.touchesCurrent.x),r.prevPositionY||(r.prevPositionY=t.touchesCurrent.y),r.prevTime||(r.prevTime=Date.now()),r.x=(t.touchesCurrent.x-r.prevPositionX)/(Date.now()-r.prevTime)/2,r.y=(t.touchesCurrent.y-r.prevPositionY)/(Date.now()-r.prevTime)/2,Math.abs(t.touchesCurrent.x-r.prevPositionX)<2&&(r.x=0),Math.abs(t.touchesCurrent.y-r.prevPositionY)<2&&(r.y=0),r.prevPositionX=t.touchesCurrent.x,r.prevPositionY=t.touchesCurrent.y,r.prevTime=Date.now(),a.imageWrapEl.style.transform=`translate3d(${t.currentX}px, ${t.currentY}px,0)`}function k(){const $=e.zoom;if(o.length=0,!a.imageEl)return;if(!t.isTouched||!t.isMoved){t.isTouched=!1,t.isMoved=!1;return}t.isTouched=!1,t.isMoved=!1;let T=300,D=300;const q=r.x*T,F=t.currentX+q,Q=r.y*D,J=t.currentY+Q;r.x!==0&&(T=Math.abs((F-t.currentX)/r.x)),r.y!==0&&(D=Math.abs((J-t.currentY)/r.y));const Z=Math.max(T,D);t.currentX=F,t.currentY=J;const ee=t.width*$.scale,G=t.height*$.scale;t.minX=Math.min(a.slideWidth/2-ee/2,0),t.maxX=-t.minX,t.minY=Math.min(a.slideHeight/2-G/2,0),t.maxY=-t.minY,t.currentX=Math.max(Math.min(t.currentX,t.maxX),t.minX),t.currentY=Math.max(Math.min(t.currentY,t.maxY),t.minY),a.imageWrapEl.style.transitionDuration=`${Z}ms`,a.imageWrapEl.style.transform=`translate3d(${t.currentX}px, ${t.currentY}px,0)`}function h(){const $=e.zoom;a.slideEl&&e.activeIndex!==e.slides.indexOf(a.slideEl)&&(a.imageEl&&(a.imageEl.style.transform="translate3d(0,0,0) scale(1)"),a.imageWrapEl&&(a.imageWrapEl.style.transform="translate3d(0,0,0)"),a.slideEl.classList.remove(`${e.params.zoom.zoomedSlideClass}`),$.scale=1,v=1,a.slideEl=void 0,a.imageEl=void 0,a.imageWrapEl=void 0,a.originX=0,a.originY=0)}function y($){if(v<=1||!a.imageWrapEl||!X($)||!A($))return;const T=g.getComputedStyle(a.imageWrapEl).transform,D=new g.DOMMatrix(T);if(!d){d=!0,l.x=$.clientX,l.y=$.clientY,t.startX=D.e,t.startY=D.f,t.width=a.imageEl.offsetWidth||a.imageEl.clientWidth,t.height=a.imageEl.offsetHeight||a.imageEl.clientHeight,a.slideWidth=a.slideEl.offsetWidth,a.slideHeight=a.slideEl.offsetHeight;return}const q=($.clientX-l.x)*n,F=($.clientY-l.y)*n,Q=t.width*v,J=t.height*v,Z=a.slideWidth,ee=a.slideHeight,G=Math.min(Z/2-Q/2,0),j=-G,ue=Math.min(ee/2-J/2,0),ve=-ue,ne=Math.max(Math.min(t.startX+q,j),G),le=Math.max(Math.min(t.startY+F,ve),ue);a.imageWrapEl.style.transitionDuration="0ms",a.imageWrapEl.style.transform=`translate3d(${ne}px, ${le}px, 0)`,l.x=$.clientX,l.y=$.clientY,t.startX=ne,t.startY=le,t.currentX=ne,t.currentY=le}function Y($){const T=e.zoom,D=e.params.zoom;if(!a.slideEl){$&&$.target&&(a.slideEl=$.target.closest(`.${e.params.slideClass}, swiper-slide`)),a.slideEl||(e.params.virtual&&e.params.virtual.enabled&&e.virtual?a.slideEl=se(e.slidesEl,`.${e.params.slideActiveClass}`)[0]:a.slideEl=e.slides[e.activeIndex]);let me=a.slideEl.querySelector(`.${D.containerClass}`);me&&(me=me.querySelectorAll("picture, img, svg, canvas, .swiper-zoom-target")[0]),a.imageEl=me,me?a.imageWrapEl=ce(a.imageEl,`.${D.containerClass}`)[0]:a.imageWrapEl=void 0}if(!a.imageEl||!a.imageWrapEl)return;e.params.cssMode&&(e.wrapperEl.style.overflow="hidden",e.wrapperEl.style.touchAction="none"),a.slideEl.classList.add(`${D.zoomedSlideClass}`);let q,F,Q,J,Z,ee,G,j,ue,ve,ne,le,ye,we,Me,Te,Ce,$e;typeof t.touchesStart.x>"u"&&$?(q=$.pageX,F=$.pageY):(q=t.touchesStart.x,F=t.touchesStart.y);const Le=v,oe=typeof $=="number"?$:null;v===1&&oe&&(q=void 0,F=void 0,t.touchesStart.x=void 0,t.touchesStart.y=void 0);const De=s();T.scale=oe||De,v=oe||De,$&&!(v===1&&oe)?(Ce=a.slideEl.offsetWidth,$e=a.slideEl.offsetHeight,Q=be(a.slideEl).left+g.scrollX,J=be(a.slideEl).top+g.scrollY,Z=Q+Ce/2-q,ee=J+$e/2-F,ue=a.imageEl.offsetWidth||a.imageEl.clientWidth,ve=a.imageEl.offsetHeight||a.imageEl.clientHeight,ne=ue*T.scale,le=ve*T.scale,ye=Math.min(Ce/2-ne/2,0),we=Math.min($e/2-le/2,0),Me=-ye,Te=-we,Le>0&&oe&&typeof t.currentX=="number"&&typeof t.currentY=="number"?(G=t.currentX*T.scale/Le,j=t.currentY*T.scale/Le):(G=Z*T.scale,j=ee*T.scale),G<ye&&(G=ye),G>Me&&(G=Me),j<we&&(j=we),j>Te&&(j=Te)):(G=0,j=0),oe&&T.scale===1&&(a.originX=0,a.originY=0),t.currentX=G,t.currentY=j,a.imageWrapEl.style.transitionDuration="300ms",a.imageWrapEl.style.transform=`translate3d(${G}px, ${j}px,0)`,a.imageEl.style.transitionDuration="300ms",a.imageEl.style.transform=`translate3d(0,0,0) scale(${T.scale})`}function R(){const $=e.zoom,T=e.params.zoom;if(!a.slideEl){e.params.virtual&&e.params.virtual.enabled&&e.virtual?a.slideEl=se(e.slidesEl,`.${e.params.slideActiveClass}`)[0]:a.slideEl=e.slides[e.activeIndex];let D=a.slideEl.querySelector(`.${T.containerClass}`);D&&(D=D.querySelectorAll("picture, img, svg, canvas, .swiper-zoom-target")[0]),a.imageEl=D,D?a.imageWrapEl=ce(a.imageEl,`.${T.containerClass}`)[0]:a.imageWrapEl=void 0}!a.imageEl||!a.imageWrapEl||(e.params.cssMode&&(e.wrapperEl.style.overflow="",e.wrapperEl.style.touchAction=""),$.scale=1,v=1,t.currentX=void 0,t.currentY=void 0,t.touchesStart.x=void 0,t.touchesStart.y=void 0,a.imageWrapEl.style.transitionDuration="300ms",a.imageWrapEl.style.transform="translate3d(0,0,0)",a.imageEl.style.transitionDuration="300ms",a.imageEl.style.transform="translate3d(0,0,0) scale(1)",a.slideEl.classList.remove(`${T.zoomedSlideClass}`),a.slideEl=void 0,a.originX=0,a.originY=0,e.params.zoom.panOnMouseMove&&(l={x:0,y:0},d&&(d=!1,t.startX=0,t.startY=0)))}function W($){const T=e.zoom;T.scale&&T.scale!==1?R():Y($)}function N(){const $=e.params.passiveListeners?{passive:!0,capture:!1}:!1,T=e.params.passiveListeners?{passive:!1,capture:!0}:!0;return{passiveListener:$,activeListenerWithCapture:T}}function V(){const $=e.zoom;if($.enabled)return;$.enabled=!0;const{passiveListener:T,activeListenerWithCapture:D}=N();e.wrapperEl.addEventListener("pointerdown",O,T),e.wrapperEl.addEventListener("pointermove",E,D),["pointerup","pointercancel","pointerout"].forEach(q=>{e.wrapperEl.addEventListener(q,I,T)}),e.wrapperEl.addEventListener("pointermove",z,D)}function fe(){const $=e.zoom;if(!$.enabled)return;$.enabled=!1;const{passiveListener:T,activeListenerWithCapture:D}=N();e.wrapperEl.removeEventListener("pointerdown",O,T),e.wrapperEl.removeEventListener("pointermove",E,D),["pointerup","pointercancel","pointerout"].forEach(q=>{e.wrapperEl.removeEventListener(q,I,T)}),e.wrapperEl.removeEventListener("pointermove",z,D)}m("init",()=>{e.params.zoom.enabled&&V()}),m("destroy",()=>{fe()}),m("touchStart",($,T)=>{e.zoom.enabled&&x(T)}),m("touchEnd",($,T)=>{e.zoom.enabled&&k()}),m("doubleTap",($,T)=>{!e.animating&&e.params.zoom.enabled&&e.zoom.enabled&&e.params.zoom.toggle&&W(T)}),m("transitionEnd",()=>{e.zoom.enabled&&e.params.zoom.enabled&&h()}),m("slideChange",()=>{e.zoom.enabled&&e.params.zoom.enabled&&e.params.cssMode&&h()}),Object.assign(e.zoom,{enable:V,disable:fe,in:Y,out:R,toggle:W})}function Ze(L){let{swiper:e,extendParams:S,on:m}=L;S({controller:{control:void 0,inverse:!1,by:"slide"}}),e.controller={control:void 0};function b(l,n){const f=function(){let t,r,c;return(i,s)=>{for(r=-1,t=i.length;t-r>1;)c=t+r>>1,i[c]<=s?r=c:t=c;return t}}();this.x=l,this.y=n,this.lastIndex=l.length-1;let u,o;return this.interpolate=function(t){return t?(o=f(this.x,t),u=o-1,(t-this.x[u])*(this.y[o]-this.y[u])/(this.x[o]-this.x[u])+this.y[u]):0},this}function g(l){e.controller.spline=e.params.loop?new b(e.slidesGrid,l.slidesGrid):new b(e.snapGrid,l.snapGrid)}function v(l,n){const f=e.controller.control;let u,o;const a=e.constructor;function t(r){if(r.destroyed)return;const c=e.rtlTranslate?-e.translate:e.translate;e.params.controller.by==="slide"&&(g(r),o=-e.controller.spline.interpolate(-c)),(!o||e.params.controller.by==="container")&&(u=(r.maxTranslate()-r.minTranslate())/(e.maxTranslate()-e.minTranslate()),(Number.isNaN(u)||!Number.isFinite(u))&&(u=1),o=(c-e.minTranslate())*u+r.minTranslate()),e.params.controller.inverse&&(o=r.maxTranslate()-o),r.updateProgress(o),r.setTranslate(o,e),r.updateActiveIndex(),r.updateSlidesClasses()}if(Array.isArray(f))for(let r=0;r<f.length;r+=1)f[r]!==n&&f[r]instanceof a&&t(f[r]);else f instanceof a&&n!==f&&t(f)}function w(l,n){const f=e.constructor,u=e.controller.control;let o;function a(t){t.destroyed||(t.setTransition(l,e),l!==0&&(t.transitionStart(),t.params.autoHeight&&Ee(()=>{t.updateAutoHeight()}),he(t.wrapperEl,()=>{u&&t.transitionEnd()})))}if(Array.isArray(u))for(o=0;o<u.length;o+=1)u[o]!==n&&u[o]instanceof f&&a(u[o]);else u instanceof f&&n!==u&&a(u)}function d(){e.controller.control&&e.controller.spline&&(e.controller.spline=void 0,delete e.controller.spline)}m("beforeInit",()=>{if(typeof window<"u"&&(typeof e.params.controller.control=="string"||e.params.controller.control instanceof HTMLElement)){(typeof e.params.controller.control=="string"?[...document.querySelectorAll(e.params.controller.control)]:[e.params.controller.control]).forEach(n=>{if(e.controller.control||(e.controller.control=[]),n&&n.swiper)e.controller.control.push(n.swiper);else if(n){const f=`${e.params.eventsPrefix}init`,u=o=>{e.controller.control.push(o.detail[0]),e.update(),n.removeEventListener(f,u)};n.addEventListener(f,u)}});return}e.controller.control=e.params.controller.control}),m("update",()=>{d()}),m("resize",()=>{d()}),m("observerUpdate",()=>{d()}),m("setTranslate",(l,n,f)=>{!e.controller.control||e.controller.control.destroyed||e.controller.setTranslate(n,f)}),m("setTransition",(l,n,f)=>{!e.controller.control||e.controller.control.destroyed||e.controller.setTransition(n,f)}),Object.assign(e.controller,{setTranslate:v,setTransition:w})}function Ke(L){let{swiper:e,extendParams:S,on:m}=L;S({a11y:{enabled:!0,notificationClass:"swiper-notification",prevSlideMessage:"Previous slide",nextSlideMessage:"Next slide",firstSlideMessage:"This is the first slide",lastSlideMessage:"This is the last slide",paginationBulletMessage:"Go to slide {{index}}",slideLabelMessage:"{{index}} / {{slidesLength}}",containerMessage:null,containerRoleDescriptionMessage:null,containerRole:null,itemRoleDescriptionMessage:null,slideRole:"group",id:null,scrollOnFocus:!0}}),e.a11y={clicked:!1};let b=null,g,v,w=new Date().getTime();function d(h){const y=b;y.length!==0&&(y.innerHTML="",y.innerHTML=h)}function l(h){const y=()=>Math.round(16*Math.random()).toString(16);return"x".repeat(h).replace(/x/g,y)}function n(h){h=B(h),h.forEach(y=>{y.setAttribute("tabIndex","0")})}function f(h){h=B(h),h.forEach(y=>{y.setAttribute("tabIndex","-1")})}function u(h,y){h=B(h),h.forEach(Y=>{Y.setAttribute("role",y)})}function o(h,y){h=B(h),h.forEach(Y=>{Y.setAttribute("aria-roledescription",y)})}function a(h,y){h=B(h),h.forEach(Y=>{Y.setAttribute("aria-controls",y)})}function t(h,y){h=B(h),h.forEach(Y=>{Y.setAttribute("aria-label",y)})}function r(h,y){h=B(h),h.forEach(Y=>{Y.setAttribute("id",y)})}function c(h,y){h=B(h),h.forEach(Y=>{Y.setAttribute("aria-live",y)})}function i(h){h=B(h),h.forEach(y=>{y.setAttribute("aria-disabled",!0)})}function s(h){h=B(h),h.forEach(y=>{y.setAttribute("aria-disabled",!1)})}function p(h){if(h.keyCode!==13&&h.keyCode!==32)return;const y=e.params.a11y,Y=h.target;if(!(e.pagination&&e.pagination.el&&(Y===e.pagination.el||e.pagination.el.contains(h.target))&&!h.target.matches(K(e.params.pagination.bulletClass)))){if(e.navigation&&e.navigation.prevEl&&e.navigation.nextEl){const R=B(e.navigation.prevEl);B(e.navigation.nextEl).includes(Y)&&(e.isEnd&&!e.params.loop||e.slideNext(),e.isEnd?d(y.lastSlideMessage):d(y.nextSlideMessage)),R.includes(Y)&&(e.isBeginning&&!e.params.loop||e.slidePrev(),e.isBeginning?d(y.firstSlideMessage):d(y.prevSlideMessage))}e.pagination&&Y.matches(K(e.params.pagination.bulletClass))&&Y.click()}}function C(){if(e.params.loop||e.params.rewind||!e.navigation)return;const{nextEl:h,prevEl:y}=e.navigation;y&&(e.isBeginning?(i(y),f(y)):(s(y),n(y))),h&&(e.isEnd?(i(h),f(h)):(s(h),n(h)))}function X(){return e.pagination&&e.pagination.bullets&&e.pagination.bullets.length}function A(){return X()&&e.params.pagination.clickable}function O(){const h=e.params.a11y;X()&&e.pagination.bullets.forEach(y=>{e.params.pagination.clickable&&(n(y),e.params.pagination.renderBullet||(u(y,"button"),t(y,h.paginationBulletMessage.replace(/\{\{index\}\}/,Pe(y)+1)))),y.matches(K(e.params.pagination.bulletActiveClass))?y.setAttribute("aria-current","true"):y.removeAttribute("aria-current")})}const E=(h,y,Y)=>{n(h),h.tagName!=="BUTTON"&&(u(h,"button"),h.addEventListener("keydown",p)),t(h,Y),a(h,y)},I=h=>{v&&v!==h.target&&!v.contains(h.target)&&(g=!0),e.a11y.clicked=!0},H=()=>{g=!1,requestAnimationFrame(()=>{requestAnimationFrame(()=>{e.destroyed||(e.a11y.clicked=!1)})})},P=h=>{w=new Date().getTime()},M=h=>{if(e.a11y.clicked||!e.params.a11y.scrollOnFocus||new Date().getTime()-w<100)return;const y=h.target.closest(`.${e.params.slideClass}, swiper-slide`);if(!y||!e.slides.includes(y))return;v=y;const Y=e.slides.indexOf(y)===e.activeIndex,R=e.params.watchSlidesProgress&&e.visibleSlides&&e.visibleSlides.includes(y);Y||R||h.sourceCapabilities&&h.sourceCapabilities.firesTouchEvents||(e.isHorizontal()?e.el.scrollLeft=0:e.el.scrollTop=0,requestAnimationFrame(()=>{g||(e.params.loop?e.slideToLoop(parseInt(y.getAttribute("data-swiper-slide-index")),0):e.slideTo(e.slides.indexOf(y),0),g=!1)}))},x=()=>{const h=e.params.a11y;h.itemRoleDescriptionMessage&&o(e.slides,h.itemRoleDescriptionMessage),h.slideRole&&u(e.slides,h.slideRole);const y=e.slides.length;h.slideLabelMessage&&e.slides.forEach((Y,R)=>{const W=e.params.loop?parseInt(Y.getAttribute("data-swiper-slide-index"),10):R,N=h.slideLabelMessage.replace(/\{\{index\}\}/,W+1).replace(/\{\{slidesLength\}\}/,y);t(Y,N)})},z=()=>{const h=e.params.a11y;e.el.append(b);const y=e.el;h.containerRoleDescriptionMessage&&o(y,h.containerRoleDescriptionMessage),h.containerMessage&&t(y,h.containerMessage),h.containerRole&&u(y,h.containerRole);const Y=e.wrapperEl,R=h.id||Y.getAttribute("id")||`swiper-wrapper-${l(16)}`,W=e.params.autoplay&&e.params.autoplay.enabled?"off":"polite";r(Y,R),c(Y,W),x();let{nextEl:N,prevEl:V}=e.navigation?e.navigation:{};N=B(N),V=B(V),N&&N.forEach($=>E($,R,h.nextSlideMessage)),V&&V.forEach($=>E($,R,h.prevSlideMessage)),A()&&B(e.pagination.el).forEach(T=>{T.addEventListener("keydown",p)}),U().addEventListener("visibilitychange",P),e.el.addEventListener("focus",M,!0),e.el.addEventListener("focus",M,!0),e.el.addEventListener("pointerdown",I,!0),e.el.addEventListener("pointerup",H,!0)};function k(){b&&b.remove();let{nextEl:h,prevEl:y}=e.navigation?e.navigation:{};h=B(h),y=B(y),h&&h.forEach(R=>R.removeEventListener("keydown",p)),y&&y.forEach(R=>R.removeEventListener("keydown",p)),A()&&B(e.pagination.el).forEach(W=>{W.removeEventListener("keydown",p)}),U().removeEventListener("visibilitychange",P),e.el&&typeof e.el!="string"&&(e.el.removeEventListener("focus",M,!0),e.el.removeEventListener("pointerdown",I,!0),e.el.removeEventListener("pointerup",H,!0))}m("beforeInit",()=>{b=_("span",e.params.a11y.notificationClass),b.setAttribute("aria-live","assertive"),b.setAttribute("aria-atomic","true")}),m("afterInit",()=>{e.params.a11y.enabled&&z()}),m("slidesLengthChange snapGridLengthChange slidesGridLengthChange",()=>{e.params.a11y.enabled&&x()}),m("fromEdge toEdge afterInit lock unlock",()=>{e.params.a11y.enabled&&C()}),m("paginationUpdate",()=>{e.params.a11y.enabled&&O()}),m("destroy",()=>{e.params.a11y.enabled&&k()})}function Qe(L){let{swiper:e,extendParams:S,on:m}=L;S({history:{enabled:!1,root:"",replaceState:!1,key:"slides",keepQuery:!1}});let b=!1,g={};const v=o=>o.toString().replace(/\s+/g,"-").replace(/[^\w-]+/g,"").replace(/--+/g,"-").replace(/^-+/,"").replace(/-+$/,""),w=o=>{const a=ae();let t;o?t=new URL(o):t=a.location;const r=t.pathname.slice(1).split("/").filter(p=>p!==""),c=r.length,i=r[c-2],s=r[c-1];return{key:i,value:s}},d=(o,a)=>{const t=ae();if(!b||!e.params.history.enabled)return;let r;e.params.url?r=new URL(e.params.url):r=t.location;const c=e.virtual&&e.params.virtual.enabled?e.slidesEl.querySelector(`[data-swiper-slide-index="${a}"]`):e.slides[a];let i=v(c.getAttribute("data-history"));if(e.params.history.root.length>0){let p=e.params.history.root;p[p.length-1]==="/"&&(p=p.slice(0,p.length-1)),i=`${p}/${o?`${o}/`:""}${i}`}else r.pathname.includes(o)||(i=`${o?`${o}/`:""}${i}`);e.params.history.keepQuery&&(i+=r.search);const s=t.history.state;s&&s.value===i||(e.params.history.replaceState?t.history.replaceState({value:i},null,i):t.history.pushState({value:i},null,i))},l=(o,a,t)=>{if(a)for(let r=0,c=e.slides.length;r<c;r+=1){const i=e.slides[r];if(v(i.getAttribute("data-history"))===a){const p=e.getSlideIndex(i);e.slideTo(p,o,t)}}else e.slideTo(0,o,t)},n=()=>{g=w(e.params.url),l(e.params.speed,g.value,!1)},f=()=>{const o=ae();if(e.params.history){if(!o.history||!o.history.pushState){e.params.history.enabled=!1,e.params.hashNavigation.enabled=!0;return}if(b=!0,g=w(e.params.url),!g.key&&!g.value){e.params.history.replaceState||o.addEventListener("popstate",n);return}l(0,g.value,e.params.runCallbacksOnInit),e.params.history.replaceState||o.addEventListener("popstate",n)}},u=()=>{const o=ae();e.params.history.replaceState||o.removeEventListener("popstate",n)};m("init",()=>{e.params.history.enabled&&f()}),m("destroy",()=>{e.params.history.enabled&&u()}),m("transitionEnd _freeModeNoMomentumRelease",()=>{b&&d(e.params.history.key,e.activeIndex)}),m("slideChange",()=>{b&&e.params.cssMode&&d(e.params.history.key,e.activeIndex)})}function Je(L){let{swiper:e,extendParams:S,emit:m,on:b}=L,g=!1;const v=U(),w=ae();S({hashNavigation:{enabled:!1,replaceState:!1,watchState:!1,getSlideIndex(u,o){if(e.virtual&&e.params.virtual.enabled){const a=e.slides.find(r=>r.getAttribute("data-hash")===o);return a?parseInt(a.getAttribute("data-swiper-slide-index"),10):0}return e.getSlideIndex(se(e.slidesEl,`.${e.params.slideClass}[data-hash="${o}"], swiper-slide[data-hash="${o}"]`)[0])}}});const d=()=>{m("hashChange");const u=v.location.hash.replace("#",""),o=e.virtual&&e.params.virtual.enabled?e.slidesEl.querySelector(`[data-swiper-slide-index="${e.activeIndex}"]`):e.slides[e.activeIndex],a=o?o.getAttribute("data-hash"):"";if(u!==a){const t=e.params.hashNavigation.getSlideIndex(e,u);if(typeof t>"u"||Number.isNaN(t))return;e.slideTo(t)}},l=()=>{if(!g||!e.params.hashNavigation.enabled)return;const u=e.virtual&&e.params.virtual.enabled?e.slidesEl.querySelector(`[data-swiper-slide-index="${e.activeIndex}"]`):e.slides[e.activeIndex],o=u?u.getAttribute("data-hash")||u.getAttribute("data-history"):"";e.params.hashNavigation.replaceState&&w.history&&w.history.replaceState?(w.history.replaceState(null,null,`#${o}`||""),m("hashSet")):(v.location.hash=o||"",m("hashSet"))},n=()=>{if(!e.params.hashNavigation.enabled||e.params.history&&e.params.history.enabled)return;g=!0;const u=v.location.hash.replace("#","");if(u){const a=e.params.hashNavigation.getSlideIndex(e,u);e.slideTo(a||0,0,e.params.runCallbacksOnInit,!0)}e.params.hashNavigation.watchState&&w.addEventListener("hashchange",d)},f=()=>{e.params.hashNavigation.watchState&&w.removeEventListener("hashchange",d)};b("init",()=>{e.params.hashNavigation.enabled&&n()}),b("destroy",()=>{e.params.hashNavigation.enabled&&f()}),b("transitionEnd _freeModeNoMomentumRelease",()=>{g&&l()}),b("slideChange",()=>{g&&e.params.cssMode&&l()})}function et(L){let{swiper:e,extendParams:S,on:m,emit:b,params:g}=L;e.autoplay={running:!1,paused:!1,timeLeft:0},S({autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!1,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}});let v,w,d=g&&g.autoplay?g.autoplay.delay:3e3,l=g&&g.autoplay?g.autoplay.delay:3e3,n,f=new Date().getTime(),u,o,a,t,r,c,i;function s(y){!e||e.destroyed||!e.wrapperEl||y.target===e.wrapperEl&&(e.wrapperEl.removeEventListener("transitionend",s),!(i||y.detail&&y.detail.bySwiperTouchMove)&&I())}const p=()=>{if(e.destroyed||!e.autoplay.running)return;e.autoplay.paused?u=!0:u&&(l=n,u=!1);const y=e.autoplay.paused?n:f+l-new Date().getTime();e.autoplay.timeLeft=y,b("autoplayTimeLeft",y,y/d),w=requestAnimationFrame(()=>{p()})},C=()=>{let y;return e.virtual&&e.params.virtual.enabled?y=e.slides.find(R=>R.classList.contains("swiper-slide-active")):y=e.slides[e.activeIndex],y?parseInt(y.getAttribute("data-swiper-autoplay"),10):void 0},X=y=>{if(e.destroyed||!e.autoplay.running)return;cancelAnimationFrame(w),p();let Y=typeof y>"u"?e.params.autoplay.delay:y;d=e.params.autoplay.delay,l=e.params.autoplay.delay;const R=C();!Number.isNaN(R)&&R>0&&typeof y>"u"&&(Y=R,d=R,l=R),n=Y;const W=e.params.speed,N=()=>{!e||e.destroyed||(e.params.autoplay.reverseDirection?!e.isBeginning||e.params.loop||e.params.rewind?(e.slidePrev(W,!0,!0),b("autoplay")):e.params.autoplay.stopOnLastSlide||(e.slideTo(e.slides.length-1,W,!0,!0),b("autoplay")):!e.isEnd||e.params.loop||e.params.rewind?(e.slideNext(W,!0,!0),b("autoplay")):e.params.autoplay.stopOnLastSlide||(e.slideTo(0,W,!0,!0),b("autoplay")),e.params.cssMode&&(f=new Date().getTime(),requestAnimationFrame(()=>{X()})))};return Y>0?(clearTimeout(v),v=setTimeout(()=>{N()},Y)):requestAnimationFrame(()=>{N()}),Y},A=()=>{f=new Date().getTime(),e.autoplay.running=!0,X(),b("autoplayStart")},O=()=>{e.autoplay.running=!1,clearTimeout(v),cancelAnimationFrame(w),b("autoplayStop")},E=(y,Y)=>{if(e.destroyed||!e.autoplay.running)return;clearTimeout(v),y||(c=!0);const R=()=>{b("autoplayPause"),e.params.autoplay.waitForTransition?e.wrapperEl.addEventListener("transitionend",s):I()};if(e.autoplay.paused=!0,Y){r&&(n=e.params.autoplay.delay),r=!1,R();return}n=(n||e.params.autoplay.delay)-(new Date().getTime()-f),!(e.isEnd&&n<0&&!e.params.loop)&&(n<0&&(n=0),R())},I=()=>{e.isEnd&&n<0&&!e.params.loop||e.destroyed||!e.autoplay.running||(f=new Date().getTime(),c?(c=!1,X(n)):X(),e.autoplay.paused=!1,b("autoplayResume"))},H=()=>{if(e.destroyed||!e.autoplay.running)return;const y=U();y.visibilityState==="hidden"&&(c=!0,E(!0)),y.visibilityState==="visible"&&I()},P=y=>{y.pointerType==="mouse"&&(c=!0,i=!0,!(e.animating||e.autoplay.paused)&&E(!0))},M=y=>{y.pointerType==="mouse"&&(i=!1,e.autoplay.paused&&I())},x=()=>{e.params.autoplay.pauseOnMouseEnter&&(e.el.addEventListener("pointerenter",P),e.el.addEventListener("pointerleave",M))},z=()=>{e.el&&typeof e.el!="string"&&(e.el.removeEventListener("pointerenter",P),e.el.removeEventListener("pointerleave",M))},k=()=>{U().addEventListener("visibilitychange",H)},h=()=>{U().removeEventListener("visibilitychange",H)};m("init",()=>{e.params.autoplay.enabled&&(x(),k(),A())}),m("destroy",()=>{z(),h(),e.autoplay.running&&O()}),m("_freeModeStaticRelease",()=>{(a||c)&&I()}),m("_freeModeNoMomentumRelease",()=>{e.params.autoplay.disableOnInteraction?O():E(!0,!0)}),m("beforeTransitionStart",(y,Y,R)=>{e.destroyed||!e.autoplay.running||(R||!e.params.autoplay.disableOnInteraction?E(!0,!0):O())}),m("sliderFirstMove",()=>{if(!(e.destroyed||!e.autoplay.running)){if(e.params.autoplay.disableOnInteraction){O();return}o=!0,a=!1,c=!1,t=setTimeout(()=>{c=!0,a=!0,E(!0)},200)}}),m("touchEnd",()=>{if(!(e.destroyed||!e.autoplay.running||!o)){if(clearTimeout(t),clearTimeout(v),e.params.autoplay.disableOnInteraction){a=!1,o=!1;return}a&&e.params.cssMode&&I(),a=!1,o=!1}}),m("slideChange",()=>{e.destroyed||!e.autoplay.running||(r=!0)}),Object.assign(e.autoplay,{start:A,stop:O,pause:E,resume:I})}function tt(L){let{swiper:e,extendParams:S,on:m}=L;S({thumbs:{swiper:null,multipleActiveThumbs:!0,autoScrollOffset:0,slideThumbActiveClass:"swiper-slide-thumb-active",thumbsContainerClass:"swiper-thumbs"}});let b=!1,g=!1;e.thumbs={swiper:null};function v(){const l=e.thumbs.swiper;if(!l||l.destroyed)return;const n=l.clickedIndex,f=l.clickedSlide;if(f&&f.classList.contains(e.params.thumbs.slideThumbActiveClass)||typeof n>"u"||n===null)return;let u;l.params.loop?u=parseInt(l.clickedSlide.getAttribute("data-swiper-slide-index"),10):u=n,e.params.loop?e.slideToLoop(u):e.slideTo(u)}function w(){const{thumbs:l}=e.params;if(b)return!1;b=!0;const n=e.constructor;if(l.swiper instanceof n){if(l.swiper.destroyed)return b=!1,!1;e.thumbs.swiper=l.swiper,Object.assign(e.thumbs.swiper.originalParams,{watchSlidesProgress:!0,slideToClickedSlide:!1}),Object.assign(e.thumbs.swiper.params,{watchSlidesProgress:!0,slideToClickedSlide:!1}),e.thumbs.swiper.update()}else if(Oe(l.swiper)){const f=Object.assign({},l.swiper);Object.assign(f,{watchSlidesProgress:!0,slideToClickedSlide:!1}),e.thumbs.swiper=new n(f),g=!0}return e.thumbs.swiper.el.classList.add(e.params.thumbs.thumbsContainerClass),e.thumbs.swiper.on("tap",v),!0}function d(l){const n=e.thumbs.swiper;if(!n||n.destroyed)return;const f=n.params.slidesPerView==="auto"?n.slidesPerViewDynamic():n.params.slidesPerView;let u=1;const o=e.params.thumbs.slideThumbActiveClass;if(e.params.slidesPerView>1&&!e.params.centeredSlides&&(u=e.params.slidesPerView),e.params.thumbs.multipleActiveThumbs||(u=1),u=Math.floor(u),n.slides.forEach(r=>r.classList.remove(o)),n.params.loop||n.params.virtual&&n.params.virtual.enabled)for(let r=0;r<u;r+=1)se(n.slidesEl,`[data-swiper-slide-index="${e.realIndex+r}"]`).forEach(c=>{c.classList.add(o)});else for(let r=0;r<u;r+=1)n.slides[e.realIndex+r]&&n.slides[e.realIndex+r].classList.add(o);const a=e.params.thumbs.autoScrollOffset,t=a&&!n.params.loop;if(e.realIndex!==n.realIndex||t){const r=n.activeIndex;let c,i;if(n.params.loop){const s=n.slides.find(p=>p.getAttribute("data-swiper-slide-index")===`${e.realIndex}`);c=n.slides.indexOf(s),i=e.activeIndex>e.previousIndex?"next":"prev"}else c=e.realIndex,i=c>e.previousIndex?"next":"prev";t&&(c+=i==="next"?a:-1*a),n.visibleSlidesIndexes&&n.visibleSlidesIndexes.indexOf(c)<0&&(n.params.centeredSlides?c>r?c=c-Math.floor(f/2)+1:c=c+Math.floor(f/2)-1:c>r&&n.params.slidesPerGroup,n.slideTo(c,l?0:void 0))}}m("beforeInit",()=>{const{thumbs:l}=e.params;if(!(!l||!l.swiper))if(typeof l.swiper=="string"||l.swiper instanceof HTMLElement){const n=U(),f=()=>{const o=typeof l.swiper=="string"?n.querySelector(l.swiper):l.swiper;if(o&&o.swiper)l.swiper=o.swiper,w(),d(!0);else if(o){const a=`${e.params.eventsPrefix}init`,t=r=>{l.swiper=r.detail[0],o.removeEventListener(a,t),w(),d(!0),l.swiper.update(),e.update()};o.addEventListener(a,t)}return o},u=()=>{if(e.destroyed)return;f()||requestAnimationFrame(u)};requestAnimationFrame(u)}else w(),d(!0)}),m("slideChange update resize observerUpdate",()=>{d()}),m("setTransition",(l,n)=>{const f=e.thumbs.swiper;!f||f.destroyed||f.setTransition(n)}),m("beforeDestroy",()=>{const l=e.thumbs.swiper;!l||l.destroyed||g&&l.destroy()}),Object.assign(e.thumbs,{init:w,update:d})}function at(L){let{swiper:e,extendParams:S,emit:m,once:b}=L;S({freeMode:{enabled:!1,momentum:!0,momentumRatio:1,momentumBounce:!0,momentumBounceRatio:1,momentumVelocityRatio:1,sticky:!1,minimumVelocity:.02}});function g(){if(e.params.cssMode)return;const d=e.getTranslate();e.setTranslate(d),e.setTransition(0),e.touchEventsData.velocities.length=0,e.freeMode.onTouchEnd({currentPos:e.rtl?e.translate:-e.translate})}function v(){if(e.params.cssMode)return;const{touchEventsData:d,touches:l}=e;d.velocities.length===0&&d.velocities.push({position:l[e.isHorizontal()?"startX":"startY"],time:d.touchStartTime}),d.velocities.push({position:l[e.isHorizontal()?"currentX":"currentY"],time:te()})}function w(d){let{currentPos:l}=d;if(e.params.cssMode)return;const{params:n,wrapperEl:f,rtlTranslate:u,snapGrid:o,touchEventsData:a}=e,r=te()-a.touchStartTime;if(l<-e.minTranslate()){e.slideTo(e.activeIndex);return}if(l>-e.maxTranslate()){e.slides.length<o.length?e.slideTo(o.length-1):e.slideTo(e.slides.length-1);return}if(n.freeMode.momentum){if(a.velocities.length>1){const O=a.velocities.pop(),E=a.velocities.pop(),I=O.position-E.position,H=O.time-E.time;e.velocity=I/H,e.velocity/=2,Math.abs(e.velocity)<n.freeMode.minimumVelocity&&(e.velocity=0),(H>150||te()-O.time>300)&&(e.velocity=0)}else e.velocity=0;e.velocity*=n.freeMode.momentumVelocityRatio,a.velocities.length=0;let c=1e3*n.freeMode.momentumRatio;const i=e.velocity*c;let s=e.translate+i;u&&(s=-s);let p=!1,C;const X=Math.abs(e.velocity)*20*n.freeMode.momentumBounceRatio;let A;if(s<e.maxTranslate())n.freeMode.momentumBounce?(s+e.maxTranslate()<-X&&(s=e.maxTranslate()-X),C=e.maxTranslate(),p=!0,a.allowMomentumBounce=!0):s=e.maxTranslate(),n.loop&&n.centeredSlides&&(A=!0);else if(s>e.minTranslate())n.freeMode.momentumBounce?(s-e.minTranslate()>X&&(s=e.minTranslate()+X),C=e.minTranslate(),p=!0,a.allowMomentumBounce=!0):s=e.minTranslate(),n.loop&&n.centeredSlides&&(A=!0);else if(n.freeMode.sticky){let O;for(let E=0;E<o.length;E+=1)if(o[E]>-s){O=E;break}Math.abs(o[O]-s)<Math.abs(o[O-1]-s)||e.swipeDirection==="next"?s=o[O]:s=o[O-1],s=-s}if(A&&b("transitionEnd",()=>{e.loopFix()}),e.velocity!==0){if(u?c=Math.abs((-s-e.translate)/e.velocity):c=Math.abs((s-e.translate)/e.velocity),n.freeMode.sticky){const O=Math.abs((u?-s:s)-e.translate),E=e.slidesSizesGrid[e.activeIndex];O<E?c=n.speed:O<2*E?c=n.speed*1.5:c=n.speed*2.5}}else if(n.freeMode.sticky){e.slideToClosest();return}n.freeMode.momentumBounce&&p?(e.updateProgress(C),e.setTransition(c),e.setTranslate(s),e.transitionStart(!0,e.swipeDirection),e.animating=!0,he(f,()=>{!e||e.destroyed||!a.allowMomentumBounce||(m("momentumBounce"),e.setTransition(n.speed),setTimeout(()=>{e.setTranslate(C),he(f,()=>{!e||e.destroyed||e.transitionEnd()})},0))})):e.velocity?(m("_freeModeNoMomentumRelease"),e.updateProgress(s),e.setTransition(c),e.setTranslate(s),e.transitionStart(!0,e.swipeDirection),e.animating||(e.animating=!0,he(f,()=>{!e||e.destroyed||e.transitionEnd()}))):e.updateProgress(s),e.updateActiveIndex(),e.updateSlidesClasses()}else if(n.freeMode.sticky){e.slideToClosest();return}else n.freeMode&&m("_freeModeNoMomentumRelease");(!n.freeMode.momentum||r>=n.longSwipesMs)&&(m("_freeModeStaticRelease"),e.updateProgress(),e.updateActiveIndex(),e.updateSlidesClasses())}Object.assign(e,{freeMode:{onTouchStart:g,onTouchMove:v,onTouchEnd:w}})}function st(L){let{swiper:e,extendParams:S,on:m}=L;S({grid:{rows:1,fill:"column"}});let b,g,v,w;const d=()=>{let t=e.params.spaceBetween;return typeof t=="string"&&t.indexOf("%")>=0?t=parseFloat(t.replace("%",""))/100*e.size:typeof t=="string"&&(t=parseFloat(t)),t},l=t=>{const{slidesPerView:r}=e.params,{rows:c,fill:i}=e.params.grid,s=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:t.length;v=Math.floor(s/c),Math.floor(s/c)===s/c?b=s:b=Math.ceil(s/c)*c,r!=="auto"&&i==="row"&&(b=Math.max(b,r*c)),g=b/c},n=()=>{e.slides&&e.slides.forEach(t=>{t.swiperSlideGridSet&&(t.style.height="",t.style[e.getDirectionLabel("margin-top")]="")})},f=(t,r,c)=>{const{slidesPerGroup:i}=e.params,s=d(),{rows:p,fill:C}=e.params.grid,X=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:c.length;let A,O,E;if(C==="row"&&i>1){const I=Math.floor(t/(i*p)),H=t-p*i*I,P=I===0?i:Math.min(Math.ceil((X-I*p*i)/p),i);E=Math.floor(H/P),O=H-E*P+I*i,A=O+E*b/p,r.style.order=A}else C==="column"?(O=Math.floor(t/p),E=t-O*p,(O>v||O===v&&E===p-1)&&(E+=1,E>=p&&(E=0,O+=1))):(E=Math.floor(t/g),O=t-E*g);r.row=E,r.column=O,r.style.height=`calc((100% - ${(p-1)*s}px) / ${p})`,r.style[e.getDirectionLabel("margin-top")]=E!==0?s&&`${s}px`:"",r.swiperSlideGridSet=!0},u=(t,r)=>{const{centeredSlides:c,roundLengths:i}=e.params,s=d(),{rows:p}=e.params.grid;if(e.virtualSize=(t+s)*b,e.virtualSize=Math.ceil(e.virtualSize/p)-s,e.params.cssMode||(e.wrapperEl.style[e.getDirectionLabel("width")]=`${e.virtualSize+s}px`),c){const C=[];for(let X=0;X<r.length;X+=1){let A=r[X];i&&(A=Math.floor(A)),r[X]<e.virtualSize+r[0]&&C.push(A)}r.splice(0,r.length),r.push(...C)}},o=()=>{w=e.params.grid&&e.params.grid.rows>1},a=()=>{const{params:t,el:r}=e,c=t.grid&&t.grid.rows>1;w&&!c?(r.classList.remove(`${t.containerModifierClass}grid`,`${t.containerModifierClass}grid-column`),v=1,e.emitContainerClasses()):!w&&c&&(r.classList.add(`${t.containerModifierClass}grid`),t.grid.fill==="column"&&r.classList.add(`${t.containerModifierClass}grid-column`),e.emitContainerClasses()),w=c};m("init",o),m("update",a),e.grid={initSlides:l,unsetSlides:n,updateSlide:f,updateWrapperSize:u}}function He(L){const e=this,{params:S,slidesEl:m}=e;S.loop&&e.loopDestroy();const b=g=>{if(typeof g=="string"){const v=document.createElement("div");v.innerHTML=g,m.append(v.children[0]),v.innerHTML=""}else m.append(g)};if(typeof L=="object"&&"length"in L)for(let g=0;g<L.length;g+=1)L[g]&&b(L[g]);else b(L);e.recalcSlides(),S.loop&&e.loopCreate(),(!S.observer||e.isElement)&&e.update()}function Ye(L){const e=this,{params:S,activeIndex:m,slidesEl:b}=e;S.loop&&e.loopDestroy();let g=m+1;const v=w=>{if(typeof w=="string"){const d=document.createElement("div");d.innerHTML=w,b.prepend(d.children[0]),d.innerHTML=""}else b.prepend(w)};if(typeof L=="object"&&"length"in L){for(let w=0;w<L.length;w+=1)L[w]&&v(L[w]);g=m+L.length}else v(L);e.recalcSlides(),S.loop&&e.loopCreate(),(!S.observer||e.isElement)&&e.update(),e.slideTo(g,0,!1)}function ke(L,e){const S=this,{params:m,activeIndex:b,slidesEl:g}=S;let v=b;m.loop&&(v-=S.loopedSlides,S.loopDestroy(),S.recalcSlides());const w=S.slides.length;if(L<=0){S.prependSlide(e);return}if(L>=w){S.appendSlide(e);return}let d=v>L?v+1:v;const l=[];for(let n=w-1;n>=L;n-=1){const f=S.slides[n];f.remove(),l.unshift(f)}if(typeof e=="object"&&"length"in e){for(let n=0;n<e.length;n+=1)e[n]&&g.append(e[n]);d=v>L?v+e.length:v}else g.append(e);for(let n=0;n<l.length;n+=1)g.append(l[n]);S.recalcSlides(),m.loop&&S.loopCreate(),(!m.observer||S.isElement)&&S.update(),m.loop?S.slideTo(d+S.loopedSlides,0,!1):S.slideTo(d,0,!1)}function Re(L){const e=this,{params:S,activeIndex:m}=e;let b=m;S.loop&&(b-=e.loopedSlides,e.loopDestroy());let g=b,v;if(typeof L=="object"&&"length"in L){for(let w=0;w<L.length;w+=1)v=L[w],e.slides[v]&&e.slides[v].remove(),v<g&&(g-=1);g=Math.max(g,0)}else v=L,e.slides[v]&&e.slides[v].remove(),v<g&&(g-=1),g=Math.max(g,0);e.recalcSlides(),S.loop&&e.loopCreate(),(!S.observer||e.isElement)&&e.update(),S.loop?e.slideTo(g+e.loopedSlides,0,!1):e.slideTo(g,0,!1)}function Be(){const L=this,e=[];for(let S=0;S<L.slides.length;S+=1)e.push(S);L.removeSlide(e)}function it(L){let{swiper:e}=L;Object.assign(e,{appendSlide:He.bind(e),prependSlide:Ye.bind(e),addSlide:ke.bind(e),removeSlide:Re.bind(e),removeAllSlides:Be.bind(e)})}function pe(L){const{effect:e,swiper:S,on:m,setTranslate:b,setTransition:g,overwriteParams:v,perspective:w,recreateShadows:d,getEffectParams:l}=L;m("beforeInit",()=>{if(S.params.effect!==e)return;S.classNames.push(`${S.params.containerModifierClass}${e}`),w&&w()&&S.classNames.push(`${S.params.containerModifierClass}3d`);const f=v?v():{};Object.assign(S.params,f),Object.assign(S.originalParams,f)}),m("setTranslate",()=>{S.params.effect===e&&b()}),m("setTransition",(f,u)=>{S.params.effect===e&&g(u)}),m("transitionEnd",()=>{if(S.params.effect===e&&d){if(!l||!l().slideShadows)return;S.slides.forEach(f=>{f.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(u=>u.remove())}),d()}});let n;m("virtualUpdate",()=>{S.params.effect===e&&(S.slides.length||(n=!0),requestAnimationFrame(()=>{n&&S.slides&&S.slides.length&&(b(),n=!1)}))})}function ge(L,e){const S=re(e);return S!==e&&(S.style.backfaceVisibility="hidden",S.style["-webkit-backface-visibility"]="hidden"),S}function Se(L){let{swiper:e,duration:S,transformElements:m,allSlides:b}=L;const{activeIndex:g}=e,v=w=>w.parentElement?w.parentElement:e.slides.find(l=>l.shadowRoot&&l.shadowRoot===w.parentNode);if(e.params.virtualTranslate&&S!==0){let w=!1,d;b?d=m:d=m.filter(l=>{const n=l.classList.contains("swiper-slide-transform")?v(l):l;return e.getSlideIndex(n)===g}),d.forEach(l=>{he(l,()=>{if(w||!e||e.destroyed)return;w=!0,e.animating=!1;const n=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0});e.wrapperEl.dispatchEvent(n)})})}}function rt(L){let{swiper:e,extendParams:S,on:m}=L;S({fadeEffect:{crossFade:!1}}),pe({effect:"fade",swiper:e,on:m,setTranslate:()=>{const{slides:v}=e,w=e.params.fadeEffect;for(let d=0;d<v.length;d+=1){const l=e.slides[d];let f=-l.swiperSlideOffset;e.params.virtualTranslate||(f-=e.translate);let u=0;e.isHorizontal()||(u=f,f=0);const o=e.params.fadeEffect.crossFade?Math.max(1-Math.abs(l.progress),0):1+Math.min(Math.max(l.progress,-1),0),a=ge(w,l);a.style.opacity=o,a.style.transform=`translate3d(${f}px, ${u}px, 0px)`}},setTransition:v=>{const w=e.slides.map(d=>re(d));w.forEach(d=>{d.style.transitionDuration=`${v}ms`}),Se({swiper:e,duration:v,transformElements:w,allSlides:!0})},overwriteParams:()=>({slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!e.params.cssMode})})}function nt(L){let{swiper:e,extendParams:S,on:m}=L;S({cubeEffect:{slideShadows:!0,shadow:!0,shadowOffset:20,shadowScale:.94}});const b=(d,l,n)=>{let f=n?d.querySelector(".swiper-slide-shadow-left"):d.querySelector(".swiper-slide-shadow-top"),u=n?d.querySelector(".swiper-slide-shadow-right"):d.querySelector(".swiper-slide-shadow-bottom");f||(f=_("div",`swiper-slide-shadow-cube swiper-slide-shadow-${n?"left":"top"}`.split(" ")),d.append(f)),u||(u=_("div",`swiper-slide-shadow-cube swiper-slide-shadow-${n?"right":"bottom"}`.split(" ")),d.append(u)),f&&(f.style.opacity=Math.max(-l,0)),u&&(u.style.opacity=Math.max(l,0))};pe({effect:"cube",swiper:e,on:m,setTranslate:()=>{const{el:d,wrapperEl:l,slides:n,width:f,height:u,rtlTranslate:o,size:a,browser:t}=e,r=xe(e),c=e.params.cubeEffect,i=e.isHorizontal(),s=e.virtual&&e.params.virtual.enabled;let p=0,C;c.shadow&&(i?(C=e.wrapperEl.querySelector(".swiper-cube-shadow"),C||(C=_("div","swiper-cube-shadow"),e.wrapperEl.append(C)),C.style.height=`${f}px`):(C=d.querySelector(".swiper-cube-shadow"),C||(C=_("div","swiper-cube-shadow"),d.append(C))));for(let A=0;A<n.length;A+=1){const O=n[A];let E=A;s&&(E=parseInt(O.getAttribute("data-swiper-slide-index"),10));let I=E*90,H=Math.floor(I/360);o&&(I=-I,H=Math.floor(-I/360));const P=Math.max(Math.min(O.progress,1),-1);let M=0,x=0,z=0;E%4===0?(M=-H*4*a,z=0):(E-1)%4===0?(M=0,z=-H*4*a):(E-2)%4===0?(M=a+H*4*a,z=a):(E-3)%4===0&&(M=-a,z=3*a+a*4*H),o&&(M=-M),i||(x=M,M=0);const k=`rotateX(${r(i?0:-I)}deg) rotateY(${r(i?I:0)}deg) translate3d(${M}px, ${x}px, ${z}px)`;P<=1&&P>-1&&(p=E*90+P*90,o&&(p=-E*90-P*90)),O.style.transform=k,c.slideShadows&&b(O,P,i)}if(l.style.transformOrigin=`50% 50% -${a/2}px`,l.style["-webkit-transform-origin"]=`50% 50% -${a/2}px`,c.shadow)if(i)C.style.transform=`translate3d(0px, ${f/2+c.shadowOffset}px, ${-f/2}px) rotateX(89.99deg) rotateZ(0deg) scale(${c.shadowScale})`;else{const A=Math.abs(p)-Math.floor(Math.abs(p)/90)*90,O=1.5-(Math.sin(A*2*Math.PI/360)/2+Math.cos(A*2*Math.PI/360)/2),E=c.shadowScale,I=c.shadowScale/O,H=c.shadowOffset;C.style.transform=`scale3d(${E}, 1, ${I}) translate3d(0px, ${u/2+H}px, ${-u/2/I}px) rotateX(-89.99deg)`}const X=(t.isSafari||t.isWebView)&&t.needPerspectiveFix?-a/2:0;l.style.transform=`translate3d(0px,0,${X}px) rotateX(${r(e.isHorizontal()?0:p)}deg) rotateY(${r(e.isHorizontal()?-p:0)}deg)`,l.style.setProperty("--swiper-cube-translate-z",`${X}px`)},setTransition:d=>{const{el:l,slides:n}=e;if(n.forEach(f=>{f.style.transitionDuration=`${d}ms`,f.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(u=>{u.style.transitionDuration=`${d}ms`})}),e.params.cubeEffect.shadow&&!e.isHorizontal()){const f=l.querySelector(".swiper-cube-shadow");f&&(f.style.transitionDuration=`${d}ms`)}},recreateShadows:()=>{const d=e.isHorizontal();e.slides.forEach(l=>{const n=Math.max(Math.min(l.progress,1),-1);b(l,n,d)})},getEffectParams:()=>e.params.cubeEffect,perspective:()=>!0,overwriteParams:()=>({slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,resistanceRatio:0,spaceBetween:0,centeredSlides:!1,virtualTranslate:!0})})}function de(L,e,S){const m=`swiper-slide-shadow${S?`-${S}`:""}${L?` swiper-slide-shadow-${L}`:""}`,b=re(e);let g=b.querySelector(`.${m.split(" ").join(".")}`);return g||(g=_("div",m.split(" ")),b.append(g)),g}function lt(L){let{swiper:e,extendParams:S,on:m}=L;S({flipEffect:{slideShadows:!0,limitRotation:!0}});const b=(d,l)=>{let n=e.isHorizontal()?d.querySelector(".swiper-slide-shadow-left"):d.querySelector(".swiper-slide-shadow-top"),f=e.isHorizontal()?d.querySelector(".swiper-slide-shadow-right"):d.querySelector(".swiper-slide-shadow-bottom");n||(n=de("flip",d,e.isHorizontal()?"left":"top")),f||(f=de("flip",d,e.isHorizontal()?"right":"bottom")),n&&(n.style.opacity=Math.max(-l,0)),f&&(f.style.opacity=Math.max(l,0))};pe({effect:"flip",swiper:e,on:m,setTranslate:()=>{const{slides:d,rtlTranslate:l}=e,n=e.params.flipEffect,f=xe(e);for(let u=0;u<d.length;u+=1){const o=d[u];let a=o.progress;e.params.flipEffect.limitRotation&&(a=Math.max(Math.min(o.progress,1),-1));const t=o.swiperSlideOffset;let c=-180*a,i=0,s=e.params.cssMode?-t-e.translate:-t,p=0;e.isHorizontal()?l&&(c=-c):(p=s,s=0,i=-c,c=0),o.style.zIndex=-Math.abs(Math.round(a))+d.length,n.slideShadows&&b(o,a);const C=`translate3d(${s}px, ${p}px, 0px) rotateX(${f(i)}deg) rotateY(${f(c)}deg)`,X=ge(n,o);X.style.transform=C}},setTransition:d=>{const l=e.slides.map(n=>re(n));l.forEach(n=>{n.style.transitionDuration=`${d}ms`,n.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(f=>{f.style.transitionDuration=`${d}ms`})}),Se({swiper:e,duration:d,transformElements:l})},recreateShadows:()=>{e.params.flipEffect,e.slides.forEach(d=>{let l=d.progress;e.params.flipEffect.limitRotation&&(l=Math.max(Math.min(d.progress,1),-1)),b(d,l)})},getEffectParams:()=>e.params.flipEffect,perspective:()=>!0,overwriteParams:()=>({slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!e.params.cssMode})})}function ot(L){let{swiper:e,extendParams:S,on:m}=L;S({coverflowEffect:{rotate:50,stretch:0,depth:100,scale:1,modifier:1,slideShadows:!0}}),pe({effect:"coverflow",swiper:e,on:m,setTranslate:()=>{const{width:v,height:w,slides:d,slidesSizesGrid:l}=e,n=e.params.coverflowEffect,f=e.isHorizontal(),u=e.translate,o=f?-u+v/2:-u+w/2,a=f?n.rotate:-n.rotate,t=n.depth,r=xe(e);for(let c=0,i=d.length;c<i;c+=1){const s=d[c],p=l[c],C=s.swiperSlideOffset,X=(o-C-p/2)/p,A=typeof n.modifier=="function"?n.modifier(X):X*n.modifier;let O=f?a*A:0,E=f?0:a*A,I=-t*Math.abs(A),H=n.stretch;typeof H=="string"&&H.indexOf("%")!==-1&&(H=parseFloat(n.stretch)/100*p);let P=f?0:H*A,M=f?H*A:0,x=1-(1-n.scale)*Math.abs(A);Math.abs(M)<.001&&(M=0),Math.abs(P)<.001&&(P=0),Math.abs(I)<.001&&(I=0),Math.abs(O)<.001&&(O=0),Math.abs(E)<.001&&(E=0),Math.abs(x)<.001&&(x=0);const z=`translate3d(${M}px,${P}px,${I}px)  rotateX(${r(E)}deg) rotateY(${r(O)}deg) scale(${x})`,k=ge(n,s);if(k.style.transform=z,s.style.zIndex=-Math.abs(Math.round(A))+1,n.slideShadows){let h=f?s.querySelector(".swiper-slide-shadow-left"):s.querySelector(".swiper-slide-shadow-top"),y=f?s.querySelector(".swiper-slide-shadow-right"):s.querySelector(".swiper-slide-shadow-bottom");h||(h=de("coverflow",s,f?"left":"top")),y||(y=de("coverflow",s,f?"right":"bottom")),h&&(h.style.opacity=A>0?A:0),y&&(y.style.opacity=-A>0?-A:0)}}},setTransition:v=>{e.slides.map(d=>re(d)).forEach(d=>{d.style.transitionDuration=`${v}ms`,d.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(l=>{l.style.transitionDuration=`${v}ms`})})},perspective:()=>!0,overwriteParams:()=>({watchSlidesProgress:!0})})}function ct(L){let{swiper:e,extendParams:S,on:m}=L;S({creativeEffect:{limitProgress:1,shadowPerProgress:!1,progressMultiplier:1,perspective:!0,prev:{translate:[0,0,0],rotate:[0,0,0],opacity:1,scale:1},next:{translate:[0,0,0],rotate:[0,0,0],opacity:1,scale:1}}});const b=w=>typeof w=="string"?w:`${w}px`;pe({effect:"creative",swiper:e,on:m,setTranslate:()=>{const{slides:w,wrapperEl:d,slidesSizesGrid:l}=e,n=e.params.creativeEffect,{progressMultiplier:f}=n,u=e.params.centeredSlides,o=xe(e);if(u){const a=l[0]/2-e.params.slidesOffsetBefore||0;d.style.transform=`translateX(calc(50% - ${a}px))`}for(let a=0;a<w.length;a+=1){const t=w[a],r=t.progress,c=Math.min(Math.max(t.progress,-n.limitProgress),n.limitProgress);let i=c;u||(i=Math.min(Math.max(t.originalProgress,-n.limitProgress),n.limitProgress));const s=t.swiperSlideOffset,p=[e.params.cssMode?-s-e.translate:-s,0,0],C=[0,0,0];let X=!1;e.isHorizontal()||(p[1]=p[0],p[0]=0);let A={translate:[0,0,0],rotate:[0,0,0],scale:1,opacity:1};c<0?(A=n.next,X=!0):c>0&&(A=n.prev,X=!0),p.forEach((x,z)=>{p[z]=`calc(${x}px + (${b(A.translate[z])} * ${Math.abs(c*f)}))`}),C.forEach((x,z)=>{let k=A.rotate[z]*Math.abs(c*f);C[z]=k}),t.style.zIndex=-Math.abs(Math.round(r))+w.length;const O=p.join(", "),E=`rotateX(${o(C[0])}deg) rotateY(${o(C[1])}deg) rotateZ(${o(C[2])}deg)`,I=i<0?`scale(${1+(1-A.scale)*i*f})`:`scale(${1-(1-A.scale)*i*f})`,H=i<0?1+(1-A.opacity)*i*f:1-(1-A.opacity)*i*f,P=`translate3d(${O}) ${E} ${I}`;if(X&&A.shadow||!X){let x=t.querySelector(".swiper-slide-shadow");if(!x&&A.shadow&&(x=de("creative",t)),x){const z=n.shadowPerProgress?c*(1/n.limitProgress):c;x.style.opacity=Math.min(Math.max(Math.abs(z),0),1)}}const M=ge(n,t);M.style.transform=P,M.style.opacity=H,A.origin&&(M.style.transformOrigin=A.origin)}},setTransition:w=>{const d=e.slides.map(l=>re(l));d.forEach(l=>{l.style.transitionDuration=`${w}ms`,l.querySelectorAll(".swiper-slide-shadow").forEach(n=>{n.style.transitionDuration=`${w}ms`})}),Se({swiper:e,duration:w,transformElements:d,allSlides:!0})},perspective:()=>e.params.creativeEffect.perspective,overwriteParams:()=>({watchSlidesProgress:!0,virtualTranslate:!e.params.cssMode})})}function dt(L){let{swiper:e,extendParams:S,on:m}=L;S({cardsEffect:{slideShadows:!0,rotate:!0,perSlideRotate:2,perSlideOffset:8}}),pe({effect:"cards",swiper:e,on:m,setTranslate:()=>{const{slides:v,activeIndex:w,rtlTranslate:d}=e,l=e.params.cardsEffect,{startTranslate:n,isTouched:f}=e.touchEventsData,u=d?-e.translate:e.translate;for(let o=0;o<v.length;o+=1){const a=v[o],t=a.progress,r=Math.min(Math.max(t,-4),4);let c=a.swiperSlideOffset;e.params.centeredSlides&&!e.params.cssMode&&(e.wrapperEl.style.transform=`translateX(${e.minTranslate()}px)`),e.params.centeredSlides&&e.params.cssMode&&(c-=v[0].swiperSlideOffset);let i=e.params.cssMode?-c-e.translate:-c,s=0;const p=-100*Math.abs(r);let C=1,X=-l.perSlideRotate*r,A=l.perSlideOffset-Math.abs(r)*.75;const O=e.virtual&&e.params.virtual.enabled?e.virtual.from+o:o,E=(O===w||O===w-1)&&r>0&&r<1&&(f||e.params.cssMode)&&u<n,I=(O===w||O===w+1)&&r<0&&r>-1&&(f||e.params.cssMode)&&u>n;if(E||I){const x=(1-Math.abs((Math.abs(r)-.5)/.5))**.5;X+=-28*r*x,C+=-.5*x,A+=96*x,s=`${-25*x*Math.abs(r)}%`}if(r<0?i=`calc(${i}px ${d?"-":"+"} (${A*Math.abs(r)}%))`:r>0?i=`calc(${i}px ${d?"-":"+"} (-${A*Math.abs(r)}%))`:i=`${i}px`,!e.isHorizontal()){const x=s;s=i,i=x}const H=r<0?`${1+(1-C)*r}`:`${1-(1-C)*r}`,P=`
        translate3d(${i}, ${s}, ${p}px)
        rotateZ(${l.rotate?d?-X:X:0}deg)
        scale(${H})
      `;if(l.slideShadows){let x=a.querySelector(".swiper-slide-shadow");x||(x=de("cards",a)),x&&(x.style.opacity=Math.min(Math.max((Math.abs(r)-.5)/.5,0),1))}a.style.zIndex=-Math.abs(Math.round(t))+v.length;const M=ge(l,a);M.style.transform=P}},setTransition:v=>{const w=e.slides.map(d=>re(d));w.forEach(d=>{d.style.transitionDuration=`${v}ms`,d.querySelectorAll(".swiper-slide-shadow").forEach(l=>{l.style.transitionDuration=`${v}ms`})}),Se({swiper:e,duration:v,transformElements:w})},perspective:()=>!0,overwriteParams:()=>({_loopSwapReset:!1,watchSlidesProgress:!0,loopAdditionalSlides:e.params.cardsEffect.rotate?3:2,centeredSlides:!0,virtualTranslate:!e.params.cssMode})})}export{Ke as A11y,et as Autoplay,Ze as Controller,dt as EffectCards,ot as EffectCoverflow,ct as EffectCreative,nt as EffectCube,rt as EffectFade,lt as EffectFlip,at as FreeMode,st as Grid,Je as HashNavigation,Qe as History,Ne as Keyboard,it as Manipulation,Fe as Mousewheel,Ge as Navigation,je as Pagination,_e as Parallax,Ve as Scrollbar,tt as Thumbs,We as Virtual,Ue as Zoom};

# Vehicle Creation Tests

This directory contains feature tests for the vehicle creation functionality in the CarBnB application.

## Test Files

1. `VehicleCreationTest.php` - Tests basic vehicle creation functionality
2. `VehicleCreationFlowTest.php` - Tests the specific flow of creating a vehicle with required vehicle type, country, state, and city
3. `VehicleImageUploadTest.php` - Tests specifically focused on file uploads for vehicle images and documents

## Database Seeders

The tests use the following seeders to set up the test environment:

1. `LocationSeeder.php` - Creates countries, states, and cities
2. `VehicleTypeSeeder.php` - Creates vehicle types
3. `DocumentTypeSeeder.php` - Creates document types for vehicle documents

## Running the Tests

To run all the tests:

```bash
php artisan test
```

To run a specific test file:

```bash
php artisan test tests/Feature/VehicleCreationTest.php
```

To run a specific test:

```bash
php artisan test --filter="admin can create a vehicle with required fields"
```

## Test Coverage

The tests cover the following scenarios:

1. <PERSON><PERSON> can access the vehicle creation page
2. Ad<PERSON> can create a vehicle with all required fields
3. Vehicle creation fails if required fields are missing
4. Vehicle can be saved as a draft
5. Vehicle creation requires login
6. Vehicle creation requires selecting vehicle type, country, state, and city
7. Vehicle creation requires a primary image
8. Vehicle creation with primary image and gallery images
9. Vehicle creation with document uploads

## Notes

- The tests use the `RefreshDatabase` trait to reset the database between tests
- The tests create file records in the database to simulate file uploads
- The tests verify that vehicles are created with the correct status ('pending' or 'draft')
- The tests check that validation errors are returned when required fields are missing
- The tests verify that vehicle images and documents are properly associated with vehicles
- The tests handle the specific requirements for primary images in the vehicle creation process

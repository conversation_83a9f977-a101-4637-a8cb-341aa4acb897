<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('drivers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->text('bio')->nullable();
            $table->string('license_number');
            $table->date('license_expiry');
            $table->string('experience_years');
            $table->json('languages')->nullable();
            $table->decimal('hourly_rate', 10, 2)->nullable();
            $table->decimal('daily_rate', 10, 2);
            $table->boolean('available_weekdays')->default(true);
            $table->boolean('available_weekends')->default(true);
            $table->enum('status', ['pending', 'active', 'rejected', 'inactive'])->default('pending');
            $table->boolean('is_featured')->default(false);
            $table->foreignId('city_id')->nullable()->constrained('cities');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::disableForeignKeyConstraints();
        Schema::dropIfExists('drivers');
        Schema::enableForeignKeyConstraints();
    }
};

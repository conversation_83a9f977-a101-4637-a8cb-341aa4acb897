function de(i){return i!==null&&typeof i=="object"&&"constructor"in i&&i.constructor===Object}function le(i,e){i===void 0&&(i={}),e===void 0&&(e={});const t=["__proto__","constructor","prototype"];Object.keys(e).filter(s=>t.indexOf(s)<0).forEach(s=>{typeof i[s]>"u"?i[s]=e[s]:de(e[s])&&de(i[s])&&Object.keys(e[s]).length>0&&le(i[s],e[s])})}const ye={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector(){return null},querySelectorAll(){return[]},getElementById(){return null},createEvent(){return{initEvent(){}}},createElement(){return{children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName(){return[]}}},createElementNS(){return{}},importNode(){return null},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function j(){const i=typeof document<"u"?document:{};return le(i,ye),i}const Pe={document:ye,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle(){return{getPropertyValue(){return""}}},Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia(){return{}},requestAnimationFrame(i){return typeof setTimeout>"u"?(i(),null):setTimeout(i,0)},cancelAnimationFrame(i){typeof setTimeout>"u"||clearTimeout(i)}};function D(){const i=typeof window<"u"?window:{};return le(i,Pe),i}function Le(i){return i===void 0&&(i=""),i.trim().split(" ").filter(e=>!!e.trim())}function Oe(i){const e=i;Object.keys(e).forEach(t=>{try{e[t]=null}catch{}try{delete e[t]}catch{}})}function re(i,e){return e===void 0&&(e=0),setTimeout(i,e)}function U(){return Date.now()}function ze(i){const e=D();let t;return e.getComputedStyle&&(t=e.getComputedStyle(i,null)),!t&&i.currentStyle&&(t=i.currentStyle),t||(t=i.style),t}function Ae(i,e){e===void 0&&(e="x");const t=D();let s,r,n;const o=ze(i);return t.WebKitCSSMatrix?(r=o.transform||o.webkitTransform,r.split(",").length>6&&(r=r.split(", ").map(l=>l.replace(",",".")).join(", ")),n=new t.WebKitCSSMatrix(r==="none"?"":r)):(n=o.MozTransform||o.OTransform||o.MsTransform||o.msTransform||o.transform||o.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),s=n.toString().split(",")),e==="x"&&(t.WebKitCSSMatrix?r=n.m41:s.length===16?r=parseFloat(s[12]):r=parseFloat(s[4])),e==="y"&&(t.WebKitCSSMatrix?r=n.m42:s.length===16?r=parseFloat(s[13]):r=parseFloat(s[5])),r||0}function Y(i){return typeof i=="object"&&i!==null&&i.constructor&&Object.prototype.toString.call(i).slice(8,-1)==="Object"}function ke(i){return typeof window<"u"&&typeof window.HTMLElement<"u"?i instanceof HTMLElement:i&&(i.nodeType===1||i.nodeType===11)}function F(){const i=Object(arguments.length<=0?void 0:arguments[0]),e=["__proto__","constructor","prototype"];for(let t=1;t<arguments.length;t+=1){const s=t<0||arguments.length<=t?void 0:arguments[t];if(s!=null&&!ke(s)){const r=Object.keys(Object(s)).filter(n=>e.indexOf(n)<0);for(let n=0,o=r.length;n<o;n+=1){const l=r[n],d=Object.getOwnPropertyDescriptor(s,l);d!==void 0&&d.enumerable&&(Y(i[l])&&Y(s[l])?s[l].__swiper__?i[l]=s[l]:F(i[l],s[l]):!Y(i[l])&&Y(s[l])?(i[l]={},s[l].__swiper__?i[l]=s[l]:F(i[l],s[l])):i[l]=s[l])}}}return i}function X(i,e,t){i.style.setProperty(e,t)}function we(i){let{swiper:e,targetPosition:t,side:s}=i;const r=D(),n=-e.translate;let o=null,l;const d=e.params.speed;e.wrapperEl.style.scrollSnapType="none",r.cancelAnimationFrame(e.cssModeFrameID);const a=t>n?"next":"prev",c=(f,h)=>a==="next"&&f>=h||a==="prev"&&f<=h,u=()=>{l=new Date().getTime(),o===null&&(o=l);const f=Math.max(Math.min((l-o)/d,1),0),h=.5-Math.cos(f*Math.PI)/2;let p=n+h*(t-n);if(c(p,t)&&(p=t),e.wrapperEl.scrollTo({[s]:p}),c(p,t)){e.wrapperEl.style.overflow="hidden",e.wrapperEl.style.scrollSnapType="",setTimeout(()=>{e.wrapperEl.style.overflow="",e.wrapperEl.scrollTo({[s]:p})}),r.cancelAnimationFrame(e.cssModeFrameID);return}e.cssModeFrameID=r.requestAnimationFrame(u)};u()}function ti(i){return i.querySelector(".swiper-slide-transform")||i.shadowRoot&&i.shadowRoot.querySelector(".swiper-slide-transform")||i}function $(i,e){e===void 0&&(e="");const t=D(),s=[...i.children];return t.HTMLSlotElement&&i instanceof HTMLSlotElement&&s.push(...i.assignedElements()),e?s.filter(r=>r.matches(e)):s}function Ge(i,e){const t=[e];for(;t.length>0;){const s=t.shift();if(i===s)return!0;t.push(...s.children,...s.shadowRoot?s.shadowRoot.children:[],...s.assignedElements?s.assignedElements():[])}}function De(i,e){const t=D();let s=e.contains(i);return!s&&t.HTMLSlotElement&&e instanceof HTMLSlotElement&&(s=[...e.assignedElements()].includes(i),s||(s=Ge(i,e))),s}function K(i){try{console.warn(i);return}catch{}}function ne(i,e){e===void 0&&(e=[]);const t=document.createElement(i);return t.classList.add(...Array.isArray(e)?e:Le(e)),t}function ii(i){const e=D(),t=j(),s=i.getBoundingClientRect(),r=t.body,n=i.clientTop||r.clientTop||0,o=i.clientLeft||r.clientLeft||0,l=i===e?e.scrollY:i.scrollTop,d=i===e?e.scrollX:i.scrollLeft;return{top:s.top+l-n,left:s.left+d-o}}function _e(i,e){const t=[];for(;i.previousElementSibling;){const s=i.previousElementSibling;e?s.matches(e)&&t.push(s):t.push(s),i=s}return t}function Be(i,e){const t=[];for(;i.nextElementSibling;){const s=i.nextElementSibling;e?s.matches(e)&&t.push(s):t.push(s),i=s}return t}function W(i,e){return D().getComputedStyle(i,null).getPropertyValue(e)}function ce(i){let e=i,t;if(e){for(t=0;(e=e.previousSibling)!==null;)e.nodeType===1&&(t+=1);return t}}function Fe(i,e){const t=[];let s=i.parentElement;for(;s;)e?s.matches(e)&&t.push(s):t.push(s),s=s.parentElement;return t}function si(i,e){function t(s){s.target===i&&(e.call(i,s),i.removeEventListener("transitionend",t))}e&&i.addEventListener("transitionend",t)}function ue(i,e,t){const s=D();return i[e==="width"?"offsetWidth":"offsetHeight"]+parseFloat(s.getComputedStyle(i,null).getPropertyValue(e==="width"?"margin-right":"margin-top"))+parseFloat(s.getComputedStyle(i,null).getPropertyValue(e==="width"?"margin-left":"margin-bottom"))}function ri(i){return(Array.isArray(i)?i:[i]).filter(e=>!!e)}function ni(i){return e=>Math.abs(e)>0&&i.browser&&i.browser.need3dFix&&Math.abs(e)%90===0?e+.001:e}let Z;function Ve(){const i=D(),e=j();return{smoothScroll:e.documentElement&&e.documentElement.style&&"scrollBehavior"in e.documentElement.style,touch:!!("ontouchstart"in i||i.DocumentTouch&&e instanceof i.DocumentTouch)}}function be(){return Z||(Z=Ve()),Z}let Q;function He(i){let{userAgent:e}=i===void 0?{}:i;const t=be(),s=D(),r=s.navigator.platform,n=e||s.navigator.userAgent,o={ios:!1,android:!1},l=s.screen.width,d=s.screen.height,a=n.match(/(Android);?[\s\/]+([\d.]+)?/);let c=n.match(/(iPad).*OS\s([\d_]+)/);const u=n.match(/(iPod)(.*OS\s([\d_]+))?/),f=!c&&n.match(/(iPhone\sOS|iOS)\s([\d_]+)/),h=r==="Win32";let p=r==="MacIntel";const m=["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"];return!c&&p&&t.touch&&m.indexOf(`${l}x${d}`)>=0&&(c=n.match(/(Version)\/([\d.]+)/),c||(c=[0,1,"13_0_0"]),p=!1),a&&!h&&(o.os="android",o.android=!0),(c||f||u)&&(o.os="ios",o.ios=!0),o}function Se(i){return i===void 0&&(i={}),Q||(Q=He(i)),Q}let J;function $e(){const i=D(),e=Se();let t=!1;function s(){const l=i.navigator.userAgent.toLowerCase();return l.indexOf("safari")>=0&&l.indexOf("chrome")<0&&l.indexOf("android")<0}if(s()){const l=String(i.navigator.userAgent);if(l.includes("Version/")){const[d,a]=l.split("Version/")[1].split(" ")[0].split(".").map(c=>Number(c));t=d<16||d===16&&a<2}}const r=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(i.navigator.userAgent),n=s(),o=n||r&&e.ios;return{isSafari:t||n,needPerspectiveFix:t,need3dFix:o,isWebView:r}}function Te(){return J||(J=$e()),J}function Ne(i){let{swiper:e,on:t,emit:s}=i;const r=D();let n=null,o=null;const l=()=>{!e||e.destroyed||!e.initialized||(s("beforeResize"),s("resize"))},d=()=>{!e||e.destroyed||!e.initialized||(n=new ResizeObserver(u=>{o=r.requestAnimationFrame(()=>{const{width:f,height:h}=e;let p=f,m=h;u.forEach(x=>{let{contentBoxSize:g,contentRect:y,target:v}=x;v&&v!==e.el||(p=y?y.width:(g[0]||g).inlineSize,m=y?y.height:(g[0]||g).blockSize)}),(p!==f||m!==h)&&l()})}),n.observe(e.el))},a=()=>{o&&r.cancelAnimationFrame(o),n&&n.unobserve&&e.el&&(n.unobserve(e.el),n=null)},c=()=>{!e||e.destroyed||!e.initialized||s("orientationchange")};t("init",()=>{if(e.params.resizeObserver&&typeof r.ResizeObserver<"u"){d();return}r.addEventListener("resize",l),r.addEventListener("orientationchange",c)}),t("destroy",()=>{a(),r.removeEventListener("resize",l),r.removeEventListener("orientationchange",c)})}function Re(i){let{swiper:e,extendParams:t,on:s,emit:r}=i;const n=[],o=D(),l=function(c,u){u===void 0&&(u={});const f=o.MutationObserver||o.WebkitMutationObserver,h=new f(p=>{if(e.__preventObserver__)return;if(p.length===1){r("observerUpdate",p[0]);return}const m=function(){r("observerUpdate",p[0])};o.requestAnimationFrame?o.requestAnimationFrame(m):o.setTimeout(m,0)});h.observe(c,{attributes:typeof u.attributes>"u"?!0:u.attributes,childList:e.isElement||(typeof u.childList>"u"?!0:u).childList,characterData:typeof u.characterData>"u"?!0:u.characterData}),n.push(h)},d=()=>{if(e.params.observer){if(e.params.observeParents){const c=Fe(e.hostEl);for(let u=0;u<c.length;u+=1)l(c[u])}l(e.hostEl,{childList:e.params.observeSlideChildren}),l(e.wrapperEl,{attributes:!1})}},a=()=>{n.forEach(c=>{c.disconnect()}),n.splice(0,n.length)};t({observer:!1,observeParents:!1,observeSlideChildren:!1}),s("init",d),s("destroy",a)}var We={on(i,e,t){const s=this;if(!s.eventsListeners||s.destroyed||typeof e!="function")return s;const r=t?"unshift":"push";return i.split(" ").forEach(n=>{s.eventsListeners[n]||(s.eventsListeners[n]=[]),s.eventsListeners[n][r](e)}),s},once(i,e,t){const s=this;if(!s.eventsListeners||s.destroyed||typeof e!="function")return s;function r(){s.off(i,r),r.__emitterProxy&&delete r.__emitterProxy;for(var n=arguments.length,o=new Array(n),l=0;l<n;l++)o[l]=arguments[l];e.apply(s,o)}return r.__emitterProxy=e,s.on(i,r,t)},onAny(i,e){const t=this;if(!t.eventsListeners||t.destroyed||typeof i!="function")return t;const s=e?"unshift":"push";return t.eventsAnyListeners.indexOf(i)<0&&t.eventsAnyListeners[s](i),t},offAny(i){const e=this;if(!e.eventsListeners||e.destroyed||!e.eventsAnyListeners)return e;const t=e.eventsAnyListeners.indexOf(i);return t>=0&&e.eventsAnyListeners.splice(t,1),e},off(i,e){const t=this;return!t.eventsListeners||t.destroyed||!t.eventsListeners||i.split(" ").forEach(s=>{typeof e>"u"?t.eventsListeners[s]=[]:t.eventsListeners[s]&&t.eventsListeners[s].forEach((r,n)=>{(r===e||r.__emitterProxy&&r.__emitterProxy===e)&&t.eventsListeners[s].splice(n,1)})}),t},emit(){const i=this;if(!i.eventsListeners||i.destroyed||!i.eventsListeners)return i;let e,t,s;for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return typeof n[0]=="string"||Array.isArray(n[0])?(e=n[0],t=n.slice(1,n.length),s=i):(e=n[0].events,t=n[0].data,s=n[0].context||i),t.unshift(s),(Array.isArray(e)?e:e.split(" ")).forEach(d=>{i.eventsAnyListeners&&i.eventsAnyListeners.length&&i.eventsAnyListeners.forEach(a=>{a.apply(s,[d,...t])}),i.eventsListeners&&i.eventsListeners[d]&&i.eventsListeners[d].forEach(a=>{a.apply(s,t)})}),i}};function je(){const i=this;let e,t;const s=i.el;typeof i.params.width<"u"&&i.params.width!==null?e=i.params.width:e=s.clientWidth,typeof i.params.height<"u"&&i.params.height!==null?t=i.params.height:t=s.clientHeight,!(e===0&&i.isHorizontal()||t===0&&i.isVertical())&&(e=e-parseInt(W(s,"padding-left")||0,10)-parseInt(W(s,"padding-right")||0,10),t=t-parseInt(W(s,"padding-top")||0,10)-parseInt(W(s,"padding-bottom")||0,10),Number.isNaN(e)&&(e=0),Number.isNaN(t)&&(t=0),Object.assign(i,{width:e,height:t,size:i.isHorizontal()?e:t}))}function Ye(){const i=this;function e(w,T){return parseFloat(w.getPropertyValue(i.getDirectionLabel(T))||0)}const t=i.params,{wrapperEl:s,slidesEl:r,size:n,rtlTranslate:o,wrongRTL:l}=i,d=i.virtual&&t.virtual.enabled,a=d?i.virtual.slides.length:i.slides.length,c=$(r,`.${i.params.slideClass}, swiper-slide`),u=d?i.virtual.slides.length:c.length;let f=[];const h=[],p=[];let m=t.slidesOffsetBefore;typeof m=="function"&&(m=t.slidesOffsetBefore.call(i));let x=t.slidesOffsetAfter;typeof x=="function"&&(x=t.slidesOffsetAfter.call(i));const g=i.snapGrid.length,y=i.slidesGrid.length;let v=t.spaceBetween,C=-m,S=0,L=0;if(typeof n>"u")return;typeof v=="string"&&v.indexOf("%")>=0?v=parseFloat(v.replace("%",""))/100*n:typeof v=="string"&&(v=parseFloat(v)),i.virtualSize=-v,c.forEach(w=>{o?w.style.marginLeft="":w.style.marginRight="",w.style.marginBottom="",w.style.marginTop=""}),t.centeredSlides&&t.cssMode&&(X(s,"--swiper-centered-offset-before",""),X(s,"--swiper-centered-offset-after",""));const E=t.grid&&t.grid.rows>1&&i.grid;E?i.grid.initSlides(c):i.grid&&i.grid.unsetSlides();let I;const k=t.slidesPerView==="auto"&&t.breakpoints&&Object.keys(t.breakpoints).filter(w=>typeof t.breakpoints[w].slidesPerView<"u").length>0;for(let w=0;w<u;w+=1){I=0;let T;if(c[w]&&(T=c[w]),E&&i.grid.updateSlide(w,T,c),!(c[w]&&W(T,"display")==="none")){if(t.slidesPerView==="auto"){k&&(c[w].style[i.getDirectionLabel("width")]="");const M=getComputedStyle(T),z=T.style.transform,V=T.style.webkitTransform;if(z&&(T.style.transform="none"),V&&(T.style.webkitTransform="none"),t.roundLengths)I=i.isHorizontal()?ue(T,"width"):ue(T,"height");else{const N=e(M,"width"),P=e(M,"padding-left"),G=e(M,"padding-right"),_=e(M,"margin-left"),H=e(M,"margin-right"),ae=M.getPropertyValue("box-sizing");if(ae&&ae==="border-box")I=N+_+H;else{const{clientWidth:Ee,offsetWidth:Me}=T;I=N+P+G+_+H+(Me-Ee)}}z&&(T.style.transform=z),V&&(T.style.webkitTransform=V),t.roundLengths&&(I=Math.floor(I))}else I=(n-(t.slidesPerView-1)*v)/t.slidesPerView,t.roundLengths&&(I=Math.floor(I)),c[w]&&(c[w].style[i.getDirectionLabel("width")]=`${I}px`);c[w]&&(c[w].swiperSlideSize=I),p.push(I),t.centeredSlides?(C=C+I/2+S/2+v,S===0&&w!==0&&(C=C-n/2-v),w===0&&(C=C-n/2-v),Math.abs(C)<1/1e3&&(C=0),t.roundLengths&&(C=Math.floor(C)),L%t.slidesPerGroup===0&&f.push(C),h.push(C)):(t.roundLengths&&(C=Math.floor(C)),(L-Math.min(i.params.slidesPerGroupSkip,L))%i.params.slidesPerGroup===0&&f.push(C),h.push(C),C=C+I+v),i.virtualSize+=I+v,S=I,L+=1}}if(i.virtualSize=Math.max(i.virtualSize,n)+x,o&&l&&(t.effect==="slide"||t.effect==="coverflow")&&(s.style.width=`${i.virtualSize+v}px`),t.setWrapperSize&&(s.style[i.getDirectionLabel("width")]=`${i.virtualSize+v}px`),E&&i.grid.updateWrapperSize(I,f),!t.centeredSlides){const w=[];for(let T=0;T<f.length;T+=1){let M=f[T];t.roundLengths&&(M=Math.floor(M)),f[T]<=i.virtualSize-n&&w.push(M)}f=w,Math.floor(i.virtualSize-n)-Math.floor(f[f.length-1])>1&&f.push(i.virtualSize-n)}if(d&&t.loop){const w=p[0]+v;if(t.slidesPerGroup>1){const T=Math.ceil((i.virtual.slidesBefore+i.virtual.slidesAfter)/t.slidesPerGroup),M=w*t.slidesPerGroup;for(let z=0;z<T;z+=1)f.push(f[f.length-1]+M)}for(let T=0;T<i.virtual.slidesBefore+i.virtual.slidesAfter;T+=1)t.slidesPerGroup===1&&f.push(f[f.length-1]+w),h.push(h[h.length-1]+w),i.virtualSize+=w}if(f.length===0&&(f=[0]),v!==0){const w=i.isHorizontal()&&o?"marginLeft":i.getDirectionLabel("marginRight");c.filter((T,M)=>!t.cssMode||t.loop?!0:M!==c.length-1).forEach(T=>{T.style[w]=`${v}px`})}if(t.centeredSlides&&t.centeredSlidesBounds){let w=0;p.forEach(M=>{w+=M+(v||0)}),w-=v;const T=w>n?w-n:0;f=f.map(M=>M<=0?-m:M>T?T+x:M)}if(t.centerInsufficientSlides){let w=0;p.forEach(M=>{w+=M+(v||0)}),w-=v;const T=(t.slidesOffsetBefore||0)+(t.slidesOffsetAfter||0);if(w+T<n){const M=(n-w-T)/2;f.forEach((z,V)=>{f[V]=z-M}),h.forEach((z,V)=>{h[V]=z+M})}}if(Object.assign(i,{slides:c,snapGrid:f,slidesGrid:h,slidesSizesGrid:p}),t.centeredSlides&&t.cssMode&&!t.centeredSlidesBounds){X(s,"--swiper-centered-offset-before",`${-f[0]}px`),X(s,"--swiper-centered-offset-after",`${i.size/2-p[p.length-1]/2}px`);const w=-i.snapGrid[0],T=-i.slidesGrid[0];i.snapGrid=i.snapGrid.map(M=>M+w),i.slidesGrid=i.slidesGrid.map(M=>M+T)}if(u!==a&&i.emit("slidesLengthChange"),f.length!==g&&(i.params.watchOverflow&&i.checkOverflow(),i.emit("snapGridLengthChange")),h.length!==y&&i.emit("slidesGridLengthChange"),t.watchSlidesProgress&&i.updateSlidesOffset(),i.emit("slidesUpdated"),!d&&!t.cssMode&&(t.effect==="slide"||t.effect==="fade")){const w=`${t.containerModifierClass}backface-hidden`,T=i.el.classList.contains(w);u<=t.maxBackfaceHiddenSlides?T||i.el.classList.add(w):T&&i.el.classList.remove(w)}}function Xe(i){const e=this,t=[],s=e.virtual&&e.params.virtual.enabled;let r=0,n;typeof i=="number"?e.setTransition(i):i===!0&&e.setTransition(e.params.speed);const o=l=>s?e.slides[e.getSlideIndexByData(l)]:e.slides[l];if(e.params.slidesPerView!=="auto"&&e.params.slidesPerView>1)if(e.params.centeredSlides)(e.visibleSlides||[]).forEach(l=>{t.push(l)});else for(n=0;n<Math.ceil(e.params.slidesPerView);n+=1){const l=e.activeIndex+n;if(l>e.slides.length&&!s)break;t.push(o(l))}else t.push(o(e.activeIndex));for(n=0;n<t.length;n+=1)if(typeof t[n]<"u"){const l=t[n].offsetHeight;r=l>r?l:r}(r||r===0)&&(e.wrapperEl.style.height=`${r}px`)}function qe(){const i=this,e=i.slides,t=i.isElement?i.isHorizontal()?i.wrapperEl.offsetLeft:i.wrapperEl.offsetTop:0;for(let s=0;s<e.length;s+=1)e[s].swiperSlideOffset=(i.isHorizontal()?e[s].offsetLeft:e[s].offsetTop)-t-i.cssOverflowAdjustment()}const fe=(i,e,t)=>{e&&!i.classList.contains(t)?i.classList.add(t):!e&&i.classList.contains(t)&&i.classList.remove(t)};function Ue(i){i===void 0&&(i=this&&this.translate||0);const e=this,t=e.params,{slides:s,rtlTranslate:r,snapGrid:n}=e;if(s.length===0)return;typeof s[0].swiperSlideOffset>"u"&&e.updateSlidesOffset();let o=-i;r&&(o=i),e.visibleSlidesIndexes=[],e.visibleSlides=[];let l=t.spaceBetween;typeof l=="string"&&l.indexOf("%")>=0?l=parseFloat(l.replace("%",""))/100*e.size:typeof l=="string"&&(l=parseFloat(l));for(let d=0;d<s.length;d+=1){const a=s[d];let c=a.swiperSlideOffset;t.cssMode&&t.centeredSlides&&(c-=s[0].swiperSlideOffset);const u=(o+(t.centeredSlides?e.minTranslate():0)-c)/(a.swiperSlideSize+l),f=(o-n[0]+(t.centeredSlides?e.minTranslate():0)-c)/(a.swiperSlideSize+l),h=-(o-c),p=h+e.slidesSizesGrid[d],m=h>=0&&h<=e.size-e.slidesSizesGrid[d],x=h>=0&&h<e.size-1||p>1&&p<=e.size||h<=0&&p>=e.size;x&&(e.visibleSlides.push(a),e.visibleSlidesIndexes.push(d)),fe(a,x,t.slideVisibleClass),fe(a,m,t.slideFullyVisibleClass),a.progress=r?-u:u,a.originalProgress=r?-f:f}}function Ke(i){const e=this;if(typeof i>"u"){const c=e.rtlTranslate?-1:1;i=e&&e.translate&&e.translate*c||0}const t=e.params,s=e.maxTranslate()-e.minTranslate();let{progress:r,isBeginning:n,isEnd:o,progressLoop:l}=e;const d=n,a=o;if(s===0)r=0,n=!0,o=!0;else{r=(i-e.minTranslate())/s;const c=Math.abs(i-e.minTranslate())<1,u=Math.abs(i-e.maxTranslate())<1;n=c||r<=0,o=u||r>=1,c&&(r=0),u&&(r=1)}if(t.loop){const c=e.getSlideIndexByData(0),u=e.getSlideIndexByData(e.slides.length-1),f=e.slidesGrid[c],h=e.slidesGrid[u],p=e.slidesGrid[e.slidesGrid.length-1],m=Math.abs(i);m>=f?l=(m-f)/p:l=(m+p-h)/p,l>1&&(l-=1)}Object.assign(e,{progress:r,progressLoop:l,isBeginning:n,isEnd:o}),(t.watchSlidesProgress||t.centeredSlides&&t.autoHeight)&&e.updateSlidesProgress(i),n&&!d&&e.emit("reachBeginning toEdge"),o&&!a&&e.emit("reachEnd toEdge"),(d&&!n||a&&!o)&&e.emit("fromEdge"),e.emit("progress",r)}const ee=(i,e,t)=>{e&&!i.classList.contains(t)?i.classList.add(t):!e&&i.classList.contains(t)&&i.classList.remove(t)};function Ze(){const i=this,{slides:e,params:t,slidesEl:s,activeIndex:r}=i,n=i.virtual&&t.virtual.enabled,o=i.grid&&t.grid&&t.grid.rows>1,l=u=>$(s,`.${t.slideClass}${u}, swiper-slide${u}`)[0];let d,a,c;if(n)if(t.loop){let u=r-i.virtual.slidesBefore;u<0&&(u=i.virtual.slides.length+u),u>=i.virtual.slides.length&&(u-=i.virtual.slides.length),d=l(`[data-swiper-slide-index="${u}"]`)}else d=l(`[data-swiper-slide-index="${r}"]`);else o?(d=e.find(u=>u.column===r),c=e.find(u=>u.column===r+1),a=e.find(u=>u.column===r-1)):d=e[r];d&&(o||(c=Be(d,`.${t.slideClass}, swiper-slide`)[0],t.loop&&!c&&(c=e[0]),a=_e(d,`.${t.slideClass}, swiper-slide`)[0],t.loop&&!a===0&&(a=e[e.length-1]))),e.forEach(u=>{ee(u,u===d,t.slideActiveClass),ee(u,u===c,t.slideNextClass),ee(u,u===a,t.slidePrevClass)}),i.emitSlidesClasses()}const q=(i,e)=>{if(!i||i.destroyed||!i.params)return;const t=()=>i.isElement?"swiper-slide":`.${i.params.slideClass}`,s=e.closest(t());if(s){let r=s.querySelector(`.${i.params.lazyPreloaderClass}`);!r&&i.isElement&&(s.shadowRoot?r=s.shadowRoot.querySelector(`.${i.params.lazyPreloaderClass}`):requestAnimationFrame(()=>{s.shadowRoot&&(r=s.shadowRoot.querySelector(`.${i.params.lazyPreloaderClass}`),r&&r.remove())})),r&&r.remove()}},te=(i,e)=>{if(!i.slides[e])return;const t=i.slides[e].querySelector('[loading="lazy"]');t&&t.removeAttribute("loading")},oe=i=>{if(!i||i.destroyed||!i.params)return;let e=i.params.lazyPreloadPrevNext;const t=i.slides.length;if(!t||!e||e<0)return;e=Math.min(e,t);const s=i.params.slidesPerView==="auto"?i.slidesPerViewDynamic():Math.ceil(i.params.slidesPerView),r=i.activeIndex;if(i.params.grid&&i.params.grid.rows>1){const o=r,l=[o-e];l.push(...Array.from({length:e}).map((d,a)=>o+s+a)),i.slides.forEach((d,a)=>{l.includes(d.column)&&te(i,a)});return}const n=r+s-1;if(i.params.rewind||i.params.loop)for(let o=r-e;o<=n+e;o+=1){const l=(o%t+t)%t;(l<r||l>n)&&te(i,l)}else for(let o=Math.max(r-e,0);o<=Math.min(n+e,t-1);o+=1)o!==r&&(o>n||o<r)&&te(i,o)};function Qe(i){const{slidesGrid:e,params:t}=i,s=i.rtlTranslate?i.translate:-i.translate;let r;for(let n=0;n<e.length;n+=1)typeof e[n+1]<"u"?s>=e[n]&&s<e[n+1]-(e[n+1]-e[n])/2?r=n:s>=e[n]&&s<e[n+1]&&(r=n+1):s>=e[n]&&(r=n);return t.normalizeSlideIndex&&(r<0||typeof r>"u")&&(r=0),r}function Je(i){const e=this,t=e.rtlTranslate?e.translate:-e.translate,{snapGrid:s,params:r,activeIndex:n,realIndex:o,snapIndex:l}=e;let d=i,a;const c=h=>{let p=h-e.virtual.slidesBefore;return p<0&&(p=e.virtual.slides.length+p),p>=e.virtual.slides.length&&(p-=e.virtual.slides.length),p};if(typeof d>"u"&&(d=Qe(e)),s.indexOf(t)>=0)a=s.indexOf(t);else{const h=Math.min(r.slidesPerGroupSkip,d);a=h+Math.floor((d-h)/r.slidesPerGroup)}if(a>=s.length&&(a=s.length-1),d===n&&!e.params.loop){a!==l&&(e.snapIndex=a,e.emit("snapIndexChange"));return}if(d===n&&e.params.loop&&e.virtual&&e.params.virtual.enabled){e.realIndex=c(d);return}const u=e.grid&&r.grid&&r.grid.rows>1;let f;if(e.virtual&&r.virtual.enabled&&r.loop)f=c(d);else if(u){const h=e.slides.find(m=>m.column===d);let p=parseInt(h.getAttribute("data-swiper-slide-index"),10);Number.isNaN(p)&&(p=Math.max(e.slides.indexOf(h),0)),f=Math.floor(p/r.grid.rows)}else if(e.slides[d]){const h=e.slides[d].getAttribute("data-swiper-slide-index");h?f=parseInt(h,10):f=d}else f=d;Object.assign(e,{previousSnapIndex:l,snapIndex:a,previousRealIndex:o,realIndex:f,previousIndex:n,activeIndex:d}),e.initialized&&oe(e),e.emit("activeIndexChange"),e.emit("snapIndexChange"),(e.initialized||e.params.runCallbacksOnInit)&&(o!==f&&e.emit("realIndexChange"),e.emit("slideChange"))}function et(i,e){const t=this,s=t.params;let r=i.closest(`.${s.slideClass}, swiper-slide`);!r&&t.isElement&&e&&e.length>1&&e.includes(i)&&[...e.slice(e.indexOf(i)+1,e.length)].forEach(l=>{!r&&l.matches&&l.matches(`.${s.slideClass}, swiper-slide`)&&(r=l)});let n=!1,o;if(r){for(let l=0;l<t.slides.length;l+=1)if(t.slides[l]===r){n=!0,o=l;break}}if(r&&n)t.clickedSlide=r,t.virtual&&t.params.virtual.enabled?t.clickedIndex=parseInt(r.getAttribute("data-swiper-slide-index"),10):t.clickedIndex=o;else{t.clickedSlide=void 0,t.clickedIndex=void 0;return}s.slideToClickedSlide&&t.clickedIndex!==void 0&&t.clickedIndex!==t.activeIndex&&t.slideToClickedSlide()}var tt={updateSize:je,updateSlides:Ye,updateAutoHeight:Xe,updateSlidesOffset:qe,updateSlidesProgress:Ue,updateProgress:Ke,updateSlidesClasses:Ze,updateActiveIndex:Je,updateClickedSlide:et};function it(i){i===void 0&&(i=this.isHorizontal()?"x":"y");const e=this,{params:t,rtlTranslate:s,translate:r,wrapperEl:n}=e;if(t.virtualTranslate)return s?-r:r;if(t.cssMode)return r;let o=Ae(n,i);return o+=e.cssOverflowAdjustment(),s&&(o=-o),o||0}function st(i,e){const t=this,{rtlTranslate:s,params:r,wrapperEl:n,progress:o}=t;let l=0,d=0;const a=0;t.isHorizontal()?l=s?-i:i:d=i,r.roundLengths&&(l=Math.floor(l),d=Math.floor(d)),t.previousTranslate=t.translate,t.translate=t.isHorizontal()?l:d,r.cssMode?n[t.isHorizontal()?"scrollLeft":"scrollTop"]=t.isHorizontal()?-l:-d:r.virtualTranslate||(t.isHorizontal()?l-=t.cssOverflowAdjustment():d-=t.cssOverflowAdjustment(),n.style.transform=`translate3d(${l}px, ${d}px, ${a}px)`);let c;const u=t.maxTranslate()-t.minTranslate();u===0?c=0:c=(i-t.minTranslate())/u,c!==o&&t.updateProgress(i),t.emit("setTranslate",t.translate,e)}function rt(){return-this.snapGrid[0]}function nt(){return-this.snapGrid[this.snapGrid.length-1]}function ot(i,e,t,s,r){i===void 0&&(i=0),e===void 0&&(e=this.params.speed),t===void 0&&(t=!0),s===void 0&&(s=!0);const n=this,{params:o,wrapperEl:l}=n;if(n.animating&&o.preventInteractionOnTransition)return!1;const d=n.minTranslate(),a=n.maxTranslate();let c;if(s&&i>d?c=d:s&&i<a?c=a:c=i,n.updateProgress(c),o.cssMode){const u=n.isHorizontal();if(e===0)l[u?"scrollLeft":"scrollTop"]=-c;else{if(!n.support.smoothScroll)return we({swiper:n,targetPosition:-c,side:u?"left":"top"}),!0;l.scrollTo({[u?"left":"top"]:-c,behavior:"smooth"})}return!0}return e===0?(n.setTransition(0),n.setTranslate(c),t&&(n.emit("beforeTransitionStart",e,r),n.emit("transitionEnd"))):(n.setTransition(e),n.setTranslate(c),t&&(n.emit("beforeTransitionStart",e,r),n.emit("transitionStart")),n.animating||(n.animating=!0,n.onTranslateToWrapperTransitionEnd||(n.onTranslateToWrapperTransitionEnd=function(f){!n||n.destroyed||f.target===this&&(n.wrapperEl.removeEventListener("transitionend",n.onTranslateToWrapperTransitionEnd),n.onTranslateToWrapperTransitionEnd=null,delete n.onTranslateToWrapperTransitionEnd,n.animating=!1,t&&n.emit("transitionEnd"))}),n.wrapperEl.addEventListener("transitionend",n.onTranslateToWrapperTransitionEnd))),!0}var lt={getTranslate:it,setTranslate:st,minTranslate:rt,maxTranslate:nt,translateTo:ot};function at(i,e){const t=this;t.params.cssMode||(t.wrapperEl.style.transitionDuration=`${i}ms`,t.wrapperEl.style.transitionDelay=i===0?"0ms":""),t.emit("setTransition",i,e)}function xe(i){let{swiper:e,runCallbacks:t,direction:s,step:r}=i;const{activeIndex:n,previousIndex:o}=e;let l=s;if(l||(n>o?l="next":n<o?l="prev":l="reset"),e.emit(`transition${r}`),t&&n!==o){if(l==="reset"){e.emit(`slideResetTransition${r}`);return}e.emit(`slideChangeTransition${r}`),l==="next"?e.emit(`slideNextTransition${r}`):e.emit(`slidePrevTransition${r}`)}}function dt(i,e){i===void 0&&(i=!0);const t=this,{params:s}=t;s.cssMode||(s.autoHeight&&t.updateAutoHeight(),xe({swiper:t,runCallbacks:i,direction:e,step:"Start"}))}function ct(i,e){i===void 0&&(i=!0);const t=this,{params:s}=t;t.animating=!1,!s.cssMode&&(t.setTransition(0),xe({swiper:t,runCallbacks:i,direction:e,step:"End"}))}var ut={setTransition:at,transitionStart:dt,transitionEnd:ct};function ft(i,e,t,s,r){i===void 0&&(i=0),t===void 0&&(t=!0),typeof i=="string"&&(i=parseInt(i,10));const n=this;let o=i;o<0&&(o=0);const{params:l,snapGrid:d,slidesGrid:a,previousIndex:c,activeIndex:u,rtlTranslate:f,wrapperEl:h,enabled:p}=n;if(!p&&!s&&!r||n.destroyed||n.animating&&l.preventInteractionOnTransition)return!1;typeof e>"u"&&(e=n.params.speed);const m=Math.min(n.params.slidesPerGroupSkip,o);let x=m+Math.floor((o-m)/n.params.slidesPerGroup);x>=d.length&&(x=d.length-1);const g=-d[x];if(l.normalizeSlideIndex)for(let E=0;E<a.length;E+=1){const I=-Math.floor(g*100),k=Math.floor(a[E]*100),w=Math.floor(a[E+1]*100);typeof a[E+1]<"u"?I>=k&&I<w-(w-k)/2?o=E:I>=k&&I<w&&(o=E+1):I>=k&&(o=E)}if(n.initialized&&o!==u&&(!n.allowSlideNext&&(f?g>n.translate&&g>n.minTranslate():g<n.translate&&g<n.minTranslate())||!n.allowSlidePrev&&g>n.translate&&g>n.maxTranslate()&&(u||0)!==o))return!1;o!==(c||0)&&t&&n.emit("beforeSlideChangeStart"),n.updateProgress(g);let y;o>u?y="next":o<u?y="prev":y="reset";const v=n.virtual&&n.params.virtual.enabled;if(!(v&&r)&&(f&&-g===n.translate||!f&&g===n.translate))return n.updateActiveIndex(o),l.autoHeight&&n.updateAutoHeight(),n.updateSlidesClasses(),l.effect!=="slide"&&n.setTranslate(g),y!=="reset"&&(n.transitionStart(t,y),n.transitionEnd(t,y)),!1;if(l.cssMode){const E=n.isHorizontal(),I=f?g:-g;if(e===0)v&&(n.wrapperEl.style.scrollSnapType="none",n._immediateVirtual=!0),v&&!n._cssModeVirtualInitialSet&&n.params.initialSlide>0?(n._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{h[E?"scrollLeft":"scrollTop"]=I})):h[E?"scrollLeft":"scrollTop"]=I,v&&requestAnimationFrame(()=>{n.wrapperEl.style.scrollSnapType="",n._immediateVirtual=!1});else{if(!n.support.smoothScroll)return we({swiper:n,targetPosition:I,side:E?"left":"top"}),!0;h.scrollTo({[E?"left":"top"]:I,behavior:"smooth"})}return!0}const L=Te().isSafari;return v&&!r&&L&&n.isElement&&n.virtual.update(!1,!1,o),n.setTransition(e),n.setTranslate(g),n.updateActiveIndex(o),n.updateSlidesClasses(),n.emit("beforeTransitionStart",e,s),n.transitionStart(t,y),e===0?n.transitionEnd(t,y):n.animating||(n.animating=!0,n.onSlideToWrapperTransitionEnd||(n.onSlideToWrapperTransitionEnd=function(I){!n||n.destroyed||I.target===this&&(n.wrapperEl.removeEventListener("transitionend",n.onSlideToWrapperTransitionEnd),n.onSlideToWrapperTransitionEnd=null,delete n.onSlideToWrapperTransitionEnd,n.transitionEnd(t,y))}),n.wrapperEl.addEventListener("transitionend",n.onSlideToWrapperTransitionEnd)),!0}function pt(i,e,t,s){i===void 0&&(i=0),t===void 0&&(t=!0),typeof i=="string"&&(i=parseInt(i,10));const r=this;if(r.destroyed)return;typeof e>"u"&&(e=r.params.speed);const n=r.grid&&r.params.grid&&r.params.grid.rows>1;let o=i;if(r.params.loop)if(r.virtual&&r.params.virtual.enabled)o=o+r.virtual.slidesBefore;else{let l;if(n){const f=o*r.params.grid.rows;l=r.slides.find(h=>h.getAttribute("data-swiper-slide-index")*1===f).column}else l=r.getSlideIndexByData(o);const d=n?Math.ceil(r.slides.length/r.params.grid.rows):r.slides.length,{centeredSlides:a}=r.params;let c=r.params.slidesPerView;c==="auto"?c=r.slidesPerViewDynamic():(c=Math.ceil(parseFloat(r.params.slidesPerView,10)),a&&c%2===0&&(c=c+1));let u=d-l<c;if(a&&(u=u||l<Math.ceil(c/2)),s&&a&&r.params.slidesPerView!=="auto"&&!n&&(u=!1),u){const f=a?l<r.activeIndex?"prev":"next":l-r.activeIndex-1<r.params.slidesPerView?"next":"prev";r.loopFix({direction:f,slideTo:!0,activeSlideIndex:f==="next"?l+1:l-d+1,slideRealIndex:f==="next"?r.realIndex:void 0})}if(n){const f=o*r.params.grid.rows;o=r.slides.find(h=>h.getAttribute("data-swiper-slide-index")*1===f).column}else o=r.getSlideIndexByData(o)}return requestAnimationFrame(()=>{r.slideTo(o,e,t,s)}),r}function ht(i,e,t){e===void 0&&(e=!0);const s=this,{enabled:r,params:n,animating:o}=s;if(!r||s.destroyed)return s;typeof i>"u"&&(i=s.params.speed);let l=n.slidesPerGroup;n.slidesPerView==="auto"&&n.slidesPerGroup===1&&n.slidesPerGroupAuto&&(l=Math.max(s.slidesPerViewDynamic("current",!0),1));const d=s.activeIndex<n.slidesPerGroupSkip?1:l,a=s.virtual&&n.virtual.enabled;if(n.loop){if(o&&!a&&n.loopPreventsSliding)return!1;if(s.loopFix({direction:"next"}),s._clientLeft=s.wrapperEl.clientLeft,s.activeIndex===s.slides.length-1&&n.cssMode)return requestAnimationFrame(()=>{s.slideTo(s.activeIndex+d,i,e,t)}),!0}return n.rewind&&s.isEnd?s.slideTo(0,i,e,t):s.slideTo(s.activeIndex+d,i,e,t)}function gt(i,e,t){e===void 0&&(e=!0);const s=this,{params:r,snapGrid:n,slidesGrid:o,rtlTranslate:l,enabled:d,animating:a}=s;if(!d||s.destroyed)return s;typeof i>"u"&&(i=s.params.speed);const c=s.virtual&&r.virtual.enabled;if(r.loop){if(a&&!c&&r.loopPreventsSliding)return!1;s.loopFix({direction:"prev"}),s._clientLeft=s.wrapperEl.clientLeft}const u=l?s.translate:-s.translate;function f(y){return y<0?-Math.floor(Math.abs(y)):Math.floor(y)}const h=f(u),p=n.map(y=>f(y)),m=r.freeMode&&r.freeMode.enabled;let x=n[p.indexOf(h)-1];if(typeof x>"u"&&(r.cssMode||m)){let y;n.forEach((v,C)=>{h>=v&&(y=C)}),typeof y<"u"&&(x=m?n[y]:n[y>0?y-1:y])}let g=0;if(typeof x<"u"&&(g=o.indexOf(x),g<0&&(g=s.activeIndex-1),r.slidesPerView==="auto"&&r.slidesPerGroup===1&&r.slidesPerGroupAuto&&(g=g-s.slidesPerViewDynamic("previous",!0)+1,g=Math.max(g,0))),r.rewind&&s.isBeginning){const y=s.params.virtual&&s.params.virtual.enabled&&s.virtual?s.virtual.slides.length-1:s.slides.length-1;return s.slideTo(y,i,e,t)}else if(r.loop&&s.activeIndex===0&&r.cssMode)return requestAnimationFrame(()=>{s.slideTo(g,i,e,t)}),!0;return s.slideTo(g,i,e,t)}function mt(i,e,t){e===void 0&&(e=!0);const s=this;if(!s.destroyed)return typeof i>"u"&&(i=s.params.speed),s.slideTo(s.activeIndex,i,e,t)}function vt(i,e,t,s){e===void 0&&(e=!0),s===void 0&&(s=.5);const r=this;if(r.destroyed)return;typeof i>"u"&&(i=r.params.speed);let n=r.activeIndex;const o=Math.min(r.params.slidesPerGroupSkip,n),l=o+Math.floor((n-o)/r.params.slidesPerGroup),d=r.rtlTranslate?r.translate:-r.translate;if(d>=r.snapGrid[l]){const a=r.snapGrid[l],c=r.snapGrid[l+1];d-a>(c-a)*s&&(n+=r.params.slidesPerGroup)}else{const a=r.snapGrid[l-1],c=r.snapGrid[l];d-a<=(c-a)*s&&(n-=r.params.slidesPerGroup)}return n=Math.max(n,0),n=Math.min(n,r.slidesGrid.length-1),r.slideTo(n,i,e,t)}function yt(){const i=this;if(i.destroyed)return;const{params:e,slidesEl:t}=i,s=e.slidesPerView==="auto"?i.slidesPerViewDynamic():e.slidesPerView;let r=i.clickedIndex,n;const o=i.isElement?"swiper-slide":`.${e.slideClass}`;if(e.loop){if(i.animating)return;n=parseInt(i.clickedSlide.getAttribute("data-swiper-slide-index"),10),e.centeredSlides?r<i.loopedSlides-s/2||r>i.slides.length-i.loopedSlides+s/2?(i.loopFix(),r=i.getSlideIndex($(t,`${o}[data-swiper-slide-index="${n}"]`)[0]),re(()=>{i.slideTo(r)})):i.slideTo(r):r>i.slides.length-s?(i.loopFix(),r=i.getSlideIndex($(t,`${o}[data-swiper-slide-index="${n}"]`)[0]),re(()=>{i.slideTo(r)})):i.slideTo(r)}else i.slideTo(r)}var wt={slideTo:ft,slideToLoop:pt,slideNext:ht,slidePrev:gt,slideReset:mt,slideToClosest:vt,slideToClickedSlide:yt};function bt(i,e){const t=this,{params:s,slidesEl:r}=t;if(!s.loop||t.virtual&&t.params.virtual.enabled)return;const n=()=>{$(r,`.${s.slideClass}, swiper-slide`).forEach((f,h)=>{f.setAttribute("data-swiper-slide-index",h)})},o=t.grid&&s.grid&&s.grid.rows>1,l=s.slidesPerGroup*(o?s.grid.rows:1),d=t.slides.length%l!==0,a=o&&t.slides.length%s.grid.rows!==0,c=u=>{for(let f=0;f<u;f+=1){const h=t.isElement?ne("swiper-slide",[s.slideBlankClass]):ne("div",[s.slideClass,s.slideBlankClass]);t.slidesEl.append(h)}};if(d){if(s.loopAddBlankSlides){const u=l-t.slides.length%l;c(u),t.recalcSlides(),t.updateSlides()}else K("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");n()}else if(a){if(s.loopAddBlankSlides){const u=s.grid.rows-t.slides.length%s.grid.rows;c(u),t.recalcSlides(),t.updateSlides()}else K("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");n()}else n();t.loopFix({slideRealIndex:i,direction:s.centeredSlides?void 0:"next",initial:e})}function St(i){let{slideRealIndex:e,slideTo:t=!0,direction:s,setTranslate:r,activeSlideIndex:n,initial:o,byController:l,byMousewheel:d}=i===void 0?{}:i;const a=this;if(!a.params.loop)return;a.emit("beforeLoopFix");const{slides:c,allowSlidePrev:u,allowSlideNext:f,slidesEl:h,params:p}=a,{centeredSlides:m,initialSlide:x}=p;if(a.allowSlidePrev=!0,a.allowSlideNext=!0,a.virtual&&p.virtual.enabled){t&&(!p.centeredSlides&&a.snapIndex===0?a.slideTo(a.virtual.slides.length,0,!1,!0):p.centeredSlides&&a.snapIndex<p.slidesPerView?a.slideTo(a.virtual.slides.length+a.snapIndex,0,!1,!0):a.snapIndex===a.snapGrid.length-1&&a.slideTo(a.virtual.slidesBefore,0,!1,!0)),a.allowSlidePrev=u,a.allowSlideNext=f,a.emit("loopFix");return}let g=p.slidesPerView;g==="auto"?g=a.slidesPerViewDynamic():(g=Math.ceil(parseFloat(p.slidesPerView,10)),m&&g%2===0&&(g=g+1));const y=p.slidesPerGroupAuto?g:p.slidesPerGroup;let v=y;v%y!==0&&(v+=y-v%y),v+=p.loopAdditionalSlides,a.loopedSlides=v;const C=a.grid&&p.grid&&p.grid.rows>1;c.length<g+v||a.params.effect==="cards"&&c.length<g+v*2?K("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled or not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):C&&p.grid.fill==="row"&&K("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`");const S=[],L=[],E=C?Math.ceil(c.length/p.grid.rows):c.length,I=o&&E-x<g&&!m;let k=I?x:a.activeIndex;typeof n>"u"?n=a.getSlideIndex(c.find(P=>P.classList.contains(p.slideActiveClass))):k=n;const w=s==="next"||!s,T=s==="prev"||!s;let M=0,z=0;const N=(C?c[n].column:n)+(m&&typeof r>"u"?-g/2+.5:0);if(N<v){M=Math.max(v-N,y);for(let P=0;P<v-N;P+=1){const G=P-Math.floor(P/E)*E;if(C){const _=E-G-1;for(let H=c.length-1;H>=0;H-=1)c[H].column===_&&S.push(H)}else S.push(E-G-1)}}else if(N+g>E-v){z=Math.max(N-(E-v*2),y),I&&(z=Math.max(z,g-E+x+1));for(let P=0;P<z;P+=1){const G=P-Math.floor(P/E)*E;C?c.forEach((_,H)=>{_.column===G&&L.push(H)}):L.push(G)}}if(a.__preventObserver__=!0,requestAnimationFrame(()=>{a.__preventObserver__=!1}),a.params.effect==="cards"&&c.length<g+v*2&&(L.includes(n)&&L.splice(L.indexOf(n),1),S.includes(n)&&S.splice(S.indexOf(n),1)),T&&S.forEach(P=>{c[P].swiperLoopMoveDOM=!0,h.prepend(c[P]),c[P].swiperLoopMoveDOM=!1}),w&&L.forEach(P=>{c[P].swiperLoopMoveDOM=!0,h.append(c[P]),c[P].swiperLoopMoveDOM=!1}),a.recalcSlides(),p.slidesPerView==="auto"?a.updateSlides():C&&(S.length>0&&T||L.length>0&&w)&&a.slides.forEach((P,G)=>{a.grid.updateSlide(G,P,a.slides)}),p.watchSlidesProgress&&a.updateSlidesOffset(),t){if(S.length>0&&T){if(typeof e>"u"){const P=a.slidesGrid[k],_=a.slidesGrid[k+M]-P;d?a.setTranslate(a.translate-_):(a.slideTo(k+Math.ceil(M),0,!1,!0),r&&(a.touchEventsData.startTranslate=a.touchEventsData.startTranslate-_,a.touchEventsData.currentTranslate=a.touchEventsData.currentTranslate-_))}else if(r){const P=C?S.length/p.grid.rows:S.length;a.slideTo(a.activeIndex+P,0,!1,!0),a.touchEventsData.currentTranslate=a.translate}}else if(L.length>0&&w)if(typeof e>"u"){const P=a.slidesGrid[k],_=a.slidesGrid[k-z]-P;d?a.setTranslate(a.translate-_):(a.slideTo(k-z,0,!1,!0),r&&(a.touchEventsData.startTranslate=a.touchEventsData.startTranslate-_,a.touchEventsData.currentTranslate=a.touchEventsData.currentTranslate-_))}else{const P=C?L.length/p.grid.rows:L.length;a.slideTo(a.activeIndex-P,0,!1,!0)}}if(a.allowSlidePrev=u,a.allowSlideNext=f,a.controller&&a.controller.control&&!l){const P={slideRealIndex:e,direction:s,setTranslate:r,activeSlideIndex:n,byController:!0};Array.isArray(a.controller.control)?a.controller.control.forEach(G=>{!G.destroyed&&G.params.loop&&G.loopFix({...P,slideTo:G.params.slidesPerView===p.slidesPerView?t:!1})}):a.controller.control instanceof a.constructor&&a.controller.control.params.loop&&a.controller.control.loopFix({...P,slideTo:a.controller.control.params.slidesPerView===p.slidesPerView?t:!1})}a.emit("loopFix")}function Tt(){const i=this,{params:e,slidesEl:t}=i;if(!e.loop||!t||i.virtual&&i.params.virtual.enabled)return;i.recalcSlides();const s=[];i.slides.forEach(r=>{const n=typeof r.swiperSlideIndex>"u"?r.getAttribute("data-swiper-slide-index")*1:r.swiperSlideIndex;s[n]=r}),i.slides.forEach(r=>{r.removeAttribute("data-swiper-slide-index")}),s.forEach(r=>{t.append(r)}),i.recalcSlides(),i.slideTo(i.realIndex,0)}var xt={loopCreate:bt,loopFix:St,loopDestroy:Tt};function Ct(i){const e=this;if(!e.params.simulateTouch||e.params.watchOverflow&&e.isLocked||e.params.cssMode)return;const t=e.params.touchEventsTarget==="container"?e.el:e.wrapperEl;e.isElement&&(e.__preventObserver__=!0),t.style.cursor="move",t.style.cursor=i?"grabbing":"grab",e.isElement&&requestAnimationFrame(()=>{e.__preventObserver__=!1})}function It(){const i=this;i.params.watchOverflow&&i.isLocked||i.params.cssMode||(i.isElement&&(i.__preventObserver__=!0),i[i.params.touchEventsTarget==="container"?"el":"wrapperEl"].style.cursor="",i.isElement&&requestAnimationFrame(()=>{i.__preventObserver__=!1}))}var Et={setGrabCursor:Ct,unsetGrabCursor:It};function Mt(i,e){e===void 0&&(e=this);function t(s){if(!s||s===j()||s===D())return null;s.assignedSlot&&(s=s.assignedSlot);const r=s.closest(i);return!r&&!s.getRootNode?null:r||t(s.getRootNode().host)}return t(e)}function pe(i,e,t){const s=D(),{params:r}=i,n=r.edgeSwipeDetection,o=r.edgeSwipeThreshold;return n&&(t<=o||t>=s.innerWidth-o)?n==="prevent"?(e.preventDefault(),!0):!1:!0}function Pt(i){const e=this,t=j();let s=i;s.originalEvent&&(s=s.originalEvent);const r=e.touchEventsData;if(s.type==="pointerdown"){if(r.pointerId!==null&&r.pointerId!==s.pointerId)return;r.pointerId=s.pointerId}else s.type==="touchstart"&&s.targetTouches.length===1&&(r.touchId=s.targetTouches[0].identifier);if(s.type==="touchstart"){pe(e,s,s.targetTouches[0].pageX);return}const{params:n,touches:o,enabled:l}=e;if(!l||!n.simulateTouch&&s.pointerType==="mouse"||e.animating&&n.preventInteractionOnTransition)return;!e.animating&&n.cssMode&&n.loop&&e.loopFix();let d=s.target;if(n.touchEventsTarget==="wrapper"&&!De(d,e.wrapperEl)||"which"in s&&s.which===3||"button"in s&&s.button>0||r.isTouched&&r.isMoved)return;const a=!!n.noSwipingClass&&n.noSwipingClass!=="",c=s.composedPath?s.composedPath():s.path;a&&s.target&&s.target.shadowRoot&&c&&(d=c[0]);const u=n.noSwipingSelector?n.noSwipingSelector:`.${n.noSwipingClass}`,f=!!(s.target&&s.target.shadowRoot);if(n.noSwiping&&(f?Mt(u,d):d.closest(u))){e.allowClick=!0;return}if(n.swipeHandler&&!d.closest(n.swipeHandler))return;o.currentX=s.pageX,o.currentY=s.pageY;const h=o.currentX,p=o.currentY;if(!pe(e,s,h))return;Object.assign(r,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),o.startX=h,o.startY=p,r.touchStartTime=U(),e.allowClick=!0,e.updateSize(),e.swipeDirection=void 0,n.threshold>0&&(r.allowThresholdMove=!1);let m=!0;d.matches(r.focusableElements)&&(m=!1,d.nodeName==="SELECT"&&(r.isTouched=!1)),t.activeElement&&t.activeElement.matches(r.focusableElements)&&t.activeElement!==d&&(s.pointerType==="mouse"||s.pointerType!=="mouse"&&!d.matches(r.focusableElements))&&t.activeElement.blur();const x=m&&e.allowTouchMove&&n.touchStartPreventDefault;(n.touchStartForcePreventDefault||x)&&!d.isContentEditable&&s.preventDefault(),n.freeMode&&n.freeMode.enabled&&e.freeMode&&e.animating&&!n.cssMode&&e.freeMode.onTouchStart(),e.emit("touchStart",s)}function Lt(i){const e=j(),t=this,s=t.touchEventsData,{params:r,touches:n,rtlTranslate:o,enabled:l}=t;if(!l||!r.simulateTouch&&i.pointerType==="mouse")return;let d=i;if(d.originalEvent&&(d=d.originalEvent),d.type==="pointermove"&&(s.touchId!==null||d.pointerId!==s.pointerId))return;let a;if(d.type==="touchmove"){if(a=[...d.changedTouches].find(S=>S.identifier===s.touchId),!a||a.identifier!==s.touchId)return}else a=d;if(!s.isTouched){s.startMoving&&s.isScrolling&&t.emit("touchMoveOpposite",d);return}const c=a.pageX,u=a.pageY;if(d.preventedByNestedSwiper){n.startX=c,n.startY=u;return}if(!t.allowTouchMove){d.target.matches(s.focusableElements)||(t.allowClick=!1),s.isTouched&&(Object.assign(n,{startX:c,startY:u,currentX:c,currentY:u}),s.touchStartTime=U());return}if(r.touchReleaseOnEdges&&!r.loop)if(t.isVertical()){if(u<n.startY&&t.translate<=t.maxTranslate()||u>n.startY&&t.translate>=t.minTranslate()){s.isTouched=!1,s.isMoved=!1;return}}else{if(o&&(c>n.startX&&-t.translate<=t.maxTranslate()||c<n.startX&&-t.translate>=t.minTranslate()))return;if(!o&&(c<n.startX&&t.translate<=t.maxTranslate()||c>n.startX&&t.translate>=t.minTranslate()))return}if(e.activeElement&&e.activeElement.matches(s.focusableElements)&&e.activeElement!==d.target&&d.pointerType!=="mouse"&&e.activeElement.blur(),e.activeElement&&d.target===e.activeElement&&d.target.matches(s.focusableElements)){s.isMoved=!0,t.allowClick=!1;return}s.allowTouchCallbacks&&t.emit("touchMove",d),n.previousX=n.currentX,n.previousY=n.currentY,n.currentX=c,n.currentY=u;const f=n.currentX-n.startX,h=n.currentY-n.startY;if(t.params.threshold&&Math.sqrt(f**2+h**2)<t.params.threshold)return;if(typeof s.isScrolling>"u"){let S;t.isHorizontal()&&n.currentY===n.startY||t.isVertical()&&n.currentX===n.startX?s.isScrolling=!1:f*f+h*h>=25&&(S=Math.atan2(Math.abs(h),Math.abs(f))*180/Math.PI,s.isScrolling=t.isHorizontal()?S>r.touchAngle:90-S>r.touchAngle)}if(s.isScrolling&&t.emit("touchMoveOpposite",d),typeof s.startMoving>"u"&&(n.currentX!==n.startX||n.currentY!==n.startY)&&(s.startMoving=!0),s.isScrolling||d.type==="touchmove"&&s.preventTouchMoveFromPointerMove){s.isTouched=!1;return}if(!s.startMoving)return;t.allowClick=!1,!r.cssMode&&d.cancelable&&d.preventDefault(),r.touchMoveStopPropagation&&!r.nested&&d.stopPropagation();let p=t.isHorizontal()?f:h,m=t.isHorizontal()?n.currentX-n.previousX:n.currentY-n.previousY;r.oneWayMovement&&(p=Math.abs(p)*(o?1:-1),m=Math.abs(m)*(o?1:-1)),n.diff=p,p*=r.touchRatio,o&&(p=-p,m=-m);const x=t.touchesDirection;t.swipeDirection=p>0?"prev":"next",t.touchesDirection=m>0?"prev":"next";const g=t.params.loop&&!r.cssMode,y=t.touchesDirection==="next"&&t.allowSlideNext||t.touchesDirection==="prev"&&t.allowSlidePrev;if(!s.isMoved){if(g&&y&&t.loopFix({direction:t.swipeDirection}),s.startTranslate=t.getTranslate(),t.setTransition(0),t.animating){const S=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}});t.wrapperEl.dispatchEvent(S)}s.allowMomentumBounce=!1,r.grabCursor&&(t.allowSlideNext===!0||t.allowSlidePrev===!0)&&t.setGrabCursor(!0),t.emit("sliderFirstMove",d)}if(new Date().getTime(),r._loopSwapReset!==!1&&s.isMoved&&s.allowThresholdMove&&x!==t.touchesDirection&&g&&y&&Math.abs(p)>=1){Object.assign(n,{startX:c,startY:u,currentX:c,currentY:u,startTranslate:s.currentTranslate}),s.loopSwapReset=!0,s.startTranslate=s.currentTranslate;return}t.emit("sliderMove",d),s.isMoved=!0,s.currentTranslate=p+s.startTranslate;let v=!0,C=r.resistanceRatio;if(r.touchReleaseOnEdges&&(C=0),p>0?(g&&y&&s.allowThresholdMove&&s.currentTranslate>(r.centeredSlides?t.minTranslate()-t.slidesSizesGrid[t.activeIndex+1]-(r.slidesPerView!=="auto"&&t.slides.length-r.slidesPerView>=2?t.slidesSizesGrid[t.activeIndex+1]+t.params.spaceBetween:0)-t.params.spaceBetween:t.minTranslate())&&t.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),s.currentTranslate>t.minTranslate()&&(v=!1,r.resistance&&(s.currentTranslate=t.minTranslate()-1+(-t.minTranslate()+s.startTranslate+p)**C))):p<0&&(g&&y&&s.allowThresholdMove&&s.currentTranslate<(r.centeredSlides?t.maxTranslate()+t.slidesSizesGrid[t.slidesSizesGrid.length-1]+t.params.spaceBetween+(r.slidesPerView!=="auto"&&t.slides.length-r.slidesPerView>=2?t.slidesSizesGrid[t.slidesSizesGrid.length-1]+t.params.spaceBetween:0):t.maxTranslate())&&t.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:t.slides.length-(r.slidesPerView==="auto"?t.slidesPerViewDynamic():Math.ceil(parseFloat(r.slidesPerView,10)))}),s.currentTranslate<t.maxTranslate()&&(v=!1,r.resistance&&(s.currentTranslate=t.maxTranslate()+1-(t.maxTranslate()-s.startTranslate-p)**C))),v&&(d.preventedByNestedSwiper=!0),!t.allowSlideNext&&t.swipeDirection==="next"&&s.currentTranslate<s.startTranslate&&(s.currentTranslate=s.startTranslate),!t.allowSlidePrev&&t.swipeDirection==="prev"&&s.currentTranslate>s.startTranslate&&(s.currentTranslate=s.startTranslate),!t.allowSlidePrev&&!t.allowSlideNext&&(s.currentTranslate=s.startTranslate),r.threshold>0)if(Math.abs(p)>r.threshold||s.allowThresholdMove){if(!s.allowThresholdMove){s.allowThresholdMove=!0,n.startX=n.currentX,n.startY=n.currentY,s.currentTranslate=s.startTranslate,n.diff=t.isHorizontal()?n.currentX-n.startX:n.currentY-n.startY;return}}else{s.currentTranslate=s.startTranslate;return}!r.followFinger||r.cssMode||((r.freeMode&&r.freeMode.enabled&&t.freeMode||r.watchSlidesProgress)&&(t.updateActiveIndex(),t.updateSlidesClasses()),r.freeMode&&r.freeMode.enabled&&t.freeMode&&t.freeMode.onTouchMove(),t.updateProgress(s.currentTranslate),t.setTranslate(s.currentTranslate))}function Ot(i){const e=this,t=e.touchEventsData;let s=i;s.originalEvent&&(s=s.originalEvent);let r;if(s.type==="touchend"||s.type==="touchcancel"){if(r=[...s.changedTouches].find(S=>S.identifier===t.touchId),!r||r.identifier!==t.touchId)return}else{if(t.touchId!==null||s.pointerId!==t.pointerId)return;r=s}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(s.type)&&!(["pointercancel","contextmenu"].includes(s.type)&&(e.browser.isSafari||e.browser.isWebView)))return;t.pointerId=null,t.touchId=null;const{params:o,touches:l,rtlTranslate:d,slidesGrid:a,enabled:c}=e;if(!c||!o.simulateTouch&&s.pointerType==="mouse")return;if(t.allowTouchCallbacks&&e.emit("touchEnd",s),t.allowTouchCallbacks=!1,!t.isTouched){t.isMoved&&o.grabCursor&&e.setGrabCursor(!1),t.isMoved=!1,t.startMoving=!1;return}o.grabCursor&&t.isMoved&&t.isTouched&&(e.allowSlideNext===!0||e.allowSlidePrev===!0)&&e.setGrabCursor(!1);const u=U(),f=u-t.touchStartTime;if(e.allowClick){const S=s.path||s.composedPath&&s.composedPath();e.updateClickedSlide(S&&S[0]||s.target,S),e.emit("tap click",s),f<300&&u-t.lastClickTime<300&&e.emit("doubleTap doubleClick",s)}if(t.lastClickTime=U(),re(()=>{e.destroyed||(e.allowClick=!0)}),!t.isTouched||!t.isMoved||!e.swipeDirection||l.diff===0&&!t.loopSwapReset||t.currentTranslate===t.startTranslate&&!t.loopSwapReset){t.isTouched=!1,t.isMoved=!1,t.startMoving=!1;return}t.isTouched=!1,t.isMoved=!1,t.startMoving=!1;let h;if(o.followFinger?h=d?e.translate:-e.translate:h=-t.currentTranslate,o.cssMode)return;if(o.freeMode&&o.freeMode.enabled){e.freeMode.onTouchEnd({currentPos:h});return}const p=h>=-e.maxTranslate()&&!e.params.loop;let m=0,x=e.slidesSizesGrid[0];for(let S=0;S<a.length;S+=S<o.slidesPerGroupSkip?1:o.slidesPerGroup){const L=S<o.slidesPerGroupSkip-1?1:o.slidesPerGroup;typeof a[S+L]<"u"?(p||h>=a[S]&&h<a[S+L])&&(m=S,x=a[S+L]-a[S]):(p||h>=a[S])&&(m=S,x=a[a.length-1]-a[a.length-2])}let g=null,y=null;o.rewind&&(e.isBeginning?y=o.virtual&&o.virtual.enabled&&e.virtual?e.virtual.slides.length-1:e.slides.length-1:e.isEnd&&(g=0));const v=(h-a[m])/x,C=m<o.slidesPerGroupSkip-1?1:o.slidesPerGroup;if(f>o.longSwipesMs){if(!o.longSwipes){e.slideTo(e.activeIndex);return}e.swipeDirection==="next"&&(v>=o.longSwipesRatio?e.slideTo(o.rewind&&e.isEnd?g:m+C):e.slideTo(m)),e.swipeDirection==="prev"&&(v>1-o.longSwipesRatio?e.slideTo(m+C):y!==null&&v<0&&Math.abs(v)>o.longSwipesRatio?e.slideTo(y):e.slideTo(m))}else{if(!o.shortSwipes){e.slideTo(e.activeIndex);return}e.navigation&&(s.target===e.navigation.nextEl||s.target===e.navigation.prevEl)?s.target===e.navigation.nextEl?e.slideTo(m+C):e.slideTo(m):(e.swipeDirection==="next"&&e.slideTo(g!==null?g:m+C),e.swipeDirection==="prev"&&e.slideTo(y!==null?y:m))}}function he(){const i=this,{params:e,el:t}=i;if(t&&t.offsetWidth===0)return;e.breakpoints&&i.setBreakpoint();const{allowSlideNext:s,allowSlidePrev:r,snapGrid:n}=i,o=i.virtual&&i.params.virtual.enabled;i.allowSlideNext=!0,i.allowSlidePrev=!0,i.updateSize(),i.updateSlides(),i.updateSlidesClasses();const l=o&&e.loop;(e.slidesPerView==="auto"||e.slidesPerView>1)&&i.isEnd&&!i.isBeginning&&!i.params.centeredSlides&&!l?i.slideTo(i.slides.length-1,0,!1,!0):i.params.loop&&!o?i.slideToLoop(i.realIndex,0,!1,!0):i.slideTo(i.activeIndex,0,!1,!0),i.autoplay&&i.autoplay.running&&i.autoplay.paused&&(clearTimeout(i.autoplay.resizeTimeout),i.autoplay.resizeTimeout=setTimeout(()=>{i.autoplay&&i.autoplay.running&&i.autoplay.paused&&i.autoplay.resume()},500)),i.allowSlidePrev=r,i.allowSlideNext=s,i.params.watchOverflow&&n!==i.snapGrid&&i.checkOverflow()}function zt(i){const e=this;e.enabled&&(e.allowClick||(e.params.preventClicks&&i.preventDefault(),e.params.preventClicksPropagation&&e.animating&&(i.stopPropagation(),i.stopImmediatePropagation())))}function At(){const i=this,{wrapperEl:e,rtlTranslate:t,enabled:s}=i;if(!s)return;i.previousTranslate=i.translate,i.isHorizontal()?i.translate=-e.scrollLeft:i.translate=-e.scrollTop,i.translate===0&&(i.translate=0),i.updateActiveIndex(),i.updateSlidesClasses();let r;const n=i.maxTranslate()-i.minTranslate();n===0?r=0:r=(i.translate-i.minTranslate())/n,r!==i.progress&&i.updateProgress(t?-i.translate:i.translate),i.emit("setTranslate",i.translate,!1)}function kt(i){const e=this;q(e,i.target),!(e.params.cssMode||e.params.slidesPerView!=="auto"&&!e.params.autoHeight)&&e.update()}function Gt(){const i=this;i.documentTouchHandlerProceeded||(i.documentTouchHandlerProceeded=!0,i.params.touchReleaseOnEdges&&(i.el.style.touchAction="auto"))}const Ce=(i,e)=>{const t=j(),{params:s,el:r,wrapperEl:n,device:o}=i,l=!!s.nested,d=e==="on"?"addEventListener":"removeEventListener",a=e;!r||typeof r=="string"||(t[d]("touchstart",i.onDocumentTouchStart,{passive:!1,capture:l}),r[d]("touchstart",i.onTouchStart,{passive:!1}),r[d]("pointerdown",i.onTouchStart,{passive:!1}),t[d]("touchmove",i.onTouchMove,{passive:!1,capture:l}),t[d]("pointermove",i.onTouchMove,{passive:!1,capture:l}),t[d]("touchend",i.onTouchEnd,{passive:!0}),t[d]("pointerup",i.onTouchEnd,{passive:!0}),t[d]("pointercancel",i.onTouchEnd,{passive:!0}),t[d]("touchcancel",i.onTouchEnd,{passive:!0}),t[d]("pointerout",i.onTouchEnd,{passive:!0}),t[d]("pointerleave",i.onTouchEnd,{passive:!0}),t[d]("contextmenu",i.onTouchEnd,{passive:!0}),(s.preventClicks||s.preventClicksPropagation)&&r[d]("click",i.onClick,!0),s.cssMode&&n[d]("scroll",i.onScroll),s.updateOnWindowResize?i[a](o.ios||o.android?"resize orientationchange observerUpdate":"resize observerUpdate",he,!0):i[a]("observerUpdate",he,!0),r[d]("load",i.onLoad,{capture:!0}))};function Dt(){const i=this,{params:e}=i;i.onTouchStart=Pt.bind(i),i.onTouchMove=Lt.bind(i),i.onTouchEnd=Ot.bind(i),i.onDocumentTouchStart=Gt.bind(i),e.cssMode&&(i.onScroll=At.bind(i)),i.onClick=zt.bind(i),i.onLoad=kt.bind(i),Ce(i,"on")}function _t(){Ce(this,"off")}var Bt={attachEvents:Dt,detachEvents:_t};const ge=(i,e)=>i.grid&&e.grid&&e.grid.rows>1;function Ft(){const i=this,{realIndex:e,initialized:t,params:s,el:r}=i,n=s.breakpoints;if(!n||n&&Object.keys(n).length===0)return;const o=j(),l=s.breakpointsBase==="window"||!s.breakpointsBase?s.breakpointsBase:"container",d=["window","container"].includes(s.breakpointsBase)||!s.breakpointsBase?i.el:o.querySelector(s.breakpointsBase),a=i.getBreakpoint(n,l,d);if(!a||i.currentBreakpoint===a)return;const u=(a in n?n[a]:void 0)||i.originalParams,f=ge(i,s),h=ge(i,u),p=i.params.grabCursor,m=u.grabCursor,x=s.enabled;f&&!h?(r.classList.remove(`${s.containerModifierClass}grid`,`${s.containerModifierClass}grid-column`),i.emitContainerClasses()):!f&&h&&(r.classList.add(`${s.containerModifierClass}grid`),(u.grid.fill&&u.grid.fill==="column"||!u.grid.fill&&s.grid.fill==="column")&&r.classList.add(`${s.containerModifierClass}grid-column`),i.emitContainerClasses()),p&&!m?i.unsetGrabCursor():!p&&m&&i.setGrabCursor(),["navigation","pagination","scrollbar"].forEach(L=>{if(typeof u[L]>"u")return;const E=s[L]&&s[L].enabled,I=u[L]&&u[L].enabled;E&&!I&&i[L].disable(),!E&&I&&i[L].enable()});const g=u.direction&&u.direction!==s.direction,y=s.loop&&(u.slidesPerView!==s.slidesPerView||g),v=s.loop;g&&t&&i.changeDirection(),F(i.params,u);const C=i.params.enabled,S=i.params.loop;Object.assign(i,{allowTouchMove:i.params.allowTouchMove,allowSlideNext:i.params.allowSlideNext,allowSlidePrev:i.params.allowSlidePrev}),x&&!C?i.disable():!x&&C&&i.enable(),i.currentBreakpoint=a,i.emit("_beforeBreakpoint",u),t&&(y?(i.loopDestroy(),i.loopCreate(e),i.updateSlides()):!v&&S?(i.loopCreate(e),i.updateSlides()):v&&!S&&i.loopDestroy()),i.emit("breakpoint",u)}function Vt(i,e,t){if(e===void 0&&(e="window"),!i||e==="container"&&!t)return;let s=!1;const r=D(),n=e==="window"?r.innerHeight:t.clientHeight,o=Object.keys(i).map(l=>{if(typeof l=="string"&&l.indexOf("@")===0){const d=parseFloat(l.substr(1));return{value:n*d,point:l}}return{value:l,point:l}});o.sort((l,d)=>parseInt(l.value,10)-parseInt(d.value,10));for(let l=0;l<o.length;l+=1){const{point:d,value:a}=o[l];e==="window"?r.matchMedia(`(min-width: ${a}px)`).matches&&(s=d):a<=t.clientWidth&&(s=d)}return s||"max"}var Ht={setBreakpoint:Ft,getBreakpoint:Vt};function $t(i,e){const t=[];return i.forEach(s=>{typeof s=="object"?Object.keys(s).forEach(r=>{s[r]&&t.push(e+r)}):typeof s=="string"&&t.push(e+s)}),t}function Nt(){const i=this,{classNames:e,params:t,rtl:s,el:r,device:n}=i,o=$t(["initialized",t.direction,{"free-mode":i.params.freeMode&&t.freeMode.enabled},{autoheight:t.autoHeight},{rtl:s},{grid:t.grid&&t.grid.rows>1},{"grid-column":t.grid&&t.grid.rows>1&&t.grid.fill==="column"},{android:n.android},{ios:n.ios},{"css-mode":t.cssMode},{centered:t.cssMode&&t.centeredSlides},{"watch-progress":t.watchSlidesProgress}],t.containerModifierClass);e.push(...o),r.classList.add(...e),i.emitContainerClasses()}function Rt(){const i=this,{el:e,classNames:t}=i;!e||typeof e=="string"||(e.classList.remove(...t),i.emitContainerClasses())}var Wt={addClasses:Nt,removeClasses:Rt};function jt(){const i=this,{isLocked:e,params:t}=i,{slidesOffsetBefore:s}=t;if(s){const r=i.slides.length-1,n=i.slidesGrid[r]+i.slidesSizesGrid[r]+s*2;i.isLocked=i.size>n}else i.isLocked=i.snapGrid.length===1;t.allowSlideNext===!0&&(i.allowSlideNext=!i.isLocked),t.allowSlidePrev===!0&&(i.allowSlidePrev=!i.isLocked),e&&e!==i.isLocked&&(i.isEnd=!1),e!==i.isLocked&&i.emit(i.isLocked?"lock":"unlock")}var Yt={checkOverflow:jt},me={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};function Xt(i,e){return function(s){s===void 0&&(s={});const r=Object.keys(s)[0],n=s[r];if(typeof n!="object"||n===null){F(e,s);return}if(i[r]===!0&&(i[r]={enabled:!0}),r==="navigation"&&i[r]&&i[r].enabled&&!i[r].prevEl&&!i[r].nextEl&&(i[r].auto=!0),["pagination","scrollbar"].indexOf(r)>=0&&i[r]&&i[r].enabled&&!i[r].el&&(i[r].auto=!0),!(r in i&&"enabled"in n)){F(e,s);return}typeof i[r]=="object"&&!("enabled"in i[r])&&(i[r].enabled=!0),i[r]||(i[r]={enabled:!1}),F(e,s)}}const ie={eventsEmitter:We,update:tt,translate:lt,transition:ut,slide:wt,loop:xt,grabCursor:Et,events:Bt,breakpoints:Ht,checkOverflow:Yt,classes:Wt},se={};class B{constructor(){let e,t;for(var s=arguments.length,r=new Array(s),n=0;n<s;n++)r[n]=arguments[n];r.length===1&&r[0].constructor&&Object.prototype.toString.call(r[0]).slice(8,-1)==="Object"?t=r[0]:[e,t]=r,t||(t={}),t=F({},t),e&&!t.el&&(t.el=e);const o=j();if(t.el&&typeof t.el=="string"&&o.querySelectorAll(t.el).length>1){const c=[];return o.querySelectorAll(t.el).forEach(u=>{const f=F({},t,{el:u});c.push(new B(f))}),c}const l=this;l.__swiper__=!0,l.support=be(),l.device=Se({userAgent:t.userAgent}),l.browser=Te(),l.eventsListeners={},l.eventsAnyListeners=[],l.modules=[...l.__modules__],t.modules&&Array.isArray(t.modules)&&l.modules.push(...t.modules);const d={};l.modules.forEach(c=>{c({params:t,swiper:l,extendParams:Xt(t,d),on:l.on.bind(l),once:l.once.bind(l),off:l.off.bind(l),emit:l.emit.bind(l)})});const a=F({},me,d);return l.params=F({},a,se,t),l.originalParams=F({},l.params),l.passedParams=F({},t),l.params&&l.params.on&&Object.keys(l.params.on).forEach(c=>{l.on(c,l.params.on[c])}),l.params&&l.params.onAny&&l.onAny(l.params.onAny),Object.assign(l,{enabled:l.params.enabled,el:e,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal(){return l.params.direction==="horizontal"},isVertical(){return l.params.direction==="vertical"},activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return Math.trunc(this.translate/2**23)*2**23},allowSlideNext:l.params.allowSlideNext,allowSlidePrev:l.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:l.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:l.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),l.emit("_swiper"),l.params.init&&l.init(),l}getDirectionLabel(e){return this.isHorizontal()?e:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[e]}getSlideIndex(e){const{slidesEl:t,params:s}=this,r=$(t,`.${s.slideClass}, swiper-slide`),n=ce(r[0]);return ce(e)-n}getSlideIndexByData(e){return this.getSlideIndex(this.slides.find(t=>t.getAttribute("data-swiper-slide-index")*1===e))}recalcSlides(){const e=this,{slidesEl:t,params:s}=e;e.slides=$(t,`.${s.slideClass}, swiper-slide`)}enable(){const e=this;e.enabled||(e.enabled=!0,e.params.grabCursor&&e.setGrabCursor(),e.emit("enable"))}disable(){const e=this;e.enabled&&(e.enabled=!1,e.params.grabCursor&&e.unsetGrabCursor(),e.emit("disable"))}setProgress(e,t){const s=this;e=Math.min(Math.max(e,0),1);const r=s.minTranslate(),o=(s.maxTranslate()-r)*e+r;s.translateTo(o,typeof t>"u"?0:t),s.updateActiveIndex(),s.updateSlidesClasses()}emitContainerClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=e.el.className.split(" ").filter(s=>s.indexOf("swiper")===0||s.indexOf(e.params.containerModifierClass)===0);e.emit("_containerClasses",t.join(" "))}getSlideClasses(e){const t=this;return t.destroyed?"":e.className.split(" ").filter(s=>s.indexOf("swiper-slide")===0||s.indexOf(t.params.slideClass)===0).join(" ")}emitSlidesClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=[];e.slides.forEach(s=>{const r=e.getSlideClasses(s);t.push({slideEl:s,classNames:r}),e.emit("_slideClass",s,r)}),e.emit("_slideClasses",t)}slidesPerViewDynamic(e,t){e===void 0&&(e="current"),t===void 0&&(t=!1);const s=this,{params:r,slides:n,slidesGrid:o,slidesSizesGrid:l,size:d,activeIndex:a}=s;let c=1;if(typeof r.slidesPerView=="number")return r.slidesPerView;if(r.centeredSlides){let u=n[a]?Math.ceil(n[a].swiperSlideSize):0,f;for(let h=a+1;h<n.length;h+=1)n[h]&&!f&&(u+=Math.ceil(n[h].swiperSlideSize),c+=1,u>d&&(f=!0));for(let h=a-1;h>=0;h-=1)n[h]&&!f&&(u+=n[h].swiperSlideSize,c+=1,u>d&&(f=!0))}else if(e==="current")for(let u=a+1;u<n.length;u+=1)(t?o[u]+l[u]-o[a]<d:o[u]-o[a]<d)&&(c+=1);else for(let u=a-1;u>=0;u-=1)o[a]-o[u]<d&&(c+=1);return c}update(){const e=this;if(!e||e.destroyed)return;const{snapGrid:t,params:s}=e;s.breakpoints&&e.setBreakpoint(),[...e.el.querySelectorAll('[loading="lazy"]')].forEach(o=>{o.complete&&q(e,o)}),e.updateSize(),e.updateSlides(),e.updateProgress(),e.updateSlidesClasses();function r(){const o=e.rtlTranslate?e.translate*-1:e.translate,l=Math.min(Math.max(o,e.maxTranslate()),e.minTranslate());e.setTranslate(l),e.updateActiveIndex(),e.updateSlidesClasses()}let n;if(s.freeMode&&s.freeMode.enabled&&!s.cssMode)r(),s.autoHeight&&e.updateAutoHeight();else{if((s.slidesPerView==="auto"||s.slidesPerView>1)&&e.isEnd&&!s.centeredSlides){const o=e.virtual&&s.virtual.enabled?e.virtual.slides:e.slides;n=e.slideTo(o.length-1,0,!1,!0)}else n=e.slideTo(e.activeIndex,0,!1,!0);n||r()}s.watchOverflow&&t!==e.snapGrid&&e.checkOverflow(),e.emit("update")}changeDirection(e,t){t===void 0&&(t=!0);const s=this,r=s.params.direction;return e||(e=r==="horizontal"?"vertical":"horizontal"),e===r||e!=="horizontal"&&e!=="vertical"||(s.el.classList.remove(`${s.params.containerModifierClass}${r}`),s.el.classList.add(`${s.params.containerModifierClass}${e}`),s.emitContainerClasses(),s.params.direction=e,s.slides.forEach(n=>{e==="vertical"?n.style.width="":n.style.height=""}),s.emit("changeDirection"),t&&s.update()),s}changeLanguageDirection(e){const t=this;t.rtl&&e==="rtl"||!t.rtl&&e==="ltr"||(t.rtl=e==="rtl",t.rtlTranslate=t.params.direction==="horizontal"&&t.rtl,t.rtl?(t.el.classList.add(`${t.params.containerModifierClass}rtl`),t.el.dir="rtl"):(t.el.classList.remove(`${t.params.containerModifierClass}rtl`),t.el.dir="ltr"),t.update())}mount(e){const t=this;if(t.mounted)return!0;let s=e||t.params.el;if(typeof s=="string"&&(s=document.querySelector(s)),!s)return!1;s.swiper=t,s.parentNode&&s.parentNode.host&&s.parentNode.host.nodeName===t.params.swiperElementNodeName.toUpperCase()&&(t.isElement=!0);const r=()=>`.${(t.params.wrapperClass||"").trim().split(" ").join(".")}`;let o=s&&s.shadowRoot&&s.shadowRoot.querySelector?s.shadowRoot.querySelector(r()):$(s,r())[0];return!o&&t.params.createElements&&(o=ne("div",t.params.wrapperClass),s.append(o),$(s,`.${t.params.slideClass}`).forEach(l=>{o.append(l)})),Object.assign(t,{el:s,wrapperEl:o,slidesEl:t.isElement&&!s.parentNode.host.slideSlots?s.parentNode.host:o,hostEl:t.isElement?s.parentNode.host:s,mounted:!0,rtl:s.dir.toLowerCase()==="rtl"||W(s,"direction")==="rtl",rtlTranslate:t.params.direction==="horizontal"&&(s.dir.toLowerCase()==="rtl"||W(s,"direction")==="rtl"),wrongRTL:W(o,"display")==="-webkit-box"}),!0}init(e){const t=this;if(t.initialized||t.mount(e)===!1)return t;t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.loop&&t.virtual&&t.params.virtual.enabled?t.slideTo(t.params.initialSlide+t.virtual.slidesBefore,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.params.loop&&t.loopCreate(void 0,!0),t.attachEvents();const r=[...t.el.querySelectorAll('[loading="lazy"]')];return t.isElement&&r.push(...t.hostEl.querySelectorAll('[loading="lazy"]')),r.forEach(n=>{n.complete?q(t,n):n.addEventListener("load",o=>{q(t,o.target)})}),oe(t),t.initialized=!0,oe(t),t.emit("init"),t.emit("afterInit"),t}destroy(e,t){e===void 0&&(e=!0),t===void 0&&(t=!0);const s=this,{params:r,el:n,wrapperEl:o,slides:l}=s;return typeof s.params>"u"||s.destroyed||(s.emit("beforeDestroy"),s.initialized=!1,s.detachEvents(),r.loop&&s.loopDestroy(),t&&(s.removeClasses(),n&&typeof n!="string"&&n.removeAttribute("style"),o&&o.removeAttribute("style"),l&&l.length&&l.forEach(d=>{d.classList.remove(r.slideVisibleClass,r.slideFullyVisibleClass,r.slideActiveClass,r.slideNextClass,r.slidePrevClass),d.removeAttribute("style"),d.removeAttribute("data-swiper-slide-index")})),s.emit("destroy"),Object.keys(s.eventsListeners).forEach(d=>{s.off(d)}),e!==!1&&(s.el&&typeof s.el!="string"&&(s.el.swiper=null),Oe(s)),s.destroyed=!0),null}static extendDefaults(e){F(se,e)}static get extendedDefaults(){return se}static get defaults(){return me}static installModule(e){B.prototype.__modules__||(B.prototype.__modules__=[]);const t=B.prototype.__modules__;typeof e=="function"&&t.indexOf(e)<0&&t.push(e)}static use(e){return Array.isArray(e)?(e.forEach(t=>B.installModule(t)),B):(B.installModule(e),B)}}Object.keys(ie).forEach(i=>{Object.keys(ie[i]).forEach(e=>{B.prototype[e]=ie[i][e]})});B.use([Ne,Re]);const oi=Object.freeze(Object.defineProperty({__proto__:null,Swiper:B,default:B},Symbol.toStringTag,{value:"Module"}));/*!
 * lightgallery | 2.8.3 | March 1st 2025
 * http://www.lightgalleryjs.com/
 * Copyright (c) 2020 Sachin Neravath;
 * @license GPLv3
 *//*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var R=function(){return R=Object.assign||function(e){for(var t,s=1,r=arguments.length;s<r;s++){t=arguments[s];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e},R.apply(this,arguments)};function qt(){for(var i=0,e=0,t=arguments.length;e<t;e++)i+=arguments[e].length;for(var s=Array(i),r=0,e=0;e<t;e++)for(var n=arguments[e],o=0,l=n.length;o<l;o++,r++)s[r]=n[o];return s}var O={afterAppendSlide:"lgAfterAppendSlide",init:"lgInit",hasVideo:"lgHasVideo",containerResize:"lgContainerResize",updateSlides:"lgUpdateSlides",afterAppendSubHtml:"lgAfterAppendSubHtml",beforeOpen:"lgBeforeOpen",afterOpen:"lgAfterOpen",slideItemLoad:"lgSlideItemLoad",beforeSlide:"lgBeforeSlide",afterSlide:"lgAfterSlide",posterClick:"lgPosterClick",dragStart:"lgDragStart",dragMove:"lgDragMove",dragEnd:"lgDragEnd",beforeNextSlide:"lgBeforeNextSlide",beforePrevSlide:"lgBeforePrevSlide",beforeClose:"lgBeforeClose",afterClose:"lgAfterClose"},Ut={mode:"lg-slide",easing:"ease",speed:400,licenseKey:"0000-0000-000-0000",height:"100%",width:"100%",addClass:"",startClass:"lg-start-zoom",backdropDuration:300,container:"",startAnimationDuration:400,zoomFromOrigin:!0,hideBarsDelay:0,showBarsAfter:1e4,slideDelay:0,supportLegacyBrowser:!0,allowMediaOverlap:!1,videoMaxSize:"1280-720",loadYouTubePoster:!0,defaultCaptionHeight:0,ariaLabelledby:"",ariaDescribedby:"",resetScrollPosition:!0,hideScrollbar:!1,closable:!0,swipeToClose:!0,closeOnTap:!0,showCloseIcon:!0,showMaximizeIcon:!1,loop:!0,escKey:!0,keyPress:!0,trapFocus:!0,controls:!0,slideEndAnimation:!0,hideControlOnEnd:!1,mousewheel:!1,getCaptionFromTitleOrAlt:!0,appendSubHtmlTo:".lg-sub-html",subHtmlSelectorRelative:!1,preload:2,numberOfSlideItemsInDom:10,selector:"",selectWithin:"",nextHtml:"",prevHtml:"",index:0,iframeWidth:"100%",iframeHeight:"100%",iframeMaxWidth:"100%",iframeMaxHeight:"100%",download:!0,counter:!0,appendCounterTo:".lg-toolbar",swipeThreshold:50,enableSwipe:!0,enableDrag:!0,dynamic:!1,dynamicEl:[],extraProps:[],exThumbImage:"",isMobile:void 0,mobileSettings:{controls:!1,showCloseIcon:!1,download:!1},plugins:[],strings:{closeGallery:"Close gallery",toggleMaximize:"Toggle maximize",previousSlide:"Previous slide",nextSlide:"Next slide",download:"Download",playVideo:"Play video",mediaLoadingFailed:"Oops... Failed to load content..."}};function Kt(){(function(){if(typeof window.CustomEvent=="function")return!1;function i(e,t){t=t||{bubbles:!1,cancelable:!1,detail:null};var s=document.createEvent("CustomEvent");return s.initCustomEvent(e,t.bubbles,t.cancelable,t.detail),s}window.CustomEvent=i})(),function(){Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector)}()}var Ie=function(){function i(e){return this.cssVenderPrefixes=["TransitionDuration","TransitionTimingFunction","Transform","Transition"],this.selector=this._getSelector(e),this.firstElement=this._getFirstEl(),this}return i.generateUUID=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=Math.random()*16|0,s=e=="x"?t:t&3|8;return s.toString(16)})},i.prototype._getSelector=function(e,t){if(t===void 0&&(t=document),typeof e!="string")return e;t=t||document;var s=e.substring(0,1);return s==="#"?t.querySelector(e):t.querySelectorAll(e)},i.prototype._each=function(e){return this.selector?(this.selector.length!==void 0?[].forEach.call(this.selector,e):e(this.selector,0),this):this},i.prototype._setCssVendorPrefix=function(e,t,s){var r=t.replace(/-([a-z])/gi,function(n,o){return o.toUpperCase()});this.cssVenderPrefixes.indexOf(r)!==-1?(e.style[r.charAt(0).toLowerCase()+r.slice(1)]=s,e.style["webkit"+r]=s,e.style["moz"+r]=s,e.style["ms"+r]=s,e.style["o"+r]=s):e.style[r]=s},i.prototype._getFirstEl=function(){return this.selector&&this.selector.length!==void 0?this.selector[0]:this.selector},i.prototype.isEventMatched=function(e,t){var s=t.split(".");return e.split(".").filter(function(r){return r}).every(function(r){return s.indexOf(r)!==-1})},i.prototype.attr=function(e,t){return t===void 0?this.firstElement?this.firstElement.getAttribute(e):"":(this._each(function(s){s.setAttribute(e,t)}),this)},i.prototype.find=function(e){return b(this._getSelector(e,this.selector))},i.prototype.first=function(){return this.selector&&this.selector.length!==void 0?b(this.selector[0]):b(this.selector)},i.prototype.eq=function(e){return b(this.selector[e])},i.prototype.parent=function(){return b(this.selector.parentElement)},i.prototype.get=function(){return this._getFirstEl()},i.prototype.removeAttr=function(e){var t=e.split(" ");return this._each(function(s){t.forEach(function(r){return s.removeAttribute(r)})}),this},i.prototype.wrap=function(e){if(!this.firstElement)return this;var t=document.createElement("div");return t.className=e,this.firstElement.parentNode.insertBefore(t,this.firstElement),this.firstElement.parentNode.removeChild(this.firstElement),t.appendChild(this.firstElement),this},i.prototype.addClass=function(e){return e===void 0&&(e=""),this._each(function(t){e.split(" ").forEach(function(s){s&&t.classList.add(s)})}),this},i.prototype.removeClass=function(e){return this._each(function(t){e.split(" ").forEach(function(s){s&&t.classList.remove(s)})}),this},i.prototype.hasClass=function(e){return this.firstElement?this.firstElement.classList.contains(e):!1},i.prototype.hasAttribute=function(e){return this.firstElement?this.firstElement.hasAttribute(e):!1},i.prototype.toggleClass=function(e){return this.firstElement?(this.hasClass(e)?this.removeClass(e):this.addClass(e),this):this},i.prototype.css=function(e,t){var s=this;return this._each(function(r){s._setCssVendorPrefix(r,e,t)}),this},i.prototype.on=function(e,t){var s=this;return this.selector?(e.split(" ").forEach(function(r){Array.isArray(i.eventListeners[r])||(i.eventListeners[r]=[]),i.eventListeners[r].push(t),s.selector.addEventListener(r.split(".")[0],t)}),this):this},i.prototype.once=function(e,t){var s=this;return this.on(e,function(){s.off(e),t(e)}),this},i.prototype.off=function(e){var t=this;return this.selector?(Object.keys(i.eventListeners).forEach(function(s){t.isEventMatched(e,s)&&(i.eventListeners[s].forEach(function(r){t.selector.removeEventListener(s.split(".")[0],r)}),i.eventListeners[s]=[])}),this):this},i.prototype.trigger=function(e,t){if(!this.firstElement)return this;var s=new CustomEvent(e.split(".")[0],{detail:t||null});return this.firstElement.dispatchEvent(s),this},i.prototype.load=function(e){var t=this;return fetch(e).then(function(s){return s.text()}).then(function(s){t.selector.innerHTML=s}),this},i.prototype.html=function(e){return e===void 0?this.firstElement?this.firstElement.innerHTML:"":(this._each(function(t){t.innerHTML=e}),this)},i.prototype.append=function(e){return this._each(function(t){typeof e=="string"?t.insertAdjacentHTML("beforeend",e):t.appendChild(e)}),this},i.prototype.prepend=function(e){return this._each(function(t){typeof e=="string"?t.insertAdjacentHTML("afterbegin",e):e instanceof HTMLElement&&t.insertBefore(e.cloneNode(!0),t.firstChild)}),this},i.prototype.remove=function(){return this._each(function(e){e.parentNode.removeChild(e)}),this},i.prototype.empty=function(){return this._each(function(e){e.innerHTML=""}),this},i.prototype.scrollTop=function(e){return e!==void 0?(document.body.scrollTop=e,document.documentElement.scrollTop=e,this):window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0},i.prototype.scrollLeft=function(e){return e!==void 0?(document.body.scrollLeft=e,document.documentElement.scrollLeft=e,this):window.pageXOffset||document.documentElement.scrollLeft||document.body.scrollLeft||0},i.prototype.offset=function(){if(!this.firstElement)return{left:0,top:0};var e=this.firstElement.getBoundingClientRect(),t=b("body").style().marginLeft;return{left:e.left-parseFloat(t)+this.scrollLeft(),top:e.top+this.scrollTop()}},i.prototype.style=function(){return this.firstElement?this.firstElement.currentStyle||window.getComputedStyle(this.firstElement):{}},i.prototype.width=function(){var e=this.style();return this.firstElement.clientWidth-parseFloat(e.paddingLeft)-parseFloat(e.paddingRight)},i.prototype.height=function(){var e=this.style();return this.firstElement.clientHeight-parseFloat(e.paddingTop)-parseFloat(e.paddingBottom)},i.eventListeners={},i}();function b(i){return Kt(),new Ie(i)}var Zt=["src","sources","subHtml","subHtmlUrl","html","video","poster","slideName","responsive","srcset","sizes","iframe","downloadUrl","download","width","facebookShareUrl","tweetText","iframeTitle","twitterShareUrl","pinterestShareUrl","pinterestText","fbHtml","disqusIdentifier","disqusUrl"];function Qt(i){return i==="href"?"src":(i=i.replace("data-",""),i=i.charAt(0).toLowerCase()+i.slice(1),i=i.replace(/-([a-z])/g,function(e){return e[1].toUpperCase()}),i)}var A={fetchCaptionFromUrl:function(i,e,t){fetch(i).then(function(s){return s.text()}).then(function(s){if(t==="append"){var r='<div class="lg-sub-html">'+s+"</div>";e.append(r)}else e.html(s)})},getSize:function(i,e,t,s){t===void 0&&(t=0);var r=b(i),n=r.attr("data-lg-size")||s;if(n){var o=n.split(",");if(o[1])for(var l=window.innerWidth,d=0;d<o.length;d++){var a=o[d],c=parseInt(a.split("-")[2],10);if(c>l){n=a;break}d===o.length-1&&(n=a)}var u=n.split("-"),f=parseInt(u[0],10),h=parseInt(u[1],10),p=e.width(),m=e.height()-t,x=Math.min(p,f),g=Math.min(m,h),y=Math.min(x/f,g/h);return{width:f*y,height:h*y}}},getTransform:function(i,e,t,s,r){if(r){var n=b(i).find("img").first();if(n.get()){var o=e.get().getBoundingClientRect(),l=o.width,d=e.height()-(t+s),a=n.width(),c=n.height(),u=n.style(),f=(l-a)/2-n.offset().left+(parseFloat(u.paddingLeft)||0)+(parseFloat(u.borderLeft)||0)+b(window).scrollLeft()+o.left,h=(d-c)/2-n.offset().top+(parseFloat(u.paddingTop)||0)+(parseFloat(u.borderTop)||0)+b(window).scrollTop()+t,p=a/r.width,m=c/r.height,x="translate3d("+(f*=-1)+"px, "+(h*=-1)+"px, 0) scale3d("+p+", "+m+", 1)";return x}}},getIframeMarkup:function(i,e,t,s,r,n){var o=n?'title="'+n+'"':"";return'<div class="lg-media-cont lg-has-iframe" style="width:'+i+"; max-width:"+t+"; height: "+e+"; max-height:"+s+`">
                    <iframe class="lg-object" frameborder="0" `+o+' src="'+r+`"  allowfullscreen="true"></iframe>
                </div>`},getImgMarkup:function(i,e,t,s,r,n){var o=s?'srcset="'+s+'"':"",l=r?'sizes="'+r+'"':"",d="<img "+t+" "+o+"  "+l+' class="lg-object lg-image" data-index="'+i+'" src="'+e+'" />',a="";if(n){var c=typeof n=="string"?JSON.parse(n):n;a=c.map(function(u){var f="";return Object.keys(u).forEach(function(h){f+=" "+h+'="'+u[h]+'"'}),"<source "+f+"></source>"})}return""+a+d},getResponsiveSrc:function(i){for(var e=[],t=[],s="",r=0;r<i.length;r++){var n=i[r].split(" ");n[0]===""&&n.splice(0,1),t.push(n[0]),e.push(n[1])}for(var o=window.innerWidth,l=0;l<e.length;l++)if(parseInt(e[l],10)>o){s=t[l];break}return s},isImageLoaded:function(i){return!(!i||!i.complete||i.naturalWidth===0)},getVideoPosterMarkup:function(i,e,t,s,r){var n="";r&&r.youtube?n="lg-has-youtube":r&&r.vimeo?n="lg-has-vimeo":n="lg-has-html5";var o=e;return typeof e!="string"&&(o=e.outerHTML),'<div class="lg-video-cont '+n+'" style="'+t+`">
                <div class="lg-video-play-button">
                <svg
                    viewBox="0 0 20 20"
                    preserveAspectRatio="xMidYMid"
                    focusable="false"
                    aria-labelledby="`+s+`"
                    role="img"
                    class="lg-video-play-icon"
                >
                    <title>`+s+`</title>
                    <polygon class="lg-video-play-icon-inner" points="1,0 20,10 1,20"></polygon>
                </svg>
                <svg class="lg-video-play-icon-bg" viewBox="0 0 50 50" focusable="false">
                    <circle cx="50%" cy="50%" r="20"></circle></svg>
                <svg class="lg-video-play-icon-circle" viewBox="0 0 50 50" focusable="false">
                    <circle cx="50%" cy="50%" r="20"></circle>
                </svg>
            </div>
            `+o+`
            <img class="lg-object lg-video-poster" src="`+i+`" />
        </div>`},getFocusableElements:function(i){var e=i.querySelectorAll('a[href]:not([disabled]), button:not([disabled]), textarea:not([disabled]), input[type="text"]:not([disabled]), input[type="radio"]:not([disabled]), input[type="checkbox"]:not([disabled]), select:not([disabled])'),t=[].filter.call(e,function(s){var r=window.getComputedStyle(s);return r.display!=="none"&&r.visibility!=="hidden"});return t},getDynamicOptions:function(i,e,t,s){var r=[],n=qt(Zt,e);return[].forEach.call(i,function(o){for(var l={},d=0;d<o.attributes.length;d++){var a=o.attributes[d];if(a.specified){var c=Qt(a.name),u="";n.indexOf(c)>-1&&(u=c),u&&(l[u]=a.value)}}var f=b(o),h=f.find("img").first().attr("alt"),p=f.attr("title"),m=s?f.attr(s):f.find("img").first().attr("src");l.thumb=m,t&&!l.subHtml&&(l.subHtml=p||h||""),l.alt=h||p||"",r.push(l)}),r},isMobile:function(){return/iPhone|iPad|iPod|Android/i.test(navigator.userAgent)},isVideo:function(i,e,t){if(!i){if(e)return{html5:!0};console.error("lightGallery :- data-src is not provided on slide item "+(t+1)+". Please make sure the selector property is properly configured. More info - https://www.lightgalleryjs.com/demos/html-markup/");return}var s=i.match(/\/\/(?:www\.)?youtu(?:\.be|be\.com|be-nocookie\.com)\/(?:watch\?v=|embed\/)?([a-z0-9\-\_\%]+)([\&|?][\S]*)*/i),r=i.match(/\/\/(?:www\.)?(?:player\.)?vimeo.com\/(?:video\/)?([0-9a-z\-_]+)(.*)?/i),n=i.match(/https?:\/\/(.+)?(wistia\.com|wi\.st)\/(medias|embed)\/([0-9a-z\-_]+)(.*)/);if(s)return{youtube:s};if(r)return{vimeo:r};if(n)return{wistia:n}}},ve=0,Jt=function(){function i(e,t){if(this.lgOpened=!1,this.index=0,this.plugins=[],this.lGalleryOn=!1,this.lgBusy=!1,this.currentItemsInDom=[],this.prevScrollTop=0,this.bodyPaddingRight=0,this.isDummyImageRemoved=!1,this.dragOrSwipeEnabled=!1,this.mediaContainerPosition={top:0,bottom:0},!e)return this;if(ve++,this.lgId=ve,this.el=e,this.LGel=b(e),this.generateSettings(t),this.buildModules(),this.settings.dynamic&&this.settings.dynamicEl!==void 0&&!Array.isArray(this.settings.dynamicEl))throw"When using dynamic mode, you must also define dynamicEl as an Array.";return this.galleryItems=this.getItems(),this.normalizeSettings(),this.init(),this.validateLicense(),this}return i.prototype.generateSettings=function(e){if(this.settings=R(R({},Ut),e),this.settings.isMobile&&typeof this.settings.isMobile=="function"?this.settings.isMobile():A.isMobile()){var t=R(R({},this.settings.mobileSettings),this.settings.mobileSettings);this.settings=R(R({},this.settings),t)}},i.prototype.normalizeSettings=function(){if(this.settings.slideEndAnimation&&(this.settings.hideControlOnEnd=!1),this.settings.closable||(this.settings.swipeToClose=!1),this.zoomFromOrigin=this.settings.zoomFromOrigin,this.settings.dynamic&&(this.zoomFromOrigin=!1),this.settings.container){var e=this.settings.container;if(typeof e=="function")this.settings.container=e();else if(typeof e=="string"){var t=document.querySelector(e);this.settings.container=t??document.body}}else this.settings.container=document.body;this.settings.preload=Math.min(this.settings.preload,this.galleryItems.length)},i.prototype.init=function(){var e=this;this.addSlideVideoInfo(this.galleryItems),this.buildStructure(),this.LGel.trigger(O.init,{instance:this}),this.settings.keyPress&&this.keyPress(),setTimeout(function(){e.enableDrag(),e.enableSwipe(),e.triggerPosterClick()},50),this.arrow(),this.settings.mousewheel&&this.mousewheel(),this.settings.dynamic||this.openGalleryOnItemClick()},i.prototype.openGalleryOnItemClick=function(){for(var e=this,t=function(n){var o=s.items[n],l=b(o),d=Ie.generateUUID();l.attr("data-lg-id",d).on("click.lgcustom-item-"+d,function(a){a.preventDefault();var c=e.settings.index||n;e.openGallery(c,o)})},s=this,r=0;r<this.items.length;r++)t(r)},i.prototype.buildModules=function(){var e=this;this.settings.plugins.forEach(function(t){e.plugins.push(new t(e,b))})},i.prototype.validateLicense=function(){this.settings.licenseKey?this.settings.licenseKey==="0000-0000-000-0000"&&console.warn("lightGallery: "+this.settings.licenseKey+" license key is not valid for production use"):console.error("Please provide a valid license key")},i.prototype.getSlideItem=function(e){return b(this.getSlideItemId(e))},i.prototype.getSlideItemId=function(e){return"#lg-item-"+this.lgId+"-"+e},i.prototype.getIdName=function(e){return e+"-"+this.lgId},i.prototype.getElementById=function(e){return b("#"+this.getIdName(e))},i.prototype.manageSingleSlideClassName=function(){this.galleryItems.length<2?this.outer.addClass("lg-single-item"):this.outer.removeClass("lg-single-item")},i.prototype.buildStructure=function(){var e=this,t=this.$container&&this.$container.get();if(!t){var s="",r="";this.settings.controls&&(s='<button type="button" id="'+this.getIdName("lg-prev")+'" aria-label="'+this.settings.strings.previousSlide+'" class="lg-prev lg-icon"> '+this.settings.prevHtml+` </button>
                <button type="button" id="`+this.getIdName("lg-next")+'" aria-label="'+this.settings.strings.nextSlide+'" class="lg-next lg-icon"> '+this.settings.nextHtml+" </button>"),this.settings.appendSubHtmlTo!==".lg-item"&&(r='<div class="lg-sub-html" role="status" aria-live="polite"></div>');var n="";this.settings.allowMediaOverlap&&(n+="lg-media-overlap ");var o=this.settings.ariaLabelledby?'aria-labelledby="'+this.settings.ariaLabelledby+'"':"",l=this.settings.ariaDescribedby?'aria-describedby="'+this.settings.ariaDescribedby+'"':"",d="lg-container "+this.settings.addClass+" "+(document.body!==this.settings.container?"lg-inline":""),a=this.settings.closable&&this.settings.showCloseIcon?'<button type="button" aria-label="'+this.settings.strings.closeGallery+'" id="'+this.getIdName("lg-close")+'" class="lg-close lg-icon"></button>':"",c=this.settings.showMaximizeIcon?'<button type="button" aria-label="'+this.settings.strings.toggleMaximize+'" id="'+this.getIdName("lg-maximize")+'" class="lg-maximize lg-icon"></button>':"",u=`
        <div class="`+d+'" id="'+this.getIdName("lg-container")+'" tabindex="-1" aria-modal="true" '+o+" "+l+` role="dialog"
        >
            <div id="`+this.getIdName("lg-backdrop")+`" class="lg-backdrop"></div>

            <div id="`+this.getIdName("lg-outer")+'" class="lg-outer lg-use-css3 lg-css3 lg-hide-items '+n+` ">

              <div id="`+this.getIdName("lg-content")+`" class="lg-content">
                <div id="`+this.getIdName("lg-inner")+`" class="lg-inner">
                </div>
                `+s+`
              </div>
                <div id="`+this.getIdName("lg-toolbar")+`" class="lg-toolbar lg-group">
                    `+c+`
                    `+a+`
                    </div>
                    `+(this.settings.appendSubHtmlTo===".lg-outer"?r:"")+`
                <div id="`+this.getIdName("lg-components")+`" class="lg-components">
                    `+(this.settings.appendSubHtmlTo===".lg-sub-html"?r:"")+`
                </div>
            </div>
        </div>
        `;b(this.settings.container).append(u),document.body!==this.settings.container&&b(this.settings.container).css("position","relative"),this.outer=this.getElementById("lg-outer"),this.$lgComponents=this.getElementById("lg-components"),this.$backdrop=this.getElementById("lg-backdrop"),this.$container=this.getElementById("lg-container"),this.$inner=this.getElementById("lg-inner"),this.$content=this.getElementById("lg-content"),this.$toolbar=this.getElementById("lg-toolbar"),this.$backdrop.css("transition-duration",this.settings.backdropDuration+"ms");var f=this.settings.mode+" ";this.manageSingleSlideClassName(),this.settings.enableDrag&&(f+="lg-grab "),this.outer.addClass(f),this.$inner.css("transition-timing-function",this.settings.easing),this.$inner.css("transition-duration",this.settings.speed+"ms"),this.settings.download&&this.$toolbar.append('<a id="'+this.getIdName("lg-download")+'" target="_blank" rel="noopener" aria-label="'+this.settings.strings.download+'" download class="lg-download lg-icon"></a>'),this.counter(),b(window).on("resize.lg.global"+this.lgId+" orientationchange.lg.global"+this.lgId,function(){e.refreshOnResize()}),this.hideBars(),this.manageCloseGallery(),this.toggleMaximize(),this.initModules()}},i.prototype.refreshOnResize=function(){if(this.lgOpened){var e=this.galleryItems[this.index],t=e.__slideVideoInfo;this.mediaContainerPosition=this.getMediaContainerPosition();var s=this.mediaContainerPosition,r=s.top,n=s.bottom;if(this.currentImageSize=A.getSize(this.items[this.index],this.outer,r+n,t&&this.settings.videoMaxSize),t&&this.resizeVideoSlide(this.index,this.currentImageSize),this.zoomFromOrigin&&!this.isDummyImageRemoved){var o=this.getDummyImgStyles(this.currentImageSize);this.outer.find(".lg-current .lg-dummy-img").first().attr("style",o)}this.LGel.trigger(O.containerResize)}},i.prototype.resizeVideoSlide=function(e,t){var s=this.getVideoContStyle(t),r=this.getSlideItem(e);r.find(".lg-video-cont").attr("style",s)},i.prototype.updateSlides=function(e,t){if(this.index>e.length-1&&(this.index=e.length-1),e.length===1&&(this.index=0),!e.length){this.closeGallery();return}var s=this.galleryItems[t].src;this.galleryItems=e,this.updateControls(),this.$inner.empty(),this.currentItemsInDom=[];var r=0;this.galleryItems.some(function(n,o){return n.src===s?(r=o,!0):!1}),this.currentItemsInDom=this.organizeSlideItems(r,-1),this.loadContent(r,!0),this.getSlideItem(r).addClass("lg-current"),this.index=r,this.updateCurrentCounter(r),this.LGel.trigger(O.updateSlides)},i.prototype.getItems=function(){if(this.items=[],this.settings.dynamic)return this.settings.dynamicEl||[];if(this.settings.selector==="this")this.items.push(this.el);else if(this.settings.selector)if(typeof this.settings.selector=="string")if(this.settings.selectWithin){var e=b(this.settings.selectWithin);this.items=e.find(this.settings.selector).get()}else this.items=this.el.querySelectorAll(this.settings.selector);else this.items=this.settings.selector;else this.items=this.el.children;return A.getDynamicOptions(this.items,this.settings.extraProps,this.settings.getCaptionFromTitleOrAlt,this.settings.exThumbImage)},i.prototype.shouldHideScrollbar=function(){return this.settings.hideScrollbar&&document.body===this.settings.container},i.prototype.hideScrollbar=function(){if(this.shouldHideScrollbar()){this.bodyPaddingRight=parseFloat(b("body").style().paddingRight);var e=document.documentElement.getBoundingClientRect(),t=window.innerWidth-e.width;b(document.body).css("padding-right",t+this.bodyPaddingRight+"px"),b(document.body).addClass("lg-overlay-open")}},i.prototype.resetScrollBar=function(){this.shouldHideScrollbar()&&(b(document.body).css("padding-right",this.bodyPaddingRight+"px"),b(document.body).removeClass("lg-overlay-open"))},i.prototype.openGallery=function(e,t){var s=this;if(e===void 0&&(e=this.settings.index),!this.lgOpened){this.lgOpened=!0,this.outer.removeClass("lg-hide-items"),this.hideScrollbar(),this.$container.addClass("lg-show");var r=this.getItemsToBeInsertedToDom(e,e);this.currentItemsInDom=r;var n="";r.forEach(function(f){n=n+('<div id="'+f+'" class="lg-item"></div>')}),this.$inner.append(n),this.addHtml(e);var o="";this.mediaContainerPosition=this.getMediaContainerPosition();var l=this.mediaContainerPosition,d=l.top,a=l.bottom;this.settings.allowMediaOverlap||this.setMediaContainerPosition(d,a);var c=this.galleryItems[e].__slideVideoInfo;this.zoomFromOrigin&&t&&(this.currentImageSize=A.getSize(t,this.outer,d+a,c&&this.settings.videoMaxSize),o=A.getTransform(t,this.outer,d,a,this.currentImageSize)),(!this.zoomFromOrigin||!o)&&(this.outer.addClass(this.settings.startClass),this.getSlideItem(e).removeClass("lg-complete"));var u=this.settings.zoomFromOrigin?100:this.settings.backdropDuration;setTimeout(function(){s.outer.addClass("lg-components-open")},u),this.index=e,this.LGel.trigger(O.beforeOpen),this.getSlideItem(e).addClass("lg-current"),this.lGalleryOn=!1,this.prevScrollTop=b(window).scrollTop(),setTimeout(function(){if(s.zoomFromOrigin&&o){var f=s.getSlideItem(e);f.css("transform",o),setTimeout(function(){f.addClass("lg-start-progress lg-start-end-progress").css("transition-duration",s.settings.startAnimationDuration+"ms"),s.outer.addClass("lg-zoom-from-image")}),setTimeout(function(){f.css("transform","translate3d(0, 0, 0)")},100)}setTimeout(function(){s.$backdrop.addClass("in"),s.$container.addClass("lg-show-in")},10),setTimeout(function(){s.settings.trapFocus&&document.body===s.settings.container&&s.trapFocus()},s.settings.backdropDuration+50),(!s.zoomFromOrigin||!o)&&setTimeout(function(){s.outer.addClass("lg-visible")},s.settings.backdropDuration),s.slide(e,!1,!1,!1),s.LGel.trigger(O.afterOpen)}),document.body===this.settings.container&&b("html").addClass("lg-on")}},i.prototype.getMediaContainerPosition=function(){if(this.settings.allowMediaOverlap)return{top:0,bottom:0};var e=this.$toolbar.get().clientHeight||0,t=this.outer.find(".lg-components .lg-sub-html").get(),s=this.settings.defaultCaptionHeight||t&&t.clientHeight||0,r=this.outer.find(".lg-thumb-outer").get(),n=r?r.clientHeight:0,o=n+s;return{top:e,bottom:o}},i.prototype.setMediaContainerPosition=function(e,t){e===void 0&&(e=0),t===void 0&&(t=0),this.$content.css("top",e+"px").css("bottom",t+"px")},i.prototype.hideBars=function(){var e=this;setTimeout(function(){e.outer.removeClass("lg-hide-items"),e.settings.hideBarsDelay>0&&(e.outer.on("mousemove.lg click.lg touchstart.lg",function(){e.outer.removeClass("lg-hide-items"),clearTimeout(e.hideBarTimeout),e.hideBarTimeout=setTimeout(function(){e.outer.addClass("lg-hide-items")},e.settings.hideBarsDelay)}),e.outer.trigger("mousemove.lg"))},this.settings.showBarsAfter)},i.prototype.initPictureFill=function(e){if(this.settings.supportLegacyBrowser)try{picturefill({elements:[e.get()]})}catch{console.warn("lightGallery :- If you want srcset or picture tag to be supported for older browser please include picturefil javascript library in your document.")}},i.prototype.counter=function(){if(this.settings.counter){var e=`<div class="lg-counter" role="status" aria-live="polite">
                <span id="`+this.getIdName("lg-counter-current")+'" class="lg-counter-current">'+(this.index+1)+` </span> /
                <span id="`+this.getIdName("lg-counter-all")+'" class="lg-counter-all">'+this.galleryItems.length+" </span></div>";this.outer.find(this.settings.appendCounterTo).append(e)}},i.prototype.addHtml=function(e){var t,s;if(this.galleryItems[e].subHtmlUrl?s=this.galleryItems[e].subHtmlUrl:t=this.galleryItems[e].subHtml,!s)if(t){var r=t.substring(0,1);(r==="."||r==="#")&&(this.settings.subHtmlSelectorRelative&&!this.settings.dynamic?t=b(this.items).eq(e).find(t).first().html():t=b(t).first().html())}else t="";if(this.settings.appendSubHtmlTo!==".lg-item")s?A.fetchCaptionFromUrl(s,this.outer.find(".lg-sub-html"),"replace"):this.outer.find(".lg-sub-html").html(t);else{var n=b(this.getSlideItemId(e));s?A.fetchCaptionFromUrl(s,n,"append"):n.append('<div class="lg-sub-html">'+t+"</div>")}typeof t<"u"&&t!==null&&(t===""?this.outer.find(this.settings.appendSubHtmlTo).addClass("lg-empty-html"):this.outer.find(this.settings.appendSubHtmlTo).removeClass("lg-empty-html")),this.LGel.trigger(O.afterAppendSubHtml,{index:e})},i.prototype.preload=function(e){for(var t=1;t<=this.settings.preload&&!(t>=this.galleryItems.length-e);t++)this.loadContent(e+t,!1);for(var s=1;s<=this.settings.preload&&!(e-s<0);s++)this.loadContent(e-s,!1)},i.prototype.getDummyImgStyles=function(e){return e?"width:"+e.width+`px;
                margin-left: -`+e.width/2+`px;
                margin-top: -`+e.height/2+`px;
                height:`+e.height+"px":""},i.prototype.getVideoContStyle=function(e){return e?"width:"+e.width+`px;
                height:`+e.height+"px":""},i.prototype.getDummyImageContent=function(e,t,s){var r;if(this.settings.dynamic||(r=b(this.items).eq(t)),r){var n=void 0;if(this.settings.exThumbImage?n=r.attr(this.settings.exThumbImage):n=r.find("img").first().attr("src"),!n)return"";var o=this.getDummyImgStyles(this.currentImageSize),l=document.createElement("img");return l.alt=s||"",l.src=n,l.className="lg-dummy-img",l.style.cssText=o,e.addClass("lg-first-slide"),this.outer.addClass("lg-first-slide-loading"),l}return""},i.prototype.setImgMarkup=function(e,t,s){var r=this.galleryItems[s],n=r.alt,o=r.srcset,l=r.sizes,d=r.sources,a="",c=n?'alt="'+n+'"':"";this.isFirstSlideWithZoomAnimation()?a=this.getDummyImageContent(t,s,c):a=A.getImgMarkup(s,e,c,o,l,d);var u=document.createElement("picture");u.className="lg-img-wrap",b(u).append(a),t.prepend(u)},i.prototype.onSlideObjectLoad=function(e,t,s,r){var n=e.find(".lg-object").first();A.isImageLoaded(n.get())||t?s():(n.on("load.lg error.lg",function(){s&&s()}),n.on("error.lg",function(){r&&r()}))},i.prototype.onLgObjectLoad=function(e,t,s,r,n,o){var l=this;this.onSlideObjectLoad(e,o,function(){l.triggerSlideItemLoad(e,t,s,r,n)},function(){e.addClass("lg-complete lg-complete_"),e.html('<span class="lg-error-msg">'+l.settings.strings.mediaLoadingFailed+"</span>")})},i.prototype.triggerSlideItemLoad=function(e,t,s,r,n){var o=this,l=this.galleryItems[t],d=n&&this.getSlideType(l)==="video"&&!l.poster?r:0;setTimeout(function(){e.addClass("lg-complete lg-complete_"),o.LGel.trigger(O.slideItemLoad,{index:t,delay:s||0,isFirstSlide:n})},d)},i.prototype.isFirstSlideWithZoomAnimation=function(){return!!(!this.lGalleryOn&&this.zoomFromOrigin&&this.currentImageSize)},i.prototype.addSlideVideoInfo=function(e){var t=this;e.forEach(function(s,r){s.__slideVideoInfo=A.isVideo(s.src,!!s.video,r),s.__slideVideoInfo&&t.settings.loadYouTubePoster&&!s.poster&&s.__slideVideoInfo.youtube&&(s.poster="//img.youtube.com/vi/"+s.__slideVideoInfo.youtube[1]+"/maxresdefault.jpg")})},i.prototype.loadContent=function(e,t){var s=this,r=this.galleryItems[e],n=b(this.getSlideItemId(e)),o=r.poster,l=r.srcset,d=r.sizes,a=r.sources,c=r.src,u=r.video,f=u&&typeof u=="string"?JSON.parse(u):u;if(r.responsive){var h=r.responsive.split(",");c=A.getResponsiveSrc(h)||c}var p=r.__slideVideoInfo,m="",x=!!r.iframe,g=!this.lGalleryOn,y=0;if(g&&(this.zoomFromOrigin&&this.currentImageSize?y=this.settings.startAnimationDuration+10:y=this.settings.backdropDuration+10),!n.hasClass("lg-loaded")){if(p){var v=this.mediaContainerPosition,C=v.top,S=v.bottom,L=A.getSize(this.items[e],this.outer,C+S,p&&this.settings.videoMaxSize);m=this.getVideoContStyle(L)}if(x){var E=A.getIframeMarkup(this.settings.iframeWidth,this.settings.iframeHeight,this.settings.iframeMaxWidth,this.settings.iframeMaxHeight,c,r.iframeTitle);n.prepend(E)}else if(o){var I="",k=g&&this.zoomFromOrigin&&this.currentImageSize;k&&(I=this.getDummyImageContent(n,e,""));var E=A.getVideoPosterMarkup(o,I||"",m,this.settings.strings.playVideo,p);n.prepend(E)}else if(p){var E='<div class="lg-video-cont " style="'+m+'"></div>';n.prepend(E)}else if(this.setImgMarkup(c,n,e),l||a){var w=n.find(".lg-object");this.initPictureFill(w)}(o||p)&&this.LGel.trigger(O.hasVideo,{index:e,src:c,html5Video:f,hasPoster:!!o}),this.LGel.trigger(O.afterAppendSlide,{index:e}),this.lGalleryOn&&this.settings.appendSubHtmlTo===".lg-item"&&this.addHtml(e)}var T=0;y&&!b(document.body).hasClass("lg-from-hash")&&(T=y),this.isFirstSlideWithZoomAnimation()&&(setTimeout(function(){n.removeClass("lg-start-end-progress lg-start-progress").removeAttr("style")},this.settings.startAnimationDuration+100),n.hasClass("lg-loaded")||setTimeout(function(){if(s.getSlideType(r)==="image"){var M=r.alt,z=M?'alt="'+M+'"':"";if(n.find(".lg-img-wrap").append(A.getImgMarkup(e,c,z,l,d,r.sources)),l||a){var V=n.find(".lg-object");s.initPictureFill(V)}}(s.getSlideType(r)==="image"||s.getSlideType(r)==="video"&&o)&&(s.onLgObjectLoad(n,e,y,T,!0,!1),s.onSlideObjectLoad(n,!!(p&&p.html5&&!o),function(){s.loadContentOnFirstSlideLoad(e,n,T)},function(){s.loadContentOnFirstSlideLoad(e,n,T)}))},this.settings.startAnimationDuration+100)),n.addClass("lg-loaded"),(!this.isFirstSlideWithZoomAnimation()||this.getSlideType(r)==="video"&&!o)&&this.onLgObjectLoad(n,e,y,T,g,!!(p&&p.html5&&!o)),(!this.zoomFromOrigin||!this.currentImageSize)&&n.hasClass("lg-complete_")&&!this.lGalleryOn&&setTimeout(function(){n.addClass("lg-complete")},this.settings.backdropDuration),this.lGalleryOn=!0,t===!0&&(n.hasClass("lg-complete_")?this.preload(e):n.find(".lg-object").first().on("load.lg error.lg",function(){s.preload(e)}))},i.prototype.loadContentOnFirstSlideLoad=function(e,t,s){var r=this;setTimeout(function(){t.find(".lg-dummy-img").remove(),t.removeClass("lg-first-slide"),r.outer.removeClass("lg-first-slide-loading"),r.isDummyImageRemoved=!0,r.preload(e)},s+300)},i.prototype.getItemsToBeInsertedToDom=function(e,t,s){var r=this;s===void 0&&(s=0);var n=[],o=Math.max(s,3);o=Math.min(o,this.galleryItems.length);var l="lg-item-"+this.lgId+"-"+t;if(this.galleryItems.length<=3)return this.galleryItems.forEach(function(c,u){n.push("lg-item-"+r.lgId+"-"+u)}),n;if(e<(this.galleryItems.length-1)/2){for(var d=e;d>e-o/2&&d>=0;d--)n.push("lg-item-"+this.lgId+"-"+d);for(var a=n.length,d=0;d<o-a;d++)n.push("lg-item-"+this.lgId+"-"+(e+d+1))}else{for(var d=e;d<=this.galleryItems.length-1&&d<e+o/2;d++)n.push("lg-item-"+this.lgId+"-"+d);for(var a=n.length,d=0;d<o-a;d++)n.push("lg-item-"+this.lgId+"-"+(e-d-1))}return this.settings.loop&&(e===this.galleryItems.length-1?n.push("lg-item-"+this.lgId+"-0"):e===0&&n.push("lg-item-"+this.lgId+"-"+(this.galleryItems.length-1))),n.indexOf(l)===-1&&n.push("lg-item-"+this.lgId+"-"+t),n},i.prototype.organizeSlideItems=function(e,t){var s=this,r=this.getItemsToBeInsertedToDom(e,t,this.settings.numberOfSlideItemsInDom);return r.forEach(function(n){s.currentItemsInDom.indexOf(n)===-1&&s.$inner.append('<div id="'+n+'" class="lg-item"></div>')}),this.currentItemsInDom.forEach(function(n){r.indexOf(n)===-1&&b("#"+n).remove()}),r},i.prototype.getPreviousSlideIndex=function(){var e=0;try{var t=this.outer.find(".lg-current").first().attr("id");e=parseInt(t.split("-")[3])||0}catch{e=0}return e},i.prototype.setDownloadValue=function(e){if(this.settings.download){var t=this.galleryItems[e],s=t.downloadUrl===!1||t.downloadUrl==="false";if(s)this.outer.addClass("lg-hide-download");else{var r=this.getElementById("lg-download");this.outer.removeClass("lg-hide-download"),r.attr("href",t.downloadUrl||t.src),t.download&&r.attr("download",t.download)}}},i.prototype.makeSlideAnimation=function(e,t,s){var r=this;this.lGalleryOn&&s.addClass("lg-slide-progress"),setTimeout(function(){r.outer.addClass("lg-no-trans"),r.outer.find(".lg-item").removeClass("lg-prev-slide lg-next-slide"),e==="prev"?(t.addClass("lg-prev-slide"),s.addClass("lg-next-slide")):(t.addClass("lg-next-slide"),s.addClass("lg-prev-slide")),setTimeout(function(){r.outer.find(".lg-item").removeClass("lg-current"),t.addClass("lg-current"),r.outer.removeClass("lg-no-trans")},50)},this.lGalleryOn?this.settings.slideDelay:0)},i.prototype.slide=function(e,t,s,r){var n=this,o=this.getPreviousSlideIndex();if(this.currentItemsInDom=this.organizeSlideItems(e,o),!(this.lGalleryOn&&o===e)){var l=this.galleryItems.length;if(!this.lgBusy){this.settings.counter&&this.updateCurrentCounter(e);var d=this.getSlideItem(e),a=this.getSlideItem(o),c=this.galleryItems[e],u=c.__slideVideoInfo;if(this.outer.attr("data-lg-slide-type",this.getSlideType(c)),this.setDownloadValue(e),u){var f=this.mediaContainerPosition,h=f.top,p=f.bottom,m=A.getSize(this.items[e],this.outer,h+p,u&&this.settings.videoMaxSize);this.resizeVideoSlide(e,m)}if(this.LGel.trigger(O.beforeSlide,{prevIndex:o,index:e,fromTouch:!!t,fromThumb:!!s}),this.lgBusy=!0,clearTimeout(this.hideBarTimeout),this.arrowDisable(e),r||(e<o?r="prev":e>o&&(r="next")),!t)this.makeSlideAnimation(r,d,a);else{this.outer.find(".lg-item").removeClass("lg-prev-slide lg-current lg-next-slide");var x=void 0,g=void 0;l>2?(x=e-1,g=e+1,(e===0&&o===l-1||e===l-1&&o===0)&&(g=0,x=l-1)):(x=0,g=1),r==="prev"?this.getSlideItem(g).addClass("lg-next-slide"):this.getSlideItem(x).addClass("lg-prev-slide"),d.addClass("lg-current")}this.lGalleryOn?setTimeout(function(){n.loadContent(e,!0),n.settings.appendSubHtmlTo!==".lg-item"&&n.addHtml(e)},this.settings.speed+50+(t?0:this.settings.slideDelay)):this.loadContent(e,!0),setTimeout(function(){n.lgBusy=!1,a.removeClass("lg-slide-progress"),n.LGel.trigger(O.afterSlide,{prevIndex:o,index:e,fromTouch:t,fromThumb:s})},(this.lGalleryOn?this.settings.speed+100:100)+(t?0:this.settings.slideDelay))}this.index=e}},i.prototype.updateCurrentCounter=function(e){this.getElementById("lg-counter-current").html(e+1+"")},i.prototype.updateCounterTotal=function(){this.getElementById("lg-counter-all").html(this.galleryItems.length+"")},i.prototype.getSlideType=function(e){return e.__slideVideoInfo?"video":e.iframe?"iframe":"image"},i.prototype.touchMove=function(e,t,s){var r=t.pageX-e.pageX,n=t.pageY-e.pageY,o=!1;if(this.swipeDirection?o=!0:Math.abs(r)>15?(this.swipeDirection="horizontal",o=!0):Math.abs(n)>15&&(this.swipeDirection="vertical",o=!0),!!o){var l=this.getSlideItem(this.index);if(this.swipeDirection==="horizontal"){s==null||s.preventDefault(),this.outer.addClass("lg-dragging"),this.setTranslate(l,r,0);var d=l.get().offsetWidth,a=d*15/100,c=a-Math.abs(r*10/100);this.setTranslate(this.outer.find(".lg-prev-slide").first(),-d+r-c,0),this.setTranslate(this.outer.find(".lg-next-slide").first(),d+r+c,0)}else if(this.swipeDirection==="vertical"&&this.settings.swipeToClose){s==null||s.preventDefault(),this.$container.addClass("lg-dragging-vertical");var u=1-Math.abs(n)/window.innerHeight;this.$backdrop.css("opacity",u);var f=1-Math.abs(n)/(window.innerWidth*2);this.setTranslate(l,0,n,f,f),Math.abs(n)>100&&this.outer.addClass("lg-hide-items").removeClass("lg-components-open")}}},i.prototype.touchEnd=function(e,t,s){var r=this,n;this.settings.mode!=="lg-slide"&&this.outer.addClass("lg-slide"),setTimeout(function(){r.$container.removeClass("lg-dragging-vertical"),r.outer.removeClass("lg-dragging lg-hide-items").addClass("lg-components-open");var o=!0;if(r.swipeDirection==="horizontal"){n=e.pageX-t.pageX;var l=Math.abs(e.pageX-t.pageX);n<0&&l>r.settings.swipeThreshold?(r.goToNextSlide(!0),o=!1):n>0&&l>r.settings.swipeThreshold&&(r.goToPrevSlide(!0),o=!1)}else if(r.swipeDirection==="vertical")if(n=Math.abs(e.pageY-t.pageY),r.settings.closable&&r.settings.swipeToClose&&n>100){r.closeGallery();return}else r.$backdrop.css("opacity",1);if(r.outer.find(".lg-item").removeAttr("style"),o&&Math.abs(e.pageX-t.pageX)<5){var d=b(s.target);r.isPosterElement(d)&&r.LGel.trigger(O.posterClick)}r.swipeDirection=void 0}),setTimeout(function(){!r.outer.hasClass("lg-dragging")&&r.settings.mode!=="lg-slide"&&r.outer.removeClass("lg-slide")},this.settings.speed+100)},i.prototype.enableSwipe=function(){var e=this,t={},s={},r=!1,n=!1;this.settings.enableSwipe&&(this.$inner.on("touchstart.lg",function(o){e.dragOrSwipeEnabled=!0;var l=e.getSlideItem(e.index);(b(o.target).hasClass("lg-item")||l.get().contains(o.target))&&!e.outer.hasClass("lg-zoomed")&&!e.lgBusy&&o.touches.length===1&&(n=!0,e.touchAction="swipe",e.manageSwipeClass(),t={pageX:o.touches[0].pageX,pageY:o.touches[0].pageY})}),this.$inner.on("touchmove.lg",function(o){n&&e.touchAction==="swipe"&&o.touches.length===1&&(s={pageX:o.touches[0].pageX,pageY:o.touches[0].pageY},e.touchMove(t,s,o),r=!0)}),this.$inner.on("touchend.lg",function(o){if(e.touchAction==="swipe"){if(r)r=!1,e.touchEnd(s,t,o);else if(n){var l=b(o.target);e.isPosterElement(l)&&e.LGel.trigger(O.posterClick)}e.touchAction=void 0,n=!1}}))},i.prototype.enableDrag=function(){var e=this,t={},s={},r=!1,n=!1;this.settings.enableDrag&&(this.outer.on("mousedown.lg",function(o){e.dragOrSwipeEnabled=!0;var l=e.getSlideItem(e.index);(b(o.target).hasClass("lg-item")||l.get().contains(o.target))&&!e.outer.hasClass("lg-zoomed")&&!e.lgBusy&&(o.preventDefault(),e.lgBusy||(e.manageSwipeClass(),t={pageX:o.pageX,pageY:o.pageY},r=!0,e.outer.get().scrollLeft+=1,e.outer.get().scrollLeft-=1,e.outer.removeClass("lg-grab").addClass("lg-grabbing"),e.LGel.trigger(O.dragStart)))}),b(window).on("mousemove.lg.global"+this.lgId,function(o){r&&e.lgOpened&&(n=!0,s={pageX:o.pageX,pageY:o.pageY},e.touchMove(t,s),e.LGel.trigger(O.dragMove))}),b(window).on("mouseup.lg.global"+this.lgId,function(o){if(e.lgOpened){var l=b(o.target);n?(n=!1,e.touchEnd(s,t,o),e.LGel.trigger(O.dragEnd)):e.isPosterElement(l)&&e.LGel.trigger(O.posterClick),r&&(r=!1,e.outer.removeClass("lg-grabbing").addClass("lg-grab"))}}))},i.prototype.triggerPosterClick=function(){var e=this;this.$inner.on("click.lg",function(t){!e.dragOrSwipeEnabled&&e.isPosterElement(b(t.target))&&e.LGel.trigger(O.posterClick)})},i.prototype.manageSwipeClass=function(){var e=this.index+1,t=this.index-1;this.settings.loop&&this.galleryItems.length>2&&(this.index===0?t=this.galleryItems.length-1:this.index===this.galleryItems.length-1&&(e=0)),this.outer.find(".lg-item").removeClass("lg-next-slide lg-prev-slide"),t>-1&&this.getSlideItem(t).addClass("lg-prev-slide"),this.getSlideItem(e).addClass("lg-next-slide")},i.prototype.goToNextSlide=function(e){var t=this,s=this.settings.loop;e&&this.galleryItems.length<3&&(s=!1),this.lgBusy||(this.index+1<this.galleryItems.length?(this.index++,this.LGel.trigger(O.beforeNextSlide,{index:this.index}),this.slide(this.index,!!e,!1,"next")):s?(this.index=0,this.LGel.trigger(O.beforeNextSlide,{index:this.index}),this.slide(this.index,!!e,!1,"next")):this.settings.slideEndAnimation&&!e&&(this.outer.addClass("lg-right-end"),setTimeout(function(){t.outer.removeClass("lg-right-end")},400)))},i.prototype.goToPrevSlide=function(e){var t=this,s=this.settings.loop;e&&this.galleryItems.length<3&&(s=!1),this.lgBusy||(this.index>0?(this.index--,this.LGel.trigger(O.beforePrevSlide,{index:this.index,fromTouch:e}),this.slide(this.index,!!e,!1,"prev")):s?(this.index=this.galleryItems.length-1,this.LGel.trigger(O.beforePrevSlide,{index:this.index,fromTouch:e}),this.slide(this.index,!!e,!1,"prev")):this.settings.slideEndAnimation&&!e&&(this.outer.addClass("lg-left-end"),setTimeout(function(){t.outer.removeClass("lg-left-end")},400)))},i.prototype.keyPress=function(){var e=this;b(window).on("keydown.lg.global"+this.lgId,function(t){e.lgOpened&&e.settings.escKey===!0&&t.keyCode===27&&(t.preventDefault(),e.settings.allowMediaOverlap&&e.outer.hasClass("lg-can-toggle")&&e.outer.hasClass("lg-components-open")?e.outer.removeClass("lg-components-open"):e.closeGallery()),e.lgOpened&&e.galleryItems.length>1&&(t.keyCode===37&&(t.preventDefault(),e.goToPrevSlide()),t.keyCode===39&&(t.preventDefault(),e.goToNextSlide()))})},i.prototype.arrow=function(){var e=this;this.getElementById("lg-prev").on("click.lg",function(){e.goToPrevSlide()}),this.getElementById("lg-next").on("click.lg",function(){e.goToNextSlide()})},i.prototype.arrowDisable=function(e){if(!this.settings.loop&&this.settings.hideControlOnEnd){var t=this.getElementById("lg-prev"),s=this.getElementById("lg-next");e+1===this.galleryItems.length?s.attr("disabled","disabled").addClass("disabled"):s.removeAttr("disabled").removeClass("disabled"),e===0?t.attr("disabled","disabled").addClass("disabled"):t.removeAttr("disabled").removeClass("disabled")}},i.prototype.setTranslate=function(e,t,s,r,n){r===void 0&&(r=1),n===void 0&&(n=1),e.css("transform","translate3d("+t+"px, "+s+"px, 0px) scale3d("+r+", "+n+", 1)")},i.prototype.mousewheel=function(){var e=this,t=0;this.outer.on("wheel.lg",function(s){if(!(!s.deltaY||e.galleryItems.length<2)){s.preventDefault();var r=new Date().getTime();r-t<1e3||(t=r,s.deltaY>0?e.goToNextSlide():s.deltaY<0&&e.goToPrevSlide())}})},i.prototype.isSlideElement=function(e){return e.hasClass("lg-outer")||e.hasClass("lg-item")||e.hasClass("lg-img-wrap")||e.hasClass("lg-img-rotate")},i.prototype.isPosterElement=function(e){var t=this.getSlideItem(this.index).find(".lg-video-play-button").get();return e.hasClass("lg-video-poster")||e.hasClass("lg-video-play-button")||t&&t.contains(e.get())},i.prototype.toggleMaximize=function(){var e=this;this.getElementById("lg-maximize").on("click.lg",function(){e.$container.toggleClass("lg-inline"),e.refreshOnResize()})},i.prototype.invalidateItems=function(){for(var e=0;e<this.items.length;e++){var t=this.items[e],s=b(t);s.off("click.lgcustom-item-"+s.attr("data-lg-id"))}},i.prototype.trapFocus=function(){var e=this;this.$container.get().focus({preventScroll:!0}),b(window).on("keydown.lg.global"+this.lgId,function(t){if(e.lgOpened){var s=t.key==="Tab"||t.keyCode===9;if(s){var r=A.getFocusableElements(e.$container.get()),n=r[0],o=r[r.length-1];t.shiftKey?document.activeElement===n&&(o.focus(),t.preventDefault()):document.activeElement===o&&(n.focus(),t.preventDefault())}}})},i.prototype.manageCloseGallery=function(){var e=this;if(this.settings.closable){var t=!1;this.getElementById("lg-close").on("click.lg",function(){e.closeGallery()}),this.settings.closeOnTap&&(this.outer.on("mousedown.lg",function(s){var r=b(s.target);e.isSlideElement(r)?t=!0:t=!1}),this.outer.on("mousemove.lg",function(){t=!1}),this.outer.on("mouseup.lg",function(s){var r=b(s.target);e.isSlideElement(r)&&t&&(e.outer.hasClass("lg-dragging")||e.closeGallery())}))}},i.prototype.closeGallery=function(e){var t=this;if(!this.lgOpened||!this.settings.closable&&!e)return 0;this.LGel.trigger(O.beforeClose),this.settings.resetScrollPosition&&!this.settings.hideScrollbar&&b(window).scrollTop(this.prevScrollTop);var s=this.items[this.index],r;if(this.zoomFromOrigin&&s){var n=this.mediaContainerPosition,o=n.top,l=n.bottom,d=this.galleryItems[this.index],a=d.__slideVideoInfo,c=d.poster,u=A.getSize(s,this.outer,o+l,a&&c&&this.settings.videoMaxSize);r=A.getTransform(s,this.outer,o,l,u)}this.zoomFromOrigin&&r?(this.outer.addClass("lg-closing lg-zoom-from-image"),this.getSlideItem(this.index).addClass("lg-start-end-progress").css("transition-duration",this.settings.startAnimationDuration+"ms").css("transform",r)):(this.outer.addClass("lg-hide-items"),this.outer.removeClass("lg-zoom-from-image")),this.destroyModules(),this.lGalleryOn=!1,this.isDummyImageRemoved=!1,this.zoomFromOrigin=this.settings.zoomFromOrigin,clearTimeout(this.hideBarTimeout),this.hideBarTimeout=!1,b("html").removeClass("lg-on"),this.outer.removeClass("lg-visible lg-components-open"),this.$backdrop.removeClass("in").css("opacity",0);var f=this.zoomFromOrigin&&r?Math.max(this.settings.startAnimationDuration,this.settings.backdropDuration):this.settings.backdropDuration;return this.$container.removeClass("lg-show-in"),setTimeout(function(){t.zoomFromOrigin&&r&&t.outer.removeClass("lg-zoom-from-image"),t.$container.removeClass("lg-show"),t.resetScrollBar(),t.$backdrop.removeAttr("style").css("transition-duration",t.settings.backdropDuration+"ms"),t.outer.removeClass("lg-closing "+t.settings.startClass),t.getSlideItem(t.index).removeClass("lg-start-end-progress"),t.$inner.empty(),t.lgOpened&&t.LGel.trigger(O.afterClose,{instance:t}),t.$container.get()&&t.$container.get().blur(),t.lgOpened=!1},f+100),f+100},i.prototype.initModules=function(){this.plugins.forEach(function(e){try{e.init()}catch{console.warn("lightGallery:- make sure lightGallery module is properly initiated")}})},i.prototype.destroyModules=function(e){this.plugins.forEach(function(t){try{e?t.destroy():t.closeGallery&&t.closeGallery()}catch{console.warn("lightGallery:- make sure lightGallery module is properly destroyed")}})},i.prototype.refresh=function(e){this.settings.dynamic||this.invalidateItems(),e?this.galleryItems=e:this.galleryItems=this.getItems(),this.updateControls(),this.openGalleryOnItemClick(),this.LGel.trigger(O.updateSlides)},i.prototype.updateControls=function(){this.addSlideVideoInfo(this.galleryItems),this.updateCounterTotal(),this.manageSingleSlideClassName()},i.prototype.destroyGallery=function(){this.destroyModules(!0),this.settings.dynamic||this.invalidateItems(),b(window).off(".lg.global"+this.lgId),this.LGel.off(".lg"),this.$container.remove()},i.prototype.destroy=function(){var e=this.closeGallery(!0);return e?setTimeout(this.destroyGallery.bind(this),e):this.destroyGallery(),e},i}();function ei(i,e){return new Jt(i,e)}const li=Object.freeze(Object.defineProperty({__proto__:null,default:ei},Symbol.toStringTag,{value:"Module"}));export{D as a,Fe as b,ne as c,ii as d,$ as e,re as f,j as g,ue as h,ce as i,Le as j,Ae as k,si as l,ri as m,U as n,Y as o,ti as p,ni as q,oi as r,X as s,li as t};

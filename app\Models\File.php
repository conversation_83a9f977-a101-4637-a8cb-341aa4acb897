<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class File extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'title',
        'filename',
        'url',
        'fileSize',
        'fileType'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'fileSize' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * Get the formatted file size.
     *
     * @return string
     */
    public function getFormattedFileSizeAttribute()
    {
        $bytes = $this->fileSize;

        if ($bytes === 0 || is_null($bytes)) {
            return '0 KB';
        }

        $units = ['Bytes', 'KB', 'MB', 'GB'];
        $unitIndex = 0;

        while ($bytes >= 1024 && $unitIndex < count($units) - 1) {
            $bytes /= 1024;
            $unitIndex++;
        }

        return round($bytes, 2) . ' ' . $units[$unitIndex];
    }

    /**
     * Get the file extension.
     *
     * @return string
     */
    public function getExtensionAttribute()
    {
        return pathinfo($this->filename, PATHINFO_EXTENSION);
    }

    /**
     * Determine if the file is an image.
     *
     * @return bool
     */
    public function getIsImageAttribute()
    {
        $imageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        return in_array($this->fileType, $imageTypes);
    }

    /**
     * Determine if the file is a document.
     *
     * @return bool
     */
    public function getIsDocumentAttribute()
    {
        $docTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
        return in_array($this->fileType, $docTypes);
    }

    /**
     * Get the URL for preview or thumbnail.
     *
     * @return string
     */
    public function getPreviewUrlAttribute()
    {
        if ($this->is_image) {
            // Ensure the URL is properly formatted
            return $this->getFullUrl();
        }

        // Return appropriate icon based on file type
        if ($this->is_document) {
            if ($this->extension === 'pdf') {
                return asset('icons/pdf.png');
            }
            return asset('icons/doc.png');
        }

        // Default file icon
        return asset('icons/file.png');
    }

    /**
     * Get the full URL with the app URL prepended if needed.
     *
     * @return string
     */
    public function getFullUrl()
    {
        if (!$this->url) {
            return '';
        }

        // If the URL already starts with http:// or https://, return it as is
        if (Str::startsWith($this->url, ['http://', 'https://'])) {
            return $this->url;
        }

        // Use Laravel's Storage facade to generate the URL
        return Storage::disk('public')->url($this->url);
    }

    /**
     * Scope a query to search by title.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $search
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSearch($query, $search)
    {
        if ($search) {
            return $query->where('title', 'like', "%{$search}%");
        }

        return $query;
    }

    /**
     * Scope a query to filter by file type.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $type
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfType($query, $type)
    {
        if ($type === 'image') {
            return $query->whereIn('fileType', ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']);
        } elseif ($type === 'document') {
            return $query->whereIn('fileType', ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']);
        }

        return $query;
    }

    /**
     * Scope a query to order by latest.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeLatest($query)
    {
        return $query->orderBy('created_at', 'desc');
    }
}
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bookings', function (Blueprint $table) {
            $table->id();
            $table->string('booking_number')->unique();
            $table->foreignId('user_id')->constrained()->comment('Renter'); // The person making the booking
            $table->foreignId('vehicle_id')->constrained();
            $table->foreignId('driver_id')->nullable()->constrained();
            $table->datetime('start_date');
            $table->datetime('end_date');
            $table->string('pickup_location');
            $table->string('return_location');
            $table->decimal('vehicle_price', 10, 2);
            $table->decimal('driver_price', 10, 2)->nullable();
            $table->decimal('insurance_price', 10, 2)->nullable();
            $table->decimal('extra_services_price', 10, 2)->default(0);
            $table->decimal('discount_amount', 10, 2)->default(0);
            $table->decimal('subtotal', 10, 2);
            $table->decimal('tax_amount', 10, 2);
            $table->decimal('total_amount', 10, 2);
            $table->decimal('security_deposit', 10, 2)->default(0);
            $table->enum('status', [
                'pending',      // Initial state
                'confirmed',    // Owner approved
                'in_progress',  // Actively rented
                'completed',    // Successfully returned
                'cancelled',    // Cancelled by either party
                'rejected'      // Rejected by owner
            ])->default('pending');
            $table->text('cancellation_reason')->nullable();
            $table->text('special_requests')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::disableForeignKeyConstraints();
        Schema::dropIfExists('bookings');
        Schema::enableForeignKeyConstraints();
    }
};

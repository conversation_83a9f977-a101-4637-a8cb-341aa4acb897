<?php

namespace Tests\Unit;

use App\Models\VehicleType;
use App\Models\Vehicle;
use Tests\Unit\TestCase;
use Mockery;

test('vehicle type has correct fillable attributes', function () {
    $vehicleType = new VehicleType();

    $fillable = [
        'name',
        'slug',
        'description',
        'icon',
        'is_active'
    ];

    expect($vehicleType->getFillable())->toBe($fillable);
});

test('vehicle type has correct casts', function () {
    $vehicleType = new VehicleType();

    $expectedCasts = [
        'is_active' => 'boolean',
    ];

    expect($vehicleType->getCasts())->toMatchArray($expectedCasts);
});

test('vehicle type has vehicles relationship method', function () {
    $vehicleType = new VehicleType();

    expect(method_exists($vehicleType, 'vehicles'))->toBeTrue();
});

test('vehicle type scope active works correctly', function () {
    // Create a mock query builder
    $query = Mockery::mock('Illuminate\Database\Eloquent\Builder');
    $query->shouldReceive('where')->with('is_active', true)->once()->andReturnSelf();

    // Call the scope
    $vehicleType = new VehicleType();
    $result = $vehicleType->scopeActive($query);

    // Verify the result
    expect($result)->toBe($query);
});

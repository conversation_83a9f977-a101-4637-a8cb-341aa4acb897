/**
 * Gallery and Carousel Helper Functions
 * This file provides helper functions for initializing Swiper and LightGallery
 * as replacements for Owl Carousel and Grid Gallery
 */

// Initialize a basic carousel with Swiper
function initCarousel(selector, options = {}) {
    const defaultOptions = {
        modules: [Swiper.Navigation, Swiper.Pagination, Swiper.Autoplay],
        slidesPerView: 1,
        spaceBetween: 30,
        loop: true,
        pagination: {
            el: '.swiper-pagination',
            clickable: true,
        },
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },
        autoplay: {
            delay: 5000,
            disableOnInteraction: false,
        }
    };

    // Merge default options with provided options
    const swiperOptions = { ...defaultOptions, ...options };
    
    // Initialize Swiper
    return new Swiper(selector, swiperOptions);
}

// Initialize a gallery with LightGallery
function initGallery(selector, options = {}) {
    const defaultOptions = {
        selector: 'a',
        download: false,
        counter: true,
        plugins: []
    };

    // Merge default options with provided options
    const galleryOptions = { ...defaultOptions, ...options };
    
    // Initialize LightGallery
    return lightGallery(document.querySelector(selector), galleryOptions);
}

// Convert existing grid gallery to use LightGallery
function convertGridGallery() {
    // Find all grid gallery containers
    const galleryContainers = document.querySelectorAll('.gg-box');
    
    galleryContainers.forEach(container => {
        // Wrap each image in an anchor tag if not already wrapped
        const images = container.querySelectorAll('img:not(a > img)');
        images.forEach(img => {
            const src = img.getAttribute('src');
            const parent = img.parentNode;
            
            // Only wrap if not already wrapped in an anchor
            if (parent.tagName !== 'A') {
                const anchor = document.createElement('a');
                anchor.href = src;
                anchor.classList.add('gallery-item');
                
                // Replace the image with the anchor containing the image
                parent.replaceChild(anchor, img);
                anchor.appendChild(img);
            }
        });
        
        // Initialize LightGallery on the container
        initGallery(container, {
            selector: '.gallery-item'
        });
    });
}

// Document ready function
document.addEventListener('DOMContentLoaded', function() {
    // Auto-initialize galleries and carousels if they exist
    convertGridGallery();
    
    // Initialize any carousels with the 'swiper' class
    const carousels = document.querySelectorAll('.swiper');
    carousels.forEach(carousel => {
        initCarousel(carousel);
    });
});

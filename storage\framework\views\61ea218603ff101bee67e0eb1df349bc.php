<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <!-- Hero Section -->
    <section class="relative h-[400px] overflow-hidden bg-gradient-to-r from-blue-900 to-indigo-900">
        <div class="absolute inset-0 opacity-40">
            <img src="https://images.unsplash.com/photo-1449965408869-eaa3f722e40d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80" alt="Driver behind wheel" class="w-full h-full object-cover">
        </div>
        <div class="absolute inset-0 bg-gradient-to-b from-transparent to-black opacity-40"></div>
        <div class="relative container mx-auto px-4 h-full flex flex-col justify-center">
            <div class="max-w-xl">
                <h1 class="text-4xl md:text-5xl font-extrabold text-white mb-6 leading-tight">Become a Driver</h1>
                <p class="text-xl text-white/90 mb-6 leading-relaxed">Register in just 2 minutes. Our team will help you complete your profile.</p>
            </div>
        </div>
        <div class="absolute bottom-0 left-0 right-0">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 120" class="w-full h-auto">
                <path fill="#ffffff" fill-opacity="1" d="M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z"></path>
            </svg>
        </div>
    </section>

    <!-- Registration Form Section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="max-w-3xl mx-auto">
                <div class="bg-white rounded-2xl shadow-xl p-8 border border-gray-100">
                    <div class="text-center mb-8">
                        <div class="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-id-card text-3xl text-blue-600"></i>
                        </div>
                        <h2 class="text-3xl font-bold text-gray-900 mb-2">Start Your Driver Journey</h2>
                        <p class="text-gray-600">Fill in these basic details and we'll help you with the rest.</p>
                    </div>

                    <?php if($errors->any()): ?>
                        <div class="mb-8 bg-red-50 border-l-4 border-red-500 p-4 rounded-md">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-exclamation-circle text-red-500"></i>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-red-800">Please fix the following errors:</h3>
                                    <ul class="mt-2 text-sm text-red-700 list-disc list-inside">
                                        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li><?php echo e($error); ?></li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <form id="quick-registration-form" action="<?php echo e(route('store-quick-driver-registration')); ?>" method="POST" class="space-y-8">
                        <?php echo csrf_field(); ?>
                        
                        <!-- Personal Information -->
                        <div>
                            <h3 class="text-xl font-bold mb-4 text-gray-900 border-b pb-2">Your Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="first_name" class="block text-gray-700 font-medium mb-2">First Name *</label>
                                    <input type="text" id="first_name" name="first_name" required 
                                        class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:outline-none"
                                        value="<?php echo e(old('first_name')); ?>">
                                </div>
                                <div>
                                    <label for="last_name" class="block text-gray-700 font-medium mb-2">Last Name *</label>
                                    <input type="text" id="last_name" name="last_name" required 
                                        class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:outline-none"
                                        value="<?php echo e(old('last_name')); ?>">
                                </div>
                                <div>
                                    <label for="email" class="block text-gray-700 font-medium mb-2">Email *</label>
                                    <input type="email" id="email" name="email" required 
                                        class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:outline-none"
                                        value="<?php echo e(old('email')); ?>">
                                </div>
                                <div>
                                    <label for="phone" class="block text-gray-700 font-medium mb-2">Phone Number *</label>
                                    <input type="tel" id="phone" name="phone" required 
                                        class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:outline-none"
                                        value="<?php echo e(old('phone')); ?>">
                                </div>
                            </div>
                        </div>
                        
                        <!-- Driver Information -->
                        <div>
                            <h3 class="text-xl font-bold mb-4 text-gray-900 border-b pb-2">Driver Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="license_number" class="block text-gray-700 font-medium mb-2">Driver's License Number *</label>
                                    <input type="text" id="license_number" name="license_number" required 
                                        class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:outline-none"
                                        value="<?php echo e(old('license_number')); ?>">
                                </div>
                                <div>
                                    <label for="license_expiry" class="block text-gray-700 font-medium mb-2">License Expiry Date *</label>
                                    <input type="date" id="license_expiry" name="license_expiry" required 
                                        class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:outline-none"
                                        value="<?php echo e(old('license_expiry')); ?>" min="<?php echo e(date('Y-m-d', strtotime('+1 day'))); ?>">
                                </div>
                                <div>
                                    <label for="experience_years" class="block text-gray-700 font-medium mb-2">Years of Driving Experience *</label>
                                    <select id="experience_years" name="experience_years" required 
                                        class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:outline-none appearance-none bg-white">
                                        <option value="">Select Experience</option>
                                        <option value="Less than 1 year" <?php echo e(old('experience_years') == 'Less than 1 year' ? 'selected' : ''); ?>>Less than 1 year</option>
                                        <option value="1-2 years" <?php echo e(old('experience_years') == '1-2 years' ? 'selected' : ''); ?>>1-2 years</option>
                                        <option value="3-5 years" <?php echo e(old('experience_years') == '3-5 years' ? 'selected' : ''); ?>>3-5 years</option>
                                        <option value="5-10 years" <?php echo e(old('experience_years') == '5-10 years' ? 'selected' : ''); ?>>5-10 years</option>
                                        <option value="10+ years" <?php echo e(old('experience_years') == '10+ years' ? 'selected' : ''); ?>>10+ years</option>
                                    </select>
                                </div>
                                <div>
                                    <label for="city_id" class="block text-gray-700 font-medium mb-2">City *</label>
                                    <select id="city_id" name="city_id" required 
                                        class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:outline-none appearance-none bg-white">
                                        <option value="">Select City</option>
                                        <?php $__currentLoopData = $cities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $city): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($city->id); ?>" <?php echo e(old('city_id') == $city->id ? 'selected' : ''); ?>>
                                                <?php echo e($city->name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Terms and Submit -->
                        <div class="pt-4">
                            <div class="flex items-start mb-6">
                                <div class="flex items-center h-5">
                                    <input id="terms" name="terms" type="checkbox" required
                                        class="w-4 h-4 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-blue-500">
                                </div>
                                <label for="terms" class="ml-2 text-sm text-gray-600">
                                    I agree to the <a href="#" class="text-blue-600 hover:underline">Terms of Service</a> and <a href="#" class="text-blue-600 hover:underline">Privacy Policy</a>
                                </label>
                            </div>
                            
                            <div class="text-center">
                                <button type="submit" class="inline-block px-8 py-4 bg-blue-600 text-white font-bold rounded-full hover:bg-blue-700 transition transform hover:scale-105 shadow-lg">
                                    <i class="fas fa-id-card mr-2"></i> Register as Driver
                                </button>
                                <p class="text-sm text-gray-500 mt-4">
                                    Our team will contact you to complete your profile and verify your documents.
                                </p>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Video Section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">See How It Works</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">Watch our short video to learn how easy it is to become a driver and start earning.</p>
            </div>
            
            <div class="max-w-4xl mx-auto rounded-2xl overflow-hidden shadow-2xl video-section">
                <div class="relative pb-[56.25%] h-0">
                    <video class="absolute inset-0 w-full h-full object-cover" controls poster="<?php echo e(asset('video/videoPoster.png')); ?>">
                        <source src="<?php echo e(asset('video/carbnbVideo.mp4')); ?>" type="video/mp4">
                        Your browser does not support the video tag.
                    </video>
                    <div class="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center group transition-opacity duration-500">
                        <button class="w-20 h-20 bg-blue-600 bg-opacity-80 rounded-full flex items-center justify-center transition-transform transform group-hover:scale-110 focus:outline-none">
                            <i class="fas fa-play text-white text-3xl pl-1"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Benefits Section -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Why Become a Driver With Us?</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">Join thousands of drivers who are already earning with our platform.</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
                <div class="bg-white p-6 rounded-xl shadow-md text-center">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-euro-sign text-2xl text-green-600"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2 text-gray-900">Competitive Earnings</h3>
                    <p class="text-gray-600">Earn up to €1000+ per week with our competitive rates and busy platform.</p>
                </div>
                
                <div class="bg-white p-6 rounded-xl shadow-md text-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-calendar-alt text-2xl text-blue-600"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2 text-gray-900">Flexible Schedule</h3>
                    <p class="text-gray-600">Work when you want. Set your own hours and availability to fit your lifestyle.</p>
                </div>
                
                <div class="bg-white p-6 rounded-xl shadow-md text-center">
                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-shield-alt text-2xl text-purple-600"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2 text-gray-900">Full Support</h3>
                    <p class="text-gray-600">Our team is here to help you succeed with 24/7 support and driver resources.</p>
                </div>
            </div>
        </div>
    </section>

    <?php $__env->startPush('scripts'); ?>
    <script>
        $(document).ready(function() {
            // Form validation with SweetAlert
            $('#quick-registration-form').submit(function(e) {
                const form = this;
                const requiredFields = $(form).find('input[required], select[required]');
                let valid = true;
                let firstError = null;

                requiredFields.each(function() {
                    if (!$(this).val()) {
                        valid = false;
                        $(this).addClass('border-red-500');
                        
                        if (!firstError) {
                            firstError = $(this);
                        }
                    } else {
                        $(this).removeClass('border-red-500');
                    }
                });

                if (!valid) {
                    e.preventDefault();
                    
                    // Scroll to the first error
                    if (firstError) {
                        $('html, body').animate({
                            scrollTop: firstError.offset().top - 100
                        }, 500);
                    }
                    
                    Swal.fire({
                        title: 'Please fill in all required fields',
                        icon: 'error',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#3B82F6'
                    });
                }
            });
            
            // Video player functionality
            const videoContainer = document.querySelector('.video-section');
            const video = document.querySelector('.video-section video');
            const playButton = document.querySelector('.video-section .group button');
            
            if (video && playButton) {
                // Make sure video is loaded
                video.addEventListener('loadedmetadata', function() {
                    console.log('Video metadata loaded');
                });
                
                // Play button click handler
                playButton.addEventListener('click', function() {
                    console.log('Play button clicked');
                    try {
                        const playPromise = video.play();
                        
                        if (playPromise !== undefined) {
                            playPromise.then(_ => {
                                // Video playback started successfully
                                console.log('Video playback started');
                                this.parentElement.classList.add('opacity-0');
                                setTimeout(() => {
                                    this.parentElement.style.display = 'none';
                                }, 500);
                            })
                            .catch(error => {
                                // Auto-play was prevented
                                console.log('Playback error:', error);
                            });
                        }
                    } catch (e) {
                        console.error('Error playing video:', e);
                    }
                });
                
                // Handle video pause
                video.addEventListener('pause', function() {
                    console.log('Video paused');
                    playButton.parentElement.style.display = 'flex';
                    setTimeout(() => {
                        playButton.parentElement.classList.remove('opacity-0');
                    }, 10);
                });
                
                // Handle video end
                video.addEventListener('ended', function() {
                    console.log('Video ended');
                    playButton.parentElement.style.display = 'flex';
                    setTimeout(() => {
                        playButton.parentElement.classList.remove('opacity-0');
                    }, 10);
                });
            }
        });
    </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\carbnb\resources\views\front\default\quick-driver-registration.blade.php ENDPATH**/ ?>
<?php

use App\Http\Controllers\AddressesController;
use App\Http\Controllers\PostsController;
use App\Http\Controllers\PostCategoriesController;
use App\Http\Controllers\BusinessSettingController;
use App\Http\Controllers\CartController;
use App\Http\Controllers\CategoriesController;
use App\Http\Controllers\CitiesController;
use App\Http\Controllers\ColorsController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\CountriesController;
use App\Http\Controllers\CurrenciesController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\FileManagerController;
use App\Http\Controllers\MenuItemController;
use App\Http\Controllers\OrdersController;
use App\Http\Controllers\PagesController;
use App\Http\Controllers\PermissionsController;
use App\Http\Controllers\ProductsController;
use App\Http\Controllers\ProfilesController;
use App\Http\Controllers\RolesController;
use App\Http\Controllers\RolePermissionsController;
use App\Http\Controllers\SalesController;
use App\Http\Controllers\SliderController;
use App\Http\Controllers\StatisticsController;
use App\Http\Controllers\StatesController;
use App\Http\Controllers\StockController;
use App\Http\Controllers\TaxesController;
use App\Http\Controllers\UsersController;
use App\Http\Controllers\AttributesController;
use App\Http\Controllers\CustomersController;
use App\Http\Controllers\BillController;
use App\Http\Controllers\QuotesController;
use App\Http\Controllers\WebController;
use App\Http\Controllers\VehicleController;
use App\Http\Controllers\ReferralController;
use App\Http\Controllers\BookingController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\RazorpayController;
use App\Http\Controllers\PagSeguroController;
use Illuminate\Support\Facades\Route;

// main routes
Route::get('/', [WebController::class, 'index'])->name('home');
Route::get('/list-car', [WebController::class, 'listCar'])->name('list-car');
Route::get('/cars', [WebController::class, 'carsListing'])->name('cars.listing');
Route::get('/vehicle/{id}', [WebController::class, 'vehicleDetails'])->name('vehicle.details');
Route::get('/change-locale/{locale}', [WebController::class, 'changeLocale'])->name('change.locale');

// Quick car registration routes
Route::get('/quick-car-registration', [WebController::class, 'quickCarRegistration'])->name('quick-car-registration');
Route::post('/quick-car-registration', [WebController::class, 'storeQuickCarRegistration'])->name('store-quick-car-registration');

// Quick driver registration routes
Route::get('/quick-driver-registration', [WebController::class, 'quickDriverRegistration'])->name('quick-driver-registration');
Route::post('/quick-driver-registration', [WebController::class, 'storeQuickDriverRegistration'])->name('store-quick-driver-registration');

// Referral routes
Route::get('/referral/register/{token}', [ReferralController::class, 'register'])->name('referral.register');

// below are the protected routes
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Referral routes
    Route::get('/referrals', [ReferralController::class, 'index'])->name('referrals.index');
    Route::post('/referrals/send', [ReferralController::class, 'send'])
        ->name('referrals.send')
        ->middleware('throttle:10,1');

    // Revenue Dashboard Routes - Only accessible to users with dashboard permission
    Route::middleware(['dashboard.access:dashboard'])->group(function () {
        Route::get('/revenue-dashboard', [App\Http\Controllers\RevenueDashboardController::class, 'index'])->name('revenue.dashboard');
        Route::get('/revenue-stats', [App\Http\Controllers\RevenueDashboardController::class, 'getRevenueStats'])->name('revenue.stats');
        Route::get('/api/user/vehicles', [App\Http\Controllers\RevenueDashboardController::class, 'getUserVehicles'])->name('api.user.vehicles');
    });

    // Access Control Routes - Only accessible to users with users.view permission
    Route::middleware(['dashboard.access:users.view'])->group(function () {
        Route::resource('users', UsersController::class);
    });

    // Roles and Permissions management
    Route::middleware(['dashboard.access:roles.view'])->group(function () {
        Route::resource('roles', RolesController::class);
        Route::resource('permissions', PermissionsController::class);
        Route::resource('roles.permissions', RolePermissionsController::class);
    });

    // File Manager Routes - Only accessible to users with file-manager permission
    Route::prefix('admin')->group(function () {
        Route::get('/file-manager', [FileManagerController::class, 'index'])->name('file-manager.index');
        Route::post('/file-manager', [FileManagerController::class, 'store'])
            ->name('file-manager.store')
            ->middleware('throttle:30,1');
        Route::get('/file-manager/batch', [FileManagerController::class, 'batch'])->name('file-manager.batch');
        Route::get('/file-manager/check/{id}', [FileManagerController::class, 'checkFile'])->name('file-manager.check');
        Route::get('/file-manager/{id}', [FileManagerController::class, 'show'])->name('file-manager.show');
        Route::delete('/file-manager/{id}', [FileManagerController::class, 'destroy'])
            ->name('file-manager.destroy')
            ->middleware('throttle:20,1');
    });

    // Page management route (full resource)
    Route::middleware(['dashboard.access:pages'])->group(function () {
        Route::resource('pages', PagesController::class);
        Route::post('/upload/image', [PagesController::class, 'imageUpload'])
            ->name('upload.image')
            ->middleware('throttle:20,1');
        Route::post('ck-image', [PagesController::class, 'ckImageUpload'])
            ->name('ck.upload')
            ->middleware('throttle:20,1');
    });

    // Categories management
    Route::middleware(['dashboard.access:dashboard'])->group(function () {
        Route::resource('categories', CategoriesController::class);
    });

    // Location management
    Route::middleware(['dashboard.access:locations.view'])->group(function () {
        Route::resource('currencies', CurrenciesController::class);
        Route::resource('countries', CountriesController::class);
        Route::resource('states', StatesController::class);
        Route::resource('cities', CitiesController::class);
    });

    // Business Settings - Only accessible to users with settings permission
    Route::middleware(['dashboard.access:settings'])->controller(BusinessSettingController::class)->group(function () {
        Route::post('/business-settings/update', 'update')->name('business_settings.update');
        Route::post('/business-settings/update/activation', 'updateActivationSettings')->name('business_settings.update.activation');
        Route::post('/payment-activation', 'updatePaymentActivationSettings')->name('payment.activation');
        Route::get('/general-setting', 'general_setting')->name('general_setting.index');
        Route::get('/activation', 'activation')->name('activation.index');
        Route::get('/payment-method', 'payment_method')->name('payment_method.index');
        Route::get('/file_system', 'file_system')->name('file_system.index');
        Route::get('/social-login', 'social_login')->name('social_login.index');
        Route::post('/social-login-update', 'social_login_update')->name('social_login_update');
        Route::get('/smtp-settings', 'smtp_settings')->name('smtp_settings.index');
        Route::get('/google-analytics', 'google_analytics')->name('google_analytics.index');
        Route::get('/google-recaptcha', 'google_recaptcha')->name('google_recaptcha.index');
        Route::get('/google-map', 'google_map')->name('google-map.index');
        Route::get('/google-firebase', 'google_firebase')->name('google-firebase.index');

        // Theme management routes
        Route::get('/theme-settings', 'theme_settings')->name('theme_settings');
        Route::post('/update-theme', 'update_theme')->name('update_theme');

        //Facebook Settings
        Route::get('/facebook-chat', 'facebook_chat')->name('facebook_chat.index');
        Route::post('/facebook_chat', 'facebook_chat_update')->name('facebook_chat.update');
        Route::get('/facebook-comment', 'facebook_comment')->name('facebook-comment');
        Route::post('/facebook-comment', 'facebook_comment_update')->name('facebook-comment.update');
        Route::post('/facebook_pixel', 'facebook_pixel_update')->name('facebook_pixel.update');

        Route::post('/env_key_update', 'env_key_update')->name('env_key_update.update');
        Route::post('/payment_method_update', 'payment_method_update')->name('payment_method.update');
        Route::post('/google_analytics', 'google_analytics_update')->name('google_analytics.update');
        Route::post('/google_recaptcha', 'google_recaptcha_update')->name('google_recaptcha.update');
        Route::post('/google-map', 'google_map_update')->name('google-map.update');
        Route::post('/google-firebase', 'google_firebase_update')->name('google-firebase.update');

        Route::get('/verification/form', 'seller_verification_form')->name('seller_verification_form.index');
        Route::post('/verification/form', 'seller_verification_form_update')->name('seller_verification_form.update');
        Route::get('/vendor_commission', 'vendor_commission')->name('business_settings.vendor_commission');

        //Shipping Configuration
        Route::get('/shipping_configuration', 'shipping_configuration')->name('shipping_configuration.index');
        Route::post('/shipping_configuration/update', 'shipping_configuration_update')->name('shipping_configuration.update');

        // Order Configuration
        Route::get('/order-configuration', 'order_configuration')->name('order_configuration.index');
        Route::post('/tailwind-config-update', [BusinessSettingController::class, 'tailwind_config_update'])->name('business_settings.tailwind_config_update');

        // Test Email Route
        Route::post('/business-settings/test-email', [BusinessSettingController::class, 'test_email'])->name('business_settings.test_email');

    });

    // Vehicle Routes
    Route::middleware(['dashboard.access:vehicles'])->group(function () {
        Route::get('/vehicles', [App\Http\Controllers\VehicleController::class, 'index'])->name('vehicles.index');
        Route::get('/vehicles/{vehicle}', [App\Http\Controllers\VehicleController::class, 'show'])->name('vehicles.show');
        Route::delete('/vehicles/{vehicle}', [App\Http\Controllers\VehicleController::class, 'destroy'])->name('vehicles.destroy');
    });

    // Vehicle Create/Edit Routes - Separate middleware for create/edit permissions
    Route::middleware(['dashboard.access:vehicles.create'])->group(function () {
        Route::get('/vehicles/create', [App\Http\Controllers\VehicleController::class, 'create'])->name('vehicles.create');
        Route::post('/vehicles', [App\Http\Controllers\VehicleController::class, 'store'])
            ->name('vehicles.store')
            ->middleware('throttle:10,1');
        Route::get('/vehicles/{vehicle}/edit', [App\Http\Controllers\VehicleController::class, 'edit'])->name('vehicles.edit');
        Route::put('/vehicles/{vehicle}', [App\Http\Controllers\VehicleController::class, 'update'])
            ->name('vehicles.update')
            ->middleware('throttle:10,1');
    });

    // Driver Routes - All users can access these
    Route::get('/drivers/register', [App\Http\Controllers\DriverController::class, 'register'])->name('drivers.register');
    Route::post('/drivers/register', [App\Http\Controllers\DriverController::class, 'storeRegistration'])
        ->name('drivers.store-registration')
        ->middleware('throttle:5,1');

    // Driver Routes - Only authenticated users can access these
    Route::middleware(['auth'])->group(function () {
        Route::get('/drivers/profile', [App\Http\Controllers\DriverController::class, 'profile'])->name('drivers.profile');
        Route::get('/drivers/profile/edit', [App\Http\Controllers\DriverController::class, 'editProfile'])->name('drivers.edit-profile');
        Route::put('/drivers/profile', [App\Http\Controllers\DriverController::class, 'updateProfile'])->name('drivers.update-profile');
        Route::get('/drivers/availability', [App\Http\Controllers\DriverController::class, 'availability'])->name('drivers.availability');
        Route::post('/drivers/availability', [App\Http\Controllers\DriverController::class, 'updateAvailability'])->name('drivers.update-availability');

        // Driver Document Routes
        Route::get('/drivers/documents/upload', [App\Http\Controllers\DriverController::class, 'documentsUpload'])->name('drivers.documents.upload');
        Route::post('/drivers/documents', [App\Http\Controllers\DriverController::class, 'documentsStore'])->name('drivers.documents.store');
    });

    // Booking Routes
    Route::get('/bookings', [BookingController::class, 'index'])->name('booking.index');
    Route::get('/bookings/owner', [BookingController::class, 'ownerBookings'])->name('booking.owner');
    Route::get('/booking/{vehicleId}/create', [BookingController::class, 'create'])->name('booking.create');
    Route::post('/booking/summary', [BookingController::class, 'summary'])->name('booking.summary');
    Route::post('/booking/store', [BookingController::class, 'store'])->name('booking.store');
    Route::get('/booking/{id}', [BookingController::class, 'show'])->name('booking.show');
    Route::post('/booking/{id}/update-status', [BookingController::class, 'updateStatus'])->name('booking.update-status');

    // Payment Routes
    Route::get('/booking/{id}/payment', [BookingController::class, 'payment'])->name('booking.payment');
    Route::post('/booking/{id}/manual-payment', [PaymentController::class, 'processManualPayment'])->name('booking.manual-payment');
    Route::get('/booking/{id}/confirmation', [PaymentController::class, 'bookingConfirmation'])->name('booking.confirmation');

    // Razorpay Routes
    Route::get('/razorpay/create-order/{bookingId}', [RazorpayController::class, 'createOrder'])->name('razorpay.create-order');
    Route::post('/razorpay/payment', [RazorpayController::class, 'handlePayment'])->name('razorpay.payment');

    // PagSeguro Routes
    Route::get('/pagseguro/create-order/{bookingId}', [PagSeguroController::class, 'createOrder'])->name('pagseguro.create-order');
    Route::post('/pagseguro/payment', [PagSeguroController::class, 'processPayment'])->name('pagseguro.payment');

    // Messaging Routes
    Route::get('/messages', [App\Http\Controllers\MessageController::class, 'index'])->name('messages.index');
    Route::get('/messages/create', [App\Http\Controllers\MessageController::class, 'create'])->name('messages.create');
    Route::post('/messages', [App\Http\Controllers\MessageController::class, 'store'])->name('messages.store');
    Route::get('/messages/{id}', [App\Http\Controllers\MessageController::class, 'show'])->name('messages.show');
    Route::post('/messages/{id}/reply', [App\Http\Controllers\MessageController::class, 'reply'])->name('messages.reply');
    Route::post('/messages/{id}/mark-as-read', [App\Http\Controllers\MessageController::class, 'markAsRead'])->name('messages.mark-as-read');
    Route::get('/messages/unread-count', [App\Http\Controllers\MessageController::class, 'getUnreadCount'])->name('messages.unread-count');

    // Dispute Routes
    Route::get('/disputes', [App\Http\Controllers\DisputeController::class, 'index'])->name('disputes.index');
    Route::get('/disputes/create', [App\Http\Controllers\DisputeController::class, 'create'])->name('disputes.create');
    Route::post('/disputes', [App\Http\Controllers\DisputeController::class, 'store'])
        ->name('disputes.store')
        ->middleware('throttle:5,1');
    Route::get('/disputes/{id}', [App\Http\Controllers\DisputeController::class, 'show'])->name('disputes.show');
    Route::post('/disputes/{id}/message', [App\Http\Controllers\DisputeController::class, 'addMessage'])
        ->name('disputes.add-message')
        ->middleware('throttle:20,1');
    Route::post('/disputes/{id}/evidence', [App\Http\Controllers\DisputeController::class, 'addEvidence'])
        ->name('disputes.add-evidence')
        ->middleware('throttle:10,1');
    Route::post('/disputes/{id}/status', [App\Http\Controllers\DisputeController::class, 'updateStatus'])
        ->name('disputes.update-status')
        ->middleware('throttle:5,1');
});

Route::prefix('management')->middleware(['auth', 'verified'])->group(function () {
    // Vehicle Management
    Route::resource('vehicles', VehicleController::class);
    Route::post('/vehicles/{id}/toggle-featured', [App\Http\Controllers\VehicleController::class, 'toggleFeatured'])->name('vehicles.toggle-featured');
    Route::post('/vehicles/{id}/update-status', [App\Http\Controllers\VehicleController::class, 'updateStatus'])->name('vehicles.update-status');
    Route::post('/vehicles/{id}/submit-draft', [App\Http\Controllers\VehicleController::class, 'submitDraft'])->name('vehicles.submit-draft');

    // Vehicle Document Management
    Route::put('/vehicle-documents/{id}/approve', [App\Http\Controllers\VehicleDocumentController::class, 'approve'])->name('vehicle-documents.approve');
    Route::put('/vehicle-documents/{id}/reject', [App\Http\Controllers\VehicleDocumentController::class, 'reject'])->name('vehicle-documents.reject');

    Route::resource('vehicle-types', App\Http\Controllers\VehicleTypeController::class)
        ->except(['create', 'edit', 'show']);

});

// Admin Routes
Route::prefix('admin')->middleware(['auth', 'verified', 'role:admin'])->name('admin.')->group(function () {
    // Document Management
    Route::get('/documents', [App\Http\Controllers\Admin\DocumentController::class, 'index'])->name('documents.index');
    Route::get('/documents/{document}', [App\Http\Controllers\Admin\DocumentController::class, 'show'])->name('documents.show');
    Route::put('/documents/{document}/approve', [App\Http\Controllers\Admin\DocumentController::class, 'approve'])->name('documents.approve');
    Route::put('/documents/{document}/reject', [App\Http\Controllers\Admin\DocumentController::class, 'reject'])->name('documents.reject');

    // Driver Management
    Route::get('/drivers', [App\Http\Controllers\Admin\DriverController::class, 'index'])->name('drivers.index');
    Route::get('/drivers/{driver}', [App\Http\Controllers\Admin\DriverController::class, 'show'])->name('drivers.show');
    Route::put('/drivers/{driver}/approve', [App\Http\Controllers\Admin\DriverController::class, 'approve'])->name('drivers.approve');
    Route::put('/drivers/{driver}/reject', [App\Http\Controllers\Admin\DriverController::class, 'reject'])->name('drivers.reject');
    Route::put('/drivers/{driver}/toggle-featured', [App\Http\Controllers\Admin\DriverController::class, 'toggleFeatured'])->name('drivers.toggle-featured');
});

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfilesController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfilesController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfilesController::class, 'destroy'])->name('profile.destroy');
    Route::get('/profile/show', [ProfilesController::class, 'show'])->name('profile.show');
});

// Payment Gateway Webhook Routes (no auth required)
Route::post('/razorpay/webhook', [RazorpayController::class, 'webhook'])
    ->name('razorpay.webhook')
    ->middleware('throttle:100,1');
Route::post('/pagseguro/webhook', [PagSeguroController::class, 'webhook'])
    ->name('pagseguro.webhook')
    ->middleware('throttle:100,1');

// Test routes
Route::get('/test', function() {
    return view('test');
})->name('test');

Route::get('/test-file-upload', function() {
    return view('test-file-upload');
})->name('test-file-upload');


// Include auth routes before the catch-all route
require __DIR__.'/auth.php';

// Pages - this should be the last route as it's a catch-all
Route::get('/{slug}', [WebController::class, 'viewPage'])->name('page.view');

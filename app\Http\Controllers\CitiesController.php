<?php

namespace App\Http\Controllers;

use App\Models\City;
use App\Models\State;
use Illuminate\Http\Request;

class CitiesController extends Controller
{
    /**
     * Display a listing of the cities.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        // Fetch all cities with their associated states
        $cities = City::with('state')->get();
        $states = State::all();

        return view('backend.city.index', compact('cities', 'states'));
    }

    /**
     * Store a newly created city in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // Validate the incoming request data
        $request->validate([
            'name' => 'required|string|max:255',
            'state_id' => 'required|exists:states,id', // Ensure the state exists
            'latitude' => 'nullable|numeric',
            'longitude' => 'nullable|numeric',
            'cost' => 'required|numeric|min:0',
            'is_active' => 'required|in:0,1',
        ]);

        // Create a new city
        $city = City::create([
            'name' => $request->name,
            'state_id' => $request->state_id,
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
            'cost' => $request->cost,
            'is_active' => $request->is_active,
        ]);

        return response()->json([
            'message' => 'City added successfully!',
            'city' => $city,
        ], 201);
    }

    /**
     * Display the specified city.
     *
     * @param  \App\Models\City  $city
     * @return \Illuminate\Http\Response
     */
    public function show(City $city)
    {
        return response()->json([
            'city' => $city,
        ]);
    }

    /**
     * Update the specified city in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\City  $city
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, City $city)
    {
        // Validate the incoming request data
        $request->validate([
            'name' => 'required|string|max:255',
            'state_id' => 'required|exists:states,id', // Ensure the state exists
            'latitude' => 'nullable|numeric',
            'longitude' => 'nullable|numeric',
            'cost' => 'required|numeric|min:0',
            'is_active' => 'required|in:0,1',
        ]);

        // Update the city
        $city->update([
            'name' => $request->name,
            'state_id' => $request->state_id,
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
            'cost' => $request->cost,
            'is_active' => $request->is_active,
        ]);

        return response()->json([
            'message' => 'City updated successfully!',
            'city' => $city,
        ]);
    }

    /**
     * Remove the specified city from storage.
     *
     * @param  \App\Models\City  $city
     * @return \Illuminate\Http\Response
     */
    public function destroy(City $city)
    {
        // Delete the city
        $city->delete();

        return response()->json([
            'message' => 'City deleted successfully!',
        ]);
    }
}
<?php if (isset($component)) { $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742 = $attributes; } ?>
<?php $component = App\View\Components\AppBackendLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-backend-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppBackendLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            <?php echo e(__('Edit Menu Item')); ?>

        </h2>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <?php if (isset($component)) { $__componentOriginaleed9964738b2019d61a1dd4ee75e7981 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginaleed9964738b2019d61a1dd4ee75e7981 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.menu-item-form','data' => ['menuItem' => $menuItem,'menuItems' => $menuItems,'routeNames' => $routeNames,'action' => route('menu-items.update', $menuItem),'method' => 'PUT']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('menu-item-form'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['menuItem' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($menuItem),'menuItems' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($menuItems),'routeNames' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($routeNames),'action' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('menu-items.update', $menuItem)),'method' => 'PUT']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginaleed9964738b2019d61a1dd4ee75e7981)): ?>
<?php $attributes = $__attributesOriginaleed9964738b2019d61a1dd4ee75e7981; ?>
<?php unset($__attributesOriginaleed9964738b2019d61a1dd4ee75e7981); ?>
<?php endif; ?>
<?php if (isset($__componentOriginaleed9964738b2019d61a1dd4ee75e7981)): ?>
<?php $component = $__componentOriginaleed9964738b2019d61a1dd4ee75e7981; ?>
<?php unset($__componentOriginaleed9964738b2019d61a1dd4ee75e7981); ?>
<?php endif; ?>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $attributes = $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $component = $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?><?php /**PATH C:\laragon\www\carbnb\resources\views\backend\menu-items\edit.blade.php ENDPATH**/ ?>
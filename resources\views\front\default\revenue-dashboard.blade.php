<x-app-layout>
    <!-- Revenue Dashboard Header -->
    <section class="bg-gradient-to-r from-primary to-red-700 py-12 text-white">
        <div class="container mx-auto px-4">
            <h1 class="text-3xl md:text-4xl font-bold mb-4">Your Earnings Dashboard</h1>
            <p class="text-xl max-w-3xl">Track your earnings, analyze your performance, and discover ways to maximize your revenue with CARBNB.</p>
        </div>
    </section>

    <!-- Revenue Summary Cards -->
    <section class="py-8 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- Total Earnings Card -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-700">Total Earnings</h3>
                        <div class="bg-green-100 p-2 rounded-full">
                            <i class="fas fa-euro-sign text-green-600"></i>
                        </div>
                    </div>
                    <p class="text-3xl font-bold text-gray-800">€<span id="total-earnings">0.00</span></p>
                    <p class="text-sm text-gray-500 mt-2">Lifetime earnings from all bookings</p>
                </div>

                <!-- This Month Card -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-700">This Month</h3>
                        <div class="bg-blue-100 p-2 rounded-full">
                            <i class="fas fa-calendar-alt text-blue-600"></i>
                        </div>
                    </div>
                    <p class="text-3xl font-bold text-gray-800">€<span id="month-earnings">0.00</span></p>
                    <p class="text-sm text-gray-500 mt-2">
                        <span id="month-comparison" class="font-medium text-green-600">
                            <i class="fas fa-arrow-up"></i> 0%
                        </span> 
                        vs last month
                    </p>
                </div>

                <!-- Completed Bookings Card -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-700">Completed Bookings</h3>
                        <div class="bg-purple-100 p-2 rounded-full">
                            <i class="fas fa-check-circle text-purple-600"></i>
                        </div>
                    </div>
                    <p class="text-3xl font-bold text-gray-800"><span id="completed-bookings">0</span></p>
                    <p class="text-sm text-gray-500 mt-2">Total successful rentals</p>
                </div>

                <!-- Occupancy Rate Card -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-700">Occupancy Rate</h3>
                        <div class="bg-yellow-100 p-2 rounded-full">
                            <i class="fas fa-chart-pie text-yellow-600"></i>
                        </div>
                    </div>
                    <p class="text-3xl font-bold text-gray-800"><span id="occupancy-rate">0</span>%</p>
                    <p class="text-sm text-gray-500 mt-2">Of available days booked</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Revenue Chart and Booking Details -->
    <section class="py-8">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Revenue Chart -->
                <div class="lg:col-span-2 bg-white rounded-lg shadow-md p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-xl font-bold text-gray-800">Earnings Over Time</h3>
                        <div class="flex space-x-2">
                            <button class="period-btn px-3 py-1 rounded-md bg-gray-200 text-gray-700 text-sm font-medium hover:bg-gray-300" data-period="week">Week</button>
                            <button class="period-btn px-3 py-1 rounded-md bg-primary text-white text-sm font-medium" data-period="month">Month</button>
                            <button class="period-btn px-3 py-1 rounded-md bg-gray-200 text-gray-700 text-sm font-medium hover:bg-gray-300" data-period="year">Year</button>
                        </div>
                    </div>
                    <div class="h-80">
                        <canvas id="revenue-chart"></canvas>
                    </div>
                </div>

                <!-- Recent Bookings -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-6">Recent Bookings</h3>
                    <div class="space-y-4" id="recent-bookings-container">
                        <!-- Booking items will be populated by JavaScript -->
                        <div class="flex items-center justify-center h-40 text-gray-400">
                            <p>No recent bookings to display</p>
                        </div>
                    </div>
                    <a href="#" class="block text-center text-primary font-medium mt-4 hover:text-red-700">View All Bookings</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Revenue Projection Tool -->
    <section class="py-8 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-xl font-bold text-gray-800 mb-6">Revenue Projection Tool</h3>
                <p class="text-gray-600 mb-6">Estimate your potential earnings based on different rental scenarios.</p>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <div>
                        <label class="block text-gray-700 mb-2">Daily Rate (€)</label>
                        <input type="number" id="daily-rate" min="10" value="45" class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none">
                    </div>
                    <div>
                        <label class="block text-gray-700 mb-2">Days Per Month</label>
                        <div class="flex items-center">
                            <input type="range" id="days-slider" min="1" max="30" value="15" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                            <span class="ml-4 text-lg font-semibold text-primary" id="days-value">15</span>
                        </div>
                    </div>
                    <div>
                        <label class="block text-gray-700 mb-2">Months</label>
                        <select id="months-select" class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none">
                            <option value="1">1 month</option>
                            <option value="3">3 months</option>
                            <option value="6">6 months</option>
                            <option value="12" selected>12 months</option>
                        </select>
                    </div>
                </div>
                
                <div class="text-center mb-8">
                    <button id="calculate-projection" class="px-6 py-3 bg-primary text-white font-bold rounded-lg hover:bg-red-700 transition">Calculate Potential Earnings</button>
                </div>
                
                <div id="projection-results" class="hidden">
                    <div class="border-t border-gray-200 pt-6">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                            <div>
                                <h4 class="text-lg font-semibold text-gray-700 mb-2">Monthly Earnings</h4>
                                <p class="text-3xl font-bold text-primary">€<span id="monthly-projection">0</span></p>
                            </div>
                            <div>
                                <h4 class="text-lg font-semibold text-gray-700 mb-2">Total Period Earnings</h4>
                                <p class="text-3xl font-bold text-primary">€<span id="total-projection">0</span></p>
                            </div>
                            <div>
                                <h4 class="text-lg font-semibold text-gray-700 mb-2">Occupancy Rate</h4>
                                <p class="text-3xl font-bold text-primary"><span id="projection-occupancy">0</span>%</p>
                            </div>
                        </div>
                        
                        <div class="mt-8 p-6 bg-gray-50 rounded-lg border border-gray-200">
                            <h4 class="text-lg font-semibold text-gray-700 mb-4">Earnings Breakdown</h4>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead>
                                        <tr>
                                            <th class="px-6 py-3 bg-gray-100 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Period</th>
                                            <th class="px-6 py-3 bg-gray-100 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Days Rented</th>
                                            <th class="px-6 py-3 bg-gray-100 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Earnings</th>
                                        </tr>
                                    </thead>
                                    <tbody id="projection-breakdown" class="bg-white divide-y divide-gray-200">
                                        <!-- Will be populated by JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Tips to Increase Revenue -->
    <section class="py-8">
        <div class="container mx-auto px-4">
            <h3 class="text-2xl font-bold text-gray-800 mb-6">Tips to Maximize Your Earnings</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-camera text-blue-600"></i>
                    </div>
                    <h4 class="text-lg font-bold mb-2">High-Quality Photos</h4>
                    <p class="text-gray-600">Listings with professional photos receive up to 35% more bookings. Make sure your car looks its best.</p>
                </div>
                
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-calendar-check text-green-600"></i>
                    </div>
                    <h4 class="text-lg font-bold mb-2">Keep Your Calendar Updated</h4>
                    <p class="text-gray-600">Regularly update your availability to increase your chances of getting bookings.</p>
                </div>
                
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-star text-purple-600"></i>
                    </div>
                    <h4 class="text-lg font-bold mb-2">Provide Excellent Service</h4>
                    <p class="text-gray-600">Highly-rated hosts receive more bookings. Respond quickly and provide a clean car.</p>
                </div>
                
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-tag text-yellow-600"></i>
                    </div>
                    <h4 class="text-lg font-bold mb-2">Competitive Pricing</h4>
                    <p class="text-gray-600">Research similar cars in your area and price competitively to attract more renters.</p>
                </div>
                
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-percentage text-red-600"></i>
                    </div>
                    <h4 class="text-lg font-bold mb-2">Offer Discounts</h4>
                    <p class="text-gray-600">Weekly and monthly discounts can encourage longer bookings, increasing your overall occupancy.</p>
                </div>
                
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-thumbs-up text-indigo-600"></i>
                    </div>
                    <h4 class="text-lg font-bold mb-2">Add Special Features</h4>
                    <p class="text-gray-600">Extras like child seats, GPS, or phone chargers can make your listing stand out.</p>
                </div>
            </div>
        </div>
    </section>

    @push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize variables
            let revenueChart;
            let currentPeriod = 'month';
            
            // Function to fetch revenue data from the API
            const fetchRevenueData = async (period = 'month') => {
                try {
                    const response = await fetch(`{{ route('revenue.stats') }}?period=${period}`);
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return await response.json();
                } catch (error) {
                    console.error('Error fetching revenue data:', error);
                    return {
                        totalEarnings: 0,
                        periodEarnings: 0,
                        periodComparison: 0,
                        completedBookings: 0,
                        occupancyRate: 0,
                        recentBookings: [],
                        chartData: []
                    };
                }
            };
            
            // Function to update the dashboard with data
            const updateDashboard = (data) => {
                // Update summary cards
                document.getElementById('total-earnings').textContent = data.totalEarnings.toFixed(2);
                document.getElementById('month-earnings').textContent = data.periodEarnings.toFixed(2);
                
                // Update comparison with appropriate icon
                const comparisonElement = document.getElementById('month-comparison');
                if (data.periodComparison > 0) {
                    comparisonElement.innerHTML = `<i class="fas fa-arrow-up"></i> ${data.periodComparison.toFixed(1)}%`;
                    comparisonElement.className = 'font-medium text-green-600';
                } else if (data.periodComparison < 0) {
                    comparisonElement.innerHTML = `<i class="fas fa-arrow-down"></i> ${Math.abs(data.periodComparison).toFixed(1)}%`;
                    comparisonElement.className = 'font-medium text-red-600';
                } else {
                    comparisonElement.innerHTML = `0%`;
                    comparisonElement.className = 'font-medium text-gray-600';
                }
                
                document.getElementById('completed-bookings').textContent = data.completedBookings;
                document.getElementById('occupancy-rate').textContent = data.occupancyRate;
                
                // Populate recent bookings
                const bookingsContainer = document.getElementById('recent-bookings-container');
                bookingsContainer.innerHTML = '';
                
                if (data.recentBookings && data.recentBookings.length > 0) {
                    data.recentBookings.forEach(booking => {
                        const statusClass = booking.status === 'completed' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800';
                        const bookingItem = `
                            <div class="flex justify-between items-center p-4 border border-gray-200 rounded-lg">
                                <div>
                                    <p class="font-medium">${booking.car}</p>
                                    <p class="text-sm text-gray-500">${booking.dates}</p>
                                </div>
                                <div class="text-right">
                                    <p class="font-bold">€${parseFloat(booking.amount).toFixed(2)}</p>
                                    <span class="inline-block px-2 py-1 text-xs rounded-full ${statusClass} mt-1">${booking.status}</span>
                                </div>
                            </div>
                        `;
                        bookingsContainer.innerHTML += bookingItem;
                    });
                } else {
                    bookingsContainer.innerHTML = `
                        <div class="flex items-center justify-center h-40 text-gray-400">
                            <p>No recent bookings to display</p>
                        </div>
                    `;
                }
                
                // Update chart data
                updateChart(data.chartData, currentPeriod);
            };
            
            // Function to initialize or update the chart
            const updateChart = (chartData, period) => {
                const ctx = document.getElementById('revenue-chart').getContext('2d');
                
                // Prepare chart data based on period
                let labels, data, label;
                
                switch(period) {
                    case 'week':
                        labels = chartData.map(item => item.label);
                        data = chartData.map(item => item.amount);
                        label = 'Daily Earnings (€)';
                        break;
                    case 'month':
                        labels = chartData.map(item => item.month);
                        data = chartData.map(item => item.amount);
                        label = 'Monthly Earnings (€)';
                        break;
                    case 'year':
                        labels = chartData.map(item => item.year);
                        data = chartData.map(item => item.amount);
                        label = 'Yearly Earnings (€)';
                        break;
                }
                
                // If chart already exists, update it
                if (revenueChart) {
                    revenueChart.data.labels = labels;
                    revenueChart.data.datasets[0].label = label;
                    revenueChart.data.datasets[0].data = data;
                    revenueChart.update();
                } else {
                    // Initialize chart
                    revenueChart = new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: labels,
                            datasets: [{
                                label: label,
                                data: data,
                                backgroundColor: 'rgba(220, 38, 38, 0.8)',
                                borderColor: 'rgba(220, 38, 38, 1)',
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        callback: function(value) {
                                            return '€' + value;
                                        }
                                    }
                                }
                            }
                        }
                    });
                }
            };
            
            // Initial data load
            fetchRevenueData('month').then(data => {
                updateDashboard(data);
            });
            
            // Period buttons functionality
            document.querySelectorAll('.period-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const period = this.dataset.period;
                    currentPeriod = period;
                    
                    // Update button styles
                    document.querySelectorAll('.period-btn').forEach(btn => {
                        btn.classList.remove('bg-primary', 'text-white');
                        btn.classList.add('bg-gray-200', 'text-gray-700');
                    });
                    this.classList.remove('bg-gray-200', 'text-gray-700');
                    this.classList.add('bg-primary', 'text-white');
                    
                    // Fetch new data based on the period
                    fetchRevenueData(period).then(data => {
                        updateDashboard(data);
                    });
                });
            });

            // Revenue projection calculator
            const daysSlider = document.getElementById('days-slider');
            const daysValue = document.getElementById('days-value');
            const calculateBtn = document.getElementById('calculate-projection');
            const projectionResults = document.getElementById('projection-results');
            
            // Initialize with average daily rate from user's vehicles
            const initializeProjectionTool = async () => {
                try {
                    const response = await fetch('{{ route('revenue.stats') }}');
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    const data = await response.json();
                    
                    // Calculate average daily rate from recent bookings if available
                    if (data.recentBookings && data.recentBookings.length > 0) {
                        // Get user's vehicles to calculate average daily rate
                        const vehicles = await fetch('{{ route('api.user.vehicles') }}')
                            .then(res => res.json())
                            .catch(() => []);
                            
                        if (vehicles && vehicles.length > 0) {
                            const totalDailyRate = vehicles.reduce((sum, vehicle) => sum + parseFloat(vehicle.daily_rate || 0), 0);
                            const avgDailyRate = totalDailyRate / vehicles.length;
                            
                            if (avgDailyRate > 0) {
                                document.getElementById('daily-rate').value = avgDailyRate.toFixed(2);
                            }
                        }
                    }
                } catch (error) {
                    console.error('Error initializing projection tool:', error);
                }
            };
            
            // Try to initialize with real data
            initializeProjectionTool().catch(() => {
                // If initialization fails, continue with default values
                console.log('Using default values for projection tool');
            });
            
            daysSlider.addEventListener('input', function() {
                daysValue.textContent = this.value;
            });
            
            calculateBtn.addEventListener('click', function() {
                const dailyRate = parseFloat(document.getElementById('daily-rate').value);
                const daysPerMonth = parseInt(daysSlider.value);
                const months = parseInt(document.getElementById('months-select').value);
                
                if (isNaN(dailyRate) || dailyRate <= 0) {
                    alert('Please enter a valid daily rate');
                    return;
                }
                
                const monthlyEarnings = dailyRate * daysPerMonth;
                const totalEarnings = monthlyEarnings * months;
                const occupancyRate = (daysPerMonth / 30) * 100;
                
                document.getElementById('monthly-projection').textContent = monthlyEarnings.toFixed(2);
                document.getElementById('total-projection').textContent = totalEarnings.toFixed(2);
                document.getElementById('projection-occupancy').textContent = occupancyRate.toFixed(0);
                
                // Generate breakdown table
                const breakdownTable = document.getElementById('projection-breakdown');
                breakdownTable.innerHTML = '';
                
                const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
                const currentMonth = new Date().getMonth();
                
                for (let i = 0; i < months; i++) {
                    const monthIndex = (currentMonth + i) % 12;
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${monthNames[monthIndex]}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">${daysPerMonth}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">€${monthlyEarnings.toFixed(2)}</td>
                    `;
                    breakdownTable.appendChild(row);
                }
                
                projectionResults.classList.remove('hidden');
            });
        });
    </script>
    @endpush
</x-app-layout>
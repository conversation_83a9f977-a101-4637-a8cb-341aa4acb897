<?php if (isset($component)) { $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742 = $attributes; } ?>
<?php $component = App\View\Components\AppBackendLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-backend-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppBackendLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div x-data="vehicleManager()" class="p-6">
        <script>
            function vehicleManager() {
                return {
                    items: <?php echo json_encode($vehicles->items(), 15, 512) ?>,
                    currentPage: <?php echo e($vehicles->currentPage()); ?>,
                    totalPages: <?php echo e($vehicles->lastPage()); ?>,
                    perPage: <?php echo e($vehicles->perPage()); ?>,
                    totalRecords: <?php echo e($vehicles->total()); ?>,
                    loading: false,
                    search: '<?php echo e(request('search')); ?>',
                    filters: {
                        vehicle_type_id: '<?php echo e(request('vehicle_type_id')); ?>',
                        status: '<?php echo e(request('status')); ?>',
                        city_id: '<?php echo e(request('city_id')); ?>'
                    },
                    successMessage: '<?php echo e(session('success')); ?>',
                    showStatusModal: false,
                    selectedVehicle: {},
                    statusForm: {
                        status: '',
                        rejection_reason: ''
                    },
                    isAdmin: <?php echo e(auth()->user()->hasRole('admin') ? 'true' : 'false'); ?>,
                    isAgent: <?php echo e(auth()->user()->hasRole('agent') ? 'true' : 'false'); ?>,
                    canApproveVehicles: <?php echo e(auth()->user()->hasRole(['admin', 'agent']) ? 'true' : 'false'); ?>,

                    // Initialize pagination array
                    get paginationArray() {
                        let pages = [];
                        let startPage = Math.max(1, this.currentPage - 2);
                        let endPage = Math.min(this.totalPages, this.currentPage + 2);

                        if (startPage > 1) {
                            pages.push(1);
                            if (startPage > 2) pages.push('...');
                        }

                        for (let i = startPage; i <= endPage; i++) {
                            pages.push(i);
                        }

                        if (endPage < this.totalPages) {
                            if (endPage < this.totalPages - 1) pages.push('...');
                            pages.push(this.totalPages);
                        }

                        return pages;
                    },

                    // Methods
                    searchItems() {
                        this.applyFilters();
                    },

                    applyFilters() {
                        this.loading = true;
                        let queryParams = new URLSearchParams();

                        // Add search term if present
                        if (this.search) {
                            queryParams.append('search', this.search);
                        }

                        // Add filters if present
                        if (this.filters.vehicle_type_id) {
                            queryParams.append('vehicle_type_id', this.filters.vehicle_type_id);
                        }

                        if (this.filters.status) {
                            queryParams.append('status', this.filters.status);
                        }

                        if (this.filters.city_id) {
                            queryParams.append('city_id', this.filters.city_id);
                        }

                        // Add pagination
                        queryParams.append('page', 1); // Reset to first page on filter

                        // Redirect with query parameters
                        window.location.href = `${window.location.pathname}?${queryParams.toString()}`;
                    },

                    resetFilters() {
                        this.search = '';
                        this.filters.vehicle_type_id = '';
                        this.filters.status = '';
                        this.filters.city_id = '';
                        this.applyFilters();
                    },

                    prevPage() {
                        if (this.currentPage > 1) {
                            this.goToPage(this.currentPage - 1);
                        }
                    },

                    nextPage() {
                        if (this.currentPage < this.totalPages) {
                            this.goToPage(this.currentPage + 1);
                        }
                    },

                    goToPage(page) {
                        let url = new URL(window.location.href);
                        url.searchParams.set('page', page);
                        window.location.href = url.toString();
                    },

                    getPrimaryImage(vehicle) {
                        if (vehicle.images && vehicle.images.length > 0) {
                            const primaryImage = vehicle.images.find(img => img.is_primary);
                            if (primaryImage && primaryImage.file) {
                                // Use the file's full URL
                                return '<?php echo e(url("/storage/")); ?>/' + primaryImage.file.url;
                            } else if (primaryImage) {
                                // Fallback for older data format
                                return '<?php echo e(asset("storage/vehicle-images")); ?>/' + primaryImage.file_id;
                            }
                        }
                        return '<?php echo e(asset("images/vehicle-placeholder.jpg")); ?>';
                    },

                    formatFuelType(fuelType) {
                        const types = {
                            'gasoline': 'Gasoline',
                            'diesel': 'Diesel',
                            'hybrid': 'Hybrid',
                            'electric': 'Electric',
                            'plugin_hybrid': 'Plug-in Hybrid'
                        };
                        return types[fuelType] || fuelType;
                    },

                    formatStatus(status) {
                        const statuses = {
                            'draft': 'Draft',
                            'pending': 'Pending',
                            'active': 'Active',
                            'rejected': 'Rejected',
                            'inactive': 'Inactive'
                        };
                        return statuses[status] || status;
                    },

                    deleteVehicle(id) {
                        if (confirm('Are you sure you want to delete this vehicle? This action cannot be undone.')) {
                            fetch(`/management/vehicles/${id}`, {
                                method: 'DELETE',
                                headers: {
                                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                                    'Accept': 'application/json'
                                }
                            })
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    this.successMessage = data.message;
                                    // Remove the vehicle from the list
                                    this.items = this.items.filter(vehicle => vehicle.id !== id);
                                    // Reload after a short delay
                                    setTimeout(() => {
                                        window.location.reload();
                                    }, 1500);
                                } else {
                                    alert('Error: ' + data.message);
                                }
                            })
                            .catch(error => {
                                console.error('Error:', error);
                                alert('An error occurred while deleting the vehicle.');
                            });
                        }
                    },

                    toggleFeatured(vehicle) {
                        fetch(`/management/vehicles/${vehicle.id}/toggle-featured`, {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                                'Accept': 'application/json',
                                'Content-Type': 'application/json'
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                vehicle.is_featured = data.is_featured;
                                this.successMessage = data.message;
                            } else {
                                alert('Error: ' + data.message);
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('An error occurred while updating featured status.');
                        });
                    },

                    openStatusModal(vehicle) {
                        this.selectedVehicle = vehicle;
                        this.statusForm.status = vehicle.status;
                        this.statusForm.rejection_reason = vehicle.rejection_reason || '';
                        this.showStatusModal = true;
                    },

                    updateVehicleStatus() {
                        fetch(`/management/vehicles/${this.selectedVehicle.id}/update-status`, {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                                'Accept': 'application/json',
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(this.statusForm)
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                this.showStatusModal = false;
                                this.selectedVehicle.status = this.statusForm.status;
                                this.selectedVehicle.rejection_reason = this.statusForm.rejection_reason;
                                this.successMessage = data.message;

                                // Update the vehicle in the items array
                                const index = this.items.findIndex(v => v.id === this.selectedVehicle.id);
                                if (index !== -1) {
                                    this.items[index].status = this.statusForm.status;
                                    this.items[index].rejection_reason = this.statusForm.rejection_reason;
                                }
                            } else {
                                alert('Error: ' + data.message);
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('An error occurred while updating vehicle status.');
                        });
                    },

                    submitDraft(id) {
                        if (confirm('Are you sure you want to submit this vehicle for approval? Once submitted, it will be reviewed by an administrator.')) {
                            fetch(`/management/vehicles/${id}/submit-draft`, {
                                method: 'POST',
                                headers: {
                                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                                    'Accept': 'application/json',
                                    'Content-Type': 'application/json'
                                }
                            })
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    this.successMessage = data.message;

                                    // Update the vehicle status in the items array
                                    const index = this.items.findIndex(v => v.id === id);
                                    if (index !== -1) {
                                        this.items[index].status = 'pending';
                                    }

                                    // Reload after a short delay
                                    setTimeout(() => {
                                        window.location.reload();
                                    }, 1500);
                                } else {
                                    alert('Error: ' + data.message);
                                }
                            })
                            .catch(error => {
                                console.error('Error:', error);
                                alert('An error occurred while submitting the vehicle for approval.');
                            });
                        }
                    }
                };
            }
        </script>
        <div class="mb-6">
            <div class="page-header">
                <h1 class="h1">Vehicle Management</h1>
                <a href="<?php echo e(route('vehicles.create')); ?>"
                   class="btn-primary px-5 py-2.5 rounded-lg shadow">
                    <i class="fas fa-plus btn-icon"></i>
                    Add Vehicle
                </a>
            </div>

            <!-- Success Message (Fallback if notification system fails) -->
            <div x-show="successMessage" x-transition x-cloak class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4">
                <span x-text="successMessage"></span>
                <button @click="successMessage = ''" class="absolute top-0 right-0 mr-2 mt-2">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <!-- Search and filters section -->
        <div class="mb-5 content-card shadow-md hover:shadow-lg transition-shadow duration-300">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">Search Vehicles</h2>

            <!-- Search Input -->
            <div class="mb-5">
                <div class="relative rounded-lg shadow-sm">
                    <input
                        id="vehicle-search"
                        type="text"
                        x-model="search"
                        @keyup.enter="searchItems"
                        placeholder="Search vehicles by make, model, or license plate..."
                        class="form-input pl-10 border-gray-300 focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-20 rounded-lg transition-all duration-200 w-full"
                        value="<?php echo e(request('search')); ?>"
                    >
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <button
                        @click="searchItems"
                        class="absolute inset-y-0 right-0 px-4 text-white bg-primary hover:bg-primary-hover rounded-r-lg focus:outline-none transition-colors duration-200"
                    >
                        Search
                    </button>
                </div>
            </div>

            <h3 class="text-base font-medium text-gray-700 mb-3 border-b pb-2">Filters</h3>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Vehicle Type Filter -->
                <div>
                    <label for="vehicle-type-filter" class="block text-sm font-medium text-gray-600 mb-1">Vehicle Type</label>
                    <select
                        id="vehicle-type-filter"
                        x-model="filters.vehicle_type_id"
                        @change="applyFilters"
                        class="form-input text-sm rounded-md border-gray-300 focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-20 w-full"
                    >
                        <option value="">All Vehicle Types</option>
                        <?php $__currentLoopData = $vehicleTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($type->id); ?>" <?php echo e(request('vehicle_type_id') == $type->id ? 'selected' : ''); ?>><?php echo e($type->name); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <!-- City Filter -->
                <div>
                    <label for="city-filter" class="block text-sm font-medium text-gray-600 mb-1">City</label>
                    <select
                        id="city-filter"
                        x-model="filters.city_id"
                        @change="applyFilters"
                        class="form-input text-sm rounded-md border-gray-300 focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-20 w-full"
                    >
                        <option value="">All Cities</option>
                        <?php $__currentLoopData = $cities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $city): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($city->id); ?>" <?php echo e(request('city_id') == $city->id ? 'selected' : ''); ?>><?php echo e($city->name); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <!-- Status Filter -->
                <div>
                    <label for="status-filter" class="block text-sm font-medium text-gray-600 mb-1">Status</label>
                    <select
                        id="status-filter"
                        x-model="filters.status"
                        @change="applyFilters"
                        class="form-input text-sm rounded-md border-gray-300 focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-20 w-full"
                    >
                        <option value="">All Statuses</option>
                        <option value="draft" <?php echo e(request('status') == 'draft' ? 'selected' : ''); ?>>Draft</option>
                        <option value="pending" <?php echo e(request('status') == 'pending' ? 'selected' : ''); ?>>Pending</option>
                        <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>Active</option>
                        <option value="rejected" <?php echo e(request('status') == 'rejected' ? 'selected' : ''); ?>>Rejected</option>
                        <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                    </select>
                </div>
            </div>

            <!-- Reset Button -->
            <div class="mt-4 flex justify-end">
                <button
                    @click="resetFilters"
                    class="flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all duration-200"
                    title="Clear filters"
                >
                    <i class="fas fa-arrow-rotate-left mr-2"></i>
                    Reset Filters
                </button>
            </div>
        </div>

        <!-- Loading Indicator -->
        <div x-show="loading" class="flex justify-center my-8">
            <i class="fas fa-spinner fa-spin text-blue-600 text-4xl"></i>
        </div>

        <!-- Vehicles list -->
        <div class="bg-white rounded-lg shadow overflow-hidden" x-show="!loading">
            <div class="overflow-x-auto table-container">
                <table class="w-full table-auto border-collapse data-table">
                    <thead>
                    <tr class="bg-gray-100 text-gray-700 uppercase text-sm leading-normal">
                        <th class="py-3 px-6 text-left">#</th>
                        <th class="py-3 px-6 text-left">Vehicle</th>
                        <th class="py-3 px-6 text-left">Type</th>
                        <th class="py-3 px-6 text-left">City</th>
                        <th class="py-3 px-6 text-left">Details</th>
                        <th class="py-3 px-6 text-center">Daily Rate</th>
                        <th class="py-3 px-6 text-center">Status</th>
                        <th class="py-3 px-6 text-center">Actions</th>
                    </tr>
                    </thead>
                    <tbody>
                    <template x-if="items.length === 0">
                        <tr class="border-b border-gray-200">
                            <td colspan="8" class="py-8 px-6 text-center text-gray-500">
                                No vehicles found
                            </td>
                        </tr>
                    </template>
                    <template x-for="(vehicle, index) in items" :key="vehicle.id">
                        <tr class="border-b border-gray-200 hover:bg-gray-50">
                            <td class="py-3 px-6 text-left" x-text="vehicle.id"></td>

                            <!-- Vehicle Info -->
                            <td class="py-3 px-6">
                                <div class="flex items-center">
                                    <!-- Vehicle Image -->
                                    <div class="flex-shrink-0 h-10 w-14 mr-3 bg-gray-300 rounded overflow-hidden">
                                        <img
                                            :src="getPrimaryImage(vehicle)"
                                            class="h-full w-full object-cover"
                                            :alt="vehicle.make + ' ' + vehicle.model"
                                        >
                                    </div>

                                    <!-- Vehicle Info -->
                                    <div>
                                        <p class="font-semibold" x-text="vehicle.year + ' ' + vehicle.make + ' ' + vehicle.model"></p>
                                        <p class="text-xs text-gray-600" x-text="'License: ' + vehicle.license_plate"></p>
                                    </div>
                                </div>
                            </td>

                            <!-- Vehicle Type -->
                            <td class="py-3 px-6 text-left">
                                <span x-text="vehicle.vehicle_type ? vehicle.vehicle_type.name : 'N/A'"></span>
                            </td>

                            <!-- City -->
                            <td class="py-3 px-6 text-left">
                                <span x-text="vehicle.city ? vehicle.city.name : 'N/A'"></span>
                            </td>

                            <!-- Details -->
                            <td class="py-3 px-6 text-left">
                                <div class="text-sm">
                                    <p>
                                        <i class="fas fa-gas-pump mr-1 text-gray-500"></i>
                                        <span x-text="formatFuelType(vehicle.fuel_type)"></span>
                                    </p>
                                    <p>
                                        <i class="fas fa-cog mr-1 text-gray-500"></i>
                                        <span x-text="vehicle.transmission === 'automatic' ? 'Automatic' : 'Manual'"></span>
                                    </p>
                                    <p>
                                        <i class="fas fa-users mr-1 text-gray-500"></i>
                                        <span x-text="vehicle.seats + ' seats'"></span>
                                    </p>
                                </div>
                            </td>

                            <!-- Daily Rate -->
                            <td class="py-3 px-6 text-center">
                                <div class="font-semibold text-blue-600">
                                    $<span x-text="vehicle.daily_rate"></span>/day
                                </div>
                                <template x-if="vehicle.is_featured">
                                    <span class="px-2 py-0.5 text-xs font-semibold bg-amber-100 text-amber-800 rounded-full">
                                        Featured
                                    </span>
                                </template>
                            </td>

                            <!-- Status -->
                            <td class="py-3 px-6 text-center">
                                <span
                                    :class="{
                                        'bg-blue-100 text-blue-800': vehicle.status === 'draft',
                                        'bg-yellow-100 text-yellow-800': vehicle.status === 'pending',
                                        'bg-green-100 text-green-800': vehicle.status === 'active',
                                        'bg-red-100 text-red-800': vehicle.status === 'rejected',
                                        'bg-gray-100 text-gray-800': vehicle.status === 'inactive'
                                    }"
                                    class="px-2 py-1 rounded-full text-xs font-semibold"
                                    x-text="formatStatus(vehicle.status)"
                                ></span>
                            </td>

                            <!-- Actions -->
                            <td class="py-3 px-6 text-center">
                                <div class="flex flex-wrap justify-center items-center gap-1">
                                    <!-- View Button -->
                                    <a :href="'/management/vehicles/' + vehicle.id"
                                       class="btn-primary bg-emerald-500 hover:bg-emerald-600 px-2 py-1.5 text-xs">
                                        <i class="fas fa-eye mr-1"></i>
                                        View
                                    </a>

                                    <!-- Edit Button -->
                                    <a :href="'/management/vehicles/' + vehicle.id + '/edit'"
                                       class="btn-primary bg-amber-500 hover:bg-amber-600 px-2 py-1.5 text-xs">
                                        <i class="fas fa-edit mr-1"></i>
                                        Edit
                                    </a>

                                    <!-- Submit Draft Button (only for draft vehicles) -->
                                    <template x-if="vehicle.status === 'draft' && !isAdmin">
                                        <button @click="submitDraft(vehicle.id)"
                                                class="btn-primary px-2 py-1.5 text-xs">
                                            <i class="fas fa-paper-plane mr-1"></i>
                                            Submit
                                        </button>
                                    </template>

                                    <!-- Delete Button -->
                                    <button @click="deleteVehicle(vehicle.id)"
                                            class="btn-primary bg-rose-500 hover:bg-rose-600 px-2 py-1.5 text-xs">
                                        <i class="fas fa-trash-alt mr-1"></i>
                                        Delete
                                    </button>

                                    <!-- Admin and Agent actions -->
                                    <div class="flex justify-center items-center gap-1 mt-1 w-full">
                                        <!-- Toggle Featured Button (Admin only) -->
                                        <template x-if="isAdmin">
                                            <button @click="toggleFeatured(vehicle)"
                                                    class="btn-primary bg-purple-500 hover:bg-purple-600 px-2 py-1.5 text-xs">
                                                <i class="fas fa-star mr-1"></i>
                                                <span x-text="vehicle.is_featured ? 'Unfeature' : 'Feature'"></span>
                                            </button>
                                        </template>

                                        <!-- Status Update Button (Admin and Agent) -->
                                        <button @click="openStatusModal(vehicle)"
                                                class="btn-primary px-2 py-1.5 text-xs">
                                            <i class="fas fa-exchange-alt mr-1"></i>
                                            Status
                                        </button>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </template>
                    </tbody>
                </table>
            </div>

            <!-- Pagination controls -->
            <template x-if="totalPages > 1">
                <div class="px-4 py-3 bg-white border-t border-gray-200 sm:px-6">
                    <div class="flex items-center justify-between">
                        <div class="flex-1 flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-700">
                                    Showing
                                    <span class="font-medium" x-text="(currentPage - 1) * perPage + 1"></span>
                                    to
                                    <span class="font-medium" x-text="Math.min(currentPage * perPage, totalRecords)"></span>
                                    of
                                    <span class="font-medium" x-text="totalRecords"></span>
                                    results
                                </p>
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                    <!-- Previous Page -->
                                    <button
                                        @click="prevPage"
                                        :disabled="currentPage === 1"
                                        :class="{'opacity-50 cursor-not-allowed': currentPage === 1}"
                                        class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                                    >
                                        <span class="sr-only">Previous</span>
                                        <i class="fas fa-chevron-left h-5 w-5"></i>
                                    </button>

                                    <!-- Page Numbers -->
                                    <template x-for="page in paginationArray" :key="page">
                                        <button
                                            @click="page !== '...' ? goToPage(page) : null"
                                            :class="{
                                                'bg-blue-50 border-blue-500 text-blue-600': page === currentPage,
                                                'bg-white border-gray-300 text-gray-500 hover:bg-gray-50': page !== currentPage && page !== '...',
                                                'bg-white border-gray-300 text-gray-500 cursor-default': page === '...'
                                            }"
                                            class="relative inline-flex items-center px-4 py-2 border text-sm font-medium"
                                            x-text="page"
                                        ></button>
                                    </template>

                                    <!-- Next Page -->
                                    <button
                                        @click="nextPage"
                                        :disabled="currentPage === totalPages"
                                        :class="{'opacity-50 cursor-not-allowed': currentPage === totalPages}"
                                        class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                                    >
                                        <span class="sr-only">Next</span>
                                        <i class="fas fa-chevron-right h-5 w-5"></i>
                                    </button>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </div>

        <!-- Status Update Modal -->
        <div x-show="showStatusModal" class="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50 z-50" x-cloak>
            <div class="bg-white p-6 rounded-lg shadow-lg w-[500px] max-w-full">
                <h3 class="h3 mb-4">Update Vehicle Status</h3>

                <div class="space-y-4">
                    <div>
                        <label class="text-gray-700 text-sm font-medium">Current Status</label>
                        <div class="mt-1 p-2 bg-gray-100 rounded-md">
                            <span
                                :class="{
                                    'bg-yellow-100 text-yellow-800': selectedVehicle.status === 'pending',
                                    'bg-green-100 text-green-800': selectedVehicle.status === 'active',
                                    'bg-red-100 text-red-800': selectedVehicle.status === 'rejected',
                                    'bg-gray-100 text-gray-800': selectedVehicle.status === 'inactive'
                                }"
                                class="px-2 py-1 rounded-full text-xs font-semibold"
                                x-text="formatStatus(selectedVehicle.status)"
                            ></span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">New Status</label>
                        <select x-model="statusForm.status" class="form-input">
                            <option value="pending">Pending</option>
                            <option value="active">Active</option>
                            <option value="rejected">Rejected</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>

                    <div x-show="statusForm.status === 'rejected'" class="form-group">
                        <label class="form-label">Rejection Reason</label>
                        <textarea
                            x-model="statusForm.rejection_reason"
                            class="form-input"
                            placeholder="Explain why this vehicle is being rejected..."
                            rows="3"
                        ></textarea>
                    </div>
                </div>

                <div class="flex justify-end space-x-2 mt-6">
                    <button @click="showStatusModal = false" class="btn-secondary">
                        Cancel
                    </button>
                    <button @click="updateVehicleStatus()" class="btn-primary">
                        <i class="fas fa-check btn-icon"></i>
                        Update Status
                    </button>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $attributes = $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $component = $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\carbnb\resources\views\backend\vehicles\index.blade.php ENDPATH**/ ?>
/* Custom fixes for specific elements */

/* Fix for button icons spacing */
.btn-icon {
    margin-right: 0.5rem;
}

/* Fix for form inputs in Alpine.js components */
[x-data] .form-input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db; /* gray-300 */
    border-radius: 0.375rem;
    background-color: white;
}

[x-data] .form-input:focus {
    outline: none;
    border-color: var(--primary-color, #ba1c1c);
    box-shadow: 0 0 0 3px rgba(186, 28, 28, 0.2);
}

/* Fix for card content padding */
.content-card {
    padding: 1.5rem;
}

/* Fix for table header alignment */
.data-table th {
    text-align: left;
    padding: 0.75rem 1rem;
}

/* Fix for pagination buttons */
.pagination-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db; /* gray-300 */
    background-color: white;
    color: #4b5563; /* gray-600 */
    font-size: 0.875rem;
    line-height: 1.25rem;
    font-weight: 500;
    border-radius: 0.375rem;
    transition: all 0.2s;
}

.pagination-button:hover {
    background-color: #f9fafb; /* gray-50 */
}

.pagination-button.active {
    background-color: #dbeafe; /* blue-100 */
    border-color: #3b82f6; /* blue-500 */
    color: #1d4ed8; /* blue-700 */
}

/* Fix for search input with icon */
.search-input-with-icon {
    position: relative;
}

.search-input-with-icon input {
    padding-left: 2.5rem;
}

.search-input-with-icon .icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af; /* gray-400 */
}

/* Fix for status badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
}

.status-badge.draft {
    background-color: #dbeafe; /* blue-100 */
    color: #1e40af; /* blue-800 */
}

.status-badge.pending {
    background-color: #fef3c7; /* amber-100 */
    color: #92400e; /* amber-800 */
}

.status-badge.active {
    background-color: #d1fae5; /* green-100 */
    color: #065f46; /* green-800 */
}

.status-badge.rejected {
    background-color: #fee2e2; /* red-100 */
    color: #991b1b; /* red-800 */
}

.status-badge.inactive {
    background-color: #f3f4f6; /* gray-100 */
    color: #4b5563; /* gray-600 */
}

/* Fix for modal styles */
.modal-backdrop {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 50;
}

.modal-content {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
    width: 100%;
    max-width: 32rem;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: 1.5rem;
    gap: 0.5rem;
}

/* Fix for action buttons container */
.action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

/* Fix for filter section */
.filter-section {
    margin-bottom: 1.5rem;
}

.filter-group {
    margin-bottom: 1rem;
}

.filter-label {
    display: block;
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #4b5563; /* gray-600 */
}

/* Fix for dashboard cards */
.dashboard-card {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
}

.dashboard-card-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.dashboard-card-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 9999px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
}

.dashboard-card-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280; /* gray-500 */
}

.dashboard-card-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937; /* gray-800 */
}

/* Fix for calendar container */
.calendar-container {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    padding: 1.5rem;
    margin-top: 1.5rem;
}

.calendar-header {
    margin-bottom: 1rem;
}

.calendar-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.legend-item {
    display: flex;
    align-items: center;
}

.legend-color {
    width: 1rem;
    height: 1rem;
    border-radius: 9999px;
    margin-right: 0.5rem;
}

.legend-label {
    font-size: 0.875rem;
    color: #4b5563; /* gray-600 */
}

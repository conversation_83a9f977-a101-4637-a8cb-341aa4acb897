<x-app-backend-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Upload Documents') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="mb-6">
                        <h3 class="text-lg font-medium text-gray-900">{{ __('Upload Required Documents') }}</h3>
                        <p class="mt-1 text-sm text-gray-600">
                            {{ __('Please upload the following required documents. All documents will be reviewed by our team.') }}
                        </p>
                    </div>

                    <form method="POST" action="{{ route('drivers.documents.store') }}" enctype="multipart/form-data" class="space-y-8">
                        @csrf

                        <div class="bg-gray-50 p-6 rounded-lg">
                            <div class="space-y-6">
                                @foreach($documentTypes as $documentType)
                                    <div>
                                        <h3 class="text-md font-medium text-gray-900 mb-1">{{ $documentType->name }}</h3>
                                        <p class="mb-3 text-sm text-gray-500">{{ $documentType->description }}</p>

                                        @php
                                            // Check if user already has this document type
                                            $existingDocument = Auth::user()->documents()
                                                ->where('document_type_id', $documentType->id)
                                                ->latest()
                                                ->first();

                                            $fileId = $existingDocument ? $existingDocument->file_id : '';
                                        @endphp

                                        @php
                                            $documentFieldName = "documents[{$documentType->id}][file_id]";
                                        @endphp

                                        <x-file-upload
                                            :name="$documentFieldName"
                                            label=""
                                            :multiple="false"
                                            accept="image/*,.pdf"
                                            :required="$documentType->is_required"
                                            :fileIds="$fileId"
                                        />

                                        @if($documentType->has_expiry)
                                            <div class="mt-4">
                                                <x-input-label for="expiry_{{ $documentType->id }}" :value="__('Expiry Date')" />
                                                <input type="date" id="expiry_{{ $documentType->id }}" name="documents[{{ $documentType->id }}][expiry_date]"
                                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                                    {{ $documentType->is_required ? 'required' : '' }}
                                                    value="{{ $existingDocument && $existingDocument->expiry_date ? $existingDocument->expiry_date->format('Y-m-d') : '' }}">
                                            </div>
                                        @endif

                                        <x-input-error :messages="$errors->get('documents.' . $documentType->id . '.file_id')" class="mt-2" />
                                        <x-input-error :messages="$errors->get('documents.' . $documentType->id . '.expiry_date')" class="mt-2" />
                                    </div>
                                @endforeach
                            </div>
                        </div>

                        <div class="flex justify-end">
                            <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                {{ __('Upload Documents') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Include File Management Modal -->
    @include('components.file-management.file-management-modal')
</x-app-backend-layout>

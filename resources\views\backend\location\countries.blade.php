<x-app-backend-layout>
    <div class="container bg-white p-5 mx-auto" x-data="countryApp()">
        <div class="flex justify-between mb-4">
            <h1 class="text-2xl">Countries</h1>
            <button @click="openCreateModal" class="bg-blue-500 text-white px-4 py-2 rounded">Create Country</button>
        </div>

        <table class="table-auto w-full mb-4">
            <thead>
            <tr>
                <th class="px-4 py-2">Name</th>
                <th class="px-4 py-2">Slug</th>
                <th class="px-4 py-2">Description</th>
                <th class="px-4 py-2">Actions</th>
            </tr>
            </thead>
            <tbody>
            @foreach($countries as $country)
                <tr>
                    <td class="border px-4 py-2">{{ $country->name }}</td>
                    <td class="border px-4 py-2">{{ $country->slug }}</td>
                    <td class="border px-4 py-2">{{ $country->description }}</td>
                    <td class="border px-4 py-2">
                        <button @click="openEditModal({{ $country }})" class="text-blue-500">Edit</button>
                        <button @click="deleteCountry({{ $country->id }})" class="text-red-500 ml-2">Delete</button>
                    </td>
                </tr>
            @endforeach
            </tbody>
        </table>

        <!-- Create/Edit Modal -->
        <div x-show="isModalOpen" class="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50" x-cloak>
            <div class="bg-white p-6 rounded-lg w-1/3 relative">
                <button @click="closeModal" class="absolute top-2 right-2 text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>

                <h2 class="text-xl mb-4" x-text="isEditMode ? 'Edit Country' : 'Create Country'"></h2>

                <form @submit.prevent="isEditMode ? updateCountry() : createCountry()">
                    <div class="mb-4">
                        <label for="name" class="block text-sm font-bold">Name</label>
                        <input type="text" name="name" id="name" x-model="form.name" class="mt-1 block w-full border border-gray-300 rounded p-2" required>
                    </div>

                    <div class="mb-4">
                        <label for="slug" class="block text-sm font-bold">Slug</label>
                        <input type="text" name="slug" id="slug" x-model="form.slug" class="mt-1 block w-full border border-gray-300 rounded p-2" required>
                    </div>

                    <div class="mb-4">
                        <label for="description" class="block text-sm font-bold">Description</label>
                        <textarea name="description" id="description" x-model="form.description" class="mt-1 block w-full border border-gray-300 rounded p-2" required></textarea>
                    </div>

                    <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded" x-text="isEditMode ? 'Update' : 'Create'"></button>
                    <button @click="closeModal" type="button" class="bg-gray-500 text-white px-4 py-2 rounded ml-2">Cancel</button>
                </form>
            </div>
        </div>
    </div>

    <script>
        function countryApp() {
            return {
                isModalOpen: false,
                isEditMode: false,
                form: {
                    id: null,
                    name: '',
                    slug: '',
                    description: ''
                },

                openCreateModal() {
                    this.isEditMode = false;
                    this.form = { id: null, name: '', slug: '', description: '' };
                    this.isModalOpen = true;
                },

                openEditModal(country) {
                    this.isEditMode = true;
                    this.form = { id: country.id, name: country.name, slug: country.slug, description: country.description };
                    this.isModalOpen = true;
                },

                closeModal() {
                    this.isModalOpen = false;
                },

                async createCountry() {
                    try {
                        const response = await fetch('{{ route('admin.countries.store') }}', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': '{{ csrf_token() }}'
                            },
                            body: JSON.stringify(this.form)
                        });

                        if (!response.ok) throw new Error('Error creating country');
                        window.location.reload();
                    } catch (error) {
                        Swal.fire('Error', error.message, 'error');
                    }
                },

                async updateCountry() {
                    try {
                        const response = await fetch(`{{ url('admin/countries') }}/${this.form.id}`, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': '{{ csrf_token() }}'
                            },
                            body: JSON.stringify(this.form)
                        });

                        if (!response.ok) throw new Error('Error updating country');
                        window.location.reload();
                    } catch (error) {
                        Swal.fire('Error', error.message, 'error');
                    }
                },

                async deleteCountry(id) {
                    Swal.fire({
                        title: 'Are you sure?',
                        text: "This action cannot be undone!",
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#3085d6',
                        cancelButtonColor: '#d33',
                        confirmButtonText: 'Yes, delete it!'
                    }).then(async (result) => {
                        if (result.isConfirmed) {
                            try {
                                const response = await fetch(`{{ url('admin/countries') }}/${id}`, {
                                    method: 'DELETE',
                                    headers: {
                                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                                    }
                                });

                                if (!response.ok) throw new Error('Error deleting country');
                                Swal.fire('Deleted!', 'Country has been deleted.', 'success');
                                window.location.reload();
                            } catch (error) {
                                Swal.fire('Error', error.message, 'error');
                            }
                        }
                    });
                }
            };
        }
    </script>
</x-app-backend-layout>

<?php if (isset($component)) { $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742 = $attributes; } ?>
<?php $component = App\View\Components\AppBackendLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-backend-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppBackendLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                <?php echo e(__('New Message')); ?>

            </h2>
            <a href="<?php echo e(route('messages.index')); ?>" class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-500 active:bg-gray-700 focus:outline-none focus:border-gray-700 focus:ring ring-gray-300 disabled:opacity-25 transition ease-in-out duration-150">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                <?php echo e(__('Back to Messages')); ?>

            </a>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <?php if(session('error')): ?>
                        <div class="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
                            <?php echo e(session('error')); ?>

                        </div>
                    <?php endif; ?>

                    <div class="mb-4 p-4 bg-blue-100 border border-blue-400 text-blue-700 rounded">
                        <p><i class="fas fa-info-circle mr-2"></i> You can only message admins, agents, or vehicle owners of cars you have currently booked.</p>
                    </div>

                    <form action="<?php echo e(route('messages.store')); ?>" method="POST">
                        <?php echo csrf_field(); ?>

                        <div class="space-y-6">
                            <!-- Recipient -->
                            <div>
                                <label for="recipient_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300"><?php echo e(__('Recipient')); ?></label>
                                <select id="recipient_id" name="recipient_id" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md" required <?php echo e(isset($recipient) ? 'disabled' : ''); ?>>
                                    <option value=""><?php echo e(__('Select a recipient')); ?></option>
                                    <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($user->id); ?>" <?php echo e(isset($recipient) && $recipient->id === $user->id ? 'selected' : ''); ?>>
                                            <?php echo e($user->first_name); ?> <?php echo e($user->last_name); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <?php if(isset($recipient)): ?>
                                    <input type="hidden" name="recipient_id" value="<?php echo e($recipient->id); ?>">
                                <?php endif; ?>
                                <?php $__errorArgs = ['recipient_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-2 text-sm text-red-600"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Related Booking (if any) -->
                            <?php if(isset($booking)): ?>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300"><?php echo e(__('Related Booking')); ?></label>
                                    <div class="mt-1 p-3 bg-gray-100 dark:bg-gray-700 rounded-md">
                                        <p class="text-sm">
                                            <span class="font-medium"><?php echo e(__('Booking')); ?> #<?php echo e($booking->booking_number); ?></span> -
                                            <?php echo e($booking->vehicle->make); ?> <?php echo e($booking->vehicle->model); ?>

                                        </p>
                                        <input type="hidden" name="booking_id" value="<?php echo e($booking->id); ?>">
                                    </div>
                                </div>
                            <?php endif; ?>

                            <!-- Message -->
                            <div>
                                <label for="message" class="block text-sm font-medium text-gray-700 dark:text-gray-300"><?php echo e(__('Message')); ?></label>
                                <textarea id="message" name="message" rows="6" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" placeholder="<?php echo e(__('Type your message here...')); ?>" required><?php echo e(old('message')); ?></textarea>
                                <?php $__errorArgs = ['message'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-2 text-sm text-red-600"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Submit Button -->
                            <div class="flex justify-end">
                                <button type="submit" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-500 active:bg-blue-700 focus:outline-none focus:border-blue-700 focus:ring ring-blue-300 disabled:opacity-25 transition ease-in-out duration-150">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                    </svg>
                                    <?php echo e(__('Send Message')); ?>

                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $attributes = $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $component = $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\carbnb\resources\views\messages\create.blade.php ENDPATH**/ ?>
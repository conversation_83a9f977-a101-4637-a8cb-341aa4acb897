<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'name' => 'file',
    'fieldName' => null,
    'label' => 'Upload File',
    'multiple' => false,
    'accept' => '*/*',
    'required' => false,
    'fileIds' => '',
    'primaryFileId' => '',
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'name' => 'file',
    'fieldName' => null,
    'label' => 'Upload File',
    'multiple' => false,
    'accept' => '*/*',
    'required' => false,
    'fileIds' => '',
    'primaryFileId' => '',
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div x-data="fileUploadSimple('<?php echo e($name); ?>', '<?php echo e($fileIds); ?>', <?php echo e($multiple ? 'true' : 'false'); ?>)">
    <!-- Label -->
    <label for="<?php echo e($name); ?>_input" class="block text-sm font-medium text-gray-700 mb-1"><?php echo e($label); ?></label>

    <!-- Hidden inputs to store file IDs -->
    <input
        type="hidden"
        name="<?php echo e($fieldName ? $fieldName : $name); ?>"
        x-ref="fileIdsInput"
        :value="fileIds"
        @change="updateFormData"
    >
    <?php if($multiple): ?>
        <input
            type="hidden"
            name="<?php echo e($fieldName ? $fieldName . '_primary' : $name . '_primary'); ?>"
            x-ref="primaryFileIdInput"
            :value="primaryFileId"
        >
    <?php endif; ?>

    <!-- File input -->
    <div class="flex flex-wrap gap-2 mb-2">
        <div class="flex-grow">
            <input
                type="file"
                id="<?php echo e($name); ?>_input"
                x-ref="fileInput"
                class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                <?php echo e($multiple ? 'multiple' : ''); ?>

                accept="<?php echo e($accept); ?>"
                <?php echo e($required ? 'required' : ''); ?>

                @change="handleFileSelect"
            >
        </div>
    </div>

    <!-- Preview of selected files -->
    <div class="mt-2 flex flex-wrap gap-2" x-show="files && files.length > 0">
        <template x-for="file in files" :key="file.id">
            <div class="relative border rounded-md p-2 bg-gray-50">
                <div class="text-xs truncate max-w-[150px]" x-text="file.name"></div>
                <button
                    type="button"
                    class="absolute top-0 right-0 -mt-2 -mr-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center"
                    @click="removeFile(file.id)"
                >
                    &times;
                </button>
            </div>
        </template>
    </div>

    <!-- Debug info -->
    <p class="mt-1 text-xs text-gray-500">Selected file ID: <span x-text="fileIds || 'None'"></span></p>
</div>

<script>
    function fileUploadSimple(name, initialFileIds, multiple) {
        return {
            name: name,
            fileIds: initialFileIds ? String(initialFileIds) : '',
            primaryFileId: '',
            files: [],

            init() {
                console.log(`Initializing file upload component: ${this.name}`, {
                    initialFileIds: this.fileIds,
                    multiple: multiple
                });

                // Initialize files array if fileIds is provided
                if (this.fileIds) {
                    this.loadFiles();
                }

                // Set up event listener for file manager integration if needed
                window.addEventListener('file-selected', (event) => {
                    if (event.detail.name === this.name) {
                        this.handleFileManagerSelection(event.detail);
                    }
                });

                // Set up event listener for our test script
                window.addEventListener('file-ids-updated', (event) => {
                    console.log(`Received file-ids-updated event for ${this.name}:`, event.detail);
                });
            },

            handleFileSelect(event) {
                const files = event.target.files;
                if (!files || files.length === 0) return;

                // Create FormData to upload files
                const formData = new FormData();
                for (let i = 0; i < files.length; i++) {
                    formData.append('files[]', files[i]);
                }

                // Add CSRF token
                const csrfToken = document.querySelector('meta[name="csrf-token"]');
                if (csrfToken) {
                    formData.append('_token', csrfToken.getAttribute('content'));
                }

                // Upload files
                fetch('/admin/file-manager', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': csrfToken ? csrfToken.getAttribute('content') : ''
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Handle successful upload
                        this.handleUploadSuccess(data.files);
                    } else {
                        console.error('Upload failed:', data.message || 'Unknown error');
                        alert('Upload failed: ' + (data.message || 'Unknown error'));
                    }
                })
                .catch(error => {
                    console.error('Error uploading files:', error);
                    alert('Error uploading files: ' + error.message);
                });

                // Reset file input
                this.$refs.fileInput.value = '';
            },

            handleUploadSuccess(uploadedFiles) {
                if (!uploadedFiles || uploadedFiles.length === 0) return;

                if (multiple === 'true') {
                    // For multiple files, append to existing list
                    const currentIds = this.fileIds ? String(this.fileIds).split(',').filter(id => id.trim() !== '') : [];
                    const newIds = uploadedFiles.map(file => file.id);

                    // Combine and remove duplicates
                    const combinedIds = [...new Set([...currentIds, ...newIds])];
                    this.fileIds = combinedIds.join(',');

                    // If no primary file is set, set the first one
                    if (!this.primaryFileId && combinedIds.length > 0) {
                        this.primaryFileId = combinedIds[0];
                    }
                } else {
                    // For single file, replace existing
                    this.fileIds = uploadedFiles[0].id;
                    this.primaryFileId = uploadedFiles[0].id;
                }

                // Update files array
                this.loadFiles();

                // Dispatch event for parent components
                this.updateFormData();
            },

            handleFileManagerSelection(detail) {
                if (!detail.files || detail.files.length === 0) return;

                if (multiple === 'true') {
                    // For multiple files, append to existing list
                    const currentIds = this.fileIds ? String(this.fileIds).split(',').filter(id => id.trim() !== '') : [];
                    const newIds = detail.files.map(file => file.id);

                    // Combine and remove duplicates
                    const combinedIds = [...new Set([...currentIds, ...newIds])];
                    this.fileIds = combinedIds.join(',');

                    // If no primary file is set, set the first one
                    if (!this.primaryFileId && combinedIds.length > 0) {
                        this.primaryFileId = combinedIds[0];
                    }
                } else {
                    // For single file, replace existing
                    this.fileIds = detail.files[0].id;
                    this.primaryFileId = detail.files[0].id;
                }

                // Update files array
                this.loadFiles();

                // Dispatch event for parent components
                this.updateFormData();
            },

            removeFile(fileId) {
                // Always convert fileIds to string before using split
                const fileIdsStr = String(this.fileIds || '');
                const ids = fileIdsStr.split(',').filter(id => id !== fileId.toString());
                this.fileIds = ids.join(',');

                // If removed file was primary, update primary
                if (this.primaryFileId === fileId.toString()) {
                    this.primaryFileId = ids.length > 0 ? ids[0] : '';
                }

                // Update files array
                this.files = this.files.filter(file => file.id.toString() !== fileId.toString());

                // Dispatch event for parent components
                this.updateFormData();
            },

            loadFiles() {
                if (!this.fileIds) {
                    this.files = [];
                    return;
                }

                // Ensure fileIds is a string before using split
                const fileIdsStr = String(this.fileIds);
                const ids = fileIdsStr.split(',').filter(id => id.trim() !== '');
                if (ids.length === 0) {
                    this.files = [];
                    return;
                }

                // Fetch file details from server
                fetch(`/admin/file-manager/${ids.join(',')}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            this.files = data.files;
                        } else {
                            console.error('Error loading files:', data.message || 'Unknown error');
                        }
                    })
                    .catch(error => {
                        console.error('Error loading files:', error);
                    });
            },

            updateFormData() {
                // Dispatch event for parent components
                window.dispatchEvent(new CustomEvent('file-ids-updated', {
                    detail: {
                        name: this.name,
                        value: String(this.fileIds || ''),
                        primaryFileId: String(this.primaryFileId || '')
                    }
                }));

                // Log the event for debugging
                console.log('Dispatched file-ids-updated event:', {
                    name: this.name,
                    value: String(this.fileIds || ''),
                    primaryFileId: String(this.primaryFileId || '')
                });
            }
        };
    }
</script>
<?php /**PATH C:\laragon\www\carbnb\resources\views\components\file-upload-enhanced.blade.php ENDPATH**/ ?>
import defaultTheme from 'tailwindcss/defaultTheme';
import forms from '@tailwindcss/forms';
/** @type {import('tailwindcss').Config} */
export default {
    content: [
        './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
        './storage/framework/views/*.php',
        './resources/views/**/*.blade.php',
    ],
    theme: {
        extend: {
            colors: {
                primary: {
                    DEFAULT: 'var(--primary-color, #ba1c1c)',
                    hover: 'var(--primary-color-hover, #a01818)',
                    disabled: 'var(--primary-color-disabled, #d47070)',
                },
                secondary: {
                    DEFAULT: 'var(--secondary-color, #ba1c1c)',
                },
                accent: {
                    DEFAULT: 'var(--accent-color, #ba1c1c)',
                },
                content: {
                    DEFAULT: 'var(--text-color, #ba1c1c)',
                    hover: 'var(--text-color-hover, #a01818)',
                    light: 'var(--text-color-light, #e9a8a8)',
                },
                surface: {
                    DEFAULT: 'var(--background-color, #ffffff)',
                    darker: 'var(--background-color-darker, #f5f5f5)',
                },
            },
            container: {
                center: true,
                padding: {
                    DEFAULT: '1rem',
                    sm: '1rem',
                    lg: '2rem',
                    xl: '3rem',
                    '2xl': '4rem'
                },
                screens: {
                    sm: '640px',
                    md: '768px',
                    lg: '1024px',
                    xl: '1280px',
                    '2xl': '1536px'
                }
            },
            fontFamily: {
                sans: ['Figtree', ...defaultTheme.fontFamily.sans],
            },
        },
    },
    plugins: [forms],
};
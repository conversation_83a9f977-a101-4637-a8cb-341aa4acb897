<?php

namespace Database\Seeders;

use App\Models\VehicleType;
use Illuminate\Database\Seeder;

class VehicleTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $vehicleTypes = [
            [
                'name' => 'Sedan',
                'slug' => 'sedan',
                'description' => 'A standard sedan with 4 doors',
                'is_active' => true,
            ],
            [
                'name' => 'SUV',
                'slug' => 'suv',
                'description' => 'Sport Utility Vehicle with higher ground clearance',
                'is_active' => true,
            ],
            [
                'name' => 'Hatchback',
                'slug' => 'hatchback',
                'description' => 'Compact car with a rear door that opens upwards',
                'is_active' => true,
            ],
            [
                'name' => 'Convertible',
                'slug' => 'convertible',
                'description' => 'Vehicle with a removable or retractable roof',
                'is_active' => true,
            ],
            [
                'name' => 'Luxury',
                'slug' => 'luxury',
                'description' => 'High-end vehicles with premium features',
                'is_active' => true,
            ],
        ];

        foreach ($vehicleTypes as $type) {
            VehicleType::firstOrCreate(
                ['slug' => $type['slug']],
                $type
            );
        }
    }
}

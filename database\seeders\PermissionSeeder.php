<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use <PERSON><PERSON>\Permission\Models\Role;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create permissions
        $permissions = [
            // Dashboard permissions
            'dashboard',
            'revenue-dashboard',
            
            // Vehicle permissions
            'vehicles',
            'vehicles.create',
            'vehicles.edit',
            'vehicles.delete',
            
            // User management permissions
            'users.view',
            'users.create',
            'users.edit',
            'users.delete',
            
            // Role & Permission management
            'roles.view',
            'roles.create',
            'roles.edit',
            'roles.delete',
            'permissions.view',
            'permissions.create',
            'permissions.edit',
            'permissions.delete',
            
            // File manager
            'file-manager',
            
            // Pages
            'pages',
            
            // Settings
            'settings',
            'locations.view'
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Assign permissions to roles
        $adminRole = Role::where('name', 'admin')->first();
        $agentRole = Role::where('name', 'agent')->first();
        $userRole = Role::where('name', 'user')->first();

        // Admin gets all permissions
        $adminRole->givePermissionTo(Permission::all());

        // Agent permissions
        $agentRole->givePermissionTo([
            'dashboard',
            'revenue-dashboard',
            'vehicles',
            'vehicles.create',
            'vehicles.edit',
            'file-manager',
            'locations.view',
        ]);

        // User permissions
        $userRole->givePermissionTo([
            'dashboard',
            'vehicles',
            'vehicles.create',
            'vehicles.edit',
            'file-manager',
        ]);
    }
}
<?php if (isset($component)) { $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742 = $attributes; } ?>
<?php $component = App\View\Components\AppBackendLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-backend-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppBackendLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div x-data="countryCrud()" class="p-6">
        <div class="mb-4">
            <div class="flex justify-between items-center mb-3">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4">Country Management</h2>
                <button @click="openModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg shadow">Add Country</button>
            </div>

            <div class="overflow-x-auto">
                <table class="w-full table-auto border-collapse bg-white shadow-md rounded-lg">
                    <thead>
                        <tr class="bg-gray-200 text-gray-600 uppercase text-sm leading-normal">
                            <th class="py-3 px-6 text-left">#</th>
                            <th class="py-3 px-6 text-left">Name</th>
                            <th class="py-3 px-6 text-left">Code</th>
                            <th class="py-3 px-6 text-left">Zone ID</th>
                            <th class="py-3 px-6 text-left">Status</th>
                            <th class="py-3 px-6 text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <template x-for="(country, index) in countries" :key="country.id">
                            <tr class="border-b border-gray-200 hover:bg-gray-100">
                                <td class="py-3 px-6 text-left" x-text="index + 1"></td>
                                <td class="py-3 px-6 text-left" x-text="country.name"></td>
                                <td class="py-3 px-6 text-left" x-text="country.code"></td>
                                <td class="py-3 px-6 text-left" x-text="country.zone_id"></td>
                                <td class="py-3 px-6 text-left">
                                    <span x-text="country.status == 1 ? 'Active' : 'Inactive'"></span>
                                </td>
                                <td class="py-3 px-6 text-center">
                                    <button @click="openModal(country)" class="bg-yellow-500 hover:bg-yellow-600 text-white px-3 py-1 rounded-lg shadow">Edit</button>
                                    <button @click="deleteCountry(country.id)" class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded-lg shadow ml-2">Delete</button>
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Modal -->
        <div x-show="showModal" class="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50" style="z-index: 100">
            <div class="bg-white p-6 rounded-lg shadow-lg w-96">
                <h3 class="text-lg font-semibold text-gray-700 mb-4" x-text="form.id ? 'Edit Country' : 'Add Country'"></h3>
                <input type="text" placeholder="Name" x-model="form.name" class="border p-2 w-full mb-2 rounded-lg shadow-sm">
                <input type="text" placeholder="Code" x-model="form.code" class="border p-2 w-full mb-2 rounded-lg shadow-sm">
                <input type="text" placeholder="Zone ID" x-model="form.zone_id" class="border p-2 w-full mb-2 rounded-lg shadow-sm">
                <select x-model="form.status" class="border p-2 w-full mb-2 rounded-lg shadow-sm">
                    <option value="">Select Status</option>
                    <option value="1">Active</option>
                    <option value="0">Inactive</option>
                </select>

                <div class="flex justify-end space-x-2">
                    <button @click="showModal = false" class="bg-gray-400 hover:bg-gray-500 text-white px-4 py-2 rounded-lg shadow">Cancel</button>
                    <button @click="saveCountry()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg shadow">
                        <span x-text="form.id ? 'Update' : 'Save'"></span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function countryCrud() {
            return {
                countries: <?php echo json_encode($countries, 15, 512) ?>,
                showModal: false,
                form: { id: null, name: '', code: '', zone_id: '', status: '' },

                openModal(country = null) {
                    this.form = country ? { ...country } : { id: null, name: '', code: '', zone_id: '', status: '' };
                    this.showModal = true;
                },

                saveCountry() {
                    let url = this.form.id ? `/countries/${this.form.id}` : '/countries';
                    let method = this.form.id ? 'PUT' : 'POST';

                    fetch(url, {
                        method: method,
                        headers: { 
                            'Content-Type': 'application/json', 
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify(this.form),
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.country) {
                            if (!this.form.id) {
                                this.countries.push(data.country);
                                Swal.fire('Success!', 'Country created successfully', 'success');
                            } else {
                                let index = this.countries.findIndex(c => c.id === this.form.id);
                                if (index !== -1) {
                                    this.countries[index] = data.country;
                                    Swal.fire('Success!', 'Country updated successfully', 'success');
                                }
                            }
                            this.showModal = false;
                        } else if (data.message) {
                            Swal.fire('Success!', data.message, 'success');
                            this.showModal = false;
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        Swal.fire('Error!', 'An error occurred. Please try again.', 'error');
                    });
                },

                deleteCountry(id) {
                    Swal.fire({
                        title: 'Are you sure?',
                        text: "You won't be able to revert this!",
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#d33',
                        cancelButtonColor: '#3085d6',
                        confirmButtonText: 'Yes, delete it!'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            fetch(`/countries/${id}`, {
                                method: 'DELETE',
                                headers: { 
                                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                                    'Accept': 'application/json'
                                }
                            })
                            .then(response => response.json())
                            .then(data => {
                                if (data.success !== false) {
                                    this.countries = this.countries.filter(c => c.id !== id);
                                    Swal.fire('Deleted!', data.message || 'Country has been deleted.', 'success');
                                } else {
                                    Swal.fire('Error!', data.message || 'Failed to delete country', 'error');
                                }
                            })
                            .catch(error => {
                                console.error('Error:', error);
                                Swal.fire('Error!', 'An error occurred. Please try again.', 'error');
                            });
                        }
                    });
                }
            };
        }
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $attributes = $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $component = $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?><?php /**PATH C:\laragon\www\carbnb\resources\views\backend\country\index.blade.php ENDPATH**/ ?>
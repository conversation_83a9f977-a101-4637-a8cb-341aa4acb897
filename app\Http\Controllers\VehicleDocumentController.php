<?php

namespace App\Http\Controllers;

use App\Models\Vehicle;
use App\Models\VehicleDocument;
use App\Models\DocumentType;
use App\Models\File;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class VehicleDocumentController extends Controller
{
    /**
     * Process vehicle documents from the request.
     *
     * @param Vehicle $vehicle
     * @param Request $request
     * @return void
     */
    public static function processVehicleDocuments(Vehicle $vehicle, Request $request)
    {
        if (!$request->has('documents') || !is_array($request->documents)) {
            return;
        }

        DB::beginTransaction();

        try {
            foreach ($request->documents as $documentTypeId => $documentData) {
                if (empty($documentData['file_id'])) {
                    continue; // Skip if no file was selected
                }

                $documentType = DocumentType::findOrFail($documentTypeId);
                $fileId = $documentData['file_id'];

                // Find the file record
                $fileModel = File::findOrFail($fileId);

                // Check if a document of this type already exists for this vehicle
                $existingDocument = VehicleDocument::where([
                    'vehicle_id' => $vehicle->id,
                    'document_type_id' => $documentTypeId,
                ])->first();

                if ($existingDocument) {
                    // Update existing document
                    $existingDocument->update([
                        'file_id' => $fileId,
                        'file_name' => $fileModel->title,
                        'file_path' => str_replace('storage/', '', $fileModel->url),
                        'expiry_date' => $documentType->has_expiry && isset($documentData['expiry_date']) ? $documentData['expiry_date'] : null,
                        'status' => 'pending', // Reset status to pending when document is updated
                        'rejection_reason' => null, // Clear any previous rejection reason
                    ]);
                } else {
                    // Create new document record
                    VehicleDocument::create([
                        'vehicle_id' => $vehicle->id,
                        'document_type_id' => $documentTypeId,
                        'file_id' => $fileId,
                        'file_name' => $fileModel->title,
                        'file_path' => str_replace('storage/', '', $fileModel->url),
                        'expiry_date' => $documentType->has_expiry && isset($documentData['expiry_date']) ? $documentData['expiry_date'] : null,
                        'status' => 'pending',
                    ]);
                }
            }

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Validate that all required documents are uploaded for a vehicle.
     *
     * @param Vehicle $vehicle
     * @return bool
     */
    public static function validateRequiredDocuments(Vehicle $vehicle)
    {
        // Get all required document types for vehicles
        $requiredDocumentTypes = DocumentType::where(function($query) {
            $query->where('applies_to', 'vehicle')
                  ->orWhere('applies_to', 'all');
        })
        ->where('is_required', true)
        ->get();

        // Get all documents for this vehicle
        $vehicleDocuments = $vehicle->documents;

        // Check if all required document types have a corresponding document
        foreach ($requiredDocumentTypes as $documentType) {
            $hasDocument = $vehicleDocuments->contains('document_type_id', $documentType->id);
            if (!$hasDocument) {
                return false;
            }
        }

        return true;
    }

    /**
     * Approve a vehicle document.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function approve(Request $request, $id)
    {
        // Only admins and agents can approve documents
        if (!auth()->user()->hasRole(['admin', 'agent'])) {
            abort(403, 'You are not authorized to approve vehicle documents');
        }

        $document = VehicleDocument::findOrFail($id);
        $document->update([
            'status' => 'approved',
            'rejection_reason' => null,
        ]);

        return redirect()->back()
            ->with('success', 'Document approved successfully');
    }

    /**
     * Reject a vehicle document.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function reject(Request $request, $id)
    {
        // Only admins and agents can reject documents
        if (!auth()->user()->hasRole(['admin', 'agent'])) {
            abort(403, 'You are not authorized to reject vehicle documents');
        }

        $request->validate([
            'rejection_reason' => 'required|string|max:255',
        ]);

        $document = VehicleDocument::findOrFail($id);
        $document->update([
            'status' => 'rejected',
            'rejection_reason' => $request->rejection_reason,
        ]);

        return redirect()->back()
            ->with('success', 'Document rejected successfully');
    }
}

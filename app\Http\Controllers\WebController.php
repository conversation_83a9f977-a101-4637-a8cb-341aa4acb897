<?php
// app\Http\Controllers\WebController.php

namespace App\Http\Controllers;

use App\Models\Page;
use App\Models\City;
use App\Models\User;
use App\Models\Vehicle;
use App\Models\VehicleType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Str;

class WebController extends Controller
{
    /**
     * Get the view path for the current theme
     *
     * @param string $view
     * @return string
     */
    protected function getThemeView($view)
    {
        $theme = get_theme();
        $themeView = "front.{$theme}.{$view}";

        return $themeView;
    }

    /**
     * Display the home page with dynamic data
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Get featured vehicles for the carousel
        $featuredVehicles = Vehicle::with(['vehicleType', 'city', 'primaryImage'])
            ->where('status', 'active')
            ->where('is_featured', true)
            ->take(3)
            ->get();

        // Get vehicle types for the browse by car type section
        $vehicleTypes = VehicleType::where('is_active', true)
            ->orderBy('name')
            ->take(4)
            ->get();

        // Get cities for the location dropdown
        $cities = City::where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('front.default.index', compact('featuredVehicles', 'vehicleTypes', 'cities'));
    }

    /**
     * Display the list car page with dynamic data
     *
     * @return \Illuminate\View\View
     */
    public function listCar()
    {
        // Get cities for the location dropdown
        $cities = City::where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('front.default.list-your-car', compact('cities'));
    }

    /**
     * Display the quick car registration form
     *
     * @return \Illuminate\View\View
     */
    public function quickCarRegistration()
    {
        // Get vehicle types for the dropdown
        $vehicleTypes = VehicleType::where('is_active', true)
            ->orderBy('name')
            ->get();

        // Get cities for the location dropdown
        $cities = City::where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('front.default.quick-car-registration', compact('vehicleTypes', 'cities'));
    }

    /**
     * Process the quick car registration
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function storeQuickCarRegistration(Request $request)
    {
        // Validate the basic information
        $validated = $request->validate([
            // User information
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',

            // Car information
            'vehicle_type_id' => 'required|exists:vehicle_types,id',
            'make' => 'required|string|max:255',
            'model' => 'required|string|max:255',
            'year' => 'required|integer|min:1900|max:'.(date('Y')+1),
            'city_id' => 'required|exists:cities,id',
        ]);

        // Check if user already exists
        $user = User::where('email', $validated['email'])->first();

        // If user doesn't exist, create a new one
        if (!$user) {
            // Generate a random password
            $password = Str::random(10);

            $user = User::create([
                'first_name' => $validated['first_name'],
                'last_name' => $validated['last_name'],
                'email' => $validated['email'],
                'phone' => $validated['phone'],
                'password' => bcrypt($password),
            ]);

            // Assign the 'user' role
            $user->assignRole('user');

            // Generate a referral code for the new user
            $user->generateReferralCode();

            // Send welcome email with password
            // Mail::to($user->email)->send(new WelcomeEmail($user, $password));
        }

        // Create a draft vehicle
        $vehicle = Vehicle::create([
            'user_id' => $user->id,
            'vehicle_type_id' => $validated['vehicle_type_id'],
            'make' => $validated['make'],
            'model' => $validated['model'],
            'year' => $validated['year'],
            'city_id' => $validated['city_id'],
            'status' => 'draft',
            // Set default values for required fields
            'license_plate' => 'TBD-' . rand(1000, 9999),
            'color' => 'Not specified',
            'mileage' => 0,
            'transmission' => 'automatic',
            'fuel_type' => 'gasoline',
            'seats' => '5',
            'doors' => 4,
            'daily_rate' => 0,
            'availability' => 'everyday',
            'advance_notice' => '24_hours',
        ]);

        // Login the user
        Auth::login($user);

        // Redirect to the vehicle edit page to complete the listing
        return redirect()->route('vehicles.edit', $vehicle->id)
            ->with('success', 'Your car has been registered! Please complete your listing with additional details.');
    }

    /**
     * Display the quick driver registration form
     *
     * @return \Illuminate\View\View
     */
    public function quickDriverRegistration()
    {
        // Get cities for the location dropdown
        $cities = City::where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('front.default.quick-driver-registration', compact('cities'));
    }

    /**
     * Process the quick driver registration
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function storeQuickDriverRegistration(Request $request)
    {
        // Validate the basic information
        $validated = $request->validate([
            // User information
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',

            // Driver information
            'license_number' => 'required|string|max:255',
            'license_expiry' => 'required|date|after:today',
            'experience_years' => 'required|string',
            'city_id' => 'required|exists:cities,id',
        ]);

        // Check if user already exists
        $user = User::where('email', $validated['email'])->first();

        // If user doesn't exist, create a new one
        if (!$user) {
            // Generate a random password
            $password = Str::random(10);

            $user = User::create([
                'first_name' => $validated['first_name'],
                'last_name' => $validated['last_name'],
                'email' => $validated['email'],
                'phone' => $validated['phone'],
                'password' => bcrypt($password),
            ]);

            // Assign the 'user' role
            $user->assignRole('user');

            // Generate a referral code for the new user
            $user->generateReferralCode();

            // Send welcome email with password
            // Mail::to($user->email)->send(new WelcomeEmail($user, $password));
        }

        // Create a draft driver profile
        $driver = Driver::create([
            'user_id' => $user->id,
            'license_number' => $validated['license_number'],
            'license_expiry' => $validated['license_expiry'],
            'experience_years' => $validated['experience_years'],
            'city_id' => $validated['city_id'],
            'status' => 'pending',
            // Set default values for required fields
            'daily_rate' => 50, // Default daily rate
            'languages' => json_encode(['English']), // Default language
            'available_weekdays' => true,
            'available_weekends' => true,
        ]);

        // Create a driver license record
        $driver->license()->create([
            'license_number' => $validated['license_number'],
            'issuing_country' => 'Default Country',
            'issue_date' => now(),
            'expiry_date' => $validated['license_expiry'],
            'license_class' => 'Standard',
            'status' => 'pending'
        ]);

        // Assign driver role to user
        $driverRole = Role::where('name', 'driver')->first();
        if ($driverRole && !$user->hasRole('driver')) {
            $user->assignRole($driverRole);
        }

        // Login the user
        Auth::login($user);

        // Redirect to the driver profile page
        return redirect()->route('drivers.profile')
            ->with('success', 'Your driver application has been submitted! Our team will contact you to complete your profile.');
    }

    /**
     * Display the cars listing page with filters
     *
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function carsListing(Request $request)
    {
        $query = Vehicle::with(['vehicleType', 'city', 'primaryImage', 'user'])
            ->where('status', 'active');

        // Filter by vehicle type
        if ($request->has('vehicle_type') && !empty($request->vehicle_type)) {
            $query->where('vehicle_type_id', $request->vehicle_type);
        }

        // Filter by city
        if ($request->has('city_id') && !empty($request->city_id)) {
            $query->where('city_id', $request->city_id);
        }

        // Filter by price range
        if ($request->has('min_price') && $request->has('max_price')) {
            $query->priceRange($request->min_price, $request->max_price);
        }

        // Filter by transmission
        if ($request->has('transmission') && !empty($request->transmission)) {
            $query->transmissionType($request->transmission);
        }

        // Filter by fuel type
        if ($request->has('fuel_type') && !empty($request->fuel_type)) {
            $query->fuelType($request->fuel_type);
        }

        // Filter by minimum seats
        if ($request->has('seats') && !empty($request->seats)) {
            $query->minSeats($request->seats);
        }

        // Filter by with driver option
        if ($request->has('with_driver') && $request->with_driver == 1) {
            $query->withDriver();
        }

        // Sort functionality
        if ($request->has('sort_by') && !empty($request->sort_by)) {
            $sortParams = explode(':', $request->sort_by);
            if (count($sortParams) == 2) {
                $column = $sortParams[0];
                $direction = $sortParams[1];

                // Only allow specific columns to be sorted
                $allowedColumns = ['daily_rate', 'created_at', 'year'];
                if (in_array($column, $allowedColumns)) {
                    $query->orderBy($column, $direction);
                }
            }
        } else {
            // Default sorting - newest first
            $query->orderBy('created_at', 'desc');
        }

        // Get vehicles with pagination
        $vehicles = $query->paginate(9);

        // Make sure pagination links include all current query parameters
        $vehicles->appends($request->except('page'));

        // Get all vehicle types for the filter sidebar
        $vehicleTypes = VehicleType::where('is_active', true)->orderBy('name')->get();

        // Get cities for the filter
        $cities = City::where('is_active', true)->orderBy('name')->get();

        return view('front.default.cars-listing', compact('vehicles', 'vehicleTypes', 'cities'));
    }

    /**
     * Display the vehicle details page
     *
     * @param int $id
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function vehicleDetails($id, Request $request)
    {
        $vehicle = Vehicle::with(['vehicleType', 'city', 'images', 'user'])
            ->where('status', 'active')
            ->findOrFail($id);

        // Get similar vehicles (same type, same city)
        $similarVehicles = Vehicle::with(['vehicleType', 'primaryImage'])
            ->where('status', 'active')
            ->where('id', '!=', $vehicle->id)
            ->where(function($query) use ($vehicle) {
                $query->where('vehicle_type_id', $vehicle->vehicle_type_id)
                    ->orWhere('city_id', $vehicle->city_id);
            })
            ->take(3)
            ->get();

        // Check if booking dates are provided
        if ($request->has('pickup_date') && $request->has('return_date')) {
            // Redirect to booking creation page
            return redirect()->route('booking.create', [
                'vehicleId' => $vehicle->id,
                'pickup_date' => $request->pickup_date,
                'return_date' => $request->return_date
            ]);
        }

        return view('front.default.vehicle-details', compact('vehicle', 'similarVehicles'));
    }

    /**
     * Change the application locale
     *
     * @param string $locale
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changeLocale($locale)
    {
        // Check if the locale is valid
        $availableLocales = ['en', 'pt']; // English and Portuguese

        if (in_array($locale, $availableLocales)) {
            session(['locale' => $locale]);
            App::setLocale($locale);

            // Check if the frontend translations exist for this locale
            if (Lang::has('frontend.home', $locale)) {
                // Log successful language change
                \Log::info('Language changed to: ' . $locale);
            } else {
                // Log missing translations
                \Log::warning('Missing translations for locale: ' . $locale);
            }
        } else {
            // Log invalid locale attempt
            \Log::warning('Invalid locale attempted: ' . $locale);
        }

        return redirect()->back();
    }
    /**
     * Display a page by its slug
     *
     * @param string $slug
     * @return \Illuminate\View\View
     */
    public function viewPage(string $slug)
    {
        try {
            // Find the page by slug
            $page = Page::where('slug', $slug)
                ->where('status', 'published')
                ->firstOrFail();

            // Return the page view with the page data
            return view('front.default.page', compact('page'));
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            // If page not found, return the 404 view
            return response()->view('front.default.404', [], 404);
        }
    }
}

<?php

use App\Models\User;
use App\Models\Vehicle;
use App\Models\VehicleType;
use App\Models\City;
use App\Models\Country;
use App\Models\State;
use App\Models\File;
use App\Models\VehicleImage;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Seed the database with required data
    $this->seed(\Database\Seeders\RoleSeeder::class);
    $this->seed(\Database\Seeders\PermissionSeeder::class);
    $this->seed(\Database\Seeders\LocationSeeder::class);
    $this->seed(\Database\Seeders\VehicleTypeSeeder::class);
    $this->seed(\Database\Seeders\DocumentTypeSeeder::class);

    // Create admin user
    $this->admin = User::create([
        'first_name' => 'Admin',
        'last_name' => 'User',
        'email' => '<EMAIL>',
        'password' => bcrypt('password'),
        'email_verified_at' => now(),
    ]);
    $this->admin->assignRole('admin');

    // Get references to seeded data
    $this->country = Country::where('code', 'US')->first();
    $this->state = State::where('name', 'New York')->first();
    $this->city = City::where('name', 'New York City')->first();
    $this->vehicleType = VehicleType::where('slug', 'sedan')->first();
});

test('vehicle creation requires selecting vehicle type, country, state, and city', function () {
    // Login as admin
    $this->actingAs($this->admin);

    // Step 1: Access the vehicle creation page
    $response = $this->get('/management/vehicles/create');
    $response->assertStatus(200);

    // Step 2: Submit the form with all required fields
    Storage::fake('public');

    // Create file records in the database
    $primaryImageFile = File::create([
        'title' => 'flow_car.jpg',
        'filename' => 'flow_car.jpg',
        'url' => '/storage/uploads/flow_car.jpg',
        'fileSize' => 1000,
        'fileType' => 'image/jpeg'
    ]);

    $ownershipDocFile = File::create([
        'title' => 'flow_ownership.pdf',
        'filename' => 'flow_ownership.pdf',
        'url' => '/storage/uploads/flow_ownership.pdf',
        'fileSize' => 2000,
        'fileType' => 'application/pdf'
    ]);

    $insuranceDocFile = File::create([
        'title' => 'flow_insurance.pdf',
        'filename' => 'flow_insurance.pdf',
        'url' => '/storage/uploads/flow_insurance.pdf',
        'fileSize' => 2000,
        'fileType' => 'application/pdf'
    ]);

    // Get document type IDs
    $ownershipDocTypeId = 2; // Vehicle Registration
    $insuranceDocTypeId = 3; // Vehicle Insurance

    $vehicleData = [
        'vehicle_type_id' => $this->vehicleType->id,
        'make' => 'Toyota',
        'model' => 'Camry',
        'year' => 2022,
        'license_plate' => 'TEST123',
        'color' => 'Blue',
        'mileage' => 15000,
        'transmission' => 'automatic',
        'fuel_type' => 'gasoline',
        'seats' => '5',
        'doors' => 4,
        'features' => ['bluetooth', 'air_conditioning'],
        'description' => 'A comfortable sedan for your journey',
        'daily_rate' => 50.00,
        'city_id' => $this->city->id,
        'primary_image' => $primaryImageFile->id,
        'documents' => [
            $ownershipDocTypeId => [
                'file_id' => $ownershipDocFile->id,
            ],
            $insuranceDocTypeId => [
                'file_id' => $insuranceDocFile->id,
                'expiry_date' => now()->addYear()->format('Y-m-d'),
            ],
        ],
    ];

    $response = $this->post('/management/vehicles', $vehicleData);

    // Check if redirected to vehicles index with success message
    $response->assertRedirect(route('vehicles.index'));
    $response->assertSessionHas('success');

    // Verify the vehicle was created with the correct data
    $vehicle = Vehicle::where('license_plate', 'TEST123')->first();
    $this->assertNotNull($vehicle);
    $this->assertEquals($this->vehicleType->id, $vehicle->vehicle_type_id);
    $this->assertEquals($this->city->id, $vehicle->city_id);
    $this->assertEquals('Toyota', $vehicle->make);
    $this->assertEquals('Camry', $vehicle->model);
    $this->assertEquals(2022, $vehicle->year);
    $this->assertEquals('pending', $vehicle->status);

    // Verify the primary image was associated with the vehicle
    $vehicleImage = VehicleImage::where('vehicle_id', $vehicle->id)->first();
    $this->assertNotNull($vehicleImage);
    $this->assertEquals($primaryImageFile->id, $vehicleImage->file_id);
    $this->assertTrue($vehicleImage->is_primary);
});

test('vehicle creation fails if vehicle type is not selected', function () {
    $this->actingAs($this->admin);

    // Create file record for primary image
    $primaryImageFile = File::create([
        'title' => 'no_type_car.jpg',
        'filename' => 'no_type_car.jpg',
        'url' => '/storage/uploads/no_type_car.jpg',
        'fileSize' => 1000,
        'fileType' => 'image/jpeg'
    ]);

    $vehicleData = [
        // Missing vehicle_type_id
        'make' => 'Toyota',
        'model' => 'Camry',
        'year' => 2022,
        'license_plate' => 'TEST123',
        'color' => 'Blue',
        'mileage' => 15000,
        'transmission' => 'automatic',
        'fuel_type' => 'gasoline',
        'seats' => '5',
        'doors' => 4,
        'city_id' => $this->city->id,
        'daily_rate' => 50.00,
        'primary_image' => $primaryImageFile->id,
    ];

    $response = $this->post('/management/vehicles', $vehicleData);

    // Should return with validation errors
    $response->assertSessionHasErrors(['vehicle_type_id']);

    // No vehicle should be created
    $this->assertEquals(0, Vehicle::count());
});

test('vehicle creation fails if city is not selected', function () {
    $this->actingAs($this->admin);

    // Create file record for primary image
    $primaryImageFile = File::create([
        'title' => 'no_city_car.jpg',
        'filename' => 'no_city_car.jpg',
        'url' => '/storage/uploads/no_city_car.jpg',
        'fileSize' => 1000,
        'fileType' => 'image/jpeg'
    ]);

    $vehicleData = [
        'vehicle_type_id' => $this->vehicleType->id,
        'make' => 'Toyota',
        'model' => 'Camry',
        'year' => 2022,
        'license_plate' => 'TEST123',
        'color' => 'Blue',
        'mileage' => 15000,
        'transmission' => 'automatic',
        'fuel_type' => 'gasoline',
        'seats' => '5',
        'doors' => 4,
        // Missing city_id
        'daily_rate' => 50.00,
        'primary_image' => $primaryImageFile->id,
    ];

    $response = $this->post('/management/vehicles', $vehicleData);

    // Should return with validation errors
    $response->assertSessionHasErrors(['city_id']);

    // No vehicle should be created
    $this->assertEquals(0, Vehicle::count());
});

test('vehicle creation fails if primary image is not provided', function () {
    $this->actingAs($this->admin);

    $vehicleData = [
        'vehicle_type_id' => $this->vehicleType->id,
        'make' => 'Toyota',
        'model' => 'Camry',
        'year' => 2022,
        'license_plate' => 'TEST123',
        'color' => 'Blue',
        'mileage' => 15000,
        'transmission' => 'automatic',
        'fuel_type' => 'gasoline',
        'seats' => '5',
        'doors' => 4,
        'city_id' => $this->city->id,
        'daily_rate' => 50.00,
        // Missing primary_image
    ];

    $response = $this->post('/management/vehicles', $vehicleData);

    // Should return with validation errors
    $response->assertSessionHasErrors(['primary_image']);

    // No vehicle should be created
    $this->assertEquals(0, Vehicle::count());
});

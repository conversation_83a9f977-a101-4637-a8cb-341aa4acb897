/**
 * Vehicle Document Fix
 * 
 * This script fixes the issue with document type IDs in the vehicle form.
 * It dynamically retrieves the correct document type IDs from the DOM and
 * updates the validation logic accordingly.
 */
document.addEventListener('DOMContentLoaded', function() {
    // Wait for Alpine.js to initialize
    setTimeout(function() {
        // Find the vehicle form Alpine component
        const vehicleFormElement = document.querySelector('[x-data="vehicleForm"]');
        if (!vehicleFormElement || !vehicleFormElement.__x) {
            console.log('Vehicle form not found or Alpine not initialized yet');
            return;
        }

        const vehicleForm = vehicleFormElement.__x.$data;
        
        // Get all document type elements from the DOM
        const documentTypeElements = document.querySelectorAll('[name^="documents["][name$="][file_id]"]');
        
        // Extract document type IDs
        const documentTypeIds = Array.from(documentTypeElements).map(element => {
            const matches = element.name.match(/documents\[(\d+)\]/);
            return matches && matches[1] ? parseInt(matches[1]) : null;
        }).filter(id => id !== null);
        
        console.log('Found document type IDs:', documentTypeIds);
        
        // Get document type names from the DOM
        const documentTypeNames = {};
        documentTypeIds.forEach(id => {
            const labelElement = document.querySelector(`label[for="documents_${id}_expiry_date"]`);
            if (labelElement) {
                const parentElement = labelElement.closest('.bg-white');
                if (parentElement) {
                    const nameElement = parentElement.querySelector('h4');
                    if (nameElement) {
                        documentTypeNames[id] = nameElement.textContent.trim();
                    }
                }
            }
        });
        
        console.log('Document type names:', documentTypeNames);
        
        // Override the saveVehicle method to use the correct document type IDs
        const originalSaveVehicle = vehicleForm.saveVehicle;
        
        vehicleForm.saveVehicle = function() {
            // Reset errors
            this.errors = {
                primary_image: [],
                images: [],
                daily_rate: [],
                availability: [],
                advance_notice: [],
                vehicle_type_id: [],
                make: [],
                model: [],
                year: [],
                license_plate: [],
                color: [],
                mileage: [],
                transmission: [],
                fuel_type: [],
                seats: [],
                doors: [],
                city_id: []
            };
            
            // Get all required document types
            const requiredDocumentTypes = documentTypeIds;
            
            // Log the current document values for debugging
            console.log('Validating documents with dynamic IDs:', this.form.documents);
            
            let hasErrors = false;
            
            // Validate primary image
            if (!this.form.primary_image) {
                this.errors.primary_image = ['Please upload a main vehicle image'];
                hasErrors = true;
            }
            
            // Validate gallery images
            if (!this.form.images) {
                this.errors.images = ['Please upload at least one vehicle image'];
                hasErrors = true;
            }
            
            // Validate required documents
            requiredDocumentTypes.forEach(docTypeId => {
                // Check if document exists and has a file_id
                if (!this.form.documents ||
                    !this.form.documents[docTypeId] ||
                    !this.form.documents[docTypeId].file_id ||
                    this.form.documents[docTypeId].file_id === '') {
                    
                    // Create the error key if it doesn't exist
                    if (!this.errors[`documents.${docTypeId}.file_id`]) {
                        this.errors[`documents.${docTypeId}.file_id`] = [];
                    }
                    
                    const docName = documentTypeNames[docTypeId] || `Document ${docTypeId}`;
                    this.errors[`documents.${docTypeId}.file_id`].push(`${docName} is required`);
                    hasErrors = true;
                    
                    console.log(`Document ${docTypeId} (${docName}) is missing or invalid:`,
                        this.form.documents && this.form.documents[docTypeId] ?
                        this.form.documents[docTypeId].file_id : 'undefined');
                } else {
                    console.log(`Document ${docTypeId} is valid:`, this.form.documents[docTypeId].file_id);
                }
                
                // Check if document requires expiry date
                const expiryDateInput = document.querySelector(`input[name="documents[${docTypeId}][expiry_date]"]`);
                if (expiryDateInput) {
                    // This document type requires an expiry date
                    if (!this.form.documents ||
                        !this.form.documents[docTypeId] ||
                        !this.form.documents[docTypeId].expiry_date ||
                        this.form.documents[docTypeId].expiry_date === '') {
                        
                        // Create the error key if it doesn't exist
                        if (!this.errors[`documents.${docTypeId}.expiry_date`]) {
                            this.errors[`documents.${docTypeId}.expiry_date`] = [];
                        }
                        
                        const docName = documentTypeNames[docTypeId] || `Document ${docTypeId}`;
                        this.errors[`documents.${docTypeId}.expiry_date`].push(`${docName} expiry date is required`);
                        hasErrors = true;
                        
                        console.log(`Document ${docTypeId} (${docName}) expiry date is missing or invalid`);
                    }
                    // Check if expiry date is in the future
                    else if (!this.isFutureDate(this.form.documents[docTypeId].expiry_date)) {
                        // Create the error key if it doesn't exist
                        if (!this.errors[`documents.${docTypeId}.expiry_date`]) {
                            this.errors[`documents.${docTypeId}.expiry_date`] = [];
                        }
                        
                        const docName = documentTypeNames[docTypeId] || `Document ${docTypeId}`;
                        this.errors[`documents.${docTypeId}.expiry_date`].push(`${docName} expiry date must be a future date`);
                        hasErrors = true;
                        
                        console.log(`Document ${docTypeId} (${docName}) expiry date is not in the future:`,
                            this.form.documents[docTypeId].expiry_date);
                    } else {
                        console.log(`Document ${docTypeId} expiry date is valid:`,
                            this.form.documents[docTypeId].expiry_date);
                    }
                }
            });
            
            // If there are errors, show the message and return
            if (hasErrors) {
                this.message = 'Please correct the errors below.';
                this.messageIsError = true;
                
                // Switch to the documents tab if there are document errors
                if (requiredDocumentTypes.some(docTypeId =>
                    this.errors[`documents.${docTypeId}.file_id`] ||
                    this.errors[`documents.${docTypeId}.expiry_date`])) {
                    
                    try {
                        const tabController = document.querySelector('[x-data*="tab"]');
                        if (tabController && tabController.__x) {
                            tabController.__x.$data.tab = 'documents';
                        }
                    } catch (e) {
                        console.error('Error switching tab:', e);
                    }
                }
                
                return;
            }
            
            // If no errors, call the original saveVehicle method
            originalSaveVehicle.call(this);
        };
        
        console.log('Vehicle document validation fix applied');
    }, 1000); // Wait 1 second for Alpine to initialize
});

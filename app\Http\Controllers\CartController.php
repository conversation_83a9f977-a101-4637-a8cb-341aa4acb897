<?php

namespace App\Http\Controllers;

use App\Models\Cart;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;

class CartController extends Controller
{
    /**
     * Add a product to the cart
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function add(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1',
        ]);

        $productId = $request->input('product_id');
        $quantity = $request->input('quantity', 1);

        // Get the product
        $product = Product::findOrFail($productId);

        // Check if user is logged in
        if (Auth::check()) {
            // User is logged in - save to database
            $userId = Auth::id();

            // Check if product is already in the cart
            $cartItem = Cart::where('user_id', $userId)
                ->where('product_id', $productId)
                ->first();

            if ($cartItem) {
                // Update existing cart item
                $cartItem->quantity += $quantity;
                $cartItem->save();
            } else {
                // Create new cart item
                $cartItem = new Cart();
                $cartItem->user_id = $userId;
                $cartItem->product_id = $productId;
                $cartItem->quantity = $quantity;
                $cartItem->price = $product->price;
                $cartItem->save();
            }

            $cartCount = Cart::where('user_id', $userId)->sum('quantity');
        } else {
            // Guest user - save to session
            $tempUserId = $this->getGuestUserId();

            // Check if product is already in the cart
            $cartItem = Cart::where('temp_user_id', $tempUserId)
                ->where('product_id', $productId)
                ->first();

            if ($cartItem) {
                // Update existing cart item
                $cartItem->quantity += $quantity;
                $cartItem->save();
            } else {
                // Create new cart item
                $cartItem = new Cart();
                $cartItem->temp_user_id = $tempUserId;
                $cartItem->product_id = $productId;
                $cartItem->quantity = $quantity;
                $cartItem->price = $product->price;
                $cartItem->save();
            }

            $cartCount = Cart::where('temp_user_id', $tempUserId)->sum('quantity');
        }

        return response()->json([
            'success' => true,
            'message' => 'Product added to cart successfully',
            'cartCount' => $cartCount
        ]);
    }

    /**
     * View the shopping cart
     *
     * @return \Illuminate\View\View
     */
    public function view()
    {
        $cartItems = $this->getCartItems();
        $cartTotal = $this->calculateCartTotal($cartItems);

        return view('front.care-connect.cart', compact('cartItems', 'cartTotal'));
    }

    /**
     * Update cart items quantity
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request)
    {
        $request->validate([
            'cart_id' => 'required|integer|exists:carts,id',
            'quantity' => 'required|integer|min:1',
        ]);

        $cartId = $request->input('cart_id');
        $quantity = $request->input('quantity');

        if (Auth::check()) {
            $cartItem = Cart::where('id', $cartId)
                ->where('user_id', Auth::id())
                ->first();
        } else {
            $tempUserId = $this->getGuestUserId();
            $cartItem = Cart::where('id', $cartId)
                ->where('temp_user_id', $tempUserId)
                ->first();
        }

        if (!$cartItem) {
            return response()->json([
                'success' => false,
                'message' => 'Cart item not found'
            ]);
        }

        $cartItem->quantity = $quantity;
        $cartItem->save();

        // Get updated cart data
        $cartItems = $this->getCartItems();
        $cartTotal = $this->calculateCartTotal($cartItems);

        return response()->json([
            'success' => true,
            'message' => 'Cart updated successfully',
            'cartTotal' => $cartTotal,
            'itemTotal' => $cartItem->quantity * $cartItem->price
        ]);
    }

    /**
     * Remove an item from the cart
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function remove(Request $request)
    {
        $request->validate([
            'cart_id' => 'required|integer|exists:carts,id',
        ]);

        $cartId = $request->input('cart_id');

        if (Auth::check()) {
            $deleted = Cart::where('id', $cartId)
                ->where('user_id', Auth::id())
                ->delete();
        } else {
            $tempUserId = $this->getGuestUserId();
            $deleted = Cart::where('id', $cartId)
                ->where('temp_user_id', $tempUserId)
                ->delete();
        }

        if (!$deleted) {
            return response()->json([
                'success' => false,
                'message' => 'Cart item not found'
            ]);
        }

        // Get updated cart data
        $cartItems = $this->getCartItems();
        $cartTotal = $this->calculateCartTotal($cartItems);
        $cartCount = $cartItems->sum('quantity');

        return response()->json([
            'success' => true,
            'message' => 'Item removed from cart',
            'cartTotal' => $cartTotal,
            'cartCount' => $cartCount
        ]);
    }

    /**
     * Clear the entire cart
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function clear()
    {
        if (Auth::check()) {
            Cart::where('user_id', Auth::id())->delete();
        } else {
            $tempUserId = $this->getGuestUserId();
            Cart::where('temp_user_id', $tempUserId)->delete();
        }

        return redirect()->route('cart.view')->with('success', 'Your cart has been cleared');
    }

    /**
     * Get all cart items for the current user (logged in or guest)
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function getCartItems()
    {
        if (Auth::check()) {
            return Cart::with('product')
                ->where('user_id', Auth::id())
                ->get();
        } else {
            $tempUserId = $this->getGuestUserId();
            return Cart::with('product')
                ->where('temp_user_id', $tempUserId)
                ->get();
        }
    }

    /**
     * Calculate the total value of the cart
     *
     * @param \Illuminate\Database\Eloquent\Collection $cartItems
     * @return float
     */
    private function calculateCartTotal($cartItems)
    {
        $total = 0;

        foreach ($cartItems as $item) {
            $total += $item->quantity * $item->price;
        }

        return $total;
    }

    /**
     * Get or create a guest user ID from session
     *
     * @return string
     */
    private function getGuestUserId()
    {
        if (!Session::has('guest_user_id')) {
            Session::put('guest_user_id', Str::uuid()->toString());
        }

        return Session::get('guest_user_id');
    }

    /**
     * Transfer guest cart to user account after login
     *
     * @param int $userId
     * @return void
     */
    public function transferGuestCart($userId)
    {
        if (!Session::has('guest_user_id')) {
            return;
        }

        $tempUserId = Session::get('guest_user_id');
        $guestCartItems = Cart::where('temp_user_id', $tempUserId)->get();

        foreach ($guestCartItems as $guestItem) {
            $userCartItem = Cart::where('user_id', $userId)
                ->where('product_id', $guestItem->product_id)
                ->first();

            if ($userCartItem) {
                // Update existing cart item
                $userCartItem->quantity += $guestItem->quantity;
                $userCartItem->save();
                $guestItem->delete();
            } else {
                // Transfer the cart item to the user
                $guestItem->user_id = $userId;
                $guestItem->temp_user_id = null;
                $guestItem->save();
            }
        }

        // Clear the guest user ID from session
        Session::forget('guest_user_id');
    }

    /**
     * Get the current cart count for the user
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCartCount()
    {
        if (Auth::check()) {
            $cartCount = Cart::where('user_id', Auth::id())->sum('quantity');
        } else {
            $tempUserId = $this->getGuestUserId();
            $cartCount = Cart::where('temp_user_id', $tempUserId)->sum('quantity');
        }

        return response()->json([
            'success' => true,
            'cartCount' => $cartCount
        ]);
    }
}

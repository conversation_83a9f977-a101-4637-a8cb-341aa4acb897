<?php if (isset($component)) { $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742 = $attributes; } ?>
<?php $component = App\View\Components\AppBackendLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-backend-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppBackendLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            <?php echo e(__('Driver Availability')); ?>

        </h2>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="mb-6">
                        <h3 class="text-lg font-medium text-gray-900"><?php echo e(__('Manage Your Availability')); ?></h3>
                        <p class="mt-1 text-sm text-gray-600">
                            <?php echo e(__('Set your availability for the next 30 days. You can mark days as available or unavailable, and set custom prices for specific dates.')); ?>

                        </p>
                    </div>

                    <form method="POST" action="<?php echo e(route('drivers.update-availability')); ?>" class="space-y-8">
                        <?php echo csrf_field(); ?>

                        <div class="bg-gray-50 p-6 rounded-lg">
                            <div class="mb-4 flex items-center justify-between">
                                <h4 class="text-md font-medium text-gray-900"><?php echo e(__('Default Settings')); ?></h4>
                                <div class="flex space-x-4">
                                    <div class="flex items-center">
                                        <div class="w-4 h-4 bg-green-100 border border-green-200 rounded-full mr-2"></div>
                                        <span class="text-sm text-gray-600"><?php echo e(__('Available')); ?></span>
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-4 h-4 bg-red-100 border border-red-200 rounded-full mr-2"></div>
                                        <span class="text-sm text-gray-600"><?php echo e(__('Unavailable')); ?></span>
                                    </div>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <div>
                                    <p class="text-sm text-gray-600 mb-2"><?php echo e(__('Your default availability:')); ?></p>
                                    <ul class="text-sm text-gray-700">
                                        <li class="flex items-center">
                                            <span class="w-4 h-4 <?php echo e($driver->available_weekdays ? 'bg-green-100 border-green-200' : 'bg-red-100 border-red-200'); ?> border rounded-full mr-2"></span>
                                            <?php echo e(__('Weekdays')); ?>: <?php echo e($driver->available_weekdays ? __('Available') : __('Unavailable')); ?>

                                        </li>
                                        <li class="flex items-center mt-1">
                                            <span class="w-4 h-4 <?php echo e($driver->available_weekends ? 'bg-green-100 border-green-200' : 'bg-red-100 border-red-200'); ?> border rounded-full mr-2"></span>
                                            <?php echo e(__('Weekends')); ?>: <?php echo e($driver->available_weekends ? __('Available') : __('Unavailable')); ?>

                                        </li>
                                    </ul>
                                    <p class="text-sm text-gray-500 mt-2">
                                        <?php echo e(__('You can change these defaults in your')); ?>

                                        <a href="<?php echo e(route('drivers.edit-profile')); ?>" class="text-indigo-600 hover:text-indigo-900"><?php echo e(__('profile settings')); ?></a>.
                                    </p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600 mb-2"><?php echo e(__('Your rates:')); ?></p>
                                    <ul class="text-sm text-gray-700">
                                        <li><?php echo e(__('Daily Rate')); ?>: <?php echo e(number_format($driver->daily_rate, 2)); ?></li>
                                        <?php if($driver->hourly_rate): ?>
                                            <li class="mt-1"><?php echo e(__('Hourly Rate')); ?>: <?php echo e(number_format($driver->hourly_rate, 2)); ?></li>
                                        <?php endif; ?>
                                    </ul>
                                </div>
                            </div>

                            <div class="mb-4">
                                <h4 class="text-md font-medium text-gray-900 mb-2"><?php echo e(__('Calendar')); ?></h4>
                                <p class="text-sm text-gray-600 mb-4">
                                    <?php echo e(__('Click on a date to toggle availability or set a custom price.')); ?>

                                </p>
                            </div>

                            <div class="grid grid-cols-7 gap-2">
                                <!-- Day headers -->
                                <?php $__currentLoopData = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $dayName): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="text-center font-medium text-gray-700 text-sm py-2">
                                        <?php echo e($dayName); ?>

                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                <!-- Calendar days -->
                                <?php
                                    $startDate = \Carbon\Carbon::today();
                                    $firstDayOfMonth = $startDate->copy()->startOfMonth();
                                    $startingDayOfWeek = $firstDayOfMonth->dayOfWeek;

                                    // Add empty cells for days before the first day of the month
                                    for ($i = 0; $i < $startingDayOfWeek; $i++) {
                                        echo '<div class="h-24 border border-gray-200 rounded-md bg-gray-50"></div>';
                                    }
                                ?>

                                <?php $__currentLoopData = $calendar; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $date => $availability): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php
                                        $dateObj = \Carbon\Carbon::parse($date);
                                        $isAvailable = $availability['is_available'] ?? false;
                                        $customPrice = $availability['custom_price'] ?? null;
                                        $isToday = $dateObj->isToday();
                                        $isWeekend = $dateObj->isWeekend();
                                    ?>
                                    <div class="h-24 border <?php echo e($isToday ? 'border-indigo-300 ring-1 ring-indigo-300' : 'border-gray-200'); ?> rounded-md overflow-hidden <?php echo e($isWeekend ? 'bg-gray-50' : 'bg-white'); ?>">
                                        <div class="p-2">
                                            <div class="flex justify-between items-center mb-2">
                                                <span class="text-sm font-medium <?php echo e($isToday ? 'text-indigo-600' : 'text-gray-700'); ?>">
                                                    <?php echo e($dateObj->format('j')); ?>

                                                </span>
                                                <span class="text-xs text-gray-500">
                                                    <?php echo e($dateObj->format('M')); ?>

                                                </span>
                                            </div>

                                            <input type="hidden" name="dates[<?php echo e($date); ?>][date]" value="<?php echo e($date); ?>">

                                            <div class="flex items-center mb-1">
                                                <input type="checkbox"
                                                       id="available_<?php echo e($date); ?>"
                                                       name="dates[<?php echo e($date); ?>][is_available]"
                                                       value="1"
                                                       <?php echo e($isAvailable ? 'checked' : ''); ?>

                                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                                <label for="available_<?php echo e($date); ?>" class="ml-2 text-xs text-gray-700">
                                                    <?php echo e(__('Available')); ?>

                                                </label>
                                            </div>

                                            <div>
                                                <input type="number"
                                                       name="dates[<?php echo e($date); ?>][custom_price]"
                                                       value="<?php echo e($customPrice); ?>"
                                                       placeholder="<?php echo e(number_format($driver->daily_rate, 2)); ?>"
                                                       class="mt-1 block w-full text-xs border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                                       min="0"
                                                       step="0.01">
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>

                        <div class="flex justify-end">
                            <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <?php echo e(__('Save Availability')); ?>

                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $attributes = $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $component = $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\carbnb\resources\views\drivers\availability.blade.php ENDPATH**/ ?>
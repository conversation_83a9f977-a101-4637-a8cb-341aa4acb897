<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Referral extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'referrer_id',
        'email',
        'token',
        'status',
        'registered_user_id',
        'vehicle_added',
    ];

    /**
     * Get the referrer user.
     */
    public function referrer()
    {
        return $this->belongsTo(User::class, 'referrer_id');
    }

    /**
     * Get the registered user.
     */
    public function registeredUser()
    {
        return $this->belongsTo(User::class, 'registered_user_id');
    }

    /**
     * Generate a unique token for the referral.
     *
     * @return string
     */
    public static function generateToken()
    {
        return Str::random(32);
    }

    /**
     * Get the referral link for this referral.
     *
     * @return string
     */
    public function getReferralLink()
    {
        return route('referral.register', ['token' => $this->token]);
    }

    /**
     * Mark the referral as registered.
     *
     * @param int $userId
     * @return bool
     */
    public function markAsRegistered($userId)
    {
        return $this->update([
            'status' => 'registered',
            'registered_user_id' => $userId,
        ]);
    }

    /**
     * Mark the referral as completed (vehicle added).
     *
     * @return bool
     */
    public function markAsCompleted()
    {
        return $this->update([
            'status' => 'completed',
            'vehicle_added' => true,
        ]);
    }
}

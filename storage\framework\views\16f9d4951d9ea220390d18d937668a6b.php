<?php if (isset($component)) { $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742 = $attributes; } ?>
<?php $component = App\View\Components\AppBackendLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-backend-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppBackendLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div x-data="pageManager" class="p-6">
        <div class="mb-4">
            <div class="flex justify-between items-center mb-3">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4">Pages Management</h2>
                <a href="<?php echo e(route('pages.create')); ?>"
                   class="inline-flex items-center justify-center px-4 py-2 rounded-lg bg-blue-600 text-white font-medium transition-all duration-200 hover:bg-blue-700 hover:shadow-lg focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                   <i class="fas fa-plus mr-2"></i>
                   Add Page
                </a>
            </div>

            <!-- Success Message (Fallback if notification system fails) -->
            <div x-show="successMessage" x-transition x-cloak class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4">
                <span x-text="successMessage"></span>
                <button @click="successMessage = ''" class="absolute top-0 right-0 mr-2 mt-2">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Search and filters section -->
            <div class="mb-4 bg-white p-4 rounded-lg shadow">
                <div class="flex flex-col md:flex-row gap-4">
                    <div class="flex-grow">
                        <div class="relative">
                            <input 
                                type="text" 
                                x-model="search" 
                                @keyup.enter="searchItems"
                                placeholder="Search pages by title..."
                                class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                            >
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-search text-gray-400"></i>
                            </div>
                            <button 
                                @click="searchItems"
                                class="absolute inset-y-0 right-0 px-4 text-gray-700 bg-gray-100 rounded-r-lg hover:bg-gray-200 focus:outline-none"
                            >
                                Search
                            </button>
                        </div>
                    </div>
                    <div>
                        <select 
                            x-model="filters.status" 
                            @change="searchItems" 
                            class="border border-gray-300 rounded-lg p-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                            <option value="">All Statuses</option>
                            <option value="published">Published</option>
                            <option value="draft">Draft</option>
                        </select>
                    </div>
                    <div>
                        <button 
                            @click="resetFilters"
                            class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-100"
                        >
                            Clear
                        </button>
                    </div>
                </div>
            </div>

            <!-- Loading Indicator (Fallback if notification loading fails) -->
            <div x-show="loading" class="flex justify-center my-4">
                <i class="fas fa-spinner fa-spin text-blue-600 text-3xl"></i>
            </div>

            <div class="bg-white rounded-lg shadow" x-show="!loading">
                <div class="overflow-x-auto">
                    <table class="w-full table-auto border-collapse">
                        <thead>
                            <tr class="bg-gray-200 text-gray-600 uppercase text-sm leading-normal">
                                <th class="py-3 px-6 text-left">#</th>
                                <th class="py-3 px-6 text-left">Title</th>
                                <th class="py-3 px-6 text-left">Slug</th>
                                <th class="py-3 px-6 text-left">Status</th>
                                <th class="py-3 px-6 text-center">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <template x-if="items.length === 0">
                                <tr class="border-b border-gray-200">
                                    <td colspan="5" class="py-8 px-6 text-center text-gray-500">
                                        No pages found
                                    </td>
                                </tr>
                            </template>
                            <template x-for="(page, index) in items" :key="page.id">
                                <tr class="border-b border-gray-200 hover:bg-gray-100">
                                    <td class="py-3 px-6 text-left" x-text="getSerialNumber(index)"></td>
                                    <td class="py-3 px-6 text-left" x-text="page.title"></td>
                                    <td class="py-3 px-6 text-left" x-text="page.slug"></td>
                                    <td class="py-3 px-6 text-left">
                                        <span 
                                            :class="{
                                                'bg-green-100 text-green-800': page.status === 'published',
                                                'bg-yellow-100 text-yellow-800': page.status === 'draft'
                                            }" 
                                            class="px-2 py-1 rounded-full text-xs font-semibold"
                                            x-text="page.status">
                                        </span>
                                    </td>
                                    <td class="py-3 px-6 text-center flex justify-center items-center space-x-2">
                                        <!-- View Button -->
                                        <a :href="`/page/${page.slug}`" 
                                           target="_blank"
                                           class="inline-flex items-center justify-center px-3 py-1.5 rounded-lg bg-emerald-500 text-white font-medium text-sm transition-all duration-200 hover:bg-emerald-600 hover:shadow-md focus:ring-2 focus:ring-emerald-400 focus:ring-opacity-50">
                                           <i class="fas fa-eye mr-1"></i>
                                           View
                                        </a>
                                        
                                        <!-- Edit Button -->
                                        <a :href="`<?php echo e(route('pages.index')); ?>/${page.id}/edit`"
                                           class="inline-flex items-center justify-center px-3 py-1.5 rounded-lg bg-amber-500 text-white font-medium text-sm transition-all duration-200 hover:bg-amber-600 hover:shadow-md focus:ring-2 focus:ring-amber-400 focus:ring-opacity-50">
                                           <i class="fas fa-edit mr-1"></i>
                                           Edit
                                        </a>
                                        
                                        <!-- Delete Button -->
                                        <button @click="deletePage(page.id)"
                                           class="inline-flex items-center justify-center px-3 py-1.5 rounded-lg bg-rose-500 text-white font-medium text-sm transition-all duration-200 hover:bg-rose-600 hover:shadow-md focus:ring-2 focus:ring-rose-400 focus:ring-opacity-50">
                                           <i class="fas fa-trash-alt mr-1"></i>
                                           Delete
                                        </button>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination controls -->
                <template x-if="totalPages > 1">
                    <div class="px-4 py-3 bg-white border-t border-gray-200 sm:px-6">
                        <div class="flex items-center justify-between">
                            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                <div>
                                    <p class="text-sm text-gray-700">
                                        Showing
                                        <span class="font-medium" x-text="(currentPage - 1) * perPage + 1"></span>
                                        to
                                        <span class="font-medium" x-text="Math.min(currentPage * perPage, totalRecords)"></span>
                                        of
                                        <span class="font-medium" x-text="totalRecords"></span>
                                        results
                                    </p>
                                </div>
                                <div>
                                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                        <!-- Previous Page -->
                                        <button 
                                            @click="prevPage" 
                                            :disabled="currentPage === 1"
                                            :class="{'opacity-50 cursor-not-allowed': currentPage === 1}"
                                            class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                                        >
                                            <span class="sr-only">Previous</span>
                                            <i class="fas fa-chevron-left h-5 w-5"></i>
                                        </button>
                                        
                                        <!-- Page Numbers -->
                                        <template x-for="page in paginationArray" :key="page">
                                            <button 
                                                @click="page !== '...' ? goToPage(page) : null" 
                                                :class="{
                                                    'bg-blue-50 border-blue-500 text-blue-600': page === currentPage, 
                                                    'bg-white border-gray-300 text-gray-500 hover:bg-gray-50': page !== currentPage && page !== '...',
                                                    'bg-white border-gray-300 text-gray-500 cursor-default': page === '...'
                                                }"
                                                class="relative inline-flex items-center px-4 py-2 border text-sm font-medium"
                                                x-text="page"
                                            ></button>
                                        </template>
                                        
                                        <!-- Next Page -->
                                        <button 
                                            @click="nextPage" 
                                            :disabled="currentPage === totalPages"
                                            :class="{'opacity-50 cursor-not-allowed': currentPage === totalPages}"
                                            class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                                        >
                                            <span class="sr-only">Next</span>
                                            <i class="fas fa-chevron-right h-5 w-5"></i>
                                        </button>
                                    </nav>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('alpine:init', () => {
            Alpine.data('pageManager', () => {
                // Import the shared pagination functionality
                const pagination = window.createPagination({
                    fetchUrl: '<?php echo e(route('pages.index')); ?>',
                    initialPage: <?php echo e($pages->currentPage()); ?>,
                    perPage: <?php echo e($pages->perPage()); ?>,
                    searchParams: {
                        status: '<?php echo e(request('status')); ?>'
                    },
                    successMessage: '<?php echo e(session('success')); ?>'
                });
                
                // Extend with page-specific functionality
                return {
                    ...pagination,
                    
                    // Initialize data
                    init() {
                        // Initialize pagination data
                        this.items = <?php echo json_encode($pages->items(), 15, 512) ?>;
                        this.totalPages = <?php echo e($pages->lastPage()); ?>;
                        this.totalRecords = <?php echo e($pages->total()); ?>;
                        
                        // Call the parent init
                        pagination.init.call(this);
                    },
                    
                    // Page-specific delete function
                    deletePage(id) {
                        // If notification system is available, use the confirmDelete method for better UX
                        if (window.notification) {
                            window.notification.confirmDelete('page')
                                .then((result) => {
                                    if (result.isConfirmed) {
                                        this.processDelete(id, '<?php echo e(route('pages.index')); ?>');
                                    }
                                });
                        } else {
                            // Fallback to the generic delete method
                            this.deleteItem(id, '<?php echo e(route('pages.index')); ?>', 'Are you sure you want to delete this page?');
                        }
                    }
                };
            });
        });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $attributes = $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $component = $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?><?php /**PATH C:\laragon\www\carbnb\resources\views\backend\pages\index.blade.php ENDPATH**/ ?>
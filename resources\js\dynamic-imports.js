/**
 * Helper functions to initialize dynamically imported libraries
 */

// Initialize html2pdf
export function initHtml2pdf(selector, options = {}) {
    if (!document.querySelector(selector)) return;
    
    return window.loadHtml2pdf().then(html2pdf => {
        const element = document.querySelector(selector);
        const defaultOptions = {
            margin: 10,
            filename: 'document.pdf',
            image: { type: 'jpeg', quality: 0.98 },
            html2canvas: { scale: 2 },
            jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
        };
        
        return html2pdf().from(element).set({...defaultOptions, ...options}).save();
    });
}

// Initialize LightGallery
export function initLightGallery(selector, options = {}) {
    const elements = document.querySelectorAll(selector);
    if (!elements.length) return;
    
    return window.loadLightGallery().then(lightGallery => {
        elements.forEach(element => {
            lightGallery(element, options);
        });
    });
}

// Initialize FilePond
export function initFilePond(selector, options = {}) {
    const elements = document.querySelectorAll(selector);
    if (!elements.length) return;
    
    return window.loadFilePond().then(FilePond => {
        return FilePond.create(elements, options);
    });
}

// Initialize Swiper
export function initSwiper(selector, options = {}) {
    if (!document.querySelector(selector)) return;
    
    return window.loadSwiper().then(() => {
        const defaultOptions = {
            modules: [
                window.SwiperModules.Navigation,
                window.SwiperModules.Pagination,
                window.SwiperModules.Autoplay
            ],
            loop: true,
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
            },
        };
        
        return new window.Swiper(selector, {...defaultOptions, ...options});
    });
}

// Auto-initialize components based on data attributes
document.addEventListener('DOMContentLoaded', () => {
    // Initialize LightGallery
    if (document.querySelector('[data-gallery]')) {
        document.querySelectorAll('[data-gallery]').forEach(gallery => {
            const options = gallery.dataset.galleryOptions ? JSON.parse(gallery.dataset.galleryOptions) : {};
            initLightGallery(gallery, options);
        });
    }
    
    // Initialize FilePond
    if (document.querySelector('input[type="file"][data-filepond]')) {
        document.querySelectorAll('input[type="file"][data-filepond]').forEach(input => {
            const options = input.dataset.filepondOptions ? JSON.parse(input.dataset.filepondOptions) : {};
            initFilePond(input, options);
        });
    }
    
    // Initialize Swiper
    if (document.querySelector('[data-swiper]')) {
        document.querySelectorAll('[data-swiper]').forEach(slider => {
            const options = slider.dataset.swiperOptions ? JSON.parse(slider.dataset.swiperOptions) : {};
            initSwiper(slider, options);
        });
    }
});

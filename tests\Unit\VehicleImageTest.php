<?php

namespace Tests\Unit;

use App\Models\VehicleImage;
use App\Models\Vehicle;
use Tests\Unit\TestCase;
use Mockery;

test('vehicle image has correct fillable attributes', function () {
    $vehicleImage = new VehicleImage();

    $fillable = [
        'vehicle_id',
        'file_id',
        'is_primary',
        'type'
    ];

    expect($vehicleImage->getFillable())->toBe($fillable);
});

test('vehicle image has correct casts', function () {
    $vehicleImage = new VehicleImage();

    $expectedCasts = [
        'is_primary' => 'boolean',
    ];

    expect($vehicleImage->getCasts())->toMatchArray($expectedCasts);
});

test('vehicle image has vehicle relationship method', function () {
    $vehicleImage = new VehicleImage();

    expect(method_exists($vehicleImage, 'vehicle'))->toBeTrue();
});

test('vehicle image has url attribute', function () {
    $vehicleImage = new VehicleImage();
    $vehicleImage->file_id = 'test-file-id';

    // Mock the asset function
    $expectedUrl = 'http://localhost/storage/vehicle-images/test-file-id';

    // Create a mock method for getUrlAttribute
    $vehicleImage = Mockery::mock(VehicleImage::class)->makePartial();
    $vehicleImage->file_id = 'test-file-id';
    $vehicleImage->shouldReceive('getUrlAttribute')->andReturn($expectedUrl);

    expect($vehicleImage->url)->toBe($expectedUrl);
});

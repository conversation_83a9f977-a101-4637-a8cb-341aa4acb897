<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class DisputeMessage extends Model
{
    use HasFactory, SoftDeletes;
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'dispute_id',
        'user_id',
        'message',
        'is_admin_message',
        'is_system_message'
    ];
    
    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'is_admin_message' => 'boolean',
        'is_system_message' => 'boolean',
    ];
    
    /**
     * Get the dispute this message belongs to.
     */
    public function dispute()
    {
        return $this->belongsTo(Dispute::class);
    }
    
    /**
     * Get the user who sent this message.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}

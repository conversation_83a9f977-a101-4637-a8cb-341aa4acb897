<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\File;

class ExampleController extends Controller
{
    /**
     * Show the file upload example page.
     *
     * @return \Illuminate\View\View
     */
    public function fileUploadExample()
    {
        return view('examples.file-upload-example');
    }
    
    /**
     * Process the file upload example form.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function processFileUpload(Request $request)
    {
        $request->validate([
            'single_file' => 'required|string',
            'multiple_files' => 'nullable|string',
            'multiple_files_primary' => 'nullable|string',
        ]);
        
        // Get the file IDs
        $singleFileId = $request->input('single_file');
        $multipleFileIds = $request->input('multiple_files') ? explode(',', $request->input('multiple_files')) : [];
        $primaryFileId = $request->input('multiple_files_primary');
        
        // Get the file details
        $singleFile = File::find($singleFileId);
        $multipleFiles = File::whereIn('id', $multipleFileIds)->get();
        
        // Here you would typically associate these files with your model
        // For example:
        // $yourModel->file_id = $singleFileId;
        // $yourModel->save();
        
        // For multiple files, you might have a relationship:
        // foreach ($multipleFileIds as $fileId) {
        //     $yourModel->files()->create(['file_id' => $fileId]);
        // }
        
        return redirect()->back()->with('success', 'Files processed successfully!');
    }
}

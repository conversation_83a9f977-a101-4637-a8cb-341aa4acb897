/**
 * Test script for file upload functionality
 *
 * This script simulates file upload events to test the file upload component
 * without actually uploading files.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Test script loaded');

    // Function to check if Alpine.js is initialized
    function checkAlpineInitialized() {
        return window.Alpine && document.querySelector('[x-data]').__x;
    }

    // Function to simulate file upload
    function simulateFileUpload() {
        console.log('Alpine.js initialized, starting simulation...');

        // Get the form data from the Alpine component
        const vehicleForm = document.querySelector('[x-data="vehicleForm"]').__x.getUnobservedData();
        console.log('Current form data:', vehicleForm.form);

        // Simulate a file upload event for the primary image
        console.log('Simulating primary image upload...');

        // Create a custom event
        const primaryImageEvent = new CustomEvent('file-ids-updated', {
            detail: {
                name: 'primary_image',
                value: '123', // Simulated file ID
                primaryFileId: '123'
            }
        });

        // Dispatch the event
        window.dispatchEvent(primaryImageEvent);

        // Log the result
        console.log('Primary image event dispatched');

        // Wait a bit and then check if the form data was updated
        setTimeout(() => {
            const updatedFormData = document.querySelector('[x-data="vehicleForm"]').__x.getUnobservedData();
            console.log('Updated form data after primary image:', updatedFormData.form);

            // Wait a bit and then simulate a gallery image upload
            setTimeout(() => {
                console.log('Simulating gallery image upload...');

                // Create a custom event
                const galleryImageEvent = new CustomEvent('file-ids-updated', {
                    detail: {
                        name: 'images',
                        value: '123,456,789', // Simulated file IDs
                        primaryFileId: '123'
                    }
                });

                // Dispatch the event
                window.dispatchEvent(galleryImageEvent);

                // Log the result
                console.log('Gallery image event dispatched');

                // Wait a bit and then check if the form data was updated
                setTimeout(() => {
                    const finalFormData = document.querySelector('[x-data="vehicleForm"]').__x.getUnobservedData();
                    console.log('Final form data after gallery images:', finalFormData.form);
                }, 500);
            }, 1000);
        }, 500);
    }

    // Wait for Alpine.js to initialize
    let checkInterval = setInterval(() => {
        try {
            if (checkAlpineInitialized()) {
                clearInterval(checkInterval);
                setTimeout(simulateFileUpload, 1000); // Wait a bit more to ensure all components are initialized
            }
        } catch (e) {
            console.log('Waiting for Alpine.js to initialize...');
        }
    }, 100);

    // Fallback in case Alpine.js doesn't initialize within 5 seconds
    setTimeout(() => {
        clearInterval(checkInterval);
        console.log('Fallback: Alpine.js initialization timeout, trying simulation anyway...');
        try {
            simulateFileUpload();
        } catch (e) {
            console.error('Failed to simulate file upload:', e);
        }
    }, 5000);
});

<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        $admin = User::create([
            'first_name' => 'Admin',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
        ]);

        // Create agent user
        $agent = User::create([
            'first_name' => 'Agent',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
        ]);

        // Create regular user
        $user = User::create([
            'first_name' => 'Regular',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
        ]);

        // Assign roles
        $adminRole = Role::where('name', 'admin')->first();
        $agentRole = Role::where('name', 'agent')->first();
        $userRole = Role::where('name', 'user')->first();

        if ($adminRole) {
            $admin->assignRole($adminRole);
        }

        if ($agentRole) {
            $agent->assignRole($agentRole);
        }

        if ($userRole) {
            $user->assignRole($userRole);
        }
    }
}

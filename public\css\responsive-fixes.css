/* Responsive fixes for the dashboard */

/* Mobile sidebar adjustments */
@media (max-width: 1023px) {
    .sidebar-open {
        overflow: hidden;
    }

    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 40;
    }
}

/* Improve spacing in mobile view */
@media (max-width: 640px) {
    .p-4 {
        padding: 0.75rem !important;
    }

    .space-x-4 > * + * {
        margin-left: 0.5rem !important;
    }

    .text-xl {
        font-size: 1.1rem !important;
    }
}

/* Improve dropdown positioning on mobile */
@media (max-width: 640px) {
    .user-dropdown {
        right: 0;
        left: auto;
        width: 200px;
        max-width: calc(100vw - 20px);
        transform: translateX(10px) !important;
    }

    .notification-dropdown {
        right: 0;
        left: auto;
        width: 280px;
        max-width: calc(100vw - 20px);
        transform: translateX(10px) !important;
    }
}

/* Handle long names in header */
.truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Ensure dropdown menus don't overflow screen */
.user-dropdown, .notification-dropdown {
    max-height: 80vh;
    overflow-y: auto;
}

/* Make dropdown menu items more touch-friendly on mobile */
@media (max-width: 640px) {
    .user-dropdown a, .notification-dropdown a {
        padding-top: 0.75rem !important;
        padding-bottom: 0.75rem !important;
    }
}

/* Ensure content doesn't overflow on small screens */
.overflow-x-auto {
    overflow-x: auto;
}

/* Add more space between sidebar menu items on touch devices */
@media (max-width: 1023px) {
    .sidebar-menu-item {
        padding-top: 0.75rem !important;
        padding-bottom: 0.75rem !important;
        margin-bottom: 0.5rem !important;
    }
}

/* Fix for FontAwesome icons spacing */
.sidebar .fas, .sidebar .far, .sidebar .fab {
    margin-right: 0.75rem;
}

/* Header icons should not have right margin */
.header-container .fas, .header-container .far, .header-container .fab {
    margin-right: 0;
}

/* But dropdown menu items should have margin */
.user-dropdown .fas, .user-dropdown .far, .user-dropdown .fab,
.notification-dropdown .fas, .notification-dropdown .far, .notification-dropdown .fab {
    margin-right: 0.5rem;
}

/* Improve header spacing on mobile */
@media (max-width: 640px) {
    .header-container {
        padding: 0.75rem !important;
    }

    .header-title {
        font-size: 1.1rem !important;
    }
}

<x-app-layout>
<!-- Hero Section -->
<section class="relative h-[600px] overflow-hidden">
    <div class="absolute inset-0">
        <img src="https://images.unsplash.com/photo-1449965408869-eaa3f722e40d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
             alt="Car sharing" class="w-full h-full object-cover brightness-50">
    </div>
    <div class="relative container mx-auto px-4 h-full flex flex-col justify-center">
        <h1 class="text-4xl md:text-6xl font-bold text-white mb-4" data-lang-key="hero_title">{{ frontend_trans('hero_title') }}</h1>
        <p class="text-xl text-white mb-8 max-w-xl" data-lang-key="hero_subtitle">{{ frontend_trans('hero_subtitle') }}</p>
        <div class="flex flex-col md:flex-row gap-4 mb-8">
            <a href="{{ route('cars.listing') }}" class="bg-primary hover:bg-red-700 text-white font-bold py-3 px-6 rounded-lg text-center transition" data-lang-key="rent_car_btn">{{ frontend_trans('rent_car_btn') }}</a>
            <a href="{{ route('list-car') }}" class="bg-white hover:bg-gray-100 text-primary font-bold py-3 px-6 rounded-lg text-center transition" data-lang-key="list_car_btn">{{ frontend_trans('list_car_btn') }}</a>
        </div>
        <form action="{{ route('cars.listing') }}" method="GET" class="bg-white p-6 rounded-lg shadow-lg max-w-4xl">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                <div>
                    <label class="block text-gray-700 mb-2" data-lang-key="location_label">Location</label>
                    <select id="city-dropdown" name="city_id" class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none">
                        <option value="" data-lang-key="select_city">Select City</option>
                        @foreach($cities as $city)
                            <option value="{{ $city->id }}">{{ $city->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div>
                    <label class="block text-gray-700 mb-2" data-lang-key="from_label">From</label>
                    <input type="date" name="pickup_date" class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none" min="{{ date('Y-m-d') }}">
                </div>
                <div>
                    <label class="block text-gray-700 mb-2" data-lang-key="to_label">To</label>
                    <input type="date" name="return_date" class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none" min="{{ date('Y-m-d', strtotime('+1 day')) }}">
                </div>
                <div>
                    <label class="block text-gray-700 mb-2" data-lang-key="car_type_label">Car Type</label>
                    <select name="vehicle_type" class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none">
                        <option value="" data-lang-key="any_car">Any Type</option>
                        @foreach($vehicleTypes as $type)
                            <option value="{{ $type->id }}">{{ $type->name }}</option>
                        @endforeach
                    </select>
                </div>
            </div>
            <button type="submit" class="w-full bg-primary text-white py-3 rounded-md hover:bg-red-700 transition" data-lang-key="find_cars_btn">Find Available Cars</button>
        </form>
        </div>
    </div>
</section>

<!-- Stats Section -->
<section class="py-10 bg-white">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
            <div>
                <p class="text-3xl font-bold text-primary">10,000+</p>
                <p class="text-gray-600" data-lang-key="cars_listed">Cars Listed</p>
            </div>
            <div>
                <p class="text-3xl font-bold text-primary">50,000+</p>
                <p class="text-gray-600" data-lang-key="happy_users">Happy Users</p>
            </div>
            <div>
                <p class="text-3xl font-bold text-primary">500+</p>
                <p class="text-gray-600" data-lang-key="cities">Cities</p>
            </div>
            <div>
                <p class="text-3xl font-bold text-primary">€5M+</p>
                <p class="text-gray-600" data-lang-key="owner_earnings">Owner Earnings</p>
            </div>
        </div>
    </div>
</section>

<!-- How It Works Section -->
<section id="how-it-works" class="py-16 bg-gray-50">
    <div class="container mx-auto px-4">
        <h2 class="text-3xl font-bold text-center mb-4" data-lang-key="how_carbnb_works">How CARBNB Works</h2>
        <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12" data-lang-key="join_thousands">Join thousands who are already saving money and making extra income.</p>

        <div class="flex flex-col md:flex-row gap-8 mb-16">
            <div class="md:w-1/2">
                <h3 class="text-2xl font-bold mb-6 text-primary" data-lang-key="rent_a_car_title">Rent a Car</h3>
                <div class="space-y-6">
                    <div class="flex">
                        <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                            <span class="text-primary font-bold">1</span>
                        </div>
                        <div>
                            <h4 class="font-bold mb-2" data-lang-key="search_compare">Search & Compare</h4>
                            <p class="text-gray-600" data-lang-key="search_compare_desc">Browse thousands of vehicles by location, date, and price to find your ideal ride.</p>
                        </div>
                    </div>
                    <div class="flex">
                        <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                            <span class="text-primary font-bold">2</span>
                        </div>
                        <div>
                            <h4 class="font-bold mb-2" data-lang-key="book_instantly">Book Instantly</h4>
                            <p class="text-gray-600" data-lang-key="book_instantly_desc">Reserve your chosen car with our secure booking system. No waiting for approval.</p>
                        </div>
                    </div>
                    <div class="flex">
                        <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                            <span class="text-primary font-bold">3</span>
                        </div>
                        <div>
                            <h4 class="font-bold mb-2" data-lang-key="pickup_go">Pick Up & Go</h4>
                            <p class="text-gray-600" data-lang-key="pickup_go_desc">Meet the owner to collect the keys or use our contactless pickup option where available.</p>
                        </div>
                    </div>
                </div>
                <a href="#" class="inline-block mt-6 px-6 py-3 bg-primary text-white font-bold rounded-lg hover:bg-red-700 transition" data-lang-key="find_car_btn">Find a Car</a>
            </div>
            <div class="md:w-1/2 mt-8 md:mt-0">
                <h3 class="text-2xl font-bold mb-6 text-primary" data-lang-key="list_your_car_title">List Your Car</h3>
                <div class="space-y-6">
                    <div class="flex">
                        <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                            <span class="text-primary font-bold">1</span>
                        </div>
                        <div>
                            <h4 class="font-bold mb-2" data-lang-key="list_for_free">List for Free</h4>
                            <p class="text-gray-600" data-lang-key="list_for_free_desc">Create a listing with photos, set your price and availability. It takes just 10 minutes.</p>
                        </div>
                    </div>
                    <div class="flex">
                        <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                            <span class="text-primary font-bold">2</span>
                        </div>
                        <div>
                            <h4 class="font-bold mb-2" data-lang-key="receive_bookings">Receive Bookings</h4>
                            <p class="text-gray-600" data-lang-key="receive_bookings_desc">Get notified when someone books your car. Accept or decline based on your schedule.</p>
                        </div>
                    </div>
                    <div class="flex">
                        <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                            <span class="text-primary font-bold">3</span>
                        </div>
                        <div>
                            <h4 class="font-bold mb-2" data-lang-key="get_paid">Get Paid</h4>
                            <p class="text-gray-600" data-lang-key="get_paid_desc">Earnings are automatically deposited to your account after each completed trip.</p>
                        </div>
                    </div>
                </div>
                <a href="#" id="list-your-car" class="inline-block mt-6 px-6 py-3 bg-primary text-white font-bold rounded-lg hover:bg-red-700 transition" data-lang-key="list_car_btn2">List Your Car</a>
            </div>
        </div>
    </div>
</section>

<!-- Popular Car Categories -->
<section id="rent-a-car" class="py-16 bg-white">
    <div class="container mx-auto px-4">
        <h2 class="text-3xl font-bold text-center mb-4" data-lang-key="browse_car_type">Browse by Car Type</h2>
        <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12" data-lang-key="find_perfect_vehicle">Find the perfect vehicle for any occasion, from compact city cars to spacious SUVs.</p>

        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
            @foreach($vehicleTypes as $type)
                <div class="bg-gray-50 rounded-lg overflow-hidden shadow-md hover:shadow-lg transition group">
                    <div class="h-44 overflow-hidden">
                        @if($type->icon)
                            <img src="{{ asset('storage/' . $type->icon) }}" alt="{{ $type->name }}" class="w-full h-full object-cover group-hover:scale-105 transition">
                        @else
                            <img src="https://via.placeholder.com/400x320?text={{ urlencode($type->name) }}" alt="{{ $type->name }}" class="w-full h-full object-cover group-hover:scale-105 transition">
                        @endif
                    </div>
                    <div class="p-4">
                        <h3 class="text-xl font-semibold mb-2">{{ $type->name }}</h3>
                        <p class="text-gray-600 mb-3">{{ $type->description ?? 'Explore our selection of ' . $type->name . ' vehicles.' }}</p>

                        @php
                            // Get the lowest daily rate for this vehicle type
                            $lowestPrice = \App\Models\Vehicle::where('vehicle_type_id', $type->id)
                                ->where('status', 'active')
                                ->min('daily_rate') ?? 0;
                        @endphp

                        <p class="text-primary font-medium">
                            @if($lowestPrice > 0)
                                From €{{ number_format($lowestPrice, 2) }}/day
                            @else
                                Check availability
                            @endif
                        </p>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</section>

<!-- Featured Cars -->
<section class="py-16 bg-gray-100">
    <div class="container mx-auto px-4">
        <h2 class="text-3xl font-bold text-center mb-4" data-lang-key="top_rated_cars">Top Rated Cars</h2>
        <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12" data-lang-key="discover_highest_rated">Discover our highest-rated vehicles from trusted owners in our community.</p>

        <div id="featured-cars" class="carousel relative">
            <div class="carousel-container flex overflow-x-auto snap-x pb-8 -mx-4 px-4 space-x-6 scrollbar-hide">
                @forelse($featuredVehicles as $vehicle)
                    <div class="carousel-item flex-none w-full sm:w-1/2 lg:w-1/3 snap-start bg-white rounded-xl shadow-md overflow-hidden">
                        <div class="relative h-56">
                            @if($vehicle->primaryImage)
                                <img src="{{ $vehicle->primaryImage->url }}" alt="{{ $vehicle->make }} {{ $vehicle->model }}" class="w-full h-full object-cover">
                            @else
                                <img src="https://via.placeholder.com/400x320?text={{ urlencode($vehicle->make . ' ' . $vehicle->model) }}" alt="{{ $vehicle->make }} {{ $vehicle->model }}" class="w-full h-full object-cover">
                            @endif
                            <div class="absolute top-4 right-4 bg-primary text-white text-sm font-semibold py-1 px-3 rounded-full" data-lang-key="featured">
                                Featured
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="flex justify-between items-center mb-3">
                                <h3 class="text-xl font-bold">{{ $vehicle->year }} {{ $vehicle->make }} {{ $vehicle->model }}</h3>
                                <p class="text-primary font-bold">€{{ number_format($vehicle->daily_rate, 2) }}/day</p>
                            </div>
                            <div class="flex items-center text-gray-600 mb-2">
                                <i class="fas fa-user text-gray-400 mr-2"></i>
                                <span class="mr-4">{{ $vehicle->user->name }}</span>
                                <i class="fas fa-star text-yellow-400"></i>
                                <span class="ml-1">
                                    @php
                                        // This would be replaced with actual rating logic
                                        $rating = rand(40, 50) / 10;
                                        $trips = rand(10, 60);
                                    @endphp
                                    {{ $rating }} ({{ $trips }} trips)
                                </span>
                            </div>
                            <div class="flex items-center text-gray-600 mb-4">
                                <i class="fas fa-map-marker-alt text-gray-400 mr-2"></i>
                                <span>{{ $vehicle->city->name }}</span>
                            </div>
                            <div class="grid grid-cols-2 gap-4 mb-6">
                                <div class="flex items-center">
                                    @if($vehicle->fuel_type == 'electric')
                                        <i class="fas fa-bolt text-green-500 mr-2"></i>
                                        <span data-lang-key="electric">Electric</span>
                                    @elseif($vehicle->fuel_type == 'hybrid' || $vehicle->fuel_type == 'plugin_hybrid')
                                        <i class="fas fa-leaf text-green-500 mr-2"></i>
                                        <span data-lang-key="{{ $vehicle->fuel_type }}">{{ ucfirst(str_replace('_', ' ', $vehicle->fuel_type)) }}</span>
                                    @else
                                        <i class="fas fa-gas-pump text-blue-500 mr-2"></i>
                                        <span data-lang-key="{{ $vehicle->fuel_type }}">{{ ucfirst($vehicle->fuel_type) }}</span>
                                    @endif
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-cog text-gray-400 mr-2"></i>
                                    <span data-lang-key="{{ $vehicle->transmission }}">{{ ucfirst($vehicle->transmission) }}</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-user-friends text-gray-400 mr-2"></i>
                                    <span>{{ $vehicle->seats }} {{ __('Seats') }}</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-door-open text-gray-400 mr-2"></i>
                                    <span>{{ $vehicle->doors }} {{ __('Doors') }}</span>
                                </div>
                            </div>
                            <a href="{{ route('vehicle.details', $vehicle->id) }}" class="block w-full bg-primary text-white py-3 text-center rounded-md hover:bg-red-700 transition" data-lang-key="view_details">View Details</a>
                        </div>
                    </div>
                @empty
                    <div class="carousel-item flex-none w-full snap-start bg-white rounded-xl shadow-md overflow-hidden p-8 text-center">
                        <i class="fas fa-car-side text-5xl text-gray-300 mb-4"></i>
                        <h3 class="text-xl font-bold mb-2" data-lang-key="no_featured_cars">No Featured Cars Available</h3>
                        <p class="text-gray-600 mb-4" data-lang-key="check_back">Check back soon for featured vehicles or browse all available cars.</p>
                        <a href="{{ route('cars.listing') }}" class="inline-block bg-primary text-white px-6 py-2 rounded-md hover:bg-red-700 transition" data-lang-key="browse_all_cars">Browse All Cars</a>
                    </div>
                @endforelse
            </div>

            <button id="prev-btn" class="absolute top-1/2 left-0 -translate-y-1/2 bg-white p-3 rounded-full shadow-md text-primary opacity-75 hover:opacity-100 focus:outline-none z-10">
                <i class="fas fa-chevron-left"></i>
            </button>
            <button id="next-btn" class="absolute top-1/2 right-0 -translate-y-1/2 bg-white p-3 rounded-full shadow-md text-primary opacity-75 hover:opacity-100 focus:outline-none z-10">
                <i class="fas fa-chevron-right"></i>
            </button>
        </div>

        <div class="text-center mt-8">
            <a href="{{ route('cars.listing') }}" class="inline-block text-primary font-semibold hover:text-red-800 transition" data-lang-key="browse_all">{{ frontend_trans('browse_all') }} <i class="fas fa-arrow-right ml-2"></i></a>
        </div>
    </div>
</section>

<!-- Benefits Section -->
<section class="py-16 bg-white">
    <div class="container mx-auto px-4">
        <h2 class="text-3xl font-bold text-center mb-4" data-lang-key="why_choose">Why Choose CARBNB</h2>
        <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12" data-lang-key="benefits_desc">Our peer-to-peer marketplace offers benefits for both car owners and renters.</p>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="bg-gray-50 p-6 rounded-lg">
                <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-6">
                    <i class="fas fa-shield-alt text-2xl text-primary"></i>
                </div>
                <h3 class="text-xl font-bold mb-3" data-lang-key="insurance_included">Insurance Included</h3>
                <p class="text-gray-600" data-lang-key="insurance_desc">Every booking comes with comprehensive insurance coverage for peace of mind.</p>
            </div>

            <div class="bg-gray-50 p-6 rounded-lg">
                <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-6">
                    <i class="fas fa-hand-holding-euro text-2xl text-primary"></i>
                </div>
                <h3 class="text-xl font-bold mb-3" data-lang-key="earn_money">Earn Money Sharing</h3>
                <p class="text-gray-600" data-lang-key="earn_money_desc">Car owners can earn an average of €500+ per month sharing their vehicle.</p>
            </div>

            <div class="bg-gray-50 p-6 rounded-lg">
                <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-6">
                    <i class="fas fa-wallet text-2xl text-primary"></i>
                </div>
                <h3 class="text-xl font-bold mb-3" data-lang-key="save_rentals">Save on Rentals</h3>
                <p class="text-gray-600" data-lang-key="save_rentals_desc">Renters typically save 25% compared to traditional car rental agencies.</p>
            </div>
        </div>
    </div>
</section>

<!-- Testimonials -->
<section class="py-16 bg-gray-100">
    <div class="container mx-auto px-4">
        <h2 class="text-3xl font-bold text-center mb-4" data-lang-key="community_says">What Our Community Says</h2>
        <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12" data-lang-key="real_stories">Real stories from car owners and renters in our growing community.</p>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div class="bg-white p-6 rounded-lg shadow-md">
                <div class="flex items-center mb-4">
                    <img src="/api/placeholder/48/48" alt="Customer" class="w-12 h-12 rounded-full mr-4">
                    <div>
                        <h4 class="font-bold">Raquel T.</h4>
                        <p class="text-gray-600 text-sm" data-lang-key="car_owner">Car Owner</p>
                    </div>
                </div>
                <div class="flex text-yellow-400 mb-3">
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                </div>
                <p class="text-gray-600" data-lang-key="testimonial_1">"My car was just sitting in my driveway most days. Now it makes me over €600 a month on CARBNB! The platform is easy to use and the support team is always helpful."</p>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-md">
                <div class="flex items-center mb-4">
                    <img src="/api/placeholder/48/48" alt="Customer" class="w-12 h-12 rounded-full mr-4">
                    <div>
                        <h4 class="font-bold">André M.</h4>
                        <p class="text-gray-600 text-sm" data-lang-key="car_renter">Car Renter</p>
                    </div>
                </div>
                <div class="flex text-yellow-400 mb-3">
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star-half-alt"></i>
                </div>
                <p class="text-gray-600" data-lang-key="testimonial_2">"I needed a truck for a weekend move and found the perfect vehicle on CARBNB. Way cheaper than rental companies and the owner was super flexible with pickup and drop-off times."</p>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-md">
                <div class="flex items-center mb-4">
                    <img src="/api/placeholder/48/48" alt="Customer" class="w-12 h-12 rounded-full mr-4">
                    <div>
                        <h4 class="font-bold">Maria L.</h4>
                        <p class="text-gray-600 text-sm" data-lang-key="owner_renter">Car Owner & Renter</p>
                    </div>
                </div>
                <div class="flex text-yellow-400 mb-3">
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                </div>
                <p class="text-gray-600" data-lang-key="testimonial_3">"I list my sedan during the week and rent SUVs for weekend trips. CARBNB has completely changed how I think about car ownership. The community is respectful and the process is seamless."</p>
            </div>
        </div>
    </div>
</section>

<!-- Trust & Safety Section -->
<section class="py-16 bg-secondary text-white">
    <div class="container mx-auto px-4">
        <div class="flex flex-col lg:flex-row items-center">
            <div class="lg:w-1/2 mb-10 lg:mb-0">
                <h2 class="text-3xl font-bold mb-4" data-lang-key="trust_safety">Trust & Safety Guaranteed</h2>
                <p class="mb-6 text-gray-300" data-lang-key="trust_safety_desc">Your safety is our top priority. Every trip is protected by our comprehensive insurance and 24/7 roadside assistance.</p>
                <div class="space-y-4">
                    <div class="flex">
                        <i class="fas fa-check-circle text-xl mr-3 text-primary"></i>
                        <div>
                            <h4 class="font-bold" data-lang-key="insurance_protection">€1M Insurance Protection</h4>
                            <p class="text-gray-300" data-lang-key="insurance_protection_desc">Every trip includes liability insurance up to €1 million.</p>
                        </div>
                    </div>
                    <div class="flex">
                        <i class="fas fa-check-circle text-xl mr-3 text-primary"></i>
                        <div>
                            <h4 class="font-bold" data-lang-key="roadside">24/7 Roadside Assistance</h4>
                            <p class="text-gray-300" data-lang-key="roadside_desc">Help is always available if you need it on the road.</p>
                        </div>
                    </div>
                    <div class="flex">
                        <i class="fas fa-check-circle text-xl mr-3 text-primary"></i>
                        <div>
                            <h4 class="font-bold" data-lang-key="secure_payments">Secure Payments</h4>
                            <p class="text-gray-300" data-lang-key="secure_payments_desc">All transactions are processed securely through our platform.</p>
                        </div>
                    </div>
                </div>
                <a href="#" class="inline-block mt-6 px-6 py-3 bg-primary text-white font-bold rounded-lg hover:bg-red-700 transition" data-lang-key="learn_more">Learn More</a>
            </div>
            <div class="lg:w-1/2 lg:pl-12">
                <img src="/api/placeholder/500/350" alt="Trust and Safety" class="w-full rounded-xl shadow-lg">
            </div>
        </div>
    </div>
</section>

<!-- Cities Section -->
<section class="py-16 bg-gray-50">
    <div class="container mx-auto px-4">
        <h2 class="text-3xl font-bold text-center mb-4" data-lang-key="available_cities">Available in Your City</h2>
        <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12" data-lang-key="expanding_cities">We're rapidly expanding to cities across Portugal. Find cars to share near you.</p>

        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-8">
            <a href="#" class="bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition text-center">
                <i class="fas fa-city text-primary text-2xl mb-2"></i>
                <p class="font-medium" data-lang-key="lisbon">Lisbon</p>
            </a>
            <a href="#" class="bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition text-center">
                <i class="fas fa-city text-primary text-2xl mb-2"></i>
                <p class="font-medium" data-lang-key="porto">Porto</p>
            </a>
            <a href="#" class="bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition text-center">
                <i class="fas fa-city text-primary text-2xl mb-2"></i>
                <p class="font-medium" data-lang-key="faro">Faro</p>
            </a>
            <a href="#" class="bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition text-center">
                <i class="fas fa-city text-primary text-2xl mb-2"></i>
                <p class="font-medium" data-lang-key="braga">Braga</p>
            </a>
            <a href="#" class="bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition text-center">
                <i class="fas fa-city text-primary text-2xl mb-2"></i>
                <p class="font-medium" data-lang-key="coimbra">Coimbra</p>
            </a>
            <a href="#" class="bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition text-center">
                <i class="fas fa-city text-primary text-2xl mb-2"></i>
                <p class="font-medium" data-lang-key="aveiro">Aveiro</p>
            </a>
        </div>

        <div class="text-center">
            <a href="#" class="inline-block text-primary font-semibold hover:text-red-800 transition" data-lang-key="view_locations">View all locations <i class="fas fa-arrow-right ml-2"></i></a>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-16 bg-gradient-to-r from-primary to-red-700 text-white">
    <div class="container mx-auto px-4 text-center">
        <h2 class="text-3xl font-bold mb-4" data-lang-key="ready_join">Ready to Join Our Community?</h2>
        <p class="text-xl mb-8 max-w-2xl mx-auto" data-lang-key="ready_join_desc">Whether you want to rent a car or share your own, it's free to join CARBNB and start exploring.</p>
        <div class="flex flex-col sm:flex-row justify-center gap-4">
            <a href="#" class="px-8 py-4 bg-white text-primary font-bold rounded-lg hover:bg-gray-100 transition" data-lang-key="rent_car_cta">Rent a Car</a>
            <a href="#" class="px-8 py-4 bg-secondary text-white font-bold rounded-lg hover:bg-gray-800 transition" data-lang-key="list_car_cta">List Your Car</a>
        </div>
    </div>
</section>

</x-app-layout>

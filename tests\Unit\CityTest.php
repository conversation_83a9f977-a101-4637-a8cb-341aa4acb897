<?php

namespace Tests\Unit;

use App\Models\City;
use App\Models\State;
use App\Models\Vehicle;
use Tests\Unit\TestCase;

test('city has correct fillable attributes', function () {
    $city = new City();

    $fillable = [
        'name',
        'state_id',
        'latitude',
        'longitude',
        'cost',
        'is_active'
    ];

    expect($city->getFillable())->toBe($fillable);
});

test('city has correct casts', function () {
    $city = new City();

    $expectedCasts = [
        'latitude' => 'decimal:7',
        'longitude' => 'decimal:7',
        'is_active' => 'boolean'
    ];

    expect($city->getCasts())->toMatchArray($expectedCasts);
});

test('city has state relationship method', function () {
    $city = new City();

    expect(method_exists($city, 'state'))->toBeTrue();
});

test('city has vehicles relationship method', function () {
    $city = new City();

    expect(method_exists($city, 'vehicles'))->toBeTrue();
});

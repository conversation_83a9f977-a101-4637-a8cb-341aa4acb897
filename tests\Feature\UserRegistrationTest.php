<?php

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Role;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Create roles
    Role::create(['name' => 'admin']);
    Role::create(['name' => 'user']);
});

test('registration page can be rendered', function () {
    $response = $this->get('/register');
    
    $response->assertStatus(200);
});

test('new users can register', function () {
    $response = $this->post('/register', [
        'first_name' => '<PERSON>',
        'last_name' => 'Doe',
        'email' => '<EMAIL>',
        'password' => 'password',
        'password_confirmation' => 'password',
    ]);
    
    $this->assertAuthenticated();
    $response->assertRedirect(route('dashboard', absolute: false));
    
    $this->assertDatabaseHas('users', [
        'first_name' => '<PERSON>',
        'last_name' => 'Doe',
        'email' => '<EMAIL>',
    ]);
    
    // Check if user has the 'user' role
    $user = User::where('email', '<EMAIL>')->first();
    expect($user->hasRole('user'))->toBeTrue();
});

test('user cannot register with invalid email', function () {
    $response = $this->post('/register', [
        'first_name' => 'John',
        'last_name' => 'Doe',
        'email' => 'not-an-email',
        'password' => 'password',
        'password_confirmation' => 'password',
    ]);
    
    $response->assertSessionHasErrors(['email']);
    $this->assertGuest();
});

test('user cannot register with password confirmation mismatch', function () {
    $response = $this->post('/register', [
        'first_name' => 'John',
        'last_name' => 'Doe',
        'email' => '<EMAIL>',
        'password' => 'password',
        'password_confirmation' => 'different-password',
    ]);
    
    $response->assertSessionHasErrors(['password']);
    $this->assertGuest();
});

test('user cannot register with existing email', function () {
    // Create a user
    User::create([
        'first_name' => 'Existing',
        'last_name' => 'User',
        'email' => '<EMAIL>',
        'password' => bcrypt('password'),
    ]);
    
    $response = $this->post('/register', [
        'first_name' => 'John',
        'last_name' => 'Doe',
        'email' => '<EMAIL>',
        'password' => 'password',
        'password_confirmation' => 'password',
    ]);
    
    $response->assertSessionHasErrors(['email']);
    $this->assertGuest();
});

test('login page can be rendered', function () {
    $response = $this->get('/login');
    
    $response->assertStatus(200);
});

test('users can authenticate using the login screen', function () {
    $user = User::create([
        'first_name' => 'John',
        'last_name' => 'Doe',
        'email' => '<EMAIL>',
        'password' => bcrypt('password'),
    ]);
    
    $response = $this->post('/login', [
        'email' => '<EMAIL>',
        'password' => 'password',
    ]);
    
    $this->assertAuthenticated();
    $response->assertRedirect(route('dashboard', absolute: false));
});

test('users cannot authenticate with invalid password', function () {
    $user = User::create([
        'first_name' => 'John',
        'last_name' => 'Doe',
        'email' => '<EMAIL>',
        'password' => bcrypt('password'),
    ]);
    
    $this->post('/login', [
        'email' => '<EMAIL>',
        'password' => 'wrong-password',
    ]);
    
    $this->assertGuest();
});

test('users can logout', function () {
    $user = User::create([
        'first_name' => 'John',
        'last_name' => 'Doe',
        'email' => '<EMAIL>',
        'password' => bcrypt('password'),
    ]);
    
    $this->actingAs($user);
    $this->assertAuthenticated();
    
    $response = $this->post('/logout');
    
    $this->assertGuest();
    $response->assertRedirect('/');
});

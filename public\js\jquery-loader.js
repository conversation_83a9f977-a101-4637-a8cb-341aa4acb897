// Load jQuery from CDN
document.addEventListener('DOMContentLoaded', function() {
    if (window.jQuery) {
        console.log('jQuery is already loaded');
        document.dispatchEvent(new Event('jQueryLoaded'));
        return;
    }

    const script = document.createElement('script');
    script.src = 'https://code.jquery.com/jquery-3.7.1.min.js';
    script.integrity = 'sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=';
    script.crossOrigin = 'anonymous';

    script.onload = function() {
        console.log('jQuery loaded successfully');

        // Trigger a custom event that other scripts can listen for
        document.dispatchEvent(new Event('jQueryLoaded'));
    };

    document.head.appendChild(script);
});

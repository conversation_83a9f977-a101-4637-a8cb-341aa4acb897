<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;

class UsersController extends Controller
{
    public function index(Request $request)
    {
        $users = User::paginate(10); // Fetch users with the specified role
        $roles = Role::all(); // Fetch all roles for dropdown or filtering

        return view('backend.users.index', compact('users', 'roles'));
    }

    public function create()
    {
        $roles = Role::all(); // Provide roles for selection during creation
        return view('backend.users.create', compact('roles'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'role' => 'required|exists:roles,name',
        ]);

        $user = User::create([
            'first_name' => $validated['first_name'],
            'last_name' => $validated['last_name'],
            'email' => $validated['email'],
            'password' => bcrypt(random_int(0, 9999999)),
        ]);

        $user->assignRole($validated['role']);

        return redirect()->route('users.index', ['role' => $validated['role']])
            ->with('success', 'User created successfully!');
    }

    public function show(User $user)
    {
        $user->load('role');
        return view('backend.users.edit', compact('user'));
    }

    public function edit(User $user)
    {
        $roles = Role::all();
        return view('backend.users.edit', compact('user', 'roles'));
    }

    public function update(Request $request, $id)
    {
        try {
            $validated = $request->validate([
                'first_name' => 'required|string|max:255',
                'last_name' => 'required|string|max:255',
                'email' => 'required|email|unique:users,email,' . $id,
                'role' => 'required|exists:roles,name',
            ]);

            $user = User::findOrFail($id);
            $user->update([
                'first_name' => $validated['first_name'],
                'last_name' => $validated['last_name'],
                'email' => $validated['email'],
            ]);

            $user->syncRoles([$validated['role']]);

            return response()->json(['success' => true], 200);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function destroy(User $user)
    {
        $role = $user->roles->first()->name; // Fetch the role for redirection
        $user->delete();

        return redirect()->route('users.index', ['role' => $role])
            ->with('success', 'User deleted successfully!');
    }
}

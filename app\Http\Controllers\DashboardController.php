<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Booking;
use App\Models\Vehicle;
use App\Models\User;
use App\Models\Driver;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function index()
    {
        $user = auth()->user();
        $userRoles = $user->getRoleNames();

        // Get statistics based on user role
        $stats = $this->getStatsByRole($userRoles);

        // Get bookings for calendar view
        $bookings = $this->getBookingsForCalendar($userRoles);

        return view('dashboard', [
            'userRoles' => $userRoles,
            'stats' => $stats,
            'bookings' => $bookings
        ]);
    }

    /**
     * Get dashboard statistics based on user role
     *
     * @param Collection $userRoles
     * @return array
     */
    private function getStatsByRole($userRoles)
    {
        $stats = [];

        // Common stats for all users
        $stats['common'] = [
            'total_vehicles' => Vehicle::count(),
        ];

        // Admin specific stats
        if ($userRoles->contains('admin')) {
            $stats['admin'] = [
                'total_users' => User::count(),
                'total_bookings' => Booking::count(),
                'pending_approvals' => Vehicle::where('status', 'pending')->count(),
            ];
        }

        // Agent specific stats
        if ($userRoles->contains('agent')) {
            $stats['agent'] = [
                'managed_vehicles' => Vehicle::where('user_id', auth()->id())->count(),
                'active_vehicles' => Vehicle::where('user_id', auth()->id())->where('status', 'active')->count(),
                'pending_vehicles' => Vehicle::where('user_id', auth()->id())->where('status', 'pending')->count(),
            ];
        }

        // Regular user specific stats
        if ($userRoles->contains('user')) {
            $stats['user'] = [
                'my_vehicles' => Vehicle::where('user_id', auth()->id())->count(),
                'my_bookings' => Booking::where('user_id', auth()->id())->count(),
            ];
        }

        return $stats;
    }

    /**
     * Get bookings for calendar view based on user role
     *
     * @param Collection $userRoles
     * @return array
     */
    private function getBookingsForCalendar($userRoles)
    {
        $userId = auth()->id();
        $startDate = Carbon::now()->startOfMonth();
        $endDate = Carbon::now()->addMonths(1)->endOfMonth();

        $bookingsQuery = Booking::with(['vehicle', 'driver', 'driver.user', 'user', 'vehicle.user'])
            ->whereIn('status', ['confirmed', 'in_progress', 'completed']) // Only show successful bookings
            ->where(function($query) use ($startDate, $endDate) {
                $query->whereBetween('start_date', [$startDate, $endDate])
                      ->orWhereBetween('end_date', [$startDate, $endDate])
                      ->orWhere(function($query) use ($startDate, $endDate) {
                          $query->where('start_date', '<=', $startDate)
                                ->where('end_date', '>=', $endDate);
                      });
            });

        // Filter bookings based on user role
        if ($userRoles->contains('admin')) {
            // Admin can see all bookings
            $bookings = $bookingsQuery->get();
        } elseif ($userRoles->contains('agent')) {
            // Agent can see bookings for vehicles they manage
            $vehicleIds = Vehicle::where('user_id', $userId)->pluck('id');
            $bookings = $bookingsQuery->whereIn('vehicle_id', $vehicleIds)->get();
        } elseif ($userRoles->contains('driver')) {
            // Driver can see bookings where they are assigned
            $driverId = Driver::where('user_id', $userId)->value('id');
            if ($driverId) {
                $bookings = $bookingsQuery->where('driver_id', $driverId)->get();
            } else {
                $bookings = collect([]);
            }
        } else {
            // Regular user can see their own bookings and bookings for their vehicles
            $vehicleIds = Vehicle::where('user_id', $userId)->pluck('id');
            $bookings = $bookingsQuery->where(function($query) use ($userId, $vehicleIds) {
                $query->where('user_id', $userId)
                      ->orWhereIn('vehicle_id', $vehicleIds);
            })->get();
        }

        // Format bookings for calendar
        $formattedBookings = [];

        foreach ($bookings as $booking) {
            $vehicleInfo = $booking->vehicle ? $booking->vehicle->make . ' ' . $booking->vehicle->model : 'Unknown Vehicle';
            $driverInfo = $booking->driver ? $booking->driver->user->name : 'No Driver';
            $renterInfo = $booking->user ? $booking->user->name : 'Unknown User';
            $ownerInfo = $booking->vehicle && $booking->vehicle->user ? $booking->vehicle->user->name : 'Unknown Owner';

            // Determine event color based on booking type and status
            $backgroundColor = '#2196F3'; // Default blue for vehicle-only bookings
            $borderColor = '#2196F3';

            if ($booking->driver_id) {
                $backgroundColor = '#4CAF50'; // Green for bookings with driver
                $borderColor = '#4CAF50';
            }

            // Adjust color based on status
            switch ($booking->status) {
                case 'pending':
                    $borderColor = '#FFC107'; // Yellow border for pending
                    break;
                case 'confirmed':
                    $borderColor = '#4CAF50'; // Green border for confirmed
                    break;
                case 'in_progress':
                    $borderColor = '#9C27B0'; // Purple border for in progress
                    break;
                case 'completed':
                    $borderColor = '#2196F3'; // Blue border for completed
                    break;
                case 'cancelled':
                case 'rejected':
                    $backgroundColor = '#F44336'; // Red for cancelled/rejected
                    $borderColor = '#F44336';
                    break;
            }

            // Create formatted booking for calendar
            $formattedBookings[] = [
                'id' => $booking->id,
                'title' => $vehicleInfo . ($booking->driver_id ? ' (with driver)' : ''),
                'start' => $booking->start_date->format('Y-m-d'),
                'end' => $booking->end_date->addDay()->format('Y-m-d'), // Add a day for inclusive display
                'url' => route('booking.show', $booking->id),
                'backgroundColor' => $backgroundColor,
                'borderColor' => $borderColor,
                'extendedProps' => [
                    'vehicle' => $vehicleInfo,
                    'driver' => $driverInfo,
                    'status' => $booking->status,
                    'renter' => $renterInfo,
                    'owner' => $ownerInfo,
                    'amount' => $booking->total_amount,
                    'pickup' => $booking->pickup_location,
                    'return' => $booking->return_location,
                    'booking_number' => $booking->booking_number,
                    'has_driver' => $booking->driver_id ? true : false
                ]
            ];
        }

        return $formattedBookings;
    }
}

@props([
    'name' => 'file',
    'fieldName' => null,
    'label' => 'Upload File',
    'multiple' => false,
    'accept' => '*/*',
    'required' => false,
    'fileManagerEnabled' => true,
    'previewEnabled' => true,
    'fileIds' => '',
    'primaryFileId' => '',
    'allowedTypes' => ['image', 'document', 'spreadsheet', 'pdf']
])

<div x-data="fileUpload('{{ $fileIds }}', '{{ $primaryFileId }}', '{{ $name }}', '{{ $multiple ? 'true' : 'false' }}')">
<script type="text/javascript">
function fileUpload(fileIds, primaryFileId, name, multiple) {
    return {
        fileIds: fileIds || '',
        primaryFileId: primaryFileId || '',
        files: [],
        fileInputId: name + '_input_' + Math.random().toString(36).substr(2, 9),
        isActive: false,
        isUploading: false,

        init() {
            this.updateFilesList();

            window.addEventListener('files-selected', (event) => {
                if (this.isActive) {
                    this.handleFileSelection(event.detail.files);
                    this.isActive = false;
                }
            });
        },

        updateFilesList() {
            if (this.fileIds && typeof this.fileIds === 'string') {
                const ids = this.fileIds.split(',').filter(id => id.trim() !== '');
                if (ids.length > 0) {
                    fetch(`/admin/file-manager/batch?ids=${ids.join(',')}`, {
                        headers: {
                            'Accept': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success && data.files) {
                            this.files = data.files;
                            this.files.forEach(file => {
                                if (file.url) {
                                    file.fullUrl = this.getFullImageUrl(file.url);
                                }
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching files:', error);
                    });
                } else {
                    this.files = [];
                }
            } else {
                this.files = [];
            }
        },

        removeFile(fileId) {
            if (typeof this.fileIds === 'string') {
                const ids = this.fileIds.split(',').filter(id => id !== fileId);
                this.fileIds = ids.join(',');

                if (this.primaryFileId === fileId) {
                    this.primaryFileId = ids.length > 0 ? ids[0] : '';
                }

                this.$refs.fileIdsInput.value = this.fileIds;
                const event = new Event('change', { bubbles: true });
                this.$refs.fileIdsInput.dispatchEvent(event);

                if (this.$refs.primaryFileIdInput) {
                    this.$refs.primaryFileIdInput.value = this.primaryFileId;
                }
            }

            this.files = this.files.filter(file => file.id.toString() !== fileId.toString());
        },

        handleFileSelection(selectedFiles) {
            if (!selectedFiles || selectedFiles.length === 0) {
                return;
            }

            selectedFiles.forEach(file => {
                if (file.url && !file.fullUrl) {
                    file.fullUrl = this.getFullImageUrl(file.url);
                }
            });

            if (multiple === 'true') {
                const currentIds = this.fileIds ? this.fileIds.split(',').filter(id => id.trim() !== '') : [];
                const newIds = selectedFiles.map(file => file.id);
                const combinedIds = [...new Set([...currentIds, ...newIds])];
                this.fileIds = combinedIds.join(',');

                if (!this.primaryFileId && combinedIds.length > 0) {
                    this.primaryFileId = combinedIds[0];
                }
            } else {
                this.fileIds = selectedFiles[0].id;
                this.primaryFileId = selectedFiles[0].id;
            }

            if (this.$refs.fileIdsInput) {
                this.$refs.fileIdsInput.value = this.fileIds;
                const event = new Event('change', { bubbles: true });
                this.$refs.fileIdsInput.dispatchEvent(event);
            }

            if (this.$refs.primaryFileIdInput) {
                this.$refs.primaryFileIdInput.value = this.primaryFileId;
            }

            this.updateFilesList();
        },

        setPrimaryFile(fileId) {
            this.primaryFileId = fileId;
            if (this.$refs.primaryFileIdInput) {
                this.$refs.primaryFileIdInput.value = this.primaryFileId;
                const event = new Event('change', { bubbles: true });
                this.$refs.primaryFileIdInput.dispatchEvent(event);
            }
        },

        openFileManager() {
            this.isActive = true;
            const modal = document.getElementById('file-manager-modal');
            if (modal) {
                modal.classList.remove('hidden');
                window.dispatchEvent(new CustomEvent('open-file-manager'));
            }
        },

        uploadFiles() {
            const input = document.getElementById(this.fileInputId);
            if (input.files.length === 0) return;

            const formData = new FormData();
            for (let i = 0; i < input.files.length; i++) {
                formData.append('files[]', input.files[i]);
            }

            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            if (!csrfToken) {
                alert('Error: CSRF token not found. Please refresh the page and try again.');
                return;
            }

            formData.append('_token', csrfToken.getAttribute('content'));
            this.isUploading = true;

            fetch('/admin/file-manager', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': csrfToken.getAttribute('content')
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    this.handleFileSelection(data.files);
                    input.value = '';
                } else {
                    alert('Upload failed: ' + (data.message || 'Unknown error'));
                }
                this.isUploading = false;
            })
            .catch(error => {
                alert('Error uploading files: ' + error.message);
                this.isUploading = false;
            });
        },

        getFullImageUrl(url) {
            if (!url) return '';
            if (url.startsWith('http://') || url.startsWith('https://')) {
                return url;
            }
            const baseUrl = '{{ config('app.url') }}';
            const cleanBaseUrl = baseUrl.replace(/\/$/, '');
            if (url.startsWith('/')) {
                return `${cleanBaseUrl}${url}`;
            }
            return `${cleanBaseUrl}/${url}`;
        },

        handleImageError(event) {
            event.target.src = '/images/fallback-image.jpg';
            event.target.classList.add('image-error');
        }
    };
}
</script>

    <!-- Label -->
    <label for="{{ $name }}" class="block text-sm font-medium text-gray-700 mb-1">{{ $label }}</label>

    <!-- Hidden inputs to store file IDs -->
    <input type="hidden" name="{{ $fieldName ? $fieldName : $name }}" x-ref="fileIdsInput" :value="fileIds">
    @if($multiple)
        <input type="hidden" name="{{ $fieldName ? $fieldName . '_primary' : $name . '_primary' }}" x-ref="primaryFileIdInput" :value="primaryFileId">
    @endif

    <!-- File input and buttons -->
    <div class="flex flex-wrap gap-2 mb-2">
        <div class="flex-grow">
            <input
                type="file"
                :id="fileInputId"
                name="{{ $fieldName ? $fieldName . '_file' : $name . '_file' }}"
                class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                {{ $multiple ? 'multiple' : '' }}
                accept="{{ $accept }}"
                {{ $required ? 'required' : '' }}
                @change="uploadFiles()"
            >
        </div>

        @if($fileManagerEnabled)
        <button
            type="button"
            @click="openFileManager()"
            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
            <i class="fas fa-folder-open mr-2"></i>
            Browse Files
        </button>
        @endif
    </div>

    <!-- Loading indicator -->
    <div x-show="isUploading" class="flex items-center text-sm text-gray-500 my-2">
        <i class="fas fa-spinner fa-spin mr-2"></i>
        Uploading files...
    </div>

    <!-- File Preview Section -->
    <div x-show="files && files.length > 0" class="mt-3">
        <h4 class="text-sm font-medium text-gray-700 mb-2">Selected Files</h4>
        <div class="space-y-2">
            <template x-for="file in files" :key="file.id">
                <div class="flex items-center justify-between p-3 border rounded-md bg-gray-50">
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 bg-gray-200 rounded flex items-center justify-center">
                            <i class="fas fa-file text-gray-400"></i>
                        </div>
                        <div>
                            <div class="text-sm font-medium" x-text="file.title"></div>
                            <div class="text-xs text-gray-500" x-text="file.fileType || 'Unknown'"></div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button
                            @click="removeFile(file.id)"
                            class="px-3 py-1 text-xs bg-red-500 text-white rounded hover:bg-red-600"
                            type="button"
                        >
                            Remove
                        </button>
                    </div>
                </div>
            </template>
        </div>
    </div>
</div>

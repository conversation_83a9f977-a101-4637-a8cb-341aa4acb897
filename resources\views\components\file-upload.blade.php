@props([
    'name' => 'file', // This is the ID of the component, not the name of the input
    'fieldName' => null, // Actual name of the input field
    'label' => 'Upload File',
    'multiple' => false,
    'accept' => '*/*',
    'required' => false,
    'fileManagerEnabled' => true,
    'previewEnabled' => true,
    'fileIds' => '',
    'primaryFileId' => '',
    'allowedTypes' => ['image', 'document', 'spreadsheet', 'pdf']
])

<div x-data="fileUpload('{{ $fileIds }}', '{{ $primaryFileId }}', '{{ $name }}', '{{ $multiple ? 'true' : 'false' }}')">
<script type="text/javascript">
function fileUpload(fileIds, primaryFileId, name, multiple) {
    return {
        fileIds: fileIds || '',
        primaryFileId: primaryFileId || '',
        files: [],
        fileInputId: name + '_input_' + Math.random().toString(36).substr(2, 9),
        isActive: false,
        isUploading: false,

        init() {
            this.updateFilesList();

            // Listen for file manager selection events
            window.addEventListener('files-selected', (event) => {
                if (this.isActive) {
                    this.handleFileSelection(event.detail.files);
                    this.isActive = false;
                }
            });
        },

        updateFilesList() {
            if (this.fileIds && typeof this.fileIds === 'string') {
                const ids = this.fileIds.split(',').filter(id => id.trim() !== '');
                if (ids.length > 0) {
                    // Fetch file details from the server
                    fetch(`/admin/file-manager/batch?ids=${ids.join(',')}`, {
                        headers: {
                            'Accept': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success && data.files) {
                            this.files = data.files;

                            // Process each file to ensure URLs are correct
                            this.files.forEach(file => {
                                // Make sure the URL is properly formatted
                                if (file.url) {
                                    // Store the full URL
                                    file.fullUrl = this.getFullImageUrl(file.url);
                                }
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching files:', error);
                    });
                } else {
                    this.files = [];
                }
            } else {
                this.files = [];
            }
        },

        removeFile(fileId) {
            // Remove the file from the selected files
            this.selectedFiles = this.selectedFiles.filter(file => file.id.toString() !== fileId.toString());

            // Update the selected file IDs
            const fileIds = this.selectedFiles.map(file => file.id);
            this.selectedFileIds = fileIds.join(',');

            // Update the hidden input value
            document.getElementById('{{ $name }}').value = this.selectedFileIds;

            // Dispatch event for Alpine.js components to listen for
            window.dispatchEvent(new CustomEvent('file-ids-updated', {
                detail: {
                    name: '{{ $name }}',
                    value: this.selectedFileIds,
                    files: this.selectedFiles
                }
            }));
        },

        handleFileSelection(selectedFiles) {
            if (!selectedFiles || selectedFiles.length === 0) {
                console.log('No files selected');
                return;
            }

            console.log('Selected files:', selectedFiles);

            // Make sure all selected files have fullUrl property
            selectedFiles.forEach(file => {
                if (file.url && !file.fullUrl) {
                    file.fullUrl = this.getFullImageUrl(file.url);
                }
            });

            if (multiple === 'true') {
                // For multiple files, append to existing list
                const currentIds = this.fileIds ? this.fileIds.split(',').filter(id => id.trim() !== '') : [];
                const newIds = selectedFiles.map(file => file.id);

                console.log('Current IDs:', currentIds);
                console.log('New IDs:', newIds);

                // Combine and remove duplicates
                const combinedIds = [...new Set([...currentIds, ...newIds])];
                this.fileIds = combinedIds.join(',');

                console.log('Combined IDs:', combinedIds);

                // If no primary file is set, set the first one
                if (!this.primaryFileId && combinedIds.length > 0) {
                    this.primaryFileId = combinedIds[0];
                    console.log('Setting primary file ID:', this.primaryFileId);
                }
            } else {
                // For single file, replace existing
                this.fileIds = selectedFiles[0].id;
                this.primaryFileId = selectedFiles[0].id;
                console.log('Single file selected, ID:', this.fileIds);
            }

            // Update hidden inputs
            if (this.$refs.fileIdsInput) {
                this.$refs.fileIdsInput.value = this.fileIds;

                // Manually trigger change event on the input to ensure it's detected
                const event = new Event('change', { bubbles: true });
                this.$refs.fileIdsInput.dispatchEvent(event);
            }

            if (this.$refs.primaryFileIdInput) {
                this.$refs.primaryFileIdInput.value = this.primaryFileId;
            }

            // Update files list
            this.updateFilesList();
        },

        removeFile(fileId) {
            if (typeof this.fileIds === 'string') {
                const ids = this.fileIds.split(',').filter(id => id !== fileId);
                this.fileIds = ids.join(',');

                // If removed file was primary, update primary
                if (this.primaryFileId === fileId) {
                    this.primaryFileId = ids.length > 0 ? ids[0] : '';
                }

                // Update hidden inputs
                this.$refs.fileIdsInput.value = this.fileIds;

                // Manually trigger change event on the input
                const event = new Event('change', { bubbles: true });
                this.$refs.fileIdsInput.dispatchEvent(event);

                if (this.$refs.primaryFileIdInput) {
                    this.$refs.primaryFileIdInput.value = this.primaryFileId;
                }
            }

            // Update files list
            this.files = this.files.filter(file => file.id.toString() !== fileId.toString());
        },

        setPrimaryFile(fileId) {
            this.primaryFileId = fileId;
            if (this.$refs.primaryFileIdInput) {
                this.$refs.primaryFileIdInput.value = this.primaryFileId;

                // Manually trigger change event on the input
                const event = new Event('change', { bubbles: true });
                this.$refs.primaryFileIdInput.dispatchEvent(event);
            }
        },

        openFileManager() {
            this.isActive = true;
            const modal = document.getElementById('file-manager-modal');
            if (modal) {
                modal.classList.remove('hidden');
                // Dispatch event to load files
                window.dispatchEvent(new CustomEvent('open-file-manager'));
            }
        },

        uploadFiles() {
            const input = document.getElementById(this.fileInputId);
            if (input.files.length === 0) return;

            console.log('Uploading files from input:', input.files.length, 'files');

            const formData = new FormData();
            for (let i = 0; i < input.files.length; i++) {
                formData.append('files[]', input.files[i]);
                console.log('Adding file to upload:', input.files[i].name, input.files[i].type, input.files[i].size);
            }

            // Add CSRF token
            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            if (!csrfToken) {
                console.error('CSRF token not found');
                alert('Error: CSRF token not found. Please refresh the page and try again.');
                return;
            }

            formData.append('_token', csrfToken.getAttribute('content'));

            // Show loading state
            this.isUploading = true;

            fetch('/admin/file-manager', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': csrfToken.getAttribute('content')
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Upload response:', data);
                if (data.success) {
                    // Handle successful upload
                    this.handleFileSelection(data.files);
                    // Reset file input
                    input.value = '';
                } else {
                    console.error('Upload failed:', data.message || 'Unknown error');
                    alert('Upload failed: ' + (data.message || 'Unknown error'));
                }
                this.isUploading = false;
            })
            .catch(error => {
                console.error('Error uploading files:', error);
                alert('Error uploading files: ' + error.message);
                this.isUploading = false;
            });
        },

        // Helper function to get the full image URL
        getFullImageUrl(url) {
            if (!url) return '';

            // If the URL already starts with http:// or https://, return it as is
            if (url.startsWith('http://') || url.startsWith('https://')) {
                return url;
            }

            // If the URL starts with a slash, it's a relative URL
            // Prepend the APP_URL from the environment
            const baseUrl = '{{ config('app.url') }}';
            const cleanBaseUrl = baseUrl.replace(/\/$/, ''); // Remove trailing slash if present

            // If the URL already starts with a slash, just append it to the base URL
            if (url.startsWith('/')) {
                return `${cleanBaseUrl}${url}`;
            }

            // Otherwise, add a slash between the base URL and the relative URL
            return `${cleanBaseUrl}/${url}`;
        },

        // Handle image loading errors
        handleImageError(event) {
            console.error('Image failed to load:', event.target.src);
            // Replace with a fallback image
            event.target.src = '/images/fallback-image.jpg';
            // Add a class to indicate the error
            event.target.classList.add('image-error');
        }
    };
}
</script>
    <!-- Label -->
    <label for="{{ $name }}" class="block text-sm font-medium text-gray-700 mb-1">{{ $label }}</label>

    <!-- Hidden inputs to store file IDs -->
    <input type="hidden" name="{{ $fieldName ? $fieldName : $name }}" x-ref="fileIdsInput" :value="fileIds"
        @change="$dispatch('file-ids-updated', { name: '{{ $name }}', value: $event.target.value })">
    @if($multiple)
        <input type="hidden" name="{{ $fieldName ? $fieldName . '_primary' : $name . '_primary' }}" x-ref="primaryFileIdInput" :value="primaryFileId">
    @endif

    <!-- File input and buttons -->
    <div class="flex flex-wrap gap-2 mb-2">
        <!-- Direct file upload -->
        <div class="flex-grow">
            <input
                type="file"
                :id="fileInputId"
                name="{{ $fieldName ? $fieldName . '_file' : $name . '_file' }}"
                class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                {{ $multiple ? 'multiple' : '' }}
                accept="{{ $accept }}"
                {{ $required ? 'required' : '' }}
                @change="uploadFiles()"
            >
        </div>

        <!-- File Manager Button -->
        @if($fileManagerEnabled)
        <button
            type="button"
            @click="openFileManager()"
            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
            <i class="fas fa-folder-open mr-2"></i>
            Browse Files
        </button>
        @endif
    </div>

    <!-- Loading indicator -->
    <div x-show="isUploading" class="flex items-center text-sm text-gray-500 my-2">
        <i class="fas fa-spinner fa-spin mr-2"></i>
        Uploading files...
    </div>

    <!-- File Preview Section -->
    <div x-show="files && files.length > 0" class="mt-3">
        <h4 class="text-sm font-medium text-gray-700 mb-2">Selected Files</h4>

        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
            <template x-for="file in files" :key="file.id">
                <div class="relative border rounded-md overflow-hidden group">
                    <!-- File Preview -->
                    <template x-if="file.fileType && file.fileType.startsWith('image/')">
                        <img :src="file.fullUrl || getFullImageUrl(file.url)" class="w-full h-24 object-cover" @error="handleImageError($event)">
                    </template>
                    <template x-if="!file.fileType || !file.fileType.startsWith('image/')">
                        <div class="w-full h-24 bg-gray-100 flex items-center justify-center">
                            <template x-if="file.fileType && file.fileType.includes('pdf')">
                                <i class="fas fa-file-pdf text-red-400 text-3xl"></i>
                            </template>
                            <template x-if="file.fileType && file.fileType.includes('word')">
                                <i class="fas fa-file-word text-blue-400 text-3xl"></i>
                            </template>
                            <template x-if="file.fileType && file.fileType.includes('excel')">
                                <i class="fas fa-file-excel text-green-400 text-3xl"></i>
                            </template>
                            <template x-if="(!file.fileType || (!file.fileType.includes('pdf') && !file.fileType.includes('word') && !file.fileType.includes('excel')))">
                                <i class="fas fa-file text-gray-400 text-3xl"></i>
                            </template>
                        </div>
                    </template>

                    <!-- File Info -->
                    <div class="p-2 text-xs truncate" x-text="file.title"></div>

                    <!-- Actions -->
                    <div class="absolute top-1 right-1 flex space-x-1">
                        <!-- Primary marker/button (only for multiple files) -->
                        <template x-if="multiple === 'true'">
                            <button
                                @click.stop="setPrimaryFile(file.id)"
                                class="p-1 rounded-full transition"
                                :class="primaryFileId == file.id ? 'bg-yellow-500 text-white' : 'bg-gray-200 text-gray-600 opacity-70 hover:opacity-100'"
                                title="Set as primary image"
                            >
                                <i class="fas fa-star text-xs"></i>
                            </button>
                        </template>

                        <!-- Remove button -->
                        <button
                            @click.stop="removeFile(file.id)"
                            class="p-1 rounded-full bg-red-500 text-white opacity-70 hover:opacity-100 transition"
                            title="Remove file"
                        >
                            <i class="fas fa-times text-xs"></i>
                        </button>
                    </div>
                </div>
            </template>
        </div>
    </div>
</div>
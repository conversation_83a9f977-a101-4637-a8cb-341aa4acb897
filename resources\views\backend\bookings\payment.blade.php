<x-app-backend-layout>
    <div class="container py-8 payment-container">
        <div class="mb-6">
            <a href="{{ route('booking.show', $booking->id) }}" class="text-primary hover:text-primary-dark flex items-center">
                <i class="fas fa-arrow-left mr-1"></i>
                Back to Booking Details
            </a>
        </div>

        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-800">Payment for Booking #{{ $booking->booking_number }}</h2>
            </div>

            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Booking Summary -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="text-lg font-semibold text-gray-800 mb-3">Booking Summary</h3>
                        <div class="flex items-start mb-4">
                            <div class="flex-shrink-0 h-16 w-16 mr-4">
                                @if($booking->vehicle->primaryImage)
                                    <img class="h-16 w-16 rounded-md object-cover" src="{{ asset('storage/' . $booking->vehicle->primaryImage->file->path) }}" alt="{{ $booking->vehicle->make }} {{ $booking->vehicle->model }}">
                                @else
                                    <div class="h-16 w-16 rounded-md bg-gray-200 flex items-center justify-center">
                                        <i class="fas fa-car text-gray-400 text-2xl"></i>
                                    </div>
                                @endif
                            </div>
                            <div>
                                <h4 class="text-md font-medium text-gray-900">{{ $booking->vehicle->make }} {{ $booking->vehicle->model }}</h4>
                                <p class="text-sm text-gray-600">{{ $booking->vehicle->year }} · {{ $booking->vehicle->color }}</p>
                                <p class="text-sm text-gray-600 mt-1">
                                    <span class="font-medium">Owner:</span> {{ $booking->vehicle->user->first_name }} {{ $booking->vehicle->user->last_name }}
                                </p>
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-3">
                            <div>
                                <p class="text-sm text-gray-600">Pickup Date</p>
                                <p class="text-md font-medium">{{ Carbon\Carbon::parse($booking->start_date)->format('M d, Y') }}</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">Return Date</p>
                                <p class="text-md font-medium">{{ Carbon\Carbon::parse($booking->end_date)->format('M d, Y') }}</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">Duration</p>
                                <p class="text-md font-medium">{{ $booking->duration_in_days }} days</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">Status</p>
                                <p class="text-md font-medium">{{ ucfirst(str_replace('_', ' ', $booking->status)) }}</p>
                            </div>
                        </div>

                        <div class="mt-4 pt-4 border-t border-gray-200">
                            <div class="flex justify-between mb-2">
                                <span class="text-sm text-gray-600">Vehicle Rental</span>
                                <span class="text-sm font-medium">{{ config('payment.currency_symbol') }}{{ number_format($booking->vehicle_price, 2) }}</span>
                            </div>
                            @if($booking->driver_price > 0)
                            <div class="flex justify-between mb-2">
                                <span class="text-sm text-gray-600">Driver Service</span>
                                <span class="text-sm font-medium">{{ config('payment.currency_symbol') }}{{ number_format($booking->driver_price, 2) }}</span>
                            </div>
                            @endif
                            @if($booking->discount_amount > 0)
                            <div class="flex justify-between mb-2">
                                <span class="text-sm text-gray-600">Discount</span>
                                <span class="text-sm font-medium text-green-600">-{{ config('payment.currency_symbol') }}{{ number_format($booking->discount_amount, 2) }}</span>
                            </div>
                            @endif
                            <div class="flex justify-between mb-2">
                                <span class="text-sm text-gray-600">Subtotal</span>
                                <span class="text-sm font-medium">{{ config('payment.currency_symbol') }}{{ number_format($booking->subtotal, 2) }}</span>
                            </div>
                            <div class="flex justify-between mb-2">
                                <span class="text-sm text-gray-600">Tax (10%)</span>
                                <span class="text-sm font-medium">{{ config('payment.currency_symbol') }}{{ number_format($booking->tax_amount, 2) }}</span>
                            </div>
                            <div class="flex justify-between mb-2 pt-2 border-t border-gray-200">
                                <span class="text-md font-semibold">Total</span>
                                <span class="text-md font-semibold">{{ config('payment.currency_symbol') }}{{ number_format($booking->total_amount, 2) }}</span>
                            </div>
                            <div class="flex justify-between mb-2">
                                <span class="text-sm text-gray-600">Security Deposit (Refundable)</span>
                                <span class="text-sm font-medium">{{ config('payment.currency_symbol') }}{{ number_format($booking->security_deposit, 2) }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Methods -->
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-3">Select Payment Method</h3>

                        <div class="bg-white border rounded-lg overflow-hidden">
                            <div class="p-4 border-b">
                                <div class="flex items-center">
                                    <input id="payment_pagseguro" name="payment_method" type="radio" checked class="h-4 w-4 text-primary focus:ring-primary-light border-gray-300">
                                    <label for="payment_pagseguro" class="ml-3 block text-sm font-medium text-gray-700">
                                        PagSeguro
                                    </label>
                                </div>
                            </div>

                            <!-- PagSeguro Payment Form -->
                            <div id="pagseguro_payment_form" class="payment-form p-4">
                                <div class="bg-blue-50 p-4 rounded-md mb-6">
                                    <div class="flex">
                                        <i class="fas fa-info-circle text-blue-400 mr-2 mt-1"></i>
                                        <div>
                                            <p class="text-sm text-blue-700">You'll be redirected to PagSeguro's secure payment page to complete your payment.</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-4">
                                    <button type="button" id="pagseguro_pay_button" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-light">
                                        Pay R${{ number_format($booking->total_amount, 2) }}
                                    </button>
                                </div>
                            </div>

                            <div class="p-4 border-b border-t">
                                <div class="flex items-center">
                                    <input id="payment_razorpay" name="payment_method" type="radio" class="h-4 w-4 text-primary focus:ring-primary-light border-gray-300">
                                    <label for="payment_razorpay" class="ml-3 block text-sm font-medium text-gray-700">
                                        Razorpay
                                    </label>
                                </div>
                            </div>

                            <!-- Razorpay Payment Form -->
                            <div id="razorpay_payment_form" class="payment-form p-4" style="display: none;">
                                <div class="bg-blue-50 p-4 rounded-md mb-6">
                                    <div class="flex">
                                        <i class="fas fa-info-circle text-blue-400 mr-2 mt-1"></i>
                                        <div>
                                            <p class="text-sm text-blue-700">You'll be redirected to Razorpay's secure payment page to complete your payment.</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-4">
                                    <button type="button" id="razorpay_pay_button" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-light">
                                        Pay {{ config('payment.currency_symbol') }}{{ number_format($booking->total_amount, 2) }}
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="mt-6">
                            <h4 class="text-md font-medium text-gray-900 mb-2">Payment Notes</h4>
                            <ul class="list-disc pl-5 text-sm text-gray-600 space-y-1">
                                <li>Your payment is secure and encrypted.</li>
                                <li>You will receive a receipt via email after successful payment.</li>
                                <li>The security deposit will be refunded after the rental period if there are no damages or additional charges.</li>
                                <li>For any payment issues, please contact our support team.</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<!-- PagSeguro Payment Form (Hidden) -->
<form action="{{ route('pagseguro.payment') }}" method="POST" id="pagseguro_hidden_form">
    @csrf
    <input type="hidden" name="pagseguro_order_id" id="pagseguro_order_id">
    <input type="hidden" name="pagseguro_payment_id" id="pagseguro_payment_id">
    <input type="hidden" name="booking_id" value="{{ $booking->id }}">
</form>

<!-- Razorpay Payment Form (Hidden) -->
<form action="{{ route('razorpay.payment') }}" method="POST" id="razorpay_hidden_form">
    @csrf
    <input type="hidden" name="razorpay_payment_id" id="razorpay_payment_id">
    <input type="hidden" name="razorpay_order_id" id="razorpay_order_id">
    <input type="hidden" name="razorpay_signature" id="razorpay_signature">
    <input type="hidden" name="booking_id" value="{{ $booking->id }}">
</form>

@push('scripts')
<script src="https://checkout.razorpay.com/v1/checkout.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Payment method selection
        const paymentOptions = document.querySelectorAll('input[name="payment_method"]');
        const paymentForms = document.querySelectorAll('.payment-form');

        paymentOptions.forEach(option => {
            option.addEventListener('change', function() {
                const selectedMethod = this.id.replace('payment_', '');

                // Hide all payment forms
                paymentForms.forEach(form => {
                    form.style.display = 'none';
                });

                // Show selected payment form
                document.getElementById(selectedMethod + '_payment_form').style.display = 'block';
            });
        });

        // Initialize PagSeguro payment
        const pagseguroButton = document.getElementById('pagseguro_pay_button');

        pagseguroButton.addEventListener('click', function() {
            // Show loading state
            pagseguroButton.disabled = true;
            pagseguroButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Processing...';

            // Create PagSeguro order
            fetch('{{ route('pagseguro.create-order', $booking->id) }}')
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(errorData => {
                            throw new Error(errorData.error || 'Failed to create payment order');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.error) {
                        // Handle error
                        console.error(data.error);

                        // Show error message in a more user-friendly way
                        const errorDiv = document.createElement('div');
                        errorDiv.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4';
                        errorDiv.innerHTML = `<strong>Error:</strong> ${data.error}`;
                        document.querySelector('.payment-container').prepend(errorDiv);

                        // Reset button
                        pagseguroButton.disabled = false;
                        pagseguroButton.innerHTML = 'Pay R${{ number_format($booking->total_amount, 2) }}';

                        return;
                    }

                    // If there's a checkout URL, redirect to PagSeguro checkout page
                    if (data.checkout_url) {
                        // Set form values
                        document.getElementById('pagseguro_order_id').value = data.order_id;
                        document.getElementById('pagseguro_payment_id').value = 'pending';

                        // Redirect to PagSeguro checkout
                        window.location.href = data.checkout_url;
                    } else {
                        // Handle direct payment flow (if needed)
                        document.getElementById('pagseguro_order_id').value = data.order_id;
                        document.getElementById('pagseguro_payment_id').value = data.payment_id || 'direct';

                        // Submit the form
                        document.getElementById('pagseguro_hidden_form').submit();
                    }
                })
                .catch(error => {
                    console.error('Error:', error);

                    // Show error message
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4';
                    errorDiv.innerHTML = `<strong>Error:</strong> ${error.message}`;
                    document.querySelector('.payment-container').prepend(errorDiv);

                    // Reset button
                    pagseguroButton.disabled = false;
                    pagseguroButton.innerHTML = 'Pay R${{ number_format($booking->total_amount, 2) }}';
                });
        });

        // Initialize Razorpay payment
        const razorpayButton = document.getElementById('razorpay_pay_button');

        razorpayButton.addEventListener('click', function() {
            // Show loading state
            razorpayButton.disabled = true;
            razorpayButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Processing...';

            // Create Razorpay order
            fetch('{{ route('razorpay.create-order', $booking->id) }}')
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(errorData => {
                            throw new Error(errorData.error || 'Failed to create payment order');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.error) {
                        // Handle error
                        console.error(data.error);

                        // Show error message in a more user-friendly way
                        const errorDiv = document.createElement('div');
                        errorDiv.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4';
                        errorDiv.innerHTML = `<strong>Error:</strong> ${data.error}`;
                        document.querySelector('.payment-container').prepend(errorDiv);

                        // Reset button
                        razorpayButton.disabled = false;
                        razorpayButton.innerHTML = 'Pay {{ config('payment.currency_symbol') }}{{ number_format($booking->total_amount, 2) }}';

                        return;
                    }

                    // Initialize Razorpay
                    const options = {
                        key: '{{ config('razorpay.key') }}',
                        amount: data.amount,
                        currency: data.currency,
                        name: 'Car Rental',
                        description: 'Booking #{{ $booking->booking_number }}',
                        order_id: data.order_id,
                        handler: function(response) {
                            // Set form values
                            document.getElementById('razorpay_payment_id').value = response.razorpay_payment_id;
                            document.getElementById('razorpay_order_id').value = response.razorpay_order_id;
                            document.getElementById('razorpay_signature').value = response.razorpay_signature;

                            // Submit the form
                            document.getElementById('razorpay_hidden_form').submit();
                        },
                        prefill: {
                            name: '{{ Auth::user()->first_name }} {{ Auth::user()->last_name }}',
                            email: '{{ Auth::user()->email }}',
                        },
                        theme: {
                            color: '#3B82F6',
                        },
                        modal: {
                            ondismiss: function() {
                                // Reset button
                                razorpayButton.disabled = false;
                                razorpayButton.innerHTML = 'Pay {{ config('payment.currency_symbol') }}{{ number_format($booking->total_amount, 2) }}';
                            }
                        }
                    };

                    const rzp = new Razorpay(options);
                    rzp.open();
                })
                .catch(error => {
                    console.error('Error:', error);

                    // Show error message
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4';
                    errorDiv.innerHTML = `<strong>Error:</strong> ${error.message}`;
                    document.querySelector('.payment-container').prepend(errorDiv);

                    // Reset button
                    razorpayButton.disabled = false;
                    razorpayButton.innerHTML = 'Pay {{ config('payment.currency_symbol') }}{{ number_format($booking->total_amount, 2) }}';
                });
        });
    });
</script>
@endpush
</x-app-backend-layout>

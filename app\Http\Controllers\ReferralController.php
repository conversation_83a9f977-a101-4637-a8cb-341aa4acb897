<?php

namespace App\Http\Controllers;

use App\Models\Referral;
use App\Models\User;
use App\Models\Vehicle;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class ReferralController extends Controller
{
    /**
     * Display the referral dashboard.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $user = Auth::user();
        
        // Generate referral code if user doesn't have one
        if (!$user->referral_code) {
            $user->generateReferralCode();
            $user->refresh();
        }
        
        $sentReferrals = $user->sentReferrals()->latest()->get();
        $referredUsers = $user->referredUsers;
        
        return view('referrals.index', compact('user', 'sentReferrals', 'referredUsers'));
    }
    
    /**
     * Send a referral invitation.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function send(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|max:255',
        ]);
        
        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        
        $user = Auth::user();
        $email = $request->email;
        
        // Check if the email is already registered
        if (User::where('email', $email)->exists()) {
            return redirect()->back()
                ->with('error', 'This email is already registered with us.');
        }
        
        // Check if a referral has already been sent to this email
        $existingReferral = Referral::where('referrer_id', $user->id)
            ->where('email', $email)
            ->where('status', 'pending')
            ->first();
        
        if ($existingReferral) {
            return redirect()->back()
                ->with('info', 'You have already sent an invitation to this email.');
        }
        
        // Generate referral code if user doesn't have one
        if (!$user->referral_code) {
            $user->generateReferralCode();
            $user->refresh();
        }
        
        // Create a new referral
        $referral = Referral::create([
            'referrer_id' => $user->id,
            'email' => $email,
            'token' => Referral::generateToken(),
            'status' => 'pending',
        ]);
        
        // Send the invitation email
        $this->sendReferralEmail($referral);
        
        return redirect()->route('referrals.index')
            ->with('success', 'Invitation sent successfully!');
    }
    
    /**
     * Handle the referral registration page.
     *
     * @param  string  $token
     * @return \Illuminate\View\View
     */
    public function register($token)
    {
        $referral = Referral::where('token', $token)->first();
        
        if (!$referral || $referral->status !== 'pending') {
            abort(404);
        }
        
        return view('auth.register', ['referral' => $referral]);
    }
    
    /**
     * Send the referral invitation email.
     *
     * @param  \App\Models\Referral  $referral
     * @return void
     */
    private function sendReferralEmail(Referral $referral)
    {
        $referrer = $referral->referrer;
        $referralLink = $referral->getReferralLink();
        
        $data = [
            'referrer' => $referrer,
            'referralLink' => $referralLink,
        ];
        
        Mail::send('emails.referral-invitation', $data, function ($message) use ($referral) {
            $message->to($referral->email)
                ->subject($referral->referrer->first_name . ' ' . $referral->referrer->last_name . ' invited you to join Carbnb');
        });
    }
    
    /**
     * Track when a referred user adds a vehicle.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\Vehicle  $vehicle
     * @return void
     */
    public static function trackVehicleAdded(User $user, Vehicle $vehicle)
    {
        // Find the referral for this user
        $referral = Referral::where('registered_user_id', $user->id)
            ->where('status', 'registered')
            ->first();
        
        if ($referral) {
            $referral->markAsCompleted();
        }
    }
}

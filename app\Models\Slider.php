<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Slider extends Model
{
    protected $fillable = [
        'name', 
        'description', 
        'is_active'
    ];

    /**
     * Get all slides for this slider
     */
    public function slides(): HasMany
    {
        return $this->hasMany(Slide::class)
                    ->where('is_active', true)
                    ->orderBy('order');
    }
}
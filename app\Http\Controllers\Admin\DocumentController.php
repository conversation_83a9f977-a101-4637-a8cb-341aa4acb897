<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Document;
use App\Models\Driver;
use App\Models\DriverLicense;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DocumentController extends Controller
{
    /**
     * Display a listing of the documents.
     */
    public function index(Request $request)
    {
        $status = $request->input('status', 'pending');

        $documents = Document::with(['user', 'documentType'])
            ->when($status, function ($query, $status) {
                return $query->where('status', $status);
            })
            ->latest()
            ->paginate(10);

        return view('admin.documents.index', compact('documents', 'status'));
    }

    /**
     * Show the document details.
     */
    public function show(Document $document)
    {
        $document->load(['user', 'documentType', 'file']);

        return view('admin.documents.show', compact('document'));
    }

    /**
     * Approve a document.
     */
    public function approve(Document $document)
    {
        DB::beginTransaction();

        try {
            $document->update([
                'status' => 'approved',
            ]);

            // Check if this is a driver document
            $user = $document->user;
            $driver = Driver::where('user_id', $user->id)->first();

            if ($driver) {
                // Check if all required driver documents are approved
                $pendingDocuments = Document::where('user_id', $user->id)
                    ->where('status', 'pending')
                    ->count();

                // If no pending documents, update driver status to active
                if ($pendingDocuments === 0) {
                    $driver->update([
                        'status' => 'active',
                    ]);

                    // Also update driver license status
                    DriverLicense::where('driver_id', $driver->id)->update([
                        'status' => 'verified',
                    ]);
                }
            }

            DB::commit();

            return redirect()->route('admin.documents.index')
                ->with('success', 'Document approved successfully.');
        } catch (\Exception $e) {
            DB::rollBack();

            return redirect()->back()
                ->with('error', 'An error occurred while approving the document: ' . $e->getMessage());
        }
    }

    /**
     * Reject a document.
     */
    public function reject(Request $request, Document $document)
    {
        DB::beginTransaction();

        try {
            $validated = $request->validate([
                'rejection_reason' => 'required|string|max:255',
            ]);

            $document->update([
                'status' => 'rejected',
                'rejection_reason' => $validated['rejection_reason'],
            ]);

            // Check if this is a driver document
            $user = $document->user;
            $driver = Driver::where('user_id', $user->id)->first();

            if ($driver && $driver->status === 'pending') {
                // Update driver status to rejected
                $driver->update([
                    'status' => 'rejected',
                    'rejection_reason' => 'Required document was rejected: ' . $validated['rejection_reason'],
                ]);

                // Also update driver license status
                DriverLicense::where('driver_id', $driver->id)->update([
                    'status' => 'rejected',
                    'rejection_reason' => 'Required document was rejected: ' . $validated['rejection_reason'],
                ]);
            }

            DB::commit();

            return redirect()->route('admin.documents.index')
                ->with('success', 'Document rejected successfully.');
        } catch (\Exception $e) {
            DB::rollBack();

            return redirect()->back()
                ->with('error', 'An error occurred while rejecting the document: ' . $e->getMessage());
        }
    }
}

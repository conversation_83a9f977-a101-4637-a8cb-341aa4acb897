<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\Order;
use App\Models\Payment;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;

class PaymentController extends Controller
{
    /**
     * Show the payment gateway page for orders
     *
     * @param string $code Order code
     * @return \Illuminate\View\View
     */
    public function gateway($code)
    {
        $orders = Order::where('combined_order_id', $code)
            ->with(['orderDetails.product'])
            ->get();

        if ($orders->isEmpty()) {
            return redirect()->route('home')->with('error', 'Order not found');
        }

        // Check if the current user owns these orders
        foreach ($orders as $order) {
            if ($order->user_id !== auth()->id()) {
                abort(403, 'You are not authorized to access this payment page');
            }
        }

        $grandTotal = $orders->sum('grand_total');

        return view('front.theme1.payment', compact('orders', 'code', 'grandTotal'));
    }

    /**
     * Process the payment for orders
     *
     * @param Request $request
     * @param string $code Order code
     * @return \Illuminate\Http\RedirectResponse
     */
    public function complete(Request $request, $code)
    {
        // Validate payment data
        $request->validate([
            'card_number' => 'required_if:payment_method,card|nullable|string',
            'expiry_month' => 'required_if:payment_method,card|nullable|string',
            'expiry_year' => 'required_if:payment_method,card|nullable|string',
            'cvv' => 'required_if:payment_method,card|nullable|string',
            'name_on_card' => 'required_if:payment_method,card|nullable|string',
        ]);

        $orders = Order::where('combined_order_id', $code)->get();

        if ($orders->isEmpty()) {
            return redirect()->route('home')->with('error', 'Order not found');
        }

        // Check if the current user owns these orders
        foreach ($orders as $order) {
            if ($order->user_id !== auth()->id()) {
                abort(403, 'You are not authorized to process payment for this order');
            }
        }

        // Here you would integrate with a real payment gateway
        // For now, we'll just simulate a successful payment

        // Update all orders to paid status
        foreach ($orders as $order) {
            $order->payment_status = 'paid';
            $order->save();

            // Update order details payment status
            foreach ($order->orderDetails as $detail) {
                $detail->payment_status = 'paid';
                $detail->save();
            }
        }

        return redirect()->route('payment.success', ['code' => $code]);
    }

    /**
     * Show payment success page for orders
     *
     * @param string $code Order code
     * @return \Illuminate\View\View
     */
    public function success($code)
    {
        return redirect()->route('checkout.confirmation', ['code' => $code])->with('success', 'Payment completed successfully!');
    }

    /**
     * Handle payment cancellation for orders
     *
     * @param string $code Order code
     * @return \Illuminate\Http\RedirectResponse
     */
    public function cancel($code)
    {
        return redirect()->route('checkout.confirmation', ['code' => $code])->with('warning', 'Payment was cancelled. Your order has been saved and you can complete the payment later.');
    }

    /**
     * Process a manual payment for a booking (for testing purposes)
     *
     * @param Request $request
     * @param int $id Booking ID
     * @return \Illuminate\Http\RedirectResponse
     */
    public function processManualPayment(Request $request, $id)
    {
        $booking = Booking::findOrFail($id);

        // Check if the user is authorized to pay for this booking
        if (Auth::id() !== $booking->user_id) {
            abort(403, 'You are not authorized to pay for this booking.');
        }

        // Check if the booking amount is valid
        if ($booking->total_amount <= 0) {
            return redirect()->route('booking.payment', $booking->id)
                ->with('error', 'Invalid booking amount. The total amount must be greater than zero.');
        }

        // Start database transaction
        DB::beginTransaction();

        try {
            // Create payment record
            $payment = Payment::create([
                'booking_id' => $booking->id,
                'user_id' => Auth::id(),
                'payment_method' => 'manual',
                'transaction_id' => 'MANUAL-' . time(),
                'amount' => $booking->total_amount,
                'type' => 'booking',
                'status' => 'completed',
                'currency' => config('razorpay.currency', 'USD'),
                'notes' => 'Manual payment for testing',
            ]);

            // Update booking status to confirmed
            $booking->status = 'confirmed';
            $booking->save();

            // Create transaction for platform commission
            $commissionRate = 10; // 10% commission rate (you can adjust this or make it configurable)
            $commissionAmount = $booking->total_amount * ($commissionRate / 100);

            Transaction::create([
                'payment_id' => $payment->id,
                'booking_id' => $booking->id,
                'user_id' => 1, // Assuming user ID 1 is the admin/platform owner
                'type' => 'platform_commission',
                'amount' => $commissionAmount,
                'commission_rate' => $commissionRate,
                'currency' => config('razorpay.currency', 'USD'),
                'status' => 'completed',
                'notes' => 'Platform commission for booking #' . $booking->booking_number,
            ]);

            // Create transaction for owner payout
            $ownerAmount = $booking->total_amount - $commissionAmount;

            Transaction::create([
                'payment_id' => $payment->id,
                'booking_id' => $booking->id,
                'user_id' => $booking->vehicle->user_id,
                'type' => 'owner_payout',
                'amount' => $ownerAmount,
                'commission_rate' => $commissionRate,
                'currency' => config('razorpay.currency', 'USD'),
                'status' => 'pending', // Will be processed later
                'notes' => 'Owner payout for booking #' . $booking->booking_number,
            ]);

            // Commit transaction
            DB::commit();

            // Redirect to booking confirmation page
            return redirect()->route('booking.confirmation', $booking->id)
                ->with('success', 'Payment successful!');
        } catch (\Exception $e) {
            // Rollback transaction on error
            DB::rollBack();

            Log::error('Manual payment processing failed: ' . $e->getMessage());
            return redirect()->route('booking.payment', $booking->id)
                ->with('error', 'Payment processing failed: ' . $e->getMessage());
        }
    }

    /**
     * Show the booking confirmation page after payment
     *
     * @param int $id Booking ID
     * @return \Illuminate\View\View
     */
    public function bookingConfirmation($id)
    {
        $booking = Booking::with([
                'user',
                'vehicle',
                'vehicle.primaryImage',
                'vehicle.user',
                'driver',
                'driver.user',
                'payments'
            ])
            ->findOrFail($id);

        // Check if the user is authorized to view this booking
        if (Auth::id() !== $booking->user_id) {
            abort(403, 'You are not authorized to view this booking.');
        }

        return view('front.bookings.confirmation', compact('booking'));
    }
}

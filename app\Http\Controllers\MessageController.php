<?php

namespace App\Http\Controllers;

use App\Models\Conversation;
use App\Models\Message;
use App\Models\User;
use App\Models\Booking;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class MessageController extends Controller
{
    /**
     * Display a listing of the user's conversations.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $conversations = Conversation::forUser(Auth::id())
            ->with(['userOne', 'userTwo', 'latestMessage', 'booking'])
            ->orderBy('last_message_at', 'desc')
            ->paginate(20);

        $unreadCount = Conversation::unreadForUser(Auth::id())->count();

        return view('messages.index', compact('conversations', 'unreadCount'));
    }

    /**
     * Show the conversation with a specific user.
     *
     * @param  int  $conversationId
     * @return \Illuminate\View\View
     */
    public function show($conversationId)
    {
        $conversation = Conversation::findOrFail($conversationId);

        // Check if user is part of this conversation
        if ($conversation->user_one_id !== Auth::id() && $conversation->user_two_id !== Auth::id()) {
            abort(403, 'You do not have access to this conversation');
        }

        // Mark conversation as read for current user
        $conversation->markAsReadFor(Auth::id());

        // Get messages with pagination
        $messages = $conversation->messages()
            ->with(['sender'])
            ->orderBy('created_at', 'desc')
            ->paginate(50);

        // Get the other user in the conversation
        $otherUser = $conversation->getOtherUser(Auth::id());

        return view('messages.show', compact('conversation', 'messages', 'otherUser'));
    }

    /**
     * Show the form for creating a new message.
     *
     * @param  Request  $request
     * @return \Illuminate\View\View
     */
    /**
     * Get the list of users that the current user can message
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function getAllowedRecipients()
    {
        $currentUser = Auth::user();

        // If user is admin or agent, they can message anyone
        if ($currentUser->hasRole(['admin', 'agent'])) {
            return User::where('id', '!=', Auth::id())->get();
        }

        // Get all admins and agents
        $adminAgentUsers = User::role(['admin', 'agent'])->where('id', '!=', Auth::id())->get();

        // Get all vehicle owners where the user has an active booking
        $bookings = Booking::where('user_id', Auth::id())
            ->whereIn('status', ['confirmed', 'in_progress'])
            ->with('vehicle.user')
            ->get();

        $vehicleOwnerIds = $bookings->pluck('vehicle.user.id')->unique()->toArray();
        $vehicleOwners = User::whereIn('id', $vehicleOwnerIds)->where('id', '!=', Auth::id())->get();

        // Combine the collections
        return $adminAgentUsers->merge($vehicleOwners);
    }

    /**
     * Check if the current user can message the specified recipient
     *
     * @param int $recipientId
     * @param int|null $bookingId
     * @return bool
     */
    private function canMessageRecipient($recipientId, $bookingId = null)
    {
        $currentUser = Auth::user();
        $recipient = User::findOrFail($recipientId);

        // If user is admin or agent, they can message anyone
        if ($currentUser->hasRole(['admin', 'agent'])) {
            return true;
        }

        // Users can always message admins and agents
        if ($recipient->hasRole(['admin', 'agent'])) {
            return true;
        }

        // For vehicle owners, check if there's an active booking
        $hasActiveBooking = Booking::where('user_id', Auth::id())
            ->whereIn('status', ['confirmed', 'in_progress'])
            ->whereHas('vehicle', function($query) use ($recipientId) {
                $query->where('user_id', $recipientId);
            })
            ->exists();

        return $hasActiveBooking;
    }

    public function create(Request $request)
    {
        $bookingId = $request->input('booking_id');
        $recipientId = $request->input('recipient_id');

        $booking = null;
        $recipient = null;

        if ($bookingId) {
            $booking = Booking::findOrFail($bookingId);
        }

        if ($recipientId) {
            $recipient = User::findOrFail($recipientId);

            // Check if the user can message this recipient
            if (!$this->canMessageRecipient($recipientId, $bookingId)) {
                return redirect()->route('messages.index')
                    ->with('error', 'You can only message admins, agents, or vehicle owners of cars you have booked.');
            }
        }

        // Get the list of users that can be messaged
        $users = $this->getAllowedRecipients();

        return view('messages.create', compact('users', 'booking', 'recipient'));
    }

    /**
     * Store a newly created message.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'recipient_id' => 'required|exists:users,id',
            'booking_id' => 'nullable|exists:bookings,id',
            'message' => 'required|string|max:5000',
        ]);

        $senderId = Auth::id();
        $recipientId = $validated['recipient_id'];
        $bookingId = $validated['booking_id'] ?? null;

        // Ensure users can't message themselves
        if ($senderId == $recipientId) {
            return back()->with('error', 'You cannot send a message to yourself.');
        }

        // Check if the user can message this recipient
        if (!$this->canMessageRecipient($recipientId, $bookingId)) {
            return back()->with('error', 'You can only message admins, agents, or vehicle owners of cars you have booked.');
        }

        try {
            DB::beginTransaction();

            // Find or create conversation
            $conversation = Conversation::where(function($query) use ($senderId, $recipientId) {
                $query->where('user_one_id', $senderId)
                      ->where('user_two_id', $recipientId);
            })->orWhere(function($query) use ($senderId, $recipientId) {
                $query->where('user_one_id', $recipientId)
                      ->where('user_two_id', $senderId);
            })->where('booking_id', $bookingId)->first();

            if (!$conversation) {
                $conversation = Conversation::create([
                    'user_one_id' => $senderId,
                    'user_two_id' => $recipientId,
                    'booking_id' => $bookingId,
                    'last_message_at' => now(),
                    'is_read_user_one' => true,
                    'is_read_user_two' => false,
                ]);
            } else {
                // Update conversation read status and last message time
                $conversation->update([
                    'last_message_at' => now(),
                    'is_read_user_one' => $senderId == $conversation->user_one_id,
                    'is_read_user_two' => $senderId == $conversation->user_two_id,
                ]);
            }

            // Create message
            $message = Message::create([
                'conversation_id' => $conversation->id,
                'sender_id' => $senderId,
                'recipient_id' => $recipientId,
                'booking_id' => $bookingId,
                'message' => $validated['message'],
                'is_read' => false,
            ]);

            DB::commit();

            return redirect()->route('messages.show', $conversation->id)
                ->with('success', 'Message sent successfully.');

        } catch (\Exception $e) {
            DB::rollBack();

            return back()->with('error', 'Failed to send message: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Send a reply to an existing conversation.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $conversationId
     * @return \Illuminate\Http\RedirectResponse
     */
    public function reply(Request $request, $conversationId)
    {
        $validated = $request->validate([
            'message' => 'required|string|max:5000',
        ]);

        $conversation = Conversation::findOrFail($conversationId);

        // Check if user is part of this conversation
        if ($conversation->user_one_id !== Auth::id() && $conversation->user_two_id !== Auth::id()) {
            abort(403, 'You do not have access to this conversation');
        }

        $senderId = Auth::id();
        $recipientId = $conversation->user_one_id == $senderId
            ? $conversation->user_two_id
            : $conversation->user_one_id;

        // For existing conversations, we need to check if the booking is still active
        // or if the recipient is an admin/agent (which is always allowed)
        $bookingId = $conversation->booking_id;

        // If there's a booking associated with this conversation, check if it's still active
        if ($bookingId && !$this->canMessageRecipient($recipientId, $bookingId)) {
            return back()->with('error', 'You can only message admins, agents, or vehicle owners of cars you have currently booked.');
        }

        try {
            DB::beginTransaction();

            // Update conversation read status and last message time
            $conversation->update([
                'last_message_at' => now(),
                'is_read_user_one' => $senderId == $conversation->user_one_id,
                'is_read_user_two' => $senderId == $conversation->user_two_id,
            ]);

            // Create message
            $message = Message::create([
                'conversation_id' => $conversation->id,
                'sender_id' => $senderId,
                'recipient_id' => $recipientId,
                'booking_id' => $conversation->booking_id,
                'message' => $validated['message'],
                'is_read' => false,
            ]);

            DB::commit();

            return redirect()->route('messages.show', $conversation->id)
                ->with('success', 'Reply sent successfully.');

        } catch (\Exception $e) {
            DB::rollBack();

            return back()->with('error', 'Failed to send reply: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Mark all messages in a conversation as read.
     *
     * @param  int  $conversationId
     * @return \Illuminate\Http\RedirectResponse
     */
    public function markAsRead($conversationId)
    {
        $conversation = Conversation::findOrFail($conversationId);

        // Check if user is part of this conversation
        if ($conversation->user_one_id !== Auth::id() && $conversation->user_two_id !== Auth::id()) {
            abort(403, 'You do not have access to this conversation');
        }

        // Mark conversation as read for current user
        $conversation->markAsReadFor(Auth::id());

        // Mark all messages as read
        Message::where('conversation_id', $conversation->id)
            ->where('recipient_id', Auth::id())
            ->where('is_read', false)
            ->update([
                'is_read' => true,
                'read_at' => now()
            ]);

        return back()->with('success', 'Conversation marked as read.');
    }

    /**
     * Get unread message count for the authenticated user.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUnreadCount()
    {
        $unreadCount = Conversation::unreadForUser(Auth::id())->count();

        return response()->json([
            'unread_count' => $unreadCount
        ]);
    }
}

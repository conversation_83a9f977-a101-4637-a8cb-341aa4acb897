<?php

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Create roles and permissions
    $driverRole = Role::create(['name' => 'driver']);
    $userRole = Role::create(['name' => 'user']);

    // Create permissions
    Permission::create(['name' => 'driver.register']);

    // Assign permissions to roles
    $driverRole->givePermissionTo(['driver.register']);

    // Create user
    $this->user = User::factory()->create([
        'first_name' => 'Regular',
        'last_name' => 'User',
        'email' => '<EMAIL>',
    ]);
    $this->user->assignRole('user');
});

test('authenticated user can access driver registration page', function () {
    $response = $this->actingAs($this->user)->get('/drivers/register');

    $response->assertStatus(200);
});

test('guest cannot access driver registration page', function () {
    $response = $this->get('/drivers/register');

    $response->assertRedirect('/login');
});

test('user can register as a driver', function () {
    $driverData = [
        'license_number' => '**********',
        'license_expiry' => now()->addYears(5)->format('Y-m-d'),
        'experience_years' => 5,
        'vehicle_types' => [1, 2], // Assuming vehicle type IDs
        'bio' => 'Experienced driver with clean record',
        'hourly_rate' => 25.00,
    ];

    $response = $this->actingAs($this->user)->post('/drivers/register', $driverData);

    $response->assertRedirect();

    // Check if user has driver role now
    $this->user->refresh();
    expect($this->user->hasRole('driver'))->toBeTrue();

    // Check if driver profile was created
    $this->assertDatabaseHas('driver_profiles', [
        'user_id' => $this->user->id,
        'license_number' => '**********',
        'experience_years' => 5,
        'hourly_rate' => 25.00,
    ]);
});

test('driver can view their profile', function () {
    // First register as driver
    $this->user->assignRole('driver');

    // Create driver profile
    $this->post('/drivers/register', [
        'license_number' => '**********',
        'license_expiry' => now()->addYears(5)->format('Y-m-d'),
        'experience_years' => 5,
        'vehicle_types' => [1, 2],
        'bio' => 'Experienced driver with clean record',
        'hourly_rate' => 25.00,
    ]);

    $response = $this->actingAs($this->user)->get('/drivers/profile');

    $response->assertStatus(200);
    $response->assertSee('**********');
    $response->assertSee('Experienced driver with clean record');
});

test('driver can update their profile', function () {
    // First register as driver
    $this->user->assignRole('driver');

    // Create driver profile
    $this->actingAs($this->user)->post('/drivers/register', [
        'license_number' => '**********',
        'license_expiry' => now()->addYears(5)->format('Y-m-d'),
        'experience_years' => 5,
        'vehicle_types' => [1, 2],
        'bio' => 'Experienced driver with clean record',
        'hourly_rate' => 25.00,
    ]);

    // Update profile
    $updatedData = [
        'license_number' => '**********',
        'license_expiry' => now()->addYears(5)->format('Y-m-d'),
        'experience_years' => 7, // Changed
        'vehicle_types' => [1, 2, 3], // Added a type
        'bio' => 'Very experienced driver with excellent record', // Changed
        'hourly_rate' => 30.00, // Changed
    ];

    $response = $this->actingAs($this->user)->put('/drivers/profile', $updatedData);

    $response->assertRedirect();

    // Check if profile was updated
    $this->assertDatabaseHas('driver_profiles', [
        'user_id' => $this->user->id,
        'experience_years' => 7,
        'hourly_rate' => 30.00,
    ]);
});

test('driver can update their availability', function () {
    // First register as driver
    $this->user->assignRole('driver');

    // Create driver profile
    $this->actingAs($this->user)->post('/drivers/register', [
        'license_number' => '**********',
        'license_expiry' => now()->addYears(5)->format('Y-m-d'),
        'experience_years' => 5,
        'vehicle_types' => [1, 2],
        'bio' => 'Experienced driver with clean record',
        'hourly_rate' => 25.00,
    ]);

    // Set availability
    $availabilityData = [
        'monday' => [
            'available' => true,
            'start_time' => '09:00',
            'end_time' => '17:00',
        ],
        'tuesday' => [
            'available' => true,
            'start_time' => '09:00',
            'end_time' => '17:00',
        ],
        'wednesday' => [
            'available' => false,
            'start_time' => null,
            'end_time' => null,
        ],
        'thursday' => [
            'available' => true,
            'start_time' => '09:00',
            'end_time' => '17:00',
        ],
        'friday' => [
            'available' => true,
            'start_time' => '09:00',
            'end_time' => '17:00',
        ],
        'saturday' => [
            'available' => true,
            'start_time' => '10:00',
            'end_time' => '15:00',
        ],
        'sunday' => [
            'available' => false,
            'start_time' => null,
            'end_time' => null,
        ],
    ];

    $response = $this->actingAs($this->user)->post('/drivers/availability', $availabilityData);

    $response->assertRedirect();

    // Check if availability was saved
    $this->assertDatabaseHas('driver_availabilities', [
        'user_id' => $this->user->id,
        'day_of_week' => 'monday',
        'available' => true,
        'start_time' => '09:00:00',
        'end_time' => '17:00:00',
    ]);

    $this->assertDatabaseHas('driver_availabilities', [
        'user_id' => $this->user->id,
        'day_of_week' => 'wednesday',
        'available' => false,
        'start_time' => null,
        'end_time' => null,
    ]);
});

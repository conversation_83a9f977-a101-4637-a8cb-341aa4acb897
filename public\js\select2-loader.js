// Load Select2 after jQuery is loaded
document.addEventListener('jQueryLoaded', function() {
    if (typeof $.fn.select2 === 'function') {
        console.log('Select2 is already loaded');
        initializeSelect2();
        return;
    }

    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js';
    
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css';
    
    script.onload = function() {
        console.log('Select2 loaded successfully');
        initializeSelect2();
    };
    
    document.head.appendChild(link);
    document.head.appendChild(script);
});

function initializeSelect2() {
    // Initialize all select2 elements
    $(document).ready(function() {
        $('select.select2').select2();
    });
}

<x-app-backend-layout>
    <div class="container mx-auto mt-5 p-6 bg-white rounded-lg shadow-md">
        <h1 class="text-2xl font-semibold mb-4 text-gray-800">Manage Permissions for Role: {{ $role->name }}</h1>

        @if(session('success'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-md mb-4" role="alert">
                <strong class="font-bold">{{ session('success') }}</strong>
            </div>
        @endif

        <form action="{{ route('roles.permissions.store', $role) }}" method="POST" class="bg-gray-50 p-4 rounded-lg shadow">
            @csrf
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                @foreach($permissions as $permission)
                    <div class="flex items-center space-x-2 bg-white p-2 rounded-md shadow-sm border">
                        <input type="checkbox" name="permissions[]" value="{{ $permission->id }}" id="permission_{{ $permission->id }}"
                               class="w-5 h-5 text-blue-600 border-gray-300 rounded focus:ring focus:ring-blue-300"
                            {{ in_array($permission->id, $rolePermissions) ? 'checked' : '' }}>
                        <label for="permission_{{ $permission->id }}" class="text-gray-700">{{ $permission->name }}</label>
                    </div>
                @endforeach
            </div>
            <div class="mt-4 flex justify-end">
                <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition">Assign Permissions</button>
            </div>
        </form>

        <h2 class="text-xl font-semibold mt-8 text-gray-800">Assigned Permissions</h2>
        <div class="overflow-x-auto mt-4">
            <table class="w-full bg-white border rounded-lg shadow-md">
                <thead class="bg-gray-100">
                <tr>
                    <th class="px-4 py-3 border text-left text-gray-700">#</th>
                    <th class="px-4 py-3 border text-left text-gray-700">Name</th>
                    <th class="px-4 py-3 border text-left text-gray-700">Actions</th>
                </tr>
                </thead>
                <tbody>
                @foreach($role->permissions as $permission)
                    <tr class="hover:bg-gray-50">
                        <td class="border px-4 py-3 text-gray-800">{{ $permission->id }}</td>
                        <td class="border px-4 py-3 text-gray-800">{{ $permission->name }}</td>
                        <td class="border px-4 py-3">
                            <form action="{{ route('roles.permissions.destroy', [$role, $permission]) }}" method="POST" class="inline-block">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="text-red-500 hover:text-red-700 transition" onclick="return confirm('Are you sure?')">Revoke</button>
                            </form>
                        </td>
                    </tr>
                @endforeach
                </tbody>
            </table>
        </div>
    </div>
</x-app-backend-layout>

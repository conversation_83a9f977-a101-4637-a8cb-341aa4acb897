<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<div class="container py-8">
    <div class="mb-6">
        <a href="<?php echo e(route('vehicle.details', $vehicle->id)); ?>" class="text-primary hover:text-primary-dark flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clip-rule="evenodd" />
            </svg>
            Back to Vehicle Details
        </a>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                    <h1 class="text-2xl font-bold text-gray-800">Complete Your Booking</h1>
                </div>

                <div class="p-6">
                    <form action="<?php echo e(route('booking.summary')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="vehicle_id" value="<?php echo e($vehicle->id); ?>">
                        <input type="hidden" name="pickup_date" value="<?php echo e($pickupDate->format('Y-m-d')); ?>">
                        <input type="hidden" name="return_date" value="<?php echo e($returnDate->format('Y-m-d')); ?>">

                        <div class="mb-6">
                            <h2 class="text-xl font-semibold mb-4 text-gray-700">Pickup & Return Details</h2>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <div>
                                    <label for="pickup_location" class="block text-sm font-medium text-gray-700 mb-1">Pickup Location</label>
                                    <input type="text" name="pickup_location" id="pickup_location" class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50" required>
                                    <?php $__errorArgs = ['pickup_location'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div>
                                    <label for="return_location" class="block text-sm font-medium text-gray-700 mb-1">Return Location</label>
                                    <input type="text" name="return_location" id="return_location" class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50" required>
                                    <?php $__errorArgs = ['return_location'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <?php if($vehicle->with_driver && $drivers->count() > 0): ?>
                        <div class="mb-6">
                            <h2 class="text-xl font-semibold mb-4 text-gray-700">Driver Options</h2>

                            <div class="mb-4">
                                <div class="flex items-center mb-2">
                                    <input type="radio" name="driver_option" id="no_driver" value="no_driver" class="h-4 w-4 text-primary focus:ring-primary border-gray-300" checked>
                                    <label for="no_driver" class="ml-2 block text-sm text-gray-700">I'll drive myself</label>
                                </div>

                                <div class="flex items-center">
                                    <input type="radio" name="driver_option" id="with_driver" value="with_driver" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                                    <label for="with_driver" class="ml-2 block text-sm text-gray-700">I need a driver</label>
                                </div>
                            </div>

                            <div id="driver_selection" class="hidden mt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Select a Driver</label>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <?php $__currentLoopData = $drivers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $driver): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="border border-gray-200 rounded-lg p-4 hover:border-primary cursor-pointer driver-option" data-driver-id="<?php echo e($driver->id); ?>">
                                        <div class="flex items-center">
                                            <input type="radio" name="driver_id" id="driver_<?php echo e($driver->id); ?>" value="<?php echo e($driver->id); ?>" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                                            <label for="driver_<?php echo e($driver->id); ?>" class="ml-2 flex-1">
                                                <span class="block font-medium text-gray-700"><?php echo e($driver->user->name); ?></span>
                                                <span class="block text-sm text-gray-500"><?php echo e($driver->experience_years); ?> years experience</span>
                                                <span class="block text-sm font-medium text-gray-900 mt-1">$<?php echo e(number_format($driver->daily_rate, 2)); ?> per day</span>
                                                <span class="block text-xs text-gray-500 mt-1">Languages: <?php echo e(implode(', ', json_decode($driver->languages, true) ?: [])); ?></span>
                                            </label>
                                        </div>
                                    </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <div class="mb-6">
                            <h2 class="text-xl font-semibold mb-4 text-gray-700">Special Requests</h2>

                            <div>
                                <label for="special_requests" class="block text-sm font-medium text-gray-700 mb-1">Any special requests or notes for the owner?</label>
                                <textarea name="special_requests" id="special_requests" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50"></textarea>
                            </div>
                        </div>

                        <div class="mt-8 flex justify-end">
                            <button type="submit" class="inline-flex items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                Continue to Summary
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-md overflow-hidden sticky top-24">
                <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                    <h2 class="text-xl font-bold text-gray-800">Booking Summary</h2>
                </div>

                <div class="p-6">
                    <div class="flex items-center mb-6">
                        <?php if($vehicle->primaryImage): ?>
                            <img src="<?php echo e(asset('storage/' . $vehicle->primaryImage->file->path)); ?>" alt="<?php echo e($vehicle->make); ?> <?php echo e($vehicle->model); ?>" class="w-20 h-20 object-cover rounded-md mr-4">
                        <?php else: ?>
                            <div class="w-20 h-20 bg-gray-200 rounded-md mr-4 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                            </div>
                        <?php endif; ?>
                        <div>
                            <h3 class="font-bold text-gray-800"><?php echo e($vehicle->make); ?> <?php echo e($vehicle->model); ?></h3>
                            <p class="text-gray-600 text-sm"><?php echo e($vehicle->year); ?> · <?php echo e($vehicle->vehicleType->name); ?></p>
                        </div>
                    </div>

                    <div class="border-t border-gray-200 pt-4 mb-4">
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-600">Pickup Date:</span>
                            <span class="font-medium"><?php echo e($pickupDate->format('M d, Y')); ?></span>
                        </div>
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-600">Return Date:</span>
                            <span class="font-medium"><?php echo e($returnDate->format('M d, Y')); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Duration:</span>
                            <span class="font-medium"><?php echo e($days); ?> days</span>
                        </div>
                    </div>

                    <div class="border-t border-gray-200 pt-4 mb-4">
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-600">Vehicle Rate:</span>
                            <span class="font-medium">$<?php echo e(number_format($vehicle->daily_rate, 2)); ?> × <?php echo e($days); ?> days</span>
                        </div>
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-600">Vehicle Total:</span>
                            <span class="font-medium">$<?php echo e(number_format($vehiclePrice, 2)); ?></span>
                        </div>

                        <?php if($discountAmount > 0): ?>
                        <div class="flex justify-between mb-2 text-green-600">
                            <span>Discount:</span>
                            <span>-$<?php echo e(number_format($discountAmount, 2)); ?></span>
                        </div>
                        <?php endif; ?>

                        <div id="driver_price_row" class="flex justify-between mb-2 hidden">
                            <span class="text-gray-600">Driver:</span>
                            <span class="font-medium" id="driver_price">$0.00</span>
                        </div>
                    </div>

                    <div class="border-t border-gray-200 pt-4 mb-4">
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-600">Subtotal:</span>
                            <span class="font-medium" id="subtotal">$<?php echo e(number_format($vehiclePrice, 2)); ?></span>
                        </div>
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-600">Tax (10%):</span>
                            <span class="font-medium" id="tax">$<?php echo e(number_format($taxAmount, 2)); ?></span>
                        </div>
                    </div>

                    <div class="border-t border-gray-200 pt-4 mb-4">
                        <div class="flex justify-between">
                            <span class="text-lg font-bold">Total:</span>
                            <span class="text-lg font-bold" id="total">$<?php echo e(number_format($totalAmount, 2)); ?></span>
                        </div>
                    </div>

                    <?php if($vehicle->security_deposit > 0): ?>
                    <div class="bg-blue-50 p-4 rounded-md mt-4">
                        <div class="flex">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                            </svg>
                            <div>
                                <p class="text-sm text-blue-700">A security deposit of $<?php echo e(number_format($vehicle->security_deposit, 2)); ?> will be collected at pickup.</p>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const driverOptions = document.querySelectorAll('input[name="driver_option"]');
        const driverSelection = document.getElementById('driver_selection');
        const driverPriceRow = document.getElementById('driver_price_row');
        const driverPriceElement = document.getElementById('driver_price');
        const subtotalElement = document.getElementById('subtotal');
        const taxElement = document.getElementById('tax');
        const totalElement = document.getElementById('total');

        // Initial values
        const vehiclePrice = <?php echo e($vehiclePrice); ?>;
        let driverPrice = 0;
        let subtotal = vehiclePrice;
        let tax = <?php echo e($taxAmount); ?>;
        let total = <?php echo e($totalAmount); ?>;

        // Driver option selection
        if (driverOptions.length > 0) {
            driverOptions.forEach(option => {
                option.addEventListener('change', function() {
                    if (this.value === 'with_driver') {
                        driverSelection.classList.remove('hidden');
                    } else {
                        driverSelection.classList.add('hidden');
                        // Reset driver selection
                        document.querySelectorAll('input[name="driver_id"]').forEach(radio => {
                            radio.checked = false;
                        });
                        // Update price
                        updateDriverPrice(0);
                    }
                });
            });
        }

        // Driver selection
        const driverRadios = document.querySelectorAll('input[name="driver_id"]');
        if (driverRadios.length > 0) {
            driverRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    if (this.checked) {
                        const driverId = this.value;
                        const driver = <?php echo json_encode($drivers, 15, 512) ?>;
                        const selectedDriver = driver.find(d => d.id == driverId);

                        if (selectedDriver) {
                            const dailyRate = selectedDriver.daily_rate;
                            const days = <?php echo e($days); ?>;
                            let totalDriverPrice = 0;

                            // Check if driver has custom pricing for any of the days
                            if (selectedDriver.dateRangeAvailability && Object.keys(selectedDriver.dateRangeAvailability).length > 0) {
                                // Calculate price based on custom pricing where available
                                const startDate = new Date('<?php echo e($pickupDate->format("Y-m-d")); ?>');
                                const endDate = new Date('<?php echo e($returnDate->format("Y-m-d")); ?>');

                                for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
                                    const dateStr = d.toISOString().split('T')[0];
                                    const dateAvailability = selectedDriver.dateRangeAvailability[dateStr];

                                    if (dateAvailability && dateAvailability.custom_price) {
                                        totalDriverPrice += parseFloat(dateAvailability.custom_price);
                                    } else {
                                        totalDriverPrice += parseFloat(dailyRate);
                                    }
                                }
                            } else {
                                // Use standard daily rate
                                totalDriverPrice = dailyRate * days;
                            }

                            updateDriverPrice(totalDriverPrice);
                        }
                    }
                });
            });
        }

        // Make the entire driver card clickable
        const driverCards = document.querySelectorAll('.driver-option');
        if (driverCards.length > 0) {
            driverCards.forEach(card => {
                card.addEventListener('click', function() {
                    const driverId = this.getAttribute('data-driver-id');
                    const radio = document.getElementById('driver_' + driverId);
                    radio.checked = true;

                    // Trigger the change event
                    const event = new Event('change');
                    radio.dispatchEvent(event);
                });
            });
        }

        // Update price calculations
        function updateDriverPrice(newDriverPrice) {
            driverPrice = newDriverPrice;

            if (driverPrice > 0) {
                driverPriceRow.classList.remove('hidden');
                driverPriceElement.textContent = '$' + driverPrice.toFixed(2);
            } else {
                driverPriceRow.classList.add('hidden');
            }

            // Update totals
            subtotal = vehiclePrice + driverPrice;
            tax = subtotal * 0.1; // 10% tax
            total = subtotal + tax;

            // Update display
            subtotalElement.textContent = '$' + subtotal.toFixed(2);
            taxElement.textContent = '$' + tax.toFixed(2);
            totalElement.textContent = '$' + total.toFixed(2);
        }
    });
</script>
<?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\carbnb\resources\views\front\bookings\create.blade.php ENDPATH**/ ?>
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\DocumentType;

class VehicleDocumentTypesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $documentTypes = [
            [
                'name' => 'Car Ownership Document',
                'description' => 'Document proving ownership of the vehicle (registration certificate, title, etc.)',
                'requires_verification' => true,
                'has_expiry' => false,
                'is_required' => true,
                'applies_to' => 'vehicle',
            ],
            [
                'name' => 'Insurance Document',
                'description' => 'Valid insurance policy document for the vehicle',
                'requires_verification' => true,
                'has_expiry' => true,
                'is_required' => true,
                'applies_to' => 'vehicle',
            ],
            [
                'name' => 'Car Inspection Document',
                'description' => 'Recent vehicle inspection certificate or report',
                'requires_verification' => true,
                'has_expiry' => true,
                'is_required' => true,
                'applies_to' => 'vehicle',
            ],
        ];

        foreach ($documentTypes as $documentType) {
            DocumentType::firstOrCreate(
                ['name' => $documentType['name'], 'applies_to' => $documentType['applies_to']],
                $documentType
            );
        }
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('disputes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('booking_id')->constrained();
            $table->foreignId('user_id')->constrained()->comment('User who opened the dispute');
            $table->foreignId('against_user_id')->constrained('users')->comment('User against whom the dispute is opened');
            $table->string('dispute_number')->unique();
            $table->enum('type', ['damage', 'cleanliness', 'late_return', 'no_show', 'extra_charges', 'incorrect_fuel', 'other']);
            $table->text('description');
            $table->decimal('amount_claimed', 10, 2)->nullable();
            $table->enum('status', ['open', 'under_review', 'resolved', 'closed'])->default('open');
            $table->text('resolution_notes')->nullable();
            $table->foreignId('resolved_by')->nullable()->constrained('users');
            $table->datetime('resolved_at')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('disputes');
    }
};

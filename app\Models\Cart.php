<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Cart extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'temp_user_id',
        'product_id',
        'variation',
        'price',
        'quantity',
        'tax',
        'shipping_cost',
        'shipping_type',
        'pickup_point',
        'carrier_id',
        'discount',
        'product_referral_code',
        'coupon_code',
        'coupon_applied',
        'address_id',
        'owner_id',
        'status'
    ];

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'carts';

    /**
     * Get the product that belongs to the cart item.
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Calculate the subtotal for this cart item.
     *
     * @return float
     */
    public function getSubtotalAttribute()
    {
        return $this->quantity * $this->price;
    }

    /**
     * Calculate the tax amount for this cart item.
     *
     * @return float
     */
    public function getTaxAmountAttribute()
    {
        if ($this->tax <= 0) {
            return 0;
        }

        return ($this->subtotal * $this->tax) / 100;
    }

    /**
     * Calculate the total for this cart item (subtotal + tax).
     *
     * @return float
     */
    public function getTotalAttribute()
    {
        return $this->subtotal + $this->tax_amount + $this->shipping_cost - $this->discount;
    }
}

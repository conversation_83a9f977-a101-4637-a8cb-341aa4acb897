<x-app-backend-layout>
    <div class="container py-8">
        <div class="mb-6">
            <h1 class="text-3xl font-bold text-gray-800">My Bookings</h1>
            <p class="text-gray-600 mt-2">Manage all your car rental bookings in one place</p>
        </div>

        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                <div class="flex flex-wrap items-center justify-between">
                    <h2 class="text-xl font-bold text-gray-800">Your Bookings</h2>
                </div>
            </div>

            <div class="p-6">
                @if($bookings->isEmpty())
                    <div class="text-center py-8">
                        <i class="fas fa-calendar-times text-gray-300 text-6xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No bookings found</h3>
                        <p class="text-gray-500 mb-6">You haven't made any bookings yet.</p>
                        <a href="{{ route('home') }}" class="inline-flex items-center px-4 py-2 bg-primary border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-primary-dark active:bg-primary-dark focus:outline-none focus:border-primary-dark focus:ring ring-primary-light disabled:opacity-25 transition ease-in-out duration-150">
                            Browse Vehicles
                        </a>
                    </div>
                @else
                    <!-- Filter Tabs -->
                    <div class="mb-6 border-b border-gray-200">
                        <nav class="-mb-px flex space-x-8">
                            <a href="#" class="filter-tab whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm border-primary text-primary" data-status="all">
                                All Bookings
                            </a>
                            <a href="#" class="filter-tab whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300" data-status="pending">
                                Pending
                            </a>
                            <a href="#" class="filter-tab whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300" data-status="confirmed">
                                Confirmed
                            </a>
                            <a href="#" class="filter-tab whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300" data-status="in_progress">
                                In Progress
                            </a>
                            <a href="#" class="filter-tab whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300" data-status="completed">
                                Completed
                            </a>
                            <a href="#" class="filter-tab whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300" data-status="cancelled">
                                Cancelled
                            </a>
                        </nav>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Vehicle
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Dates
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Status
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Total
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Payment
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($bookings as $booking)
                                <tr class="booking-row" data-status="{{ $booking->status }}">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10">
                                                @if($booking->vehicle->primaryImage)
                                                    <img class="h-10 w-10 rounded-md object-cover" src="{{ asset('storage/' . $booking->vehicle->primaryImage->file->path) }}" alt="{{ $booking->vehicle->make }} {{ $booking->vehicle->model }}">
                                                @else
                                                    <div class="h-10 w-10 rounded-md bg-gray-200 flex items-center justify-center">
                                                        <i class="fas fa-car text-gray-400"></i>
                                                    </div>
                                                @endif
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900">
                                                    {{ $booking->vehicle->make }} {{ $booking->vehicle->model }}
                                                </div>
                                                <div class="text-sm text-gray-500">
                                                    {{ $booking->booking_number }}
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            {{ $booking->start_date->format('M d, Y') }} - {{ $booking->end_date->format('M d, Y') }}
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            {{ $booking->start_date->diffInDays($booking->end_date) + 1 }} days
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        @php
                                            $statusColors = [
                                                'pending' => 'bg-yellow-100 text-yellow-800',
                                                'confirmed' => 'bg-blue-100 text-blue-800',
                                                'in_progress' => 'bg-indigo-100 text-indigo-800',
                                                'completed' => 'bg-green-100 text-green-800',
                                                'cancelled' => 'bg-red-100 text-red-800',
                                                'rejected' => 'bg-gray-100 text-gray-800',
                                            ];
                                            $statusColor = $statusColors[$booking->status] ?? 'bg-gray-100 text-gray-800';
                                        @endphp
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $statusColor }}">
                                            {{ ucfirst(str_replace('_', ' ', $booking->status)) }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ config('payment.currency_symbol') }}{{ number_format($booking->total_amount, 2) }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        @if($booking->payments->where('status', 'completed')->count() > 0)
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                Paid
                                            </span>
                                        @else
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                Unpaid
                                            </span>
                                        @endif
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <a href="{{ route('booking.show', $booking->id) }}" class="text-primary hover:text-primary-dark">
                                            View
                                        </a>

                                        @if($booking->status === 'pending' || $booking->status === 'confirmed')
                                            @if($booking->payments->where('status', 'completed')->count() === 0)
                                                <a href="{{ route('booking.payment', $booking->id) }}" class="ml-3 text-primary hover:text-primary-dark">
                                                    Pay Now
                                                </a>
                                            @endif
                                        @endif

                                        @if($booking->status === 'pending' || $booking->status === 'confirmed')
                                            <form action="{{ route('booking.update-status', $booking->id) }}" method="POST" class="inline-block ml-3">
                                                @csrf
                                                <input type="hidden" name="status" value="cancelled">
                                                <button type="submit" class="text-red-600 hover:text-red-900" onclick="return confirm('Are you sure you want to cancel this booking?')">Cancel</button>
                                            </form>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-4">
                        {{ $bookings->links() }}
                    </div>
                @endif
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Filter tabs functionality
            const filterTabs = document.querySelectorAll('.filter-tab');
            const bookingRows = document.querySelectorAll('.booking-row');

            filterTabs.forEach(tab => {
                tab.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // Remove active class from all tabs
                    filterTabs.forEach(t => {
                        t.classList.remove('border-primary', 'text-primary');
                        t.classList.add('border-transparent', 'text-gray-500');
                    });
                    
                    // Add active class to clicked tab
                    this.classList.remove('border-transparent', 'text-gray-500');
                    this.classList.add('border-primary', 'text-primary');
                    
                    const status = this.getAttribute('data-status');
                    
                    // Show/hide rows based on status
                    bookingRows.forEach(row => {
                        if (status === 'all' || row.getAttribute('data-status') === status) {
                            row.classList.remove('hidden');
                        } else {
                            row.classList.add('hidden');
                        }
                    });
                });
            });
        });
    </script>
    @endpush
</x-app-backend-layout>

<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['name', 'label', 'value' => '']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['name', 'label', 'value' => '']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div class="mb-4">
    <?php if($label): ?>
        <label for="<?php echo e($name); ?>" class="block text-gray-700 font-medium mb-2"><?php echo e($label); ?></label>
    <?php endif; ?>
    
    <div x-data="quillEditor('<?php echo e($name); ?>', '<?php echo e(old($name, $value)); ?>')" class="relative">
        <div x-ref="quillContainer" class="bg-white"></div>
        <input type="hidden" name="<?php echo e($name); ?>" x-ref="input">
        <?php $__errorArgs = [$name];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('alpine:init', () => {
    Alpine.data('quillEditor', (name, initialValue) => ({
        init() {
            // Initialize Quill
            this.quill = new Quill(this.$refs.quillContainer, {
                theme: 'snow',
                modules: {
                    toolbar: [
                        ['bold', 'italic', 'underline', 'strike'],
                        ['blockquote', 'code-block'],
                        [{ 'header': 1 }, { 'header': 2 }],
                        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                        [{ 'script': 'sub'}, { 'script': 'super' }],
                        [{ 'indent': '-1'}, { 'indent': '+1' }],
                        [{ 'direction': 'rtl' }],
                        [{ 'size': ['small', false, 'large', 'huge'] }],
                        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                        [{ 'color': [] }, { 'background': [] }],
                        [{ 'font': [] }],
                        [{ 'align': [] }],
                        ['clean'],
                        ['link', 'image', 'video']
                    ]
                },
                placeholder: 'Write something...',
            });

            // Set initial content
            if (initialValue) {
                this.quill.root.innerHTML = initialValue;
            }

            // Update hidden input on change
            this.quill.on('text-change', () => {
                this.$refs.input.value = this.quill.root.innerHTML;
            });

            // Set initial value to hidden input
            this.$refs.input.value = this.quill.root.innerHTML;

            // Handle form submission
            this.$refs.input.closest('form')?.addEventListener('submit', () => {
                this.$refs.input.value = this.quill.root.innerHTML;
            });
        }
    }));
});
</script>
<?php $__env->stopPush(); ?><?php /**PATH C:\laragon\www\carbnb\resources\views\components\quill-editor.blade.php ENDPATH**/ ?>
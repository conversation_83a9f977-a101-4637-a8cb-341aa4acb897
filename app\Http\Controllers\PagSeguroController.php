<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class PagSeguroController extends Controller
{
    /**
     * Create a PagSeguro order for a booking
     *
     * @param int $bookingId
     * @return \Illuminate\Http\JsonResponse
     */
    public function createOrder($bookingId)
    {
        $booking = Booking::findOrFail($bookingId);

        // Check if PagSeguro API keys are configured
        $apiKey = config('pagseguro.api_key');
        $token = config('pagseguro.token');

        if (empty($apiKey) || empty($token) ||
            $apiKey === 'YOUR_PAGSEGURO_API_KEY' ||
            $token === 'YOUR_PAGSEGURO_TOKEN') {
            return response()->json([
                'error' => 'PagSeguro API keys are not properly configured. Please use the manual payment option for testing.',
                'use_manual' => true
            ], 500);
        }

        try {
            // Convert amount to cents (PagSeguro requires amount in cents)
            $amountInCents = round($booking->total_amount * 100);

            // Create PagSeguro order
            $response = Http::withHeaders([
                'Authorization' => $apiKey,
                'Content-Type' => 'application/json'
            ])->post(config('pagseguro.api_url') . '/orders', [
                'reference_id' => $booking->booking_number,
                'customer' => [
                    'name' => Auth::user()->name,
                    'email' => Auth::user()->email,
                    'tax_id' => '12345678909', // CPF/CNPJ - This should be dynamically set in a real implementation
                ],
                'items' => [
                    [
                        'reference_id' => 'booking-' . $booking->id,
                        'name' => 'Booking #' . $booking->booking_number,
                        'quantity' => 1,
                        'unit_amount' => $amountInCents,
                    ]
                ],
                'shipping' => [
                    'address' => [
                        'street' => 'Address Line 1', // This should be dynamically set in a real implementation
                        'number' => '100',
                        'complement' => 'Address Line 2',
                        'locality' => 'District',
                        'city' => 'City',
                        'region_code' => 'State',
                        'country' => 'BRA',
                        'postal_code' => '01452002'
                    ]
                ],
                'notification_urls' => [
                    route('pagseguro.webhook')
                ],
                'charges' => [
                    [
                        'reference_id' => 'charge-' . $booking->id,
                        'description' => 'Payment for Booking #' . $booking->booking_number,
                        'amount' => [
                            'value' => $amountInCents,
                            'currency' => config('pagseguro.currency')
                        ],
                        'payment_method' => [
                            'type' => 'CREDIT_CARD',
                            'installments' => 1,
                            'capture' => true,
                            'card' => [
                                'security_code' => '123',
                                'holder' => [
                                    'name' => 'Card Holder Name'
                                ],
                                'store' => false
                            ]
                        ]
                    ]
                ]
            ]);

            if ($response->successful()) {
                $orderData = $response->json();
                
                return response()->json([
                    'order_id' => $orderData['id'],
                    'amount' => $amountInCents,
                    'currency' => config('pagseguro.currency'),
                    'checkout_url' => $orderData['links'][0]['href'] ?? null,
                ]);
            } else {
                Log::error('PagSeguro API Error: ' . $response->body());
                return response()->json([
                    'error' => 'Failed to create PagSeguro order. ' . ($response->json()['error_messages'][0] ?? 'Unknown error'),
                ], 500);
            }
        } catch (\Exception $e) {
            Log::error('PagSeguro Exception: ' . $e->getMessage());
            return response()->json([
                'error' => 'An error occurred while creating the PagSeguro order: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Process PagSeguro payment
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function processPayment(Request $request)
    {
        $request->validate([
            'booking_id' => 'required|exists:bookings,id',
            'pagseguro_order_id' => 'required|string',
            'pagseguro_payment_id' => 'required|string',
        ]);

        $bookingId = $request->input('booking_id');
        $orderId = $request->input('pagseguro_order_id');
        $paymentId = $request->input('pagseguro_payment_id');

        $booking = Booking::findOrFail($bookingId);

        try {
            // Verify payment status with PagSeguro API
            $response = Http::withHeaders([
                'Authorization' => config('pagseguro.api_key'),
                'Content-Type' => 'application/json'
            ])->get(config('pagseguro.api_url') . '/orders/' . $orderId);

            if (!$response->successful()) {
                return redirect()->route('bookings.payment', $booking->id)
                    ->with('error', 'Failed to verify payment with PagSeguro. Please try again or contact support.');
            }

            $paymentData = $response->json();
            $paymentStatus = $paymentData['status'] ?? null;

            // Check if payment is approved
            if ($paymentStatus !== 'PAID') {
                return redirect()->route('bookings.payment', $booking->id)
                    ->with('error', 'Payment was not approved. Status: ' . $paymentStatus);
            }

            // Start database transaction
            DB::beginTransaction();

            // Create payment record
            $payment = Payment::create([
                'booking_id' => $booking->id,
                'user_id' => Auth::id(),
                'payment_method' => 'pagseguro',
                'transaction_id' => $paymentId,
                'amount' => $booking->total_amount,
                'type' => 'booking',
                'status' => 'completed',
                'currency' => config('pagseguro.currency'),
                'payment_details' => json_encode([
                    'order_id' => $orderId,
                    'payment_id' => $paymentId,
                ]),
            ]);

            // Update booking status
            $booking->payment_status = 'paid';
            $booking->save();

            DB::commit();

            return redirect()->route('bookings.show', $booking->id)
                ->with('success', 'Payment completed successfully!');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('PagSeguro Payment Processing Error: ' . $e->getMessage());

            return redirect()->route('bookings.payment', $booking->id)
                ->with('error', 'An error occurred while processing your payment. Please try again or contact support.');
        }
    }

    /**
     * Handle PagSeguro webhook
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function webhook(Request $request)
    {
        Log::info('PagSeguro Webhook Received', $request->all());

        // Verify webhook signature
        $signature = $request->header('X-Signature');
        $webhookSecret = config('pagseguro.webhook_secret');

        if (empty($webhookSecret)) {
            Log::warning('PagSeguro webhook secret is not configured');
            return response()->json(['message' => 'Webhook received but not processed'], 200);
        }

        // Verify signature using PagSeguro's webhook signature method
        $payload = $request->getContent();
        $expectedSignature = hash_hmac('sha256', $payload, $webhookSecret);

        if (!hash_equals($expectedSignature, $signature)) {
            Log::warning('Invalid PagSeguro webhook signature', [
                'expected' => $expectedSignature,
                'received' => $signature
            ]);
            return response()->json(['error' => 'Invalid signature'], 400);
        }

        // Process the webhook
        $payload = $request->all();
        $eventType = $payload['event'] ?? null;
        $resourceId = $payload['resource_id'] ?? null;

        if (!$eventType || !$resourceId) {
            return response()->json(['error' => 'Missing event or resource_id'], 400);
        }

        try {
            // Handle different event types
            switch ($eventType) {
                case 'PAYMENT.APPROVED':
                    $this->handlePaymentApproved($resourceId);
                    break;
                case 'PAYMENT.CANCELLED':
                    $this->handlePaymentCancelled($resourceId);
                    break;
                case 'PAYMENT.REFUNDED':
                    $this->handlePaymentRefunded($resourceId);
                    break;
                default:
                    Log::info("Unhandled PagSeguro event type: {$eventType}");
            }

            return response()->json(['message' => 'Webhook processed successfully'], 200);
        } catch (\Exception $e) {
            Log::error('Error processing PagSeguro webhook: ' . $e->getMessage());
            return response()->json(['error' => 'Error processing webhook'], 500);
        }
    }

    /**
     * Handle payment approved event
     *
     * @param string $resourceId
     * @return void
     */
    private function handlePaymentApproved($resourceId)
    {
        // Fetch payment details from PagSeguro API
        $response = Http::withHeaders([
            'Authorization' => config('pagseguro.api_key'),
            'Content-Type' => 'application/json'
        ])->get(config('pagseguro.api_url') . '/orders/' . $resourceId);

        if (!$response->successful()) {
            Log::error('Failed to fetch payment details from PagSeguro: ' . $response->body());
            return;
        }

        $paymentData = $response->json();
        $referenceId = $paymentData['reference_id'] ?? null;

        if (!$referenceId) {
            Log::error('Missing reference_id in PagSeguro payment data');
            return;
        }

        // Find booking by reference ID (booking number)
        $booking = Booking::where('booking_number', $referenceId)->first();

        if (!$booking) {
            Log::error("Booking not found for reference_id: {$referenceId}");
            return;
        }

        // Check if payment already exists
        $existingPayment = Payment::where('booking_id', $booking->id)
            ->where('payment_method', 'pagseguro')
            ->where('status', 'completed')
            ->first();

        if ($existingPayment) {
            Log::info("Payment already processed for booking: {$booking->id}");
            return;
        }

        // Start database transaction
        DB::beginTransaction();

        try {
            // Create payment record
            $payment = Payment::create([
                'booking_id' => $booking->id,
                'user_id' => $booking->user_id,
                'payment_method' => 'pagseguro',
                'transaction_id' => $resourceId,
                'amount' => $booking->total_amount,
                'type' => 'booking',
                'status' => 'completed',
                'currency' => config('pagseguro.currency'),
                'payment_details' => json_encode($paymentData),
            ]);

            // Update booking status
            $booking->payment_status = 'paid';
            $booking->save();

            DB::commit();

            Log::info("Payment approved and processed for booking: {$booking->id}");
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error processing payment approval: ' . $e->getMessage());
        }
    }

    /**
     * Handle payment cancelled event
     *
     * @param string $resourceId
     * @return void
     */
    private function handlePaymentCancelled($resourceId)
    {
        // Implementation for handling cancelled payments
        Log::info("Processing payment cancellation for resource: {$resourceId}");
    }

    /**
     * Handle payment refunded event
     *
     * @param string $resourceId
     * @return void
     */
    private function handlePaymentRefunded($resourceId)
    {
        // Implementation for handling refunded payments
        Log::info("Processing payment refund for resource: {$resourceId}");
    }
}

<?php

namespace App\Http\Controllers;

use App\Models\Country;
use App\Models\State;
use Illuminate\Http\Request;

class StatesController extends Controller
{
    // Fetch all states
    public function index()
    {
        $states = State::with('country')->get();
        $countries = Country::all();
        
        return view('backend.states.index',compact('states','countries'));

    }

    // Store a new state
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'country_id' => 'required|integer|exists:countries,id',
            'status' => 'required|in:0,1',

        ]);

        $state = State::create($validatedData);

        return response()->json([
            'message' => 'State created successfully',
            'state' => $state
        ]);
    }

    // Update an existing state
    public function update(Request $request, $id)
    {
        $state = State::findOrFail($id);

        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'country_id' => 'required|integer|exists:countries,id',
            'status' => 'required|in:0,1',

        ]);

        $state->update($validatedData);

        return response()->json([
            'message' => 'State updated successfully',
            'state' => $state
        ]);
    }

    // Delete a state
    public function destroy($id)
    {
        $state = State::findOrFail($id);
        $state->delete();

        return response()->json([
            'message' => 'State deleted successfully'
        ]);
    }
}

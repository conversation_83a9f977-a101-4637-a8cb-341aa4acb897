<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Razor<PERSON><PERSON> Keys
    |--------------------------------------------------------------------------
    |
    | The Razorpay publishable key and secret key give you access to Razorpay's
    | API. The "publishable" key is typically used when interacting with
    | Razorpay.js while the "secret" key accesses private API endpoints.
    |
    */

    'key' => env('RAZORPAY_KEY', ''),
    'secret' => env('RAZORPAY_SECRET', ''),

    /*
    |--------------------------------------------------------------------------
    | Ra<PERSON><PERSON>y Webhook Secret
    |--------------------------------------------------------------------------
    |
    | The Razorpay webhook secret is used to validate that the webhooks are
    | actually coming from Razorpay. The webhook secret is used to compute
    | a signature that is sent with each webhook request.
    |
    */

    'webhook_secret' => env('RAZORPAY_WEBHOOK_SECRET', ''),

    /*
    |--------------------------------------------------------------------------
    | Currency
    |--------------------------------------------------------------------------
    |
    | This is the default currency that will be used when generating charges
    | from your application. Of course, you are welcome to use any of the
    | various currencies which Razorpay supports.
    |
    */

    'currency' => env('RAZORPAY_CURRENCY', 'USD'),

    /*
    |--------------------------------------------------------------------------
    | Test Mode
    |--------------------------------------------------------------------------
    |
    | This option determines if Razorpay operates in test mode or live mode.
    | This helps you test the integration without making actual charges.
    |
    */

    'test_mode' => env('RAZORPAY_TEST_MODE', true),
];

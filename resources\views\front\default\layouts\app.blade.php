<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'Laravel') }}</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])

        <!-- jQuery (ensure it's loaded before other scripts) -->
        <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

        <!-- Theme-specific assets (only loads the active theme) -->
        @php
            $activeTheme = get_theme(); // Using the helper function we created
        @endphp

        <!-- Custom CSS -->
        <link rel="stylesheet" href="{{ asset('css/animations.css') }}">
        <link rel="stylesheet" href="{{ asset('css/quill-override.css') }}">
        <link rel="stylesheet" href="{{ asset('css/consistent-ui.css') }}">
        <link rel="stylesheet" href="{{ asset('css/custom-fixes.css') }}">

        <!-- Custom JS -->
        <script src="{{ asset('js/gallery-helpers.js') }}" defer></script>

        <!-- Add theme class to body -->
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                document.body.classList.add('theme-{{ $activeTheme }}');
            });
        </script>

        <!-- Page-specific scripts -->
        @if(request()->route()->getName() == 'home')
            <script src="{{ asset('js/homepage.js') }}" defer></script>
        @endif
    </head>
    <body class="font-sans antialiased">
        <div class="min-h-screen bg-gray-100 dark:bg-gray-900">
            @include('front.default.layouts.navigation')

            <!-- Page Heading -->
            @isset($header)
                <header class="bg-white dark:bg-gray-800 shadow">
                    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                        {{ $header }}
                    </div>
                </header>
            @endisset

            <!-- Page Content -->
            <main>
                {{ $slot }}
            </main>

            @include('front.default.layouts.footer')

        </div>

        <!-- Language Translations -->
        <script>
            // The error is in the translations object.
            // The English translations are showing Portuguese content.
            // Here's the corrected translations section:

            const translations = {
                en: {
                    // Navigation
                    home: "Home",
                    rent_car: "Rent a Car",
                    list_car: "List Your Car",
                    how_works: "How It Works",
                    contact: "Contact",
                    login: "Login",
                    signup: "Sign Up",
                    language: "Language",

                    // Hero Section
                    hero_title: "Share Cars, Share Journeys",
                    hero_subtitle: "Rent cars from local owners or earn money by sharing your own vehicle. Join the community that's changing how we travel.",
                    rent_car_btn: "Rent a Car",
                    list_car_btn: "List Your Car",
                    location_label: "Location",
                    from_label: "From",
                    to_label: "To",
                    car_type_label: "Car Type",
                    any_car: "Any Type",
                    economy: "Economy",
                    suv: "SUV",
                    luxury: "Luxury",
                    convertible: "Convertible",
                    find_cars_btn: "Find Available Cars",

                    // Stats Section
                    cars_listed: "Cars Listed",
                    happy_users: "Happy Users",
                    cities: "Cities",
                    owner_earnings: "Owner Earnings",

                    // How It Works
                    how_carbnb_works: "How CARBNB Works",
                    join_thousands: "Join thousands who are already saving money and making extra income.",
                    rent_a_car_title: "Rent a Car",
                    search_compare: "Search & Compare",
                    search_compare_desc: "Browse thousands of vehicles by location, date, and price to find your ideal ride.",
                    book_instantly: "Book Instantly",
                    book_instantly_desc: "Reserve your chosen car with our secure booking system. No waiting for approval.",
                    pickup_go: "Pick Up & Go",
                    pickup_go_desc: "Meet the owner to collect the keys or use our contactless pickup option where available.",
                    find_car_btn: "Find a Car",
                    list_your_car_title: "List Your Car",
                    list_for_free: "List for Free",
                    list_for_free_desc: "Create a listing with photos, set your price and availability. It takes just 10 minutes.",
                    receive_bookings: "Receive Bookings",
                    receive_bookings_desc: "Get notified when someone books your car. Accept or decline based on your schedule.",
                    get_paid: "Get Paid",
                    get_paid_desc: "Earnings are automatically deposited to your account after each completed trip.",
                    list_car_btn2: "List Your Car",

                    // Car Types
                    browse_car_type: "Browse by Car Type",
                    find_perfect_vehicle: "Find the perfect vehicle for any occasion, from compact city cars to spacious SUVs.",
                    economy_desc: "Fuel-efficient and budget-friendly for city driving.",
                    suv_desc: "Spacious vehicles perfect for families and road trips.",
                    luxury_desc: "Premium vehicles for special occasions and business trips.",
                    convertible_desc: "Open-top cars for an elevated driving experience.",
                    from_price_economy: "From €30/day",
                    from_price_suv: "From €55/day",
                    from_price_luxury: "From €100/day",
                    from_price_convertible: "From €75/day",

                    // Featured Cars
                    top_rated_cars: "Top Rated Cars",
                    discover_highest_rated: "Discover our highest-rated vehicles from trusted owners in our community.",
                    top_rated: "Top Rated",
                    price_tesla: "€95/day",
                    price_bmw: "€85/day",
                    price_toyota: "€65/day",
                    instant_book: "Instant Book",
                    low_price: "Low Price",
                    electric: "Electric",
                    gasoline: "Gasoline",
                    hybrid: "Hybrid",
                    automatic: "Automatic",
                    seats_5: "5 Seats",
                    bags_2: "2 Bags",
                    bags_3: "3 Bags",
                    bags_4: "4 Bags",
                    book_now: "Book Now",
                    browse_all: "Browse all cars",

                    // Benefits
                    why_choose: "Why Choose CARBNB",
                    benefits_desc: "Our peer-to-peer marketplace offers benefits for both car owners and renters.",
                    insurance_included: "Insurance Included",
                    insurance_desc: "Every booking comes with comprehensive insurance coverage for peace of mind.",
                    earn_money: "Earn Money Sharing",
                    earn_money_desc: "Car owners can earn an average of €500+ per month sharing their vehicle.",
                    save_rentals: "Save on Rentals",
                    save_rentals_desc: "Renters typically save 25% compared to traditional car rental agencies.",

                    // Testimonials
                    community_says: "What Our Community Says",
                    real_stories: "Real stories from car owners and renters in our growing community.",
                    car_owner: "Car Owner",
                    car_renter: "Car Renter",
                    owner_renter: "Car Owner & Renter",
                    testimonial_1: "\"My car was just sitting in my driveway most days. Now it makes me over €600 a month on CARBNB! The platform is easy to use and the support team is always helpful.\"",
                    testimonial_2: "\"I needed a truck for a weekend move and found the perfect vehicle on CARBNB. Way cheaper than rental companies and the owner was super flexible with pickup and drop-off times.\"",
                    testimonial_3: "\"I list my sedan during the week and rent SUVs for weekend trips. CARBNB has completely changed how I think about car ownership. The community is respectful and the process is seamless.\"",

                    // Trust & Safety
                    trust_safety: "Trust & Safety Guaranteed",
                    trust_safety_desc: "Your safety is our top priority. Every trip is protected by our comprehensive insurance and 24/7 roadside assistance.",
                    insurance_protection: "€1M Insurance Protection",
                    insurance_protection_desc: "Every trip includes liability insurance up to €1 million.",
                    roadside: "24/7 Roadside Assistance",
                    roadside_desc: "Help is always available if you need it on the road.",
                    secure_payments: "Secure Payments",
                    secure_payments_desc: "All transactions are processed securely through our platform.",
                    learn_more: "Learn More",

                    // Cities
                    available_cities: "Available in Your City",
                    expanding_cities: "We're rapidly expanding to cities across Portugal. Find cars to share near you.",
                    lisbon: "Lisbon",
                    porto: "Porto",
                    faro: "Faro",
                    braga: "Braga",
                    coimbra: "Coimbra",
                    aveiro: "Aveiro",
                    view_locations: "View all locations",

                    // CTA
                    ready_join: "Ready to Join Our Community?",
                    ready_join_desc: "Whether you want to rent a car or share your own, it's free to join CARBNB and start exploring.",
                    rent_car_cta: "Rent a Car",
                    list_car_cta: "List Your Car",

                    // Footer
                    footer_desc: "The peer-to-peer car sharing marketplace that connects car owners with people who need a vehicle.",
                    company: "Company",
                    about_us: "About Us",
                    how_works_footer: "How It Works",
                    careers: "Careers",
                    press: "Press",
                    investors: "Investors",
                    resources: "Resources",
                    trust_safety_footer: "Trust & Safety",
                    help_center: "Help Center",
                    owner_resources: "Owner Resources",
                    insurance_footer: "Insurance",
                    community_guidelines: "Community Guidelines",
                    newsletter: "Newsletter",
                    newsletter_desc: "Subscribe to our newsletter for the latest updates and offers.",
                    email_placeholder: "Your email address",
                    privacy_note: "We respect your privacy. Unsubscribe at any time.",
                    copyright: "© 2025 CARBNB. All rights reserved.",
                    terms: "Terms of Service",
                    privacy: "Privacy Policy",
                    cookie: "Cookie Policy"
                },
                pt: {
                    // Navigation
                    home: "Início",
                    rent_car: "Alugar um Carro",
                    list_car: "Anunciar Carro",
                    how_works: "Como Funciona",
                    contact: "Contacto",
                    login: "Entrar",
                    signup: "Registar",
                    language: "Idioma",

                    // Hero Section
                    hero_title: "Partilhe Carros, Partilhe Viagens",
                    hero_subtitle: "Alugue carros de proprietários locais ou ganhe dinheiro partilhando o seu próprio veículo. Junte-se à comunidade que está a mudar a forma como viajamos.",
                    rent_car_btn: "Alugar um Carro",
                    list_car_btn: "Anunciar Carro",
                    location_label: "Localização",
                    from_label: "De",
                    to_label: "Até",
                    car_type_label: "Tipo de Carro",
                    any_car: "Qualquer Tipo",
                    economy: "Económico",
                    suv: "SUV",
                    luxury: "Luxo",
                    convertible: "Conversível",
                    find_cars_btn: "Encontrar Carros Disponíveis",

                    // Stats Section
                    cars_listed: "Carros Anunciados",
                    happy_users: "Utilizadores Satisfeitos",
                    cities: "Cidades",
                    owner_earnings: "Ganhos dos Proprietários",

                    // How It Works
                    how_carbnb_works: "Como Funciona o CARBNB",
                    join_thousands: "Junte-se a milhares que já estão a poupar dinheiro e a ganhar rendimento extra.",
                    rent_a_car_title: "Alugar um Carro",
                    search_compare: "Pesquisar e Comparar",
                    search_compare_desc: "Navegue por milhares de veículos por localização, data e preço para encontrar o carro ideal.",
                    book_instantly: "Reserve Instantaneamente",
                    book_instantly_desc: "Reserve o carro escolhido com o nosso sistema de reservas seguro. Sem esperar por aprovação.",
                    pickup_go: "Levante e Vá",
                    pickup_go_desc: "Encontre o proprietário para receber as chaves ou use a nossa opção de levantamento sem contacto quando disponível.",
                    find_car_btn: "Encontrar um Carro",
                    list_your_car_title: "Anunciar Carro",
                    list_for_free: "Anuncie Gratuitamente",
                    list_for_free_desc: "Crie um anúncio com fotos, defina o preço e disponibilidade. Demora apenas 10 minutos.",
                    receive_bookings: "Receba Reservas",
                    receive_bookings_desc: "Seja notificado quando alguém reserva o seu carro. Aceite ou recuse com base na sua agenda.",
                    get_paid: "Receba Pagamento",
                    get_paid_desc: "Os ganhos são depositados automaticamente na sua conta após cada viagem concluída.",
                    list_car_btn2: "Anunciar o Seu Carro",

                    // Car Types
                    browse_car_type: "Procurar por Tipo de Carro",
                    find_perfect_vehicle: "Encontre o veículo perfeito para qualquer ocasião, desde carros compactos até SUVs espaçosos.",
                    economy_desc: "Eficientes em termos de combustível e económicos para condução na cidade.",
                    suv_desc: "Veículos espaçosos perfeitos para famílias e viagens na estrada.",
                    luxury_desc: "Veículos premium para ocasiões especiais e viagens de negócios.",
                    convertible_desc: "Carros descapotáveis para uma experiência de condução elevada.",
                    from_price_economy: "A partir de €30/dia",
                    from_price_suv: "A partir de €55/dia",
                    from_price_luxury: "A partir de €100/dia",
                    from_price_convertible: "A partir de €75/dia",

                    // Featured Cars
                    top_rated_cars: "Carros Mais Bem Avaliados",
                    discover_highest_rated: "Descubra os nossos veículos mais bem avaliados de proprietários confiáveis na nossa comunidade.",
                    top_rated: "Mais Bem Avaliado",
                    price_tesla: "€95/dia",
                    price_bmw: "€85/dia",
                    price_toyota: "€65/dia",
                    instant_book: "Reserva Instantânea",
                    low_price: "Preço Baixo",
                    electric: "Elétrico",
                    gasoline: "Gasolina",
                    hybrid: "Híbrido",
                    automatic: "Automático",
                    seats_5: "5 Lugares",
                    bags_2: "2 Malas",
                    bags_3: "3 Malas",
                    bags_4: "4 Malas",
                    book_now: "Reservar Agora",
                    browse_all: "Ver todos os carros",

                    // Benefits
                    why_choose: "Porquê Escolher CARBNB",
                    benefits_desc: "O nosso mercado peer-to-peer oferece benefícios tanto para proprietários como para quem aluga.",
                    insurance_included: "Seguro Incluído",
                    insurance_desc: "Cada reserva vem com cobertura abrangente de seguro para sua tranquilidade.",
                    earn_money: "Ganhe Dinheiro Partilhando",
                    earn_money_desc: "Os proprietários de carros podem ganhar uma média de €500+ por mês partilhando o seu veículo.",
                    save_rentals: "Poupe em Alugueres",
                    save_rentals_desc: "Quem aluga normalmente poupa 25% em comparação com agências tradicionais de aluguer de carros.",

                    // Testimonials
                    community_says: "O Que Diz a Nossa Comunidade",
                    real_stories: "Histórias reais de proprietários e locatários na nossa comunidade crescente.",
                    car_owner: "Proprietário de Carro",
                    car_renter: "Locatário de Carro",
                    owner_renter: "Proprietário & Locatário",
                    testimonial_1: "\"O meu carro estava parado na garagem na maior parte dos dias. Agora rende-me mais de €600 por mês no CARBNB! A plataforma é fácil de usar e a equipa de suporte está sempre disponível.\"",
                    testimonial_2: "\"Precisava de uma carrinha para uma mudança de fim de semana e encontrei o veículo perfeito no CARBNB. Muito mais barato que as empresas de aluguer e o proprietário foi super flexível com os horários de recolha e entrega.\"",
                    testimonial_3: "\"Anuncio o meu sedan durante a semana e alugo SUVs para viagens de fim de semana. O CARBNB mudou completamente a forma como penso sobre a propriedade de carros. A comunidade é respeitosa e o processo é perfeito.\"",

                    // Trust & Safety
                    trust_safety: "Confiança e Segurança Garantidas",
                    trust_safety_desc: "A sua segurança é a nossa principal prioridade. Cada viagem é protegida pelo nosso seguro abrangente e assistência na estrada 24/7.",
                    insurance_protection: "Proteção de Seguro €1M",
                    insurance_protection_desc: "Cada viagem inclui seguro de responsabilidade até €1 milhão.",
                    roadside: "Assistência na Estrada 24/7",
                    roadside_desc: "A ajuda está sempre disponível se precisar na estrada.",
                    secure_payments: "Pagamentos Seguros",
                    secure_payments_desc: "Todas as transações são processadas com segurança através da nossa plataforma.",
                    learn_more: "Saiba Mais",

                    // Cities
                    available_cities: "Disponível na Sua Cidade",
                    expanding_cities: "Estamos a expandir rapidamente para cidades em todo Portugal. Encontre carros para partilhar perto de si.",
                    lisbon: "Lisboa",
                    porto: "Porto",
                    faro: "Faro",
                    braga: "Braga",
                    coimbra: "Coimbra",
                    aveiro: "Aveiro",
                    view_locations: "Ver todas as localizações",

                    // CTA
                    ready_join: "Pronto Para Se Juntar à Nossa Comunidade?",
                    ready_join_desc: "Quer alugar um carro ou partilhar o seu próprio, é gratuito juntar-se ao CARBNB e começar a explorar.",
                    rent_car_cta: "Alugar um Carro",
                    list_car_cta: "Anunciar o Seu Carro",

                    // Footer
                    footer_desc: "O mercado de partilha de carros peer-to-peer que conecta proprietários de carros com pessoas que precisam de um veículo.",
                    company: "Empresa",
                    about_us: "Sobre Nós",
                    how_works_footer: "Como Funciona",
                    careers: "Carreiras",
                    press: "Imprensa",
                    investors: "Investidores",
                    resources: "Recursos",
                    trust_safety_footer: "Confiança e Segurança",
                    help_center: "Centro de Ajuda",
                    owner_resources: "Recursos para Proprietários",
                    insurance_footer: "Seguro",
                    community_guidelines: "Diretrizes da Comunidade",
                    newsletter: "Newsletter",
                    newsletter_desc: "Subscreva a nossa newsletter para receber as últimas atualizações e ofertas.",
                    email_placeholder: "O seu endereço de email",
                    privacy_note: "Respeitamos a sua privacidade. Cancele a subscrição a qualquer momento.",
                    copyright: "© 2025 CARBNB. Todos os direitos reservados.",
                    terms: "Termos de Serviço",
                    privacy: "Política de Privacidade",
                    cookie: "Política de Cookies"
                }
            };

            // Set the default language based on the current locale
            let currentLanguage = '{{ $currentLocale ?? 'en' }}';

            // Function to update all text elements
            function updateLanguage(lang) {
                // We don't need to update the language here anymore since we're using server-side translations
                // Just update the current language display
                document.getElementById('current-language').textContent = lang.toUpperCase();
            }

            $(document).ready(function() {
                // Set initial language display
                document.getElementById('current-language').textContent = currentLanguage.toUpperCase();

                // Language dropdown toggle
                $('#language-button').click(function(e) {
                    e.stopPropagation();
                    $('#language-dropdown').toggleClass('hidden');
                });

                // Hide dropdown when clicking elsewhere
                $(document).click(function() {
                    $('#language-dropdown').addClass('hidden');
                });

                // Mobile menu toggle
                $('#mobile-menu-button').click(function() {
                    $('#mobile-menu').slideToggle();
                });

                // Featured cars carousel
                const carouselContainer = $('.carousel-container');
                const carouselItems = $('.carousel-item');
                const prevBtn = $('#prev-btn');
                const nextBtn = $('#next-btn');
                let currentIndex = 0;

                // Hide prev button initially
                prevBtn.hide();

                // Set item width based on screen size
                function setItemWidth() {
                    if ($(window).width() < 640) {
                        return carouselContainer.width();
                    } else if ($(window).width() < 1024) {
                        return carouselContainer.width() / 2;
                    } else {
                        return carouselContainer.width() / 3;
                    }
                }

                function updateCarousel() {
                    const itemWidth = setItemWidth();
                    carouselContainer.animate({
                        scrollLeft: currentIndex * itemWidth
                    }, 300);

                    // Show/hide navigation buttons
                    if (currentIndex === 0) {
                        prevBtn.hide();
                    } else {
                        prevBtn.show();
                    }

                    if (currentIndex >= carouselItems.length - ($(window).width() < 640 ? 1 : $(window).width() < 1024 ? 2 : 3)) {
                        nextBtn.hide();
                    } else {
                        nextBtn.show();
                    }
                }

                prevBtn.click(function() {
                    if (currentIndex > 0) {
                        currentIndex--;
                        updateCarousel();
                    }
                });

                nextBtn.click(function() {
                    if (currentIndex < carouselItems.length - 1) {
                        currentIndex++;
                        updateCarousel();
                    }
                });

                // Update carousel on window resize
                $(window).resize(function() {
                    updateCarousel();
                });

                // Initialize carousel
                updateCarousel();

                // Set default dates for search
                const currentDate = new Date();
                const returnDate = new Date(currentDate);
                returnDate.setDate(returnDate.getDate() + 3);

                $('input[type="date"]').eq(0).val(currentDate.toISOString().substr(0, 10));
                $('input[type="date"]').eq(1).val(returnDate.toISOString().substr(0, 10));
            });
        </script>

        <!-- Stack for page-specific scripts -->
        @stack('scripts')
    </body>
</html>

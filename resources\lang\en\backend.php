<?php

return [
    // General
    'dashboard' => 'Dashboard',
    'welcome' => 'Welcome to CARBNB Admin',
    'manage' => 'Manage',
    'create' => 'Create',
    'edit' => 'Edit',
    'update' => 'Update',
    'delete' => 'Delete',
    'save' => 'Save',
    'cancel' => 'Cancel',
    'back' => 'Back',
    'actions' => 'Actions',
    'confirm_delete' => 'Are you sure you want to delete this item?',
    'yes' => 'Yes',
    'no' => 'No',
    'success' => 'Success',
    'error' => 'Error',
    'warning' => 'Warning',
    'info' => 'Information',
    'search' => 'Search',
    'filter' => 'Filter',
    'reset' => 'Reset',
    'show' => 'Show',
    'entries' => 'entries',
    'showing' => 'Showing',
    'to' => 'to',
    'of' => 'of',
    'no_data' => 'No data available',
    'loading' => 'Loading...',
    'processing' => 'Processing...',
    'active' => 'Active',
    'inactive' => 'Inactive',
    'status' => 'Status',
    'created_at' => 'Created At',
    'updated_at' => 'Updated At',

    // Sidebar
    'admin_panel' => 'Admin Panel',
    'main_navigation' => 'Main Navigation',
    'view_site' => 'View Site',
    'settings' => 'Settings',
    'profile' => 'Profile',
    'logout' => 'Logout',
    'admin' => 'Admin',
    'document_verification' => 'Document Verification',
    'driver_management' => 'Driver Management',
    'my_bookings' => 'My Bookings',
    'received_bookings' => 'Received Bookings',
    'add_vehicles' => 'Add Vehicles',
    'vehicle_type' => 'Vehicle Type',
    'driver_account' => 'Driver Account',
    'register_as_driver' => 'Register as Driver',
    'driver_profile' => 'Driver Profile',
    'documents' => 'Documents',
    'messages' => 'Messages',
    'disputes' => 'Disputes',
    'pages' => 'Pages',
    'configurations' => 'Configurations',
    'features_activation' => 'Features Activation',
    'theme_settings' => 'Theme Settings',
    'file_manager' => 'File Manager',
    'location' => 'Location',
    'access_control' => 'Access Control',
    'revenue_dashboard' => 'Revenue Dashboard',
    'referrals' => 'Referrals',

    // Users
    'users' => 'Users',
    'user_management' => 'User Management',
    'add_user' => 'Add User',
    'edit_user' => 'Edit User',
    'user_details' => 'User Details',
    'name' => 'Name',
    'email' => 'Email',
    'password' => 'Password',
    'confirm_password' => 'Confirm Password',
    'role' => 'Role',
    'roles' => 'Roles',
    'permissions' => 'Permissions',
    'last_login' => 'Last Login',
    'registered_since' => 'Registered Since',

    // Vehicles
    'vehicles' => 'Vehicles',
    'vehicle_management' => 'Vehicle Management',
    'add_vehicle' => 'Add Vehicle',
    'edit_vehicle' => 'Edit Vehicle',
    'vehicle_details' => 'Vehicle Details',
    'vehicle_types' => 'Vehicle Types',
    'add_vehicle_type' => 'Add Vehicle Type',
    'edit_vehicle_type' => 'Edit Vehicle Type',
    'make' => 'Make',
    'model' => 'Model',
    'year' => 'Year',
    'color' => 'Color',
    'license_plate' => 'License Plate',
    'mileage' => 'Mileage',
    'transmission' => 'Transmission',
    'fuel_type' => 'Fuel Type',
    'seats' => 'Seats',
    'doors' => 'Doors',
    'daily_rate' => 'Daily Rate',
    'weekly_discount' => 'Weekly Discount',
    'monthly_discount' => 'Monthly Discount',
    'security_deposit' => 'Security Deposit',
    'features' => 'Features',
    'description' => 'Description',
    'availability' => 'Availability',
    'location' => 'Location',
    'latitude' => 'Latitude',
    'longitude' => 'Longitude',
    'address' => 'Address',
    'city' => 'City',
    'owner' => 'Owner',
    'images' => 'Images',
    'primary_image' => 'Primary Image',
    'with_driver' => 'With Driver',
    'vehicle_status' => 'Vehicle Status',
    'pending' => 'Pending',
    'approved' => 'Approved',
    'rejected' => 'Rejected',
    'featured' => 'Featured',
    'toggle_featured' => 'Toggle Featured',
    'rejection_reason' => 'Rejection Reason',

    // Bookings
    'bookings' => 'Bookings',
    'booking_management' => 'Booking Management',
    'booking_details' => 'Booking Details',
    'booking_id' => 'Booking ID',
    'booking_date' => 'Booking Date',
    'pickup_date' => 'Pickup Date',
    'return_date' => 'Return Date',
    'total_days' => 'Total Days',
    'total_amount' => 'Total Amount',
    'payment_status' => 'Payment Status',
    'booking_status' => 'Booking Status',
    'customer' => 'Customer',
    'paid' => 'Paid',
    'unpaid' => 'Unpaid',
    'confirmed' => 'Confirmed',
    'cancelled' => 'Cancelled',
    'completed' => 'Completed',

    // Locations
    'locations' => 'Locations',
    'countries' => 'Countries',
    'states' => 'States',
    'cities' => 'Cities',
    'add_country' => 'Add Country',
    'edit_country' => 'Edit Country',
    'add_state' => 'Add State',
    'edit_state' => 'Edit State',
    'add_city' => 'Add City',
    'edit_city' => 'Edit City',
    'country_name' => 'Country Name',
    'country_code' => 'Country Code',
    'state_name' => 'State Name',
    'city_name' => 'City Name',

    // Settings
    'general_settings' => 'General Settings',
    'site_settings' => 'Site Settings',
    'payment_settings' => 'Payment Settings',
    'email_settings' => 'Email Settings',
    'notification_settings' => 'Notification Settings',
    'api_settings' => 'API Settings',
    'social_login' => 'Social Login',
    'google_maps' => 'Google Maps',
    'site_name' => 'Site Name',
    'site_title' => 'Site Title',
    'site_description' => 'Site Description',
    'site_logo' => 'Site Logo',
    'favicon' => 'Favicon',
    'admin_email' => 'Admin Email',
    'currency' => 'Currency',
    'timezone' => 'Timezone',
    'date_format' => 'Date Format',
    'time_format' => 'Time Format',
    'maintenance_mode' => 'Maintenance Mode',

    // Reports
    'reports' => 'Reports',
    'sales_report' => 'Sales Report',
    'booking_report' => 'Booking Report',
    'revenue_report' => 'Revenue Report',
    'user_report' => 'User Report',
    'vehicle_report' => 'Vehicle Report',
    'export' => 'Export',
    'print' => 'Print',
    'period' => 'Period',
    'daily' => 'Daily',
    'weekly' => 'Weekly',
    'monthly' => 'Monthly',
    'yearly' => 'Yearly',
    'custom' => 'Custom',
    'from_date' => 'From Date',
    'to_date' => 'To Date',
    'total_revenue' => 'Total Revenue',
    'total_bookings' => 'Total Bookings',
    'total_users' => 'Total Users',
    'total_vehicles' => 'Total Vehicles',

    // Dashboard
    'admin_dashboard' => 'Admin Dashboard',
    'agent_dashboard' => 'Agent Dashboard',
    'my_dashboard' => 'My Dashboard',
    'driver_dashboard' => 'Driver Dashboard',
    'managed_vehicles' => 'Managed Vehicles',
    'my_vehicles' => 'My Vehicles',
    'completed_trips' => 'Completed Trips',
    'booking_calendar' => 'Booking Calendar',

    // Notifications
    'notifications' => 'Notifications',
    'mark_all_read' => 'Mark All as Read',
    'view_all' => 'View All',
    'no_notifications' => 'No notifications',
    'new_booking' => 'New Booking',
    'booking_cancelled' => 'Booking Cancelled',
    'new_user' => 'New User Registered',
    'new_vehicle' => 'New Vehicle Listed',
    'payment_received' => 'Payment Received',
];
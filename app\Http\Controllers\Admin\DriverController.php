<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Driver;
use App\Models\Document;
use App\Models\User;
use Illuminate\Http\Request;

class DriverController extends Controller
{
    /**
     * Display a listing of the drivers.
     */
    public function index(Request $request)
    {
        $status = $request->input('status', 'pending');
        
        $drivers = Driver::with(['user', 'license', 'city'])
            ->when($status, function ($query, $status) {
                return $query->where('status', $status);
            })
            ->latest()
            ->paginate(10);
            
        return view('admin.drivers.index', compact('drivers', 'status'));
    }
    
    /**
     * Show the driver details.
     */
    public function show(Driver $driver)
    {
        $driver->load(['user', 'license', 'city', 'documents.documentType']);
        
        // Get all documents for this driver
        $documents = Document::where('user_id', $driver->user_id)
            ->with('documentType')
            ->get();
            
        return view('admin.drivers.show', compact('driver', 'documents'));
    }
    
    /**
     * Approve a driver.
     */
    public function approve(Driver $driver)
    {
        // Check if all required documents are approved
        $pendingDocuments = Document::where('user_id', $driver->user_id)
            ->where('status', 'pending')
            ->count();
            
        if ($pendingDocuments > 0) {
            return redirect()->back()
                ->with('error', 'Cannot approve driver. There are still pending documents that need to be reviewed.');
        }
        
        // Update driver status
        $driver->update([
            'status' => 'active',
        ]);
        
        // Also update the driver license status
        if ($driver->license) {
            $driver->license->update([
                'status' => 'verified',
            ]);
        }
        
        return redirect()->route('admin.drivers.index')
            ->with('success', 'Driver approved successfully.');
    }
    
    /**
     * Reject a driver.
     */
    public function reject(Request $request, Driver $driver)
    {
        $validated = $request->validate([
            'rejection_reason' => 'required|string|max:255',
        ]);
        
        // Update driver status
        $driver->update([
            'status' => 'rejected',
            'rejection_reason' => $validated['rejection_reason'],
        ]);
        
        // Also update the driver license status
        if ($driver->license) {
            $driver->license->update([
                'status' => 'rejected',
                'rejection_reason' => $validated['rejection_reason'],
            ]);
        }
        
        return redirect()->route('admin.drivers.index')
            ->with('success', 'Driver application rejected successfully.');
    }
    
    /**
     * Toggle the featured status of a driver.
     */
    public function toggleFeatured(Driver $driver)
    {
        $driver->update([
            'is_featured' => !$driver->is_featured,
        ]);
        
        return redirect()->back()
            ->with('success', 'Driver featured status updated successfully.');
    }
}

import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import path from 'path';
import fs from 'fs';
import { resolve } from 'path';

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'resources/css/app.css',
                'resources/js/app.js',
            ],
            refresh: true,
            // Keep 'build' as the required subdirectory
            buildDirectory: 'build',
        }),
        {
            // Custom plugin to copy manifest out of .vite folder
            name: 'copy-manifest',
            closeBundle: async () => {
                const viteManifestPath = resolve(__dirname, 'public/build/.vite/manifest.json');
                const targetManifestPath = resolve(__dirname, 'public/build/manifest.json');

                try {
                    if (fs.existsSync(viteManifestPath)) {
                        const manifest = fs.readFileSync(viteManifestPath, 'utf-8');
                        fs.writeFileSync(targetManifestPath, manifest);
                        console.log('✓ Manifest copied to public/build/manifest.json');
                    }
                } catch (error) {
                    console.error('Error copying manifest:', error);
                }
            }
        }
    ],
    build: {
        rollupOptions: {
            output: {
                manualChunks: {
                    // UI frameworks and core dependencies
                    'vendor-alpine': ['alpinejs', '@alpinejs/collapse', '@alpinejs/focus'],
                    'vendor-ui': ['sweetalert2', 'toastify-js'],

                    // Form and input related libraries
                    'vendor-forms': ['quill', '@yaireo/tagify', 'filepond'],

                    // Media related libraries
                    'vendor-media': ['swiper', 'lightgallery'],

                    // Document and export related libraries
                    'vendor-export': ['html2pdf.js'],

                    // Icons and styling
                    'vendor-icons': ['@fortawesome/fontawesome-free/js/all.min.js'],

                    // HTTP and data handling
                    'vendor-http': ['axios']
                }
            }
        },
        manifest: true,
        outDir: 'public/build',
        assetsDir: '',
        emptyOutDir: true,
        chunkSizeWarningLimit: 1000, // Increase the warning limit to 1000kb
    },
});
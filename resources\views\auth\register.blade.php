<x-guest-layout>
    <div class="relative min-h-screen flex flex-col sm:justify-center items-center pt-6 sm:pt-0">
        <!-- Background image with overlay -->
        <div class="absolute inset-0">
            <div class="absolute inset-0 bg-gradient-to-r from-black/70 to-black/40 z-10"></div>
            <img src="https://images.unsplash.com/photo-*************-eaa3f722e40d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                 alt="Car sharing" class="w-full h-full object-cover">
        </div>

        <div class="w-full sm:max-w-md mt-6 px-6 py-8 bg-white shadow-xl overflow-hidden sm:rounded-xl z-20 relative animate-fade-in">
            <div class="mb-8 text-center">
                <h2 class="text-3xl font-bold text-gray-800 mb-2">Create an Account</h2>
                @if(isset($referral))
                    <p class="text-gray-600 text-lg">You've been invited by {{ $referral->referrer->first_name }} {{ $referral->referrer->last_name }}</p>
                @else
                    <p class="text-gray-600 text-lg">Join our community today</p>
                @endif
            </div>

            <form method="POST" action="{{ route('register') }}">
                @csrf
                @if(isset($referral))
                    <input type="hidden" name="referral_token" value="{{ $referral->token }}">
                @endif

                <!-- First Name -->
                <div class="mb-5">
                    <x-input-label for="first_name" :value="__('First Name')" class="text-gray-700 font-medium" />
                    <div class="mt-1 relative rounded-xl shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <x-text-input id="first_name" class="block mt-1 w-full pl-10 p-4 focus:ring-primary focus:border-primary rounded-xl"
                                      type="text" name="first_name" :value="old('first_name')" required autofocus autocomplete="given-name"
                                      placeholder="John" />
                    </div>
                    <x-input-error :messages="$errors->get('first_name')" class="mt-2" />
                </div>

                <!-- Last Name -->
                <div class="mb-5">
                    <x-input-label for="last_name" :value="__('Last Name')" class="text-gray-700 font-medium" />
                    <div class="mt-1 relative rounded-xl shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <x-text-input id="last_name" class="block mt-1 w-full pl-10 p-4 focus:ring-primary focus:border-primary rounded-xl"
                                      type="text" name="last_name" :value="old('last_name')" required autocomplete="family-name"
                                      placeholder="Doe" />
                    </div>
                    <x-input-error :messages="$errors->get('last_name')" class="mt-2" />
                </div>

                <!-- Email Address -->
                <div class="mb-5">
                    <x-input-label for="email" :value="__('Email Address')" class="text-gray-700 font-medium" />
                    <div class="mt-1 relative rounded-xl shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                                <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                            </svg>
                        </div>
                        <x-text-input id="email" class="block mt-1 w-full pl-10 p-4 focus:ring-primary focus:border-primary rounded-xl"
                                      type="email" name="email" :value="old('email')" required autocomplete="username"
                                      placeholder="<EMAIL>" />
                    </div>
                    <x-input-error :messages="$errors->get('email')" class="mt-2" />
                </div>

                <!-- Password -->
                <div class="mb-5">
                    <x-input-label for="password" :value="__('Password')" class="text-gray-700 font-medium" />
                    <div class="mt-1 relative rounded-xl shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <x-text-input id="password" class="block mt-1 w-full pl-10 p-4 focus:ring-primary focus:border-primary rounded-xl"
                                      type="password" name="password" required autocomplete="new-password"
                                      placeholder="••••••••" />
                    </div>
                    <x-input-error :messages="$errors->get('password')" class="mt-2" />
                    <p class="text-xs text-gray-500 mt-2">Password must be at least 8 characters and contain at least one uppercase letter, one number, and one special character.</p>
                </div>

                <!-- Confirm Password -->
                <div class="mb-6">
                    <x-input-label for="password_confirmation" :value="__('Confirm Password')" class="text-gray-700 font-medium" />
                    <div class="mt-1 relative rounded-xl shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <x-text-input id="password_confirmation" class="block mt-1 w-full pl-10 p-4 focus:ring-primary focus:border-primary rounded-xl"
                                      type="password" name="password_confirmation" required autocomplete="new-password"
                                      placeholder="••••••••" />
                    </div>
                    <x-input-error :messages="$errors->get('password_confirmation')" class="mt-2" />
                </div>

                <!-- Terms and Conditions -->
                <div class="mb-6">
                    <label for="terms" class="inline-flex items-center">
                        <input id="terms" type="checkbox"
                               class="rounded border-gray-300 text-primary shadow-sm focus:ring-primary"
                               name="terms" required>
                        <span class="ms-2 text-sm text-gray-600">
                            {{ __('I agree to the') }} {{ __('Terms of Service') }}
{{--                            <a href="{{ route('terms.show') }}" class="text-primary hover:text-red-600">{{ __('Terms of Service') }}</a>--}}
{{--                            {{ __('and') }}--}}
{{--                            <a href="{{ route('policy.show') }}" class="text-primary hover:text-red-600">{{ __('Privacy Policy') }}</a>--}}
                        </span>
                    </label>
                </div>

                <div class="flex flex-col space-y-4">
                    <button type="submit" class="w-full flex justify-center py-4 px-4 border border-transparent rounded-xl shadow-lg text-white bg-primary hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all transform hover:scale-[1.02] font-bold text-lg">
                        {{ __('Create Account') }}
                    </button>

                    <div class="text-center">
                        <a class="text-sm text-primary hover:text-red-600 font-medium" href="{{ route('login') }}">
                            {{ __('Already have an account? Sign in') }}
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</x-guest-layout>

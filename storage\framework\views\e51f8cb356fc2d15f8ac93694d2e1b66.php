<?php if (isset($component)) { $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742 = $attributes; } ?>
<?php $component = App\View\Components\AppBackendLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-backend-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppBackendLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <?php echo e(__('Driver Details')); ?>

            </h2>
            <a href="<?php echo e(route('admin.drivers.index')); ?>" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gray-600 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                <i class="fas fa-arrow-left mr-2"></i>
                <?php echo e(__('Back to Drivers')); ?>

            </a>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="mb-6">
                        <h3 class="text-lg font-medium text-gray-900"><?php echo e(__('Driver Information')); ?></h3>
                    </div>

                    <!-- Driver Status Banner -->
                    <div class="mb-6 p-4 rounded-md
                        <?php if($driver->status === 'active'): ?> bg-green-50 border border-green-200
                        <?php elseif($driver->status === 'pending'): ?> bg-yellow-50 border border-yellow-200
                        <?php elseif($driver->status === 'rejected'): ?> bg-red-50 border border-red-200
                        <?php else: ?> bg-gray-50 border border-gray-200 <?php endif; ?>">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <?php if($driver->status === 'active'): ?>
                                    <i class="fas fa-check-circle text-green-600"></i>
                                <?php elseif($driver->status === 'pending'): ?>
                                    <i class="fas fa-clock text-yellow-600"></i>
                                <?php elseif($driver->status === 'rejected'): ?>
                                    <i class="fas fa-times-circle text-red-600"></i>
                                <?php else: ?>
                                    <i class="fas fa-ban text-gray-600"></i>
                                <?php endif; ?>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium
                                    <?php if($driver->status === 'active'): ?> text-green-800
                                    <?php elseif($driver->status === 'pending'): ?> text-yellow-800
                                    <?php elseif($driver->status === 'rejected'): ?> text-red-800
                                    <?php else: ?> text-gray-800 <?php endif; ?>">
                                    <?php echo e(__('Status')); ?>: <?php echo e(ucfirst($driver->status)); ?>

                                </h3>
                                <?php if($driver->status === 'rejected' && $driver->rejection_reason): ?>
                                    <div class="mt-2 text-sm text-red-700">
                                        <p><?php echo e(__('Rejection Reason')); ?>: <?php echo e($driver->rejection_reason); ?></p>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="ml-auto">
                                <?php if($driver->status === 'pending'): ?>
                                    <div class="flex space-x-2">
                                        <form method="POST" action="<?php echo e(route('admin.drivers.approve', $driver)); ?>">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('PUT'); ?>
                                            <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                                <i class="fas fa-check mr-2"></i>
                                                <?php echo e(__('Approve Driver')); ?>

                                            </button>
                                        </form>

                                        <button type="button"
                                                onclick="openRejectModal(<?php echo e($driver->id); ?>)"
                                                class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                            <i class="fas fa-times mr-2"></i>
                                            <?php echo e(__('Reject Driver')); ?>

                                        </button>
                                    </div>
                                <?php endif; ?>

                                <?php if($driver->status === 'active'): ?>
                                    <form method="POST" action="<?php echo e(route('admin.drivers.toggle-featured', $driver)); ?>">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('PUT'); ?>
                                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-amber-600 hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500">
                                            <i class="fas fa-<?php echo e($driver->is_featured ? 'star' : 'star'); ?> mr-2"></i>
                                            <?php echo e($driver->is_featured ? __('Unfeature Driver') : __('Feature Driver')); ?>

                                        </button>
                                    </form>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Driver Personal Information -->
                    <div class="bg-gray-50 p-6 rounded-lg mb-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="text-md font-medium text-gray-900 mb-4"><?php echo e(__('Personal Information')); ?></h4>

                                <dl class="grid grid-cols-1 gap-x-4 gap-y-6">
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500"><?php echo e(__('Name')); ?></dt>
                                        <dd class="mt-1 text-sm text-gray-900"><?php echo e($driver->user->first_name); ?> <?php echo e($driver->user->last_name); ?></dd>
                                    </div>

                                    <div>
                                        <dt class="text-sm font-medium text-gray-500"><?php echo e(__('Email')); ?></dt>
                                        <dd class="mt-1 text-sm text-gray-900"><?php echo e($driver->user->email); ?></dd>
                                    </div>

                                    <div>
                                        <dt class="text-sm font-medium text-gray-500"><?php echo e(__('Experience')); ?></dt>
                                        <dd class="mt-1 text-sm text-gray-900"><?php echo e($driver->experience_years); ?> <?php echo e(Str::plural('year', $driver->experience_years)); ?></dd>
                                    </div>

                                    <div>
                                        <dt class="text-sm font-medium text-gray-500"><?php echo e(__('Languages')); ?></dt>
                                        <dd class="mt-1 text-sm text-gray-900">
                                            <?php
                                                $languages = json_decode($driver->languages) ?? [];
                                            ?>
                                            <?php $__currentLoopData = $languages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mr-1">
                                                    <?php echo e($language); ?>

                                                </span>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </dd>
                                    </div>

                                    <div>
                                        <dt class="text-sm font-medium text-gray-500"><?php echo e(__('City')); ?></dt>
                                        <dd class="mt-1 text-sm text-gray-900"><?php echo e($driver->city->name ?? 'N/A'); ?></dd>
                                    </div>
                                </dl>
                            </div>

                            <div>
                                <h4 class="text-md font-medium text-gray-900 mb-4"><?php echo e(__('Rates & Availability')); ?></h4>

                                <dl class="grid grid-cols-1 gap-x-4 gap-y-6">
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500"><?php echo e(__('Daily Rate')); ?></dt>
                                        <dd class="mt-1 text-sm text-gray-900"><?php echo e(number_format($driver->daily_rate, 2)); ?></dd>
                                    </div>

                                    <?php if($driver->hourly_rate): ?>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500"><?php echo e(__('Hourly Rate')); ?></dt>
                                        <dd class="mt-1 text-sm text-gray-900"><?php echo e(number_format($driver->hourly_rate, 2)); ?></dd>
                                    </div>
                                    <?php endif; ?>

                                    <div>
                                        <dt class="text-sm font-medium text-gray-500"><?php echo e(__('Available on Weekdays')); ?></dt>
                                        <dd class="mt-1 text-sm text-gray-900">
                                            <?php if($driver->available_weekdays): ?>
                                                <span class="text-green-600"><i class="fas fa-check"></i> Yes</span>
                                            <?php else: ?>
                                                <span class="text-red-600"><i class="fas fa-times"></i> No</span>
                                            <?php endif; ?>
                                        </dd>
                                    </div>

                                    <div>
                                        <dt class="text-sm font-medium text-gray-500"><?php echo e(__('Available on Weekends')); ?></dt>
                                        <dd class="mt-1 text-sm text-gray-900">
                                            <?php if($driver->available_weekends): ?>
                                                <span class="text-green-600"><i class="fas fa-check"></i> Yes</span>
                                            <?php else: ?>
                                                <span class="text-red-600"><i class="fas fa-times"></i> No</span>
                                            <?php endif; ?>
                                        </dd>
                                    </div>
                                </dl>
                            </div>
                        </div>

                        <?php if($driver->bio): ?>
                        <div class="mt-6">
                            <h4 class="text-md font-medium text-gray-900 mb-2"><?php echo e(__('Bio')); ?></h4>
                            <p class="text-sm text-gray-700"><?php echo e($driver->bio); ?></p>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- Driver License Information -->
                    <div class="bg-gray-50 p-6 rounded-lg mb-6">
                        <h4 class="text-md font-medium text-gray-900 mb-4"><?php echo e(__('License Information')); ?></h4>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <dl class="grid grid-cols-1 gap-x-4 gap-y-6">
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500"><?php echo e(__('License Number')); ?></dt>
                                        <dd class="mt-1 text-sm text-gray-900"><?php echo e($driver->license->license_number); ?></dd>
                                    </div>

                                    <div>
                                        <dt class="text-sm font-medium text-gray-500"><?php echo e(__('Issuing Country')); ?></dt>
                                        <dd class="mt-1 text-sm text-gray-900"><?php echo e($driver->license->issuing_country); ?></dd>
                                    </div>

                                    <div>
                                        <dt class="text-sm font-medium text-gray-500"><?php echo e(__('License Class')); ?></dt>
                                        <dd class="mt-1 text-sm text-gray-900"><?php echo e($driver->license->license_class); ?></dd>
                                    </div>

                                    <div>
                                        <dt class="text-sm font-medium text-gray-500"><?php echo e(__('Issue Date')); ?></dt>
                                        <dd class="mt-1 text-sm text-gray-900"><?php echo e($driver->license->issue_date->format('M d, Y')); ?></dd>
                                    </div>

                                    <div>
                                        <dt class="text-sm font-medium text-gray-500"><?php echo e(__('Expiry Date')); ?></dt>
                                        <dd class="mt-1 text-sm text-gray-900"><?php echo e($driver->license->expiry_date->format('M d, Y')); ?></dd>
                                    </div>

                                    <div>
                                        <dt class="text-sm font-medium text-gray-500"><?php echo e(__('License Status')); ?></dt>
                                        <dd class="mt-1 text-sm text-gray-900">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                                <?php if($driver->license->status === 'verified'): ?> bg-green-100 text-green-800
                                                <?php elseif($driver->license->status === 'pending'): ?> bg-yellow-100 text-yellow-800
                                                <?php else: ?> bg-red-100 text-red-800 <?php endif; ?>">
                                                <?php echo e(ucfirst($driver->license->status)); ?>

                                            </span>
                                        </dd>
                                    </div>
                                </dl>
                            </div>

                            <div>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <h5 class="text-sm font-medium text-gray-700 mb-2"><?php echo e(__('License Front')); ?></h5>
                                        <img src="<?php echo e($driver->license->front_image); ?>" alt="License Front" class="h-auto w-full rounded-md border border-gray-200">
                                    </div>

                                    <?php if($driver->license->back_image): ?>
                                        <div>
                                            <h5 class="text-sm font-medium text-gray-700 mb-2"><?php echo e(__('License Back')); ?></h5>
                                            <img src="<?php echo e($driver->license->back_image); ?>" alt="License Back" class="h-auto w-full rounded-md border border-gray-200">
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Driver Documents -->
                    <div class="bg-gray-50 p-6 rounded-lg">
                        <h4 class="text-md font-medium text-gray-900 mb-4"><?php echo e(__('Documents')); ?></h4>

                        <?php if($documents->count() > 0): ?>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                <?php echo e(__('Document Type')); ?>

                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                <?php echo e(__('File Name')); ?>

                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                <?php echo e(__('Expiry Date')); ?>

                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                <?php echo e(__('Status')); ?>

                                            </th>
                                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                <?php echo e(__('Actions')); ?>

                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <?php $__currentLoopData = $documents; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $document): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm font-medium text-gray-900"><?php echo e($document->documentType->name); ?></div>
                                                    <div class="text-xs text-gray-500"><?php echo e($document->documentType->description); ?></div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm text-gray-900"><?php echo e($document->file_name); ?></div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm text-gray-900">
                                                        <?php echo e($document->expiry_date ? $document->expiry_date->format('M d, Y') : 'N/A'); ?>

                                                    </div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                                        <?php if($document->status === 'approved'): ?> bg-green-100 text-green-800
                                                        <?php elseif($document->status === 'pending'): ?> bg-yellow-100 text-yellow-800
                                                        <?php else: ?> bg-red-100 text-red-800 <?php endif; ?>">
                                                        <?php echo e(ucfirst($document->status)); ?>

                                                    </span>

                                                    <?php if($document->status === 'rejected' && $document->rejection_reason): ?>
                                                        <div class="mt-1 text-xs text-red-600">
                                                            <?php echo e($document->rejection_reason); ?>

                                                        </div>
                                                    <?php endif; ?>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                    <a href="<?php echo e(route('admin.documents.show', $document)); ?>" class="text-indigo-600 hover:text-indigo-900">
                                                        <?php echo e(__('View')); ?>

                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <p class="text-gray-500"><?php echo e(__('No documents found.')); ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Reject Modal -->
    <div id="reject-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden z-50">
        <div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
            <h3 class="text-lg font-medium text-gray-900 mb-4"><?php echo e(__('Reject Driver Application')); ?></h3>

            <form id="reject-form" method="POST" action="">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>

                <div class="mb-4">
                    <label for="rejection_reason" class="block text-sm font-medium text-gray-700"><?php echo e(__('Reason for Rejection')); ?></label>
                    <textarea id="rejection_reason" name="rejection_reason" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required></textarea>
                </div>

                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeRejectModal()" class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <?php echo e(__('Cancel')); ?>

                    </button>
                    <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                        <?php echo e(__('Reject')); ?>

                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function openRejectModal(driverId) {
            document.getElementById('reject-form').action = `/admin/drivers/${driverId}/reject`;
            document.getElementById('reject-modal').classList.remove('hidden');
        }

        function closeRejectModal() {
            document.getElementById('reject-modal').classList.add('hidden');
        }
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $attributes = $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $component = $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\carbnb\resources\views\admin\drivers\show.blade.php ENDPATH**/ ?>
<x-app-backend-layout>
    <div class="p-6">
        <div class="mb-4">
            <div class="flex justify-between items-center mb-3">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4">{{ isset($page) ? 'Edit Page' : 'Create Page' }}</h2>
                <a href="{{ route('pages.index') }}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg shadow">Back to Pages</a>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-md">
                <form action="{{ isset($page) ? route('pages.update', $page->id) : route('pages.store') }}" method="POST">
                    @csrf
                    @if(isset($page))
                        @method('PUT')
                    @endif

                    <div class="mb-4">
                        <label for="title" class="block text-gray-700 font-medium mb-2">Title</label>
                        <input type="text" name="title" id="title" value="{{ isset($page) ? $page->title : old('title') }}" class="border p-2 w-full rounded-lg shadow-sm @error('title') border-red-500 @enderror">
                        @error('title')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <x-tinymce-editor 
                        name="content" 
                        label="Content"
                        :value="isset($page) ? $page->content : old('content')" 
                    />

                    <div class="mb-4">
                        <label for="status" class="block text-gray-700 font-medium mb-2">Status</label>
                        <select name="status" id="status" class="border p-2 w-full rounded-lg shadow-sm @error('status') border-red-500 @enderror">
                            <option value="published" {{ (isset($page) && $page->status == 'published') || old('status') == 'published' ? 'selected' : '' }}>Published</option>
                            <option value="draft" {{ (isset($page) && $page->status == 'draft') || old('status') == 'draft' ? 'selected' : '' }}>Draft</option>
                        </select>
                        @error('status')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="flex justify-end">
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg shadow">
                            {{ isset($page) ? 'Update Page' : 'Create Page' }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</x-app-backend-layout>

<?php

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Create roles and permissions
    $adminRole = Role::create(['name' => 'admin']);
    $userRole = Role::create(['name' => 'user']);

    // Create permissions
    Permission::create(['name' => 'dashboard']);
    Permission::create(['name' => 'users.view']);
    Permission::create(['name' => 'roles.view']);
    Permission::create(['name' => 'settings']);

    // Assign permissions to roles
    $adminRole->givePermissionTo(['dashboard', 'users.view', 'roles.view', 'settings']);

    // Create admin user
    $this->admin = User::factory()->create([
        'first_name' => 'Admin',
        'last_name' => 'User',
        'email' => '<EMAIL>',
    ]);
    $this->admin->assignRole('admin');

    // Create regular user
    $this->user = User::factory()->create([
        'first_name' => 'Regular',
        'last_name' => 'User',
        'email' => '<EMAIL>',
    ]);
    $this->user->assignRole('user');
});

test('admin can access dashboard', function () {
    $response = $this->actingAs($this->admin)->get('/dashboard');

    $response->assertStatus(200);
});

test('regular user can access dashboard but with limited options', function () {
    $response = $this->actingAs($this->user)->get('/dashboard');

    $response->assertStatus(200);
    // Regular users should not see admin options
    $response->assertDontSee('User Management');
    $response->assertDontSee('Role Management');
});

test('admin can access revenue dashboard', function () {
    $response = $this->actingAs($this->admin)->get('/revenue-dashboard');

    $response->assertStatus(200);
});

test('regular user cannot access revenue dashboard', function () {
    $response = $this->actingAs($this->user)->get('/revenue-dashboard');

    $response->assertStatus(403);
});

test('admin can access user management', function () {
    $response = $this->actingAs($this->admin)->get('/users');

    $response->assertStatus(200);
});

test('regular user cannot access user management', function () {
    $response = $this->actingAs($this->user)->get('/users');

    $response->assertStatus(403);
});

test('admin can access role management', function () {
    $response = $this->actingAs($this->admin)->get('/roles');

    $response->assertStatus(200);
});

test('regular user cannot access role management', function () {
    $response = $this->actingAs($this->user)->get('/roles');

    $response->assertStatus(403);
});

test('admin can access business settings', function () {
    $response = $this->actingAs($this->admin)->get('/general-setting');

    $response->assertStatus(200);
});

test('regular user cannot access business settings', function () {
    $response = $this->actingAs($this->user)->get('/general-setting');

    $response->assertStatus(403);
});

test('admin can update business settings', function () {
    $settingsData = [
        'site_name' => 'CarBnB Updated',
        'site_logo' => 'logo.png',
        'site_favicon' => 'favicon.ico',
        'site_email' => '<EMAIL>',
        'site_phone' => '+1234567890',
        'site_address' => '123 Car Street, Auto City',
        'site_description' => 'The best car rental platform',
        'site_keywords' => 'car, rental, luxury, driver',
        'site_currency' => 'USD',
        'site_language' => 'en',
    ];

    $response = $this->actingAs($this->admin)->post('/business-settings/update', $settingsData);

    $response->assertRedirect();

    // Check if settings were updated
    $this->assertDatabaseHas('business_settings', [
        'key' => 'site_name',
        'value' => 'CarBnB Updated',
    ]);
});

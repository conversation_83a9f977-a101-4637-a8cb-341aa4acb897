<x-app-backend-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Driver Details') }}
            </h2>
            <a href="{{ route('admin.drivers.index') }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gray-600 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                <i class="fas fa-arrow-left mr-2"></i>
                {{ __('Back to Drivers') }}
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="mb-6">
                        <h3 class="text-lg font-medium text-gray-900">{{ __('Driver Information') }}</h3>
                    </div>

                    <!-- Driver Status Banner -->
                    <div class="mb-6 p-4 rounded-md
                        @if($driver->status === 'active') bg-green-50 border border-green-200
                        @elseif($driver->status === 'pending') bg-yellow-50 border border-yellow-200
                        @elseif($driver->status === 'rejected') bg-red-50 border border-red-200
                        @else bg-gray-50 border border-gray-200 @endif">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                @if($driver->status === 'active')
                                    <i class="fas fa-check-circle text-green-600"></i>
                                @elseif($driver->status === 'pending')
                                    <i class="fas fa-clock text-yellow-600"></i>
                                @elseif($driver->status === 'rejected')
                                    <i class="fas fa-times-circle text-red-600"></i>
                                @else
                                    <i class="fas fa-ban text-gray-600"></i>
                                @endif
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium
                                    @if($driver->status === 'active') text-green-800
                                    @elseif($driver->status === 'pending') text-yellow-800
                                    @elseif($driver->status === 'rejected') text-red-800
                                    @else text-gray-800 @endif">
                                    {{ __('Status') }}: {{ ucfirst($driver->status) }}
                                </h3>
                                @if($driver->status === 'rejected' && $driver->rejection_reason)
                                    <div class="mt-2 text-sm text-red-700">
                                        <p>{{ __('Rejection Reason') }}: {{ $driver->rejection_reason }}</p>
                                    </div>
                                @endif
                            </div>

                            <div class="ml-auto">
                                @if($driver->status === 'pending')
                                    <div class="flex space-x-2">
                                        <form method="POST" action="{{ route('admin.drivers.approve', $driver) }}">
                                            @csrf
                                            @method('PUT')
                                            <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                                <i class="fas fa-check mr-2"></i>
                                                {{ __('Approve Driver') }}
                                            </button>
                                        </form>

                                        <button type="button"
                                                onclick="openRejectModal({{ $driver->id }})"
                                                class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                            <i class="fas fa-times mr-2"></i>
                                            {{ __('Reject Driver') }}
                                        </button>
                                    </div>
                                @endif

                                @if($driver->status === 'active')
                                    <form method="POST" action="{{ route('admin.drivers.toggle-featured', $driver) }}">
                                        @csrf
                                        @method('PUT')
                                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-amber-600 hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500">
                                            <i class="fas fa-{{ $driver->is_featured ? 'star' : 'star' }} mr-2"></i>
                                            {{ $driver->is_featured ? __('Unfeature Driver') : __('Feature Driver') }}
                                        </button>
                                    </form>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Driver Personal Information -->
                    <div class="bg-gray-50 p-6 rounded-lg mb-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="text-md font-medium text-gray-900 mb-4">{{ __('Personal Information') }}</h4>

                                <dl class="grid grid-cols-1 gap-x-4 gap-y-6">
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">{{ __('Name') }}</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $driver->user->first_name }} {{ $driver->user->last_name }}</dd>
                                    </div>

                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">{{ __('Email') }}</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $driver->user->email }}</dd>
                                    </div>

                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">{{ __('Experience') }}</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $driver->experience_years }} {{ Str::plural('year', $driver->experience_years) }}</dd>
                                    </div>

                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">{{ __('Languages') }}</dt>
                                        <dd class="mt-1 text-sm text-gray-900">
                                            @php
                                                $languages = json_decode($driver->languages) ?? [];
                                            @endphp
                                            @foreach($languages as $language)
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mr-1">
                                                    {{ $language }}
                                                </span>
                                            @endforeach
                                        </dd>
                                    </div>

                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">{{ __('City') }}</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $driver->city->name ?? 'N/A' }}</dd>
                                    </div>
                                </dl>
                            </div>

                            <div>
                                <h4 class="text-md font-medium text-gray-900 mb-4">{{ __('Rates & Availability') }}</h4>

                                <dl class="grid grid-cols-1 gap-x-4 gap-y-6">
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">{{ __('Daily Rate') }}</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ number_format($driver->daily_rate, 2) }}</dd>
                                    </div>

                                    @if($driver->hourly_rate)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">{{ __('Hourly Rate') }}</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ number_format($driver->hourly_rate, 2) }}</dd>
                                    </div>
                                    @endif

                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">{{ __('Available on Weekdays') }}</dt>
                                        <dd class="mt-1 text-sm text-gray-900">
                                            @if($driver->available_weekdays)
                                                <span class="text-green-600"><i class="fas fa-check"></i> Yes</span>
                                            @else
                                                <span class="text-red-600"><i class="fas fa-times"></i> No</span>
                                            @endif
                                        </dd>
                                    </div>

                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">{{ __('Available on Weekends') }}</dt>
                                        <dd class="mt-1 text-sm text-gray-900">
                                            @if($driver->available_weekends)
                                                <span class="text-green-600"><i class="fas fa-check"></i> Yes</span>
                                            @else
                                                <span class="text-red-600"><i class="fas fa-times"></i> No</span>
                                            @endif
                                        </dd>
                                    </div>
                                </dl>
                            </div>
                        </div>

                        @if($driver->bio)
                        <div class="mt-6">
                            <h4 class="text-md font-medium text-gray-900 mb-2">{{ __('Bio') }}</h4>
                            <p class="text-sm text-gray-700">{{ $driver->bio }}</p>
                        </div>
                        @endif
                    </div>

                    <!-- Driver License Information -->
                    <div class="bg-gray-50 p-6 rounded-lg mb-6">
                        <h4 class="text-md font-medium text-gray-900 mb-4">{{ __('License Information') }}</h4>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <dl class="grid grid-cols-1 gap-x-4 gap-y-6">
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">{{ __('License Number') }}</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $driver->license->license_number }}</dd>
                                    </div>

                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">{{ __('Issuing Country') }}</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $driver->license->issuing_country }}</dd>
                                    </div>

                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">{{ __('License Class') }}</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $driver->license->license_class }}</dd>
                                    </div>

                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">{{ __('Issue Date') }}</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $driver->license->issue_date->format('M d, Y') }}</dd>
                                    </div>

                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">{{ __('Expiry Date') }}</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $driver->license->expiry_date->format('M d, Y') }}</dd>
                                    </div>

                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">{{ __('License Status') }}</dt>
                                        <dd class="mt-1 text-sm text-gray-900">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                                @if($driver->license->status === 'verified') bg-green-100 text-green-800
                                                @elseif($driver->license->status === 'pending') bg-yellow-100 text-yellow-800
                                                @else bg-red-100 text-red-800 @endif">
                                                {{ ucfirst($driver->license->status) }}
                                            </span>
                                        </dd>
                                    </div>
                                </dl>
                            </div>

                            <div>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <h5 class="text-sm font-medium text-gray-700 mb-2">{{ __('License Front') }}</h5>
                                        <img src="{{ $driver->license->front_image }}" alt="License Front" class="h-auto w-full rounded-md border border-gray-200">
                                    </div>

                                    @if($driver->license->back_image)
                                        <div>
                                            <h5 class="text-sm font-medium text-gray-700 mb-2">{{ __('License Back') }}</h5>
                                            <img src="{{ $driver->license->back_image }}" alt="License Back" class="h-auto w-full rounded-md border border-gray-200">
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Driver Documents -->
                    <div class="bg-gray-50 p-6 rounded-lg">
                        <h4 class="text-md font-medium text-gray-900 mb-4">{{ __('Documents') }}</h4>

                        @if($documents->count() > 0)
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                {{ __('Document Type') }}
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                {{ __('File Name') }}
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                {{ __('Expiry Date') }}
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                {{ __('Status') }}
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                {{ __('Actions') }}
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        @foreach($documents as $document)
                                            <tr>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm font-medium text-gray-900">{{ $document->documentType->name }}</div>
                                                    <div class="text-xs text-gray-500">{{ $document->documentType->description }}</div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm text-gray-900">{{ $document->file_name }}</div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm text-gray-900">
                                                        {{ $document->expiry_date ? $document->expiry_date->format('M d, Y') : 'N/A' }}
                                                    </div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                                        @if($document->status === 'approved') bg-green-100 text-green-800
                                                        @elseif($document->status === 'pending') bg-yellow-100 text-yellow-800
                                                        @else bg-red-100 text-red-800 @endif">
                                                        {{ ucfirst($document->status) }}
                                                    </span>

                                                    @if($document->status === 'rejected' && $document->rejection_reason)
                                                        <div class="mt-1 text-xs text-red-600">
                                                            {{ $document->rejection_reason }}
                                                        </div>
                                                    @endif
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                    <a href="{{ route('admin.documents.show', $document) }}" class="text-indigo-600 hover:text-indigo-900">
                                                        {{ __('View') }}
                                                    </a>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="text-center py-4">
                                <p class="text-gray-500">{{ __('No documents found.') }}</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Reject Modal -->
    <div id="reject-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden z-50">
        <div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
            <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __('Reject Driver Application') }}</h3>

            <form id="reject-form" method="POST" action="">
                @csrf
                @method('PUT')

                <div class="mb-4">
                    <label for="rejection_reason" class="block text-sm font-medium text-gray-700">{{ __('Reason for Rejection') }}</label>
                    <textarea id="rejection_reason" name="rejection_reason" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required></textarea>
                </div>

                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeRejectModal()" class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        {{ __('Cancel') }}
                    </button>
                    <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                        {{ __('Reject') }}
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function openRejectModal(driverId) {
            document.getElementById('reject-form').action = `/admin/drivers/${driverId}/reject`;
            document.getElementById('reject-modal').classList.remove('hidden');
        }

        function closeRejectModal() {
            document.getElementById('reject-modal').classList.add('hidden');
        }
    </script>
</x-app-backend-layout>

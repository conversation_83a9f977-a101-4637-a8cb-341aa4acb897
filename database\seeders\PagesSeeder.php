<?php

namespace Database\Seeders;

use App\Models\Page;
use Illuminate\Database\Seeder;

class PagesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Terms of Service page
        Page::create([
            'title' => 'Terms of Service',
            'slug' => 'terms',
            'content' => '<h1>Terms of Service</h1>
                <p>Welcome to CARBNB. By using our services, you agree to these terms.</p>
                <h2>1. Acceptance of Terms</h2>
                <p>By accessing or using the CARBNB platform, you agree to be bound by these Terms of Service.</p>
                <h2>2. User Accounts</h2>
                <p>You must create an account to use certain features of our platform. You are responsible for maintaining the confidentiality of your account information.</p>
                <h2>3. Vehicle Listings</h2>
                <p>Vehicle owners are responsible for providing accurate information about their vehicles. CARBNB is not responsible for the accuracy of listings.</p>
                <h2>4. Booking and Payments</h2>
                <p>All bookings and payments are processed through our platform. Cancellation policies apply as specified in each listing.</p>
                <h2>5. Insurance</h2>
                <p>Insurance coverage is provided as specified in our insurance policy. Users must comply with all insurance requirements.</p>',
            'status' => 'published',
        ]);

        // Create Privacy Policy page
        Page::create([
            'title' => 'Privacy Policy',
            'slug' => 'privacy',
            'content' => '<h1>Privacy Policy</h1>
                <p>At CARBNB, we take your privacy seriously. This policy explains how we collect, use, and protect your personal information.</p>
                <h2>1. Information We Collect</h2>
                <p>We collect personal information such as your name, email address, phone number, and payment information when you create an account or make a booking.</p>
                <h2>2. How We Use Your Information</h2>
                <p>We use your information to provide our services, process payments, communicate with you, and improve our platform.</p>
                <h2>3. Information Sharing</h2>
                <p>We share your information with vehicle owners and renters as necessary to facilitate bookings. We may also share information with service providers who help us operate our platform.</p>
                <h2>4. Data Security</h2>
                <p>We implement appropriate security measures to protect your personal information from unauthorized access, alteration, or disclosure.</p>
                <h2>5. Your Rights</h2>
                <p>You have the right to access, correct, or delete your personal information. Contact us if you wish to exercise these rights.</p>',
            'status' => 'published',
        ]);

        // Create Cookie Policy page
        Page::create([
            'title' => 'Cookie Policy',
            'slug' => 'cookies',
            'content' => '<h1>Cookie Policy</h1>
                <p>This Cookie Policy explains how CARBNB uses cookies and similar technologies on our website.</p>
                <h2>1. What Are Cookies</h2>
                <p>Cookies are small text files that are stored on your device when you visit our website. They help us provide you with a better experience by remembering your preferences and actions.</p>
                <h2>2. Types of Cookies We Use</h2>
                <p>We use essential cookies that are necessary for the website to function, as well as analytics cookies that help us understand how you use our website.</p>
                <h2>3. Managing Cookies</h2>
                <p>You can manage or delete cookies through your browser settings. However, disabling certain cookies may affect the functionality of our website.</p>
                <h2>4. Third-Party Cookies</h2>
                <p>We may use third-party services that set their own cookies. These services help us analyze website traffic and provide certain features.</p>
                <h2>5. Changes to This Policy</h2>
                <p>We may update this Cookie Policy from time to time. Check back regularly for any changes.</p>',
            'status' => 'published',
        ]);
    }
}
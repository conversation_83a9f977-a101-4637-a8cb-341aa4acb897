import{a as p}from"./vendor-ui-DouPxA_H.js";import{r as s}from"./vendor-export-CAcm_ukE.js";function l(r,a){for(var o=0;o<a.length;o++){const e=a[o];if(typeof e!="string"&&!Array.isArray(e)){for(const t in e)if(t!=="default"&&!(t in r)){const f=Object.getOwnPropertyDescriptor(e,t);f&&Object.defineProperty(r,t,f.get?f:{enumerable:!0,get:()=>e[t]})}}}return Object.freeze(Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}))}var n=s();const i=p(n),u=l({__proto__:null,default:i},[n]);export{u as h};

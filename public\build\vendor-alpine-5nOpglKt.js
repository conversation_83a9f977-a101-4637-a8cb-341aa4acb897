var Ve=!1,Ge=!1,W=[],Je=-1;function Sn(e){En(e)}function En(e){W.includes(e)||W.push(e),On()}function An(e){let t=W.indexOf(e);t!==-1&&t>Je&&W.splice(t,1)}function On(){!Ge&&!Ve&&(Ve=!0,queueMicrotask(Cn))}function Cn(){Ve=!1,Ge=!0;for(let e=0;e<W.length;e++)W[e](),Je=e;W.length=0,Je=-1,Ge=!1}var ee,X,te,tr,Ye=!0;function Tn(e){Ye=!1,e(),Ye=!0}function Rn(e){ee=e.reactive,te=e.release,X=t=>e.effect(t,{scheduler:r=>{Ye?Sn(r):r()}}),tr=e.raw}function Mt(e){X=e}function Fn(e){let t=()=>{};return[n=>{let i=X(n);return e._x_effects||(e._x_effects=new Set,e._x_runEffects=()=>{e._x_effects.forEach(a=>a())}),e._x_effects.add(i),t=()=>{i!==void 0&&(e._x_effects.delete(i),te(i))},i},()=>{t()}]}function rr(e,t){let r=!0,n,i=X(()=>{let a=e();JSON.stringify(a),r?n=a:queueMicrotask(()=>{t(a,n),n=a}),r=!1});return()=>te(i)}var nr=[],ir=[],ar=[];function Nn(e){ar.push(e)}function dt(e,t){typeof t=="function"?(e._x_cleanups||(e._x_cleanups=[]),e._x_cleanups.push(t)):(t=e,ir.push(t))}function or(e){nr.push(e)}function sr(e,t,r){e._x_attributeCleanups||(e._x_attributeCleanups={}),e._x_attributeCleanups[t]||(e._x_attributeCleanups[t]=[]),e._x_attributeCleanups[t].push(r)}function ur(e,t){e._x_attributeCleanups&&Object.entries(e._x_attributeCleanups).forEach(([r,n])=>{(t===void 0||t.includes(r))&&(n.forEach(i=>i()),delete e._x_attributeCleanups[r])})}function In(e){var t,r;for((t=e._x_effects)==null||t.forEach(An);(r=e._x_cleanups)!=null&&r.length;)e._x_cleanups.pop()()}var pt=new MutationObserver(gt),ht=!1;function _t(){pt.observe(document,{subtree:!0,childList:!0,attributes:!0,attributeOldValue:!0}),ht=!0}function cr(){Mn(),pt.disconnect(),ht=!1}var oe=[];function Mn(){let e=pt.takeRecords();oe.push(()=>e.length>0&&gt(e));let t=oe.length;queueMicrotask(()=>{if(oe.length===t)for(;oe.length>0;)oe.shift()()})}function w(e){if(!ht)return e();cr();let t=e();return _t(),t}var vt=!1,Ce=[];function Pn(){vt=!0}function Dn(){vt=!1,gt(Ce),Ce=[]}function gt(e){if(vt){Ce=Ce.concat(e);return}let t=[],r=new Set,n=new Map,i=new Map;for(let a=0;a<e.length;a++)if(!e[a].target._x_ignoreMutationObserver&&(e[a].type==="childList"&&(e[a].removedNodes.forEach(o=>{o.nodeType===1&&o._x_marker&&r.add(o)}),e[a].addedNodes.forEach(o=>{if(o.nodeType===1){if(r.has(o)){r.delete(o);return}o._x_marker||t.push(o)}})),e[a].type==="attributes")){let o=e[a].target,s=e[a].attributeName,u=e[a].oldValue,c=()=>{n.has(o)||n.set(o,[]),n.get(o).push({name:s,value:o.getAttribute(s)})},l=()=>{i.has(o)||i.set(o,[]),i.get(o).push(s)};o.hasAttribute(s)&&u===null?c():o.hasAttribute(s)?(l(),c()):l()}i.forEach((a,o)=>{ur(o,a)}),n.forEach((a,o)=>{nr.forEach(s=>s(o,a))});for(let a of r)t.some(o=>o.contains(a))||ir.forEach(o=>o(a));for(let a of t)a.isConnected&&ar.forEach(o=>o(a));t=null,r=null,n=null,i=null}function lr(e){return _e(Z(e))}function he(e,t,r){return e._x_dataStack=[t,...Z(r||e)],()=>{e._x_dataStack=e._x_dataStack.filter(n=>n!==t)}}function Z(e){return e._x_dataStack?e._x_dataStack:typeof ShadowRoot=="function"&&e instanceof ShadowRoot?Z(e.host):e.parentNode?Z(e.parentNode):[]}function _e(e){return new Proxy({objects:e},$n)}var $n={ownKeys({objects:e}){return Array.from(new Set(e.flatMap(t=>Object.keys(t))))},has({objects:e},t){return t==Symbol.unscopables?!1:e.some(r=>Object.prototype.hasOwnProperty.call(r,t)||Reflect.has(r,t))},get({objects:e},t,r){return t=="toJSON"?kn:Reflect.get(e.find(n=>Reflect.has(n,t))||{},t,r)},set({objects:e},t,r,n){const i=e.find(o=>Object.prototype.hasOwnProperty.call(o,t))||e[e.length-1],a=Object.getOwnPropertyDescriptor(i,t);return a!=null&&a.set&&(a!=null&&a.get)?a.set.call(n,r)||!0:Reflect.set(i,t,r)}};function kn(){return Reflect.ownKeys(this).reduce((t,r)=>(t[r]=Reflect.get(this,r),t),{})}function fr(e){let t=n=>typeof n=="object"&&!Array.isArray(n)&&n!==null,r=(n,i="")=>{Object.entries(Object.getOwnPropertyDescriptors(n)).forEach(([a,{value:o,enumerable:s}])=>{if(s===!1||o===void 0||typeof o=="object"&&o!==null&&o.__v_skip)return;let u=i===""?a:`${i}.${a}`;typeof o=="object"&&o!==null&&o._x_interceptor?n[a]=o.initialize(e,u,a):t(o)&&o!==n&&!(o instanceof Element)&&r(o,u)})};return r(e)}function dr(e,t=()=>{}){let r={initialValue:void 0,_x_interceptor:!0,initialize(n,i,a){return e(this.initialValue,()=>Ln(n,i),o=>Xe(n,i,o),i,a)}};return t(r),n=>{if(typeof n=="object"&&n!==null&&n._x_interceptor){let i=r.initialize.bind(r);r.initialize=(a,o,s)=>{let u=n.initialize(a,o,s);return r.initialValue=u,i(a,o,s)}}else r.initialValue=n;return r}}function Ln(e,t){return t.split(".").reduce((r,n)=>r[n],e)}function Xe(e,t,r){if(typeof t=="string"&&(t=t.split(".")),t.length===1)e[t[0]]=r;else{if(t.length===0)throw error;return e[t[0]]||(e[t[0]]={}),Xe(e[t[0]],t.slice(1),r)}}var pr={};function M(e,t){pr[e]=t}function Ze(e,t){let r=jn(t);return Object.entries(pr).forEach(([n,i])=>{Object.defineProperty(e,`$${n}`,{get(){return i(t,r)},enumerable:!1})}),e}function jn(e){let[t,r]=yr(e),n={interceptor:dr,...t};return dt(e,r),n}function Bn(e,t,r,...n){try{return r(...n)}catch(i){pe(i,e,t)}}function pe(e,t,r=void 0){e=Object.assign(e??{message:"No error message given."},{el:t,expression:r}),console.warn(`Alpine Expression Error: ${e.message}

${r?'Expression: "'+r+`"

`:""}`,t),setTimeout(()=>{throw e},0)}var Ee=!0;function hr(e){let t=Ee;Ee=!1;let r=e();return Ee=t,r}function U(e,t,r={}){let n;return F(e,t)(i=>n=i,r),n}function F(...e){return _r(...e)}var _r=vr;function Kn(e){_r=e}function vr(e,t){let r={};Ze(r,e);let n=[r,...Z(e)],i=typeof t=="function"?Hn(n,t):qn(n,t,e);return Bn.bind(null,e,t,i)}function Hn(e,t){return(r=()=>{},{scope:n={},params:i=[]}={})=>{let a=t.apply(_e([n,...e]),i);Te(r,a)}}var He={};function zn(e,t){if(He[e])return He[e];let r=Object.getPrototypeOf(async function(){}).constructor,n=/^[\n\s]*if.*\(.*\)/.test(e.trim())||/^(let|const)\s/.test(e.trim())?`(async()=>{ ${e} })()`:e,a=(()=>{try{let o=new r(["__self","scope"],`with (scope) { __self.result = ${n} }; __self.finished = true; return __self.result;`);return Object.defineProperty(o,"name",{value:`[Alpine] ${e}`}),o}catch(o){return pe(o,t,e),Promise.resolve()}})();return He[e]=a,a}function qn(e,t,r){let n=zn(t,r);return(i=()=>{},{scope:a={},params:o=[]}={})=>{n.result=void 0,n.finished=!1;let s=_e([a,...e]);if(typeof n=="function"){let u=n(n,s).catch(c=>pe(c,r,t));n.finished?(Te(i,n.result,s,o,r),n.result=void 0):u.then(c=>{Te(i,c,s,o,r)}).catch(c=>pe(c,r,t)).finally(()=>n.result=void 0)}}}function Te(e,t,r,n,i){if(Ee&&typeof t=="function"){let a=t.apply(r,n);a instanceof Promise?a.then(o=>Te(e,o,r,n)).catch(o=>pe(o,i,t)):e(a)}else typeof t=="object"&&t instanceof Promise?t.then(a=>e(a)):e(t)}var bt="x-";function re(e=""){return bt+e}function Wn(e){bt=e}var Re={};function A(e,t){return Re[e]=t,{before(r){if(!Re[r]){console.warn(String.raw`Cannot find directive \`${r}\`. \`${e}\` will use the default order of execution`);return}const n=q.indexOf(r);q.splice(n>=0?n:q.indexOf("DEFAULT"),0,e)}}}function Un(e){return Object.keys(Re).includes(e)}function yt(e,t,r){if(t=Array.from(t),e._x_virtualDirectives){let a=Object.entries(e._x_virtualDirectives).map(([s,u])=>({name:s,value:u})),o=gr(a);a=a.map(s=>o.find(u=>u.name===s.name)?{name:`x-bind:${s.name}`,value:`"${s.value}"`}:s),t=t.concat(a)}let n={};return t.map(wr((a,o)=>n[a]=o)).filter(Er).map(Jn(n,r)).sort(Yn).map(a=>Gn(e,a))}function gr(e){return Array.from(e).map(wr()).filter(t=>!Er(t))}var Qe=!1,le=new Map,br=Symbol();function Vn(e){Qe=!0;let t=Symbol();br=t,le.set(t,[]);let r=()=>{for(;le.get(t).length;)le.get(t).shift()();le.delete(t)},n=()=>{Qe=!1,r()};e(r),n()}function yr(e){let t=[],r=s=>t.push(s),[n,i]=Fn(e);return t.push(i),[{Alpine:ve,effect:n,cleanup:r,evaluateLater:F.bind(F,e),evaluate:U.bind(U,e)},()=>t.forEach(s=>s())]}function Gn(e,t){let r=()=>{},n=Re[t.type]||r,[i,a]=yr(e);sr(e,t.original,a);let o=()=>{e._x_ignore||e._x_ignoreSelf||(n.inline&&n.inline(e,t,i),n=n.bind(n,e,t,i),Qe?le.get(br).push(n):n())};return o.runCleanups=a,o}var mr=(e,t)=>({name:r,value:n})=>(r.startsWith(e)&&(r=r.replace(e,t)),{name:r,value:n}),xr=e=>e;function wr(e=()=>{}){return({name:t,value:r})=>{let{name:n,value:i}=Sr.reduce((a,o)=>o(a),{name:t,value:r});return n!==t&&e(n,t),{name:n,value:i}}}var Sr=[];function mt(e){Sr.push(e)}function Er({name:e}){return Ar().test(e)}var Ar=()=>new RegExp(`^${bt}([^:^.]+)\\b`);function Jn(e,t){return({name:r,value:n})=>{let i=r.match(Ar()),a=r.match(/:([a-zA-Z0-9\-_:]+)/),o=r.match(/\.[^.\]]+(?=[^\]]*$)/g)||[],s=t||e[r]||r;return{type:i?i[1]:null,value:a?a[1]:null,modifiers:o.map(u=>u.replace(".","")),expression:n,original:s}}}var et="DEFAULT",q=["ignore","ref","data","id","anchor","bind","init","for","model","modelable","transition","show","if",et,"teleport"];function Yn(e,t){let r=q.indexOf(e.type)===-1?et:e.type,n=q.indexOf(t.type)===-1?et:t.type;return q.indexOf(r)-q.indexOf(n)}function fe(e,t,r={}){e.dispatchEvent(new CustomEvent(t,{detail:r,bubbles:!0,composed:!0,cancelable:!0}))}function J(e,t){if(typeof ShadowRoot=="function"&&e instanceof ShadowRoot){Array.from(e.children).forEach(i=>J(i,t));return}let r=!1;if(t(e,()=>r=!0),r)return;let n=e.firstElementChild;for(;n;)J(n,t),n=n.nextElementSibling}function N(e,...t){console.warn(`Alpine Warning: ${e}`,...t)}var Pt=!1;function Xn(){Pt&&N("Alpine has already been initialized on this page. Calling Alpine.start() more than once can cause problems."),Pt=!0,document.body||N("Unable to initialize. Trying to load Alpine before `<body>` is available. Did you forget to add `defer` in Alpine's `<script>` tag?"),fe(document,"alpine:init"),fe(document,"alpine:initializing"),_t(),Nn(t=>k(t,J)),dt(t=>ie(t)),or((t,r)=>{yt(t,r).forEach(n=>n())});let e=t=>!Me(t.parentElement,!0);Array.from(document.querySelectorAll(Tr().join(","))).filter(e).forEach(t=>{k(t)}),fe(document,"alpine:initialized"),setTimeout(()=>{ti()})}var xt=[],Or=[];function Cr(){return xt.map(e=>e())}function Tr(){return xt.concat(Or).map(e=>e())}function Rr(e){xt.push(e)}function Fr(e){Or.push(e)}function Me(e,t=!1){return ne(e,r=>{if((t?Tr():Cr()).some(i=>r.matches(i)))return!0})}function ne(e,t){if(e){if(t(e))return e;if(e._x_teleportBack&&(e=e._x_teleportBack),!!e.parentElement)return ne(e.parentElement,t)}}function Zn(e){return Cr().some(t=>e.matches(t))}var Nr=[];function Qn(e){Nr.push(e)}var ei=1;function k(e,t=J,r=()=>{}){ne(e,n=>n._x_ignore)||Vn(()=>{t(e,(n,i)=>{n._x_marker||(r(n,i),Nr.forEach(a=>a(n,i)),yt(n,n.attributes).forEach(a=>a()),n._x_ignore||(n._x_marker=ei++),n._x_ignore&&i())})})}function ie(e,t=J){t(e,r=>{In(r),ur(r),delete r._x_marker})}function ti(){[["ui","dialog",["[x-dialog], [x-popover]"]],["anchor","anchor",["[x-anchor]"]],["sort","sort",["[x-sort]"]]].forEach(([t,r,n])=>{Un(r)||n.some(i=>{if(document.querySelector(i))return N(`found "${i}", but missing ${t} plugin`),!0})})}var tt=[],wt=!1;function St(e=()=>{}){return queueMicrotask(()=>{wt||setTimeout(()=>{rt()})}),new Promise(t=>{tt.push(()=>{e(),t()})})}function rt(){for(wt=!1;tt.length;)tt.shift()()}function ri(){wt=!0}function Et(e,t){return Array.isArray(t)?Dt(e,t.join(" ")):typeof t=="object"&&t!==null?ni(e,t):typeof t=="function"?Et(e,t()):Dt(e,t)}function Dt(e,t){let r=i=>i.split(" ").filter(a=>!e.classList.contains(a)).filter(Boolean),n=i=>(e.classList.add(...i),()=>{e.classList.remove(...i)});return t=t===!0?t="":t||"",n(r(t))}function ni(e,t){let r=s=>s.split(" ").filter(Boolean),n=Object.entries(t).flatMap(([s,u])=>u?r(s):!1).filter(Boolean),i=Object.entries(t).flatMap(([s,u])=>u?!1:r(s)).filter(Boolean),a=[],o=[];return i.forEach(s=>{e.classList.contains(s)&&(e.classList.remove(s),o.push(s))}),n.forEach(s=>{e.classList.contains(s)||(e.classList.add(s),a.push(s))}),()=>{o.forEach(s=>e.classList.add(s)),a.forEach(s=>e.classList.remove(s))}}function Pe(e,t){return typeof t=="object"&&t!==null?ii(e,t):ai(e,t)}function ii(e,t){let r={};return Object.entries(t).forEach(([n,i])=>{r[n]=e.style[n],n.startsWith("--")||(n=oi(n)),e.style.setProperty(n,i)}),setTimeout(()=>{e.style.length===0&&e.removeAttribute("style")}),()=>{Pe(e,r)}}function ai(e,t){let r=e.getAttribute("style",t);return e.setAttribute("style",t),()=>{e.setAttribute("style",r||"")}}function oi(e){return e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}function nt(e,t=()=>{}){let r=!1;return function(){r?t.apply(this,arguments):(r=!0,e.apply(this,arguments))}}A("transition",(e,{value:t,modifiers:r,expression:n},{evaluate:i})=>{typeof n=="function"&&(n=i(n)),n!==!1&&(!n||typeof n=="boolean"?ui(e,r,t):si(e,n,t))});function si(e,t,r){Ir(e,Et,""),{enter:i=>{e._x_transition.enter.during=i},"enter-start":i=>{e._x_transition.enter.start=i},"enter-end":i=>{e._x_transition.enter.end=i},leave:i=>{e._x_transition.leave.during=i},"leave-start":i=>{e._x_transition.leave.start=i},"leave-end":i=>{e._x_transition.leave.end=i}}[r](t)}function ui(e,t,r){Ir(e,Pe);let n=!t.includes("in")&&!t.includes("out")&&!r,i=n||t.includes("in")||["enter"].includes(r),a=n||t.includes("out")||["leave"].includes(r);t.includes("in")&&!n&&(t=t.filter((b,x)=>x<t.indexOf("out"))),t.includes("out")&&!n&&(t=t.filter((b,x)=>x>t.indexOf("out")));let o=!t.includes("opacity")&&!t.includes("scale"),s=o||t.includes("opacity"),u=o||t.includes("scale"),c=s?0:1,l=u?se(t,"scale",95)/100:1,p=se(t,"delay",0)/1e3,_=se(t,"origin","center"),g="opacity, transform",C=se(t,"duration",150)/1e3,P=se(t,"duration",75)/1e3,h="cubic-bezier(0.4, 0.0, 0.2, 1)";i&&(e._x_transition.enter.during={transformOrigin:_,transitionDelay:`${p}s`,transitionProperty:g,transitionDuration:`${C}s`,transitionTimingFunction:h},e._x_transition.enter.start={opacity:c,transform:`scale(${l})`},e._x_transition.enter.end={opacity:1,transform:"scale(1)"}),a&&(e._x_transition.leave.during={transformOrigin:_,transitionDelay:`${p}s`,transitionProperty:g,transitionDuration:`${P}s`,transitionTimingFunction:h},e._x_transition.leave.start={opacity:1,transform:"scale(1)"},e._x_transition.leave.end={opacity:c,transform:`scale(${l})`})}function Ir(e,t,r={}){e._x_transition||(e._x_transition={enter:{during:r,start:r,end:r},leave:{during:r,start:r,end:r},in(n=()=>{},i=()=>{}){it(e,t,{during:this.enter.during,start:this.enter.start,end:this.enter.end},n,i)},out(n=()=>{},i=()=>{}){it(e,t,{during:this.leave.during,start:this.leave.start,end:this.leave.end},n,i)}})}window.Element.prototype._x_toggleAndCascadeWithTransitions=function(e,t,r,n){const i=document.visibilityState==="visible"?requestAnimationFrame:setTimeout;let a=()=>i(r);if(t){e._x_transition&&(e._x_transition.enter||e._x_transition.leave)?e._x_transition.enter&&(Object.entries(e._x_transition.enter.during).length||Object.entries(e._x_transition.enter.start).length||Object.entries(e._x_transition.enter.end).length)?e._x_transition.in(r):a():e._x_transition?e._x_transition.in(r):a();return}e._x_hidePromise=e._x_transition?new Promise((o,s)=>{e._x_transition.out(()=>{},()=>o(n)),e._x_transitioning&&e._x_transitioning.beforeCancel(()=>s({isFromCancelledTransition:!0}))}):Promise.resolve(n),queueMicrotask(()=>{let o=Mr(e);o?(o._x_hideChildren||(o._x_hideChildren=[]),o._x_hideChildren.push(e)):i(()=>{let s=u=>{let c=Promise.all([u._x_hidePromise,...(u._x_hideChildren||[]).map(s)]).then(([l])=>l==null?void 0:l());return delete u._x_hidePromise,delete u._x_hideChildren,c};s(e).catch(u=>{if(!u.isFromCancelledTransition)throw u})})})};function Mr(e){let t=e.parentNode;if(t)return t._x_hidePromise?t:Mr(t)}function it(e,t,{during:r,start:n,end:i}={},a=()=>{},o=()=>{}){if(e._x_transitioning&&e._x_transitioning.cancel(),Object.keys(r).length===0&&Object.keys(n).length===0&&Object.keys(i).length===0){a(),o();return}let s,u,c;ci(e,{start(){s=t(e,n)},during(){u=t(e,r)},before:a,end(){s(),c=t(e,i)},after:o,cleanup(){u(),c()}})}function ci(e,t){let r,n,i,a=nt(()=>{w(()=>{r=!0,n||t.before(),i||(t.end(),rt()),t.after(),e.isConnected&&t.cleanup(),delete e._x_transitioning})});e._x_transitioning={beforeCancels:[],beforeCancel(o){this.beforeCancels.push(o)},cancel:nt(function(){for(;this.beforeCancels.length;)this.beforeCancels.shift()();a()}),finish:a},w(()=>{t.start(),t.during()}),ri(),requestAnimationFrame(()=>{if(r)return;let o=Number(getComputedStyle(e).transitionDuration.replace(/,.*/,"").replace("s",""))*1e3,s=Number(getComputedStyle(e).transitionDelay.replace(/,.*/,"").replace("s",""))*1e3;o===0&&(o=Number(getComputedStyle(e).animationDuration.replace("s",""))*1e3),w(()=>{t.before()}),n=!0,requestAnimationFrame(()=>{r||(w(()=>{t.end()}),rt(),setTimeout(e._x_transitioning.finish,o+s),i=!0)})})}function se(e,t,r){if(e.indexOf(t)===-1)return r;const n=e[e.indexOf(t)+1];if(!n||t==="scale"&&isNaN(n))return r;if(t==="duration"||t==="delay"){let i=n.match(/([0-9]+)ms/);if(i)return i[1]}return t==="origin"&&["top","right","left","center","bottom"].includes(e[e.indexOf(t)+2])?[n,e[e.indexOf(t)+2]].join(" "):n}var B=!1;function H(e,t=()=>{}){return(...r)=>B?t(...r):e(...r)}function li(e){return(...t)=>B&&e(...t)}var Pr=[];function De(e){Pr.push(e)}function fi(e,t){Pr.forEach(r=>r(e,t)),B=!0,Dr(()=>{k(t,(r,n)=>{n(r,()=>{})})}),B=!1}var at=!1;function di(e,t){t._x_dataStack||(t._x_dataStack=e._x_dataStack),B=!0,at=!0,Dr(()=>{pi(t)}),B=!1,at=!1}function pi(e){let t=!1;k(e,(n,i)=>{J(n,(a,o)=>{if(t&&Zn(a))return o();t=!0,i(a,o)})})}function Dr(e){let t=X;Mt((r,n)=>{let i=t(r);return te(i),()=>{}}),e(),Mt(t)}function $r(e,t,r,n=[]){switch(e._x_bindings||(e._x_bindings=ee({})),e._x_bindings[t]=r,t=n.includes("camel")?xi(t):t,t){case"value":hi(e,r);break;case"style":vi(e,r);break;case"class":_i(e,r);break;case"selected":case"checked":gi(e,t,r);break;default:kr(e,t,r);break}}function hi(e,t){if(Br(e))e.attributes.value===void 0&&(e.value=t),window.fromModel&&(typeof t=="boolean"?e.checked=Ae(e.value)===t:e.checked=$t(e.value,t));else if(At(e))Number.isInteger(t)?e.value=t:!Array.isArray(t)&&typeof t!="boolean"&&![null,void 0].includes(t)?e.value=String(t):Array.isArray(t)?e.checked=t.some(r=>$t(r,e.value)):e.checked=!!t;else if(e.tagName==="SELECT")mi(e,t);else{if(e.value===t)return;e.value=t===void 0?"":t}}function _i(e,t){e._x_undoAddedClasses&&e._x_undoAddedClasses(),e._x_undoAddedClasses=Et(e,t)}function vi(e,t){e._x_undoAddedStyles&&e._x_undoAddedStyles(),e._x_undoAddedStyles=Pe(e,t)}function gi(e,t,r){kr(e,t,r),yi(e,t,r)}function kr(e,t,r){[null,void 0,!1].includes(r)&&Si(t)?e.removeAttribute(t):(Lr(t)&&(r=t),bi(e,t,r))}function bi(e,t,r){e.getAttribute(t)!=r&&e.setAttribute(t,r)}function yi(e,t,r){e[t]!==r&&(e[t]=r)}function mi(e,t){const r=[].concat(t).map(n=>n+"");Array.from(e.options).forEach(n=>{n.selected=r.includes(n.value)})}function xi(e){return e.toLowerCase().replace(/-(\w)/g,(t,r)=>r.toUpperCase())}function $t(e,t){return e==t}function Ae(e){return[1,"1","true","on","yes",!0].includes(e)?!0:[0,"0","false","off","no",!1].includes(e)?!1:e?!!e:null}var wi=new Set(["allowfullscreen","async","autofocus","autoplay","checked","controls","default","defer","disabled","formnovalidate","inert","ismap","itemscope","loop","multiple","muted","nomodule","novalidate","open","playsinline","readonly","required","reversed","selected","shadowrootclonable","shadowrootdelegatesfocus","shadowrootserializable"]);function Lr(e){return wi.has(e)}function Si(e){return!["aria-pressed","aria-checked","aria-expanded","aria-selected"].includes(e)}function Ei(e,t,r){return e._x_bindings&&e._x_bindings[t]!==void 0?e._x_bindings[t]:jr(e,t,r)}function Ai(e,t,r,n=!0){if(e._x_bindings&&e._x_bindings[t]!==void 0)return e._x_bindings[t];if(e._x_inlineBindings&&e._x_inlineBindings[t]!==void 0){let i=e._x_inlineBindings[t];return i.extract=n,hr(()=>U(e,i.expression))}return jr(e,t,r)}function jr(e,t,r){let n=e.getAttribute(t);return n===null?typeof r=="function"?r():r:n===""?!0:Lr(t)?!![t,"true"].includes(n):n}function At(e){return e.type==="checkbox"||e.localName==="ui-checkbox"||e.localName==="ui-switch"}function Br(e){return e.type==="radio"||e.localName==="ui-radio"}function Kr(e,t){var r;return function(){var n=this,i=arguments,a=function(){r=null,e.apply(n,i)};clearTimeout(r),r=setTimeout(a,t)}}function Hr(e,t){let r;return function(){let n=this,i=arguments;r||(e.apply(n,i),r=!0,setTimeout(()=>r=!1,t))}}function zr({get:e,set:t},{get:r,set:n}){let i=!0,a,o=X(()=>{let s=e(),u=r();if(i)n(ze(s)),i=!1;else{let c=JSON.stringify(s),l=JSON.stringify(u);c!==a?n(ze(s)):c!==l&&t(ze(u))}a=JSON.stringify(e()),JSON.stringify(r())});return()=>{te(o)}}function ze(e){return typeof e=="object"?JSON.parse(JSON.stringify(e)):e}function Oi(e){(Array.isArray(e)?e:[e]).forEach(r=>r(ve))}var z={},kt=!1;function Ci(e,t){if(kt||(z=ee(z),kt=!0),t===void 0)return z[e];z[e]=t,fr(z[e]),typeof t=="object"&&t!==null&&t.hasOwnProperty("init")&&typeof t.init=="function"&&z[e].init()}function Ti(){return z}var qr={};function Ri(e,t){let r=typeof t!="function"?()=>t:t;return e instanceof Element?Wr(e,r()):(qr[e]=r,()=>{})}function Fi(e){return Object.entries(qr).forEach(([t,r])=>{Object.defineProperty(e,t,{get(){return(...n)=>r(...n)}})}),e}function Wr(e,t,r){let n=[];for(;n.length;)n.pop()();let i=Object.entries(t).map(([o,s])=>({name:o,value:s})),a=gr(i);return i=i.map(o=>a.find(s=>s.name===o.name)?{name:`x-bind:${o.name}`,value:`"${o.value}"`}:o),yt(e,i,r).map(o=>{n.push(o.runCleanups),o()}),()=>{for(;n.length;)n.pop()()}}var Ur={};function Ni(e,t){Ur[e]=t}function Ii(e,t){return Object.entries(Ur).forEach(([r,n])=>{Object.defineProperty(e,r,{get(){return(...i)=>n.bind(t)(...i)},enumerable:!1})}),e}var Mi={get reactive(){return ee},get release(){return te},get effect(){return X},get raw(){return tr},version:"3.14.9",flushAndStopDeferringMutations:Dn,dontAutoEvaluateFunctions:hr,disableEffectScheduling:Tn,startObservingMutations:_t,stopObservingMutations:cr,setReactivityEngine:Rn,onAttributeRemoved:sr,onAttributesAdded:or,closestDataStack:Z,skipDuringClone:H,onlyDuringClone:li,addRootSelector:Rr,addInitSelector:Fr,interceptClone:De,addScopeToNode:he,deferMutations:Pn,mapAttributes:mt,evaluateLater:F,interceptInit:Qn,setEvaluator:Kn,mergeProxies:_e,extractProp:Ai,findClosest:ne,onElRemoved:dt,closestRoot:Me,destroyTree:ie,interceptor:dr,transition:it,setStyles:Pe,mutateDom:w,directive:A,entangle:zr,throttle:Hr,debounce:Kr,evaluate:U,initTree:k,nextTick:St,prefixed:re,prefix:Wn,plugin:Oi,magic:M,store:Ci,start:Xn,clone:di,cloneNode:fi,bound:Ei,$data:lr,watch:rr,walk:J,data:Ni,bind:Ri},ve=Mi;function Pi(e,t){const r=Object.create(null),n=e.split(",");for(let i=0;i<n.length;i++)r[n[i]]=!0;return i=>!!r[i]}var Di=Object.freeze({}),$i=Object.prototype.hasOwnProperty,$e=(e,t)=>$i.call(e,t),V=Array.isArray,de=e=>Vr(e)==="[object Map]",ki=e=>typeof e=="string",Ot=e=>typeof e=="symbol",ke=e=>e!==null&&typeof e=="object",Li=Object.prototype.toString,Vr=e=>Li.call(e),Gr=e=>Vr(e).slice(8,-1),Ct=e=>ki(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,ji=e=>{const t=Object.create(null);return r=>t[r]||(t[r]=e(r))},Bi=ji(e=>e.charAt(0).toUpperCase()+e.slice(1)),Jr=(e,t)=>e!==t&&(e===e||t===t),ot=new WeakMap,ue=[],D,G=Symbol("iterate"),st=Symbol("Map key iterate");function Ki(e){return e&&e._isEffect===!0}function Hi(e,t=Di){Ki(e)&&(e=e.raw);const r=Wi(e,t);return t.lazy||r(),r}function zi(e){e.active&&(Yr(e),e.options.onStop&&e.options.onStop(),e.active=!1)}var qi=0;function Wi(e,t){const r=function(){if(!r.active)return e();if(!ue.includes(r)){Yr(r);try{return Vi(),ue.push(r),D=r,e()}finally{ue.pop(),Xr(),D=ue[ue.length-1]}}};return r.id=qi++,r.allowRecurse=!!t.allowRecurse,r._isEffect=!0,r.active=!0,r.raw=e,r.deps=[],r.options=t,r}function Yr(e){const{deps:t}=e;if(t.length){for(let r=0;r<t.length;r++)t[r].delete(e);t.length=0}}var Q=!0,Tt=[];function Ui(){Tt.push(Q),Q=!1}function Vi(){Tt.push(Q),Q=!0}function Xr(){const e=Tt.pop();Q=e===void 0?!0:e}function I(e,t,r){if(!Q||D===void 0)return;let n=ot.get(e);n||ot.set(e,n=new Map);let i=n.get(r);i||n.set(r,i=new Set),i.has(D)||(i.add(D),D.deps.push(i),D.options.onTrack&&D.options.onTrack({effect:D,target:e,type:t,key:r}))}function K(e,t,r,n,i,a){const o=ot.get(e);if(!o)return;const s=new Set,u=l=>{l&&l.forEach(p=>{(p!==D||p.allowRecurse)&&s.add(p)})};if(t==="clear")o.forEach(u);else if(r==="length"&&V(e))o.forEach((l,p)=>{(p==="length"||p>=n)&&u(l)});else switch(r!==void 0&&u(o.get(r)),t){case"add":V(e)?Ct(r)&&u(o.get("length")):(u(o.get(G)),de(e)&&u(o.get(st)));break;case"delete":V(e)||(u(o.get(G)),de(e)&&u(o.get(st)));break;case"set":de(e)&&u(o.get(G));break}const c=l=>{l.options.onTrigger&&l.options.onTrigger({effect:l,target:e,key:r,type:t,newValue:n,oldValue:i,oldTarget:a}),l.options.scheduler?l.options.scheduler(l):l()};s.forEach(c)}var Gi=Pi("__proto__,__v_isRef,__isVue"),Zr=new Set(Object.getOwnPropertyNames(Symbol).map(e=>Symbol[e]).filter(Ot)),Ji=Qr(),Yi=Qr(!0),Lt=Xi();function Xi(){const e={};return["includes","indexOf","lastIndexOf"].forEach(t=>{e[t]=function(...r){const n=m(this);for(let a=0,o=this.length;a<o;a++)I(n,"get",a+"");const i=n[t](...r);return i===-1||i===!1?n[t](...r.map(m)):i}}),["push","pop","shift","unshift","splice"].forEach(t=>{e[t]=function(...r){Ui();const n=m(this)[t].apply(this,r);return Xr(),n}}),e}function Qr(e=!1,t=!1){return function(n,i,a){if(i==="__v_isReactive")return!e;if(i==="__v_isReadonly")return e;if(i==="__v_raw"&&a===(e?t?fa:nn:t?la:rn).get(n))return n;const o=V(n);if(!e&&o&&$e(Lt,i))return Reflect.get(Lt,i,a);const s=Reflect.get(n,i,a);return(Ot(i)?Zr.has(i):Gi(i))||(e||I(n,"get",i),t)?s:ut(s)?!o||!Ct(i)?s.value:s:ke(s)?e?an(s):It(s):s}}var Zi=Qi();function Qi(e=!1){return function(r,n,i,a){let o=r[n];if(!e&&(i=m(i),o=m(o),!V(r)&&ut(o)&&!ut(i)))return o.value=i,!0;const s=V(r)&&Ct(n)?Number(n)<r.length:$e(r,n),u=Reflect.set(r,n,i,a);return r===m(a)&&(s?Jr(i,o)&&K(r,"set",n,i,o):K(r,"add",n,i)),u}}function ea(e,t){const r=$e(e,t),n=e[t],i=Reflect.deleteProperty(e,t);return i&&r&&K(e,"delete",t,void 0,n),i}function ta(e,t){const r=Reflect.has(e,t);return(!Ot(t)||!Zr.has(t))&&I(e,"has",t),r}function ra(e){return I(e,"iterate",V(e)?"length":G),Reflect.ownKeys(e)}var na={get:Ji,set:Zi,deleteProperty:ea,has:ta,ownKeys:ra},ia={get:Yi,set(e,t){return console.warn(`Set operation on key "${String(t)}" failed: target is readonly.`,e),!0},deleteProperty(e,t){return console.warn(`Delete operation on key "${String(t)}" failed: target is readonly.`,e),!0}},Rt=e=>ke(e)?It(e):e,Ft=e=>ke(e)?an(e):e,Nt=e=>e,Le=e=>Reflect.getPrototypeOf(e);function ge(e,t,r=!1,n=!1){e=e.__v_raw;const i=m(e),a=m(t);t!==a&&!r&&I(i,"get",t),!r&&I(i,"get",a);const{has:o}=Le(i),s=n?Nt:r?Ft:Rt;if(o.call(i,t))return s(e.get(t));if(o.call(i,a))return s(e.get(a));e!==i&&e.get(t)}function be(e,t=!1){const r=this.__v_raw,n=m(r),i=m(e);return e!==i&&!t&&I(n,"has",e),!t&&I(n,"has",i),e===i?r.has(e):r.has(e)||r.has(i)}function ye(e,t=!1){return e=e.__v_raw,!t&&I(m(e),"iterate",G),Reflect.get(e,"size",e)}function jt(e){e=m(e);const t=m(this);return Le(t).has.call(t,e)||(t.add(e),K(t,"add",e,e)),this}function Bt(e,t){t=m(t);const r=m(this),{has:n,get:i}=Le(r);let a=n.call(r,e);a?tn(r,n,e):(e=m(e),a=n.call(r,e));const o=i.call(r,e);return r.set(e,t),a?Jr(t,o)&&K(r,"set",e,t,o):K(r,"add",e,t),this}function Kt(e){const t=m(this),{has:r,get:n}=Le(t);let i=r.call(t,e);i?tn(t,r,e):(e=m(e),i=r.call(t,e));const a=n?n.call(t,e):void 0,o=t.delete(e);return i&&K(t,"delete",e,void 0,a),o}function Ht(){const e=m(this),t=e.size!==0,r=de(e)?new Map(e):new Set(e),n=e.clear();return t&&K(e,"clear",void 0,void 0,r),n}function me(e,t){return function(n,i){const a=this,o=a.__v_raw,s=m(o),u=t?Nt:e?Ft:Rt;return!e&&I(s,"iterate",G),o.forEach((c,l)=>n.call(i,u(c),u(l),a))}}function xe(e,t,r){return function(...n){const i=this.__v_raw,a=m(i),o=de(a),s=e==="entries"||e===Symbol.iterator&&o,u=e==="keys"&&o,c=i[e](...n),l=r?Nt:t?Ft:Rt;return!t&&I(a,"iterate",u?st:G),{next(){const{value:p,done:_}=c.next();return _?{value:p,done:_}:{value:s?[l(p[0]),l(p[1])]:l(p),done:_}},[Symbol.iterator](){return this}}}}function j(e){return function(...t){{const r=t[0]?`on key "${t[0]}" `:"";console.warn(`${Bi(e)} operation ${r}failed: target is readonly.`,m(this))}return e==="delete"?!1:this}}function aa(){const e={get(a){return ge(this,a)},get size(){return ye(this)},has:be,add:jt,set:Bt,delete:Kt,clear:Ht,forEach:me(!1,!1)},t={get(a){return ge(this,a,!1,!0)},get size(){return ye(this)},has:be,add:jt,set:Bt,delete:Kt,clear:Ht,forEach:me(!1,!0)},r={get(a){return ge(this,a,!0)},get size(){return ye(this,!0)},has(a){return be.call(this,a,!0)},add:j("add"),set:j("set"),delete:j("delete"),clear:j("clear"),forEach:me(!0,!1)},n={get(a){return ge(this,a,!0,!0)},get size(){return ye(this,!0)},has(a){return be.call(this,a,!0)},add:j("add"),set:j("set"),delete:j("delete"),clear:j("clear"),forEach:me(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(a=>{e[a]=xe(a,!1,!1),r[a]=xe(a,!0,!1),t[a]=xe(a,!1,!0),n[a]=xe(a,!0,!0)}),[e,r,t,n]}var[oa,sa,Qa,eo]=aa();function en(e,t){const r=e?sa:oa;return(n,i,a)=>i==="__v_isReactive"?!e:i==="__v_isReadonly"?e:i==="__v_raw"?n:Reflect.get($e(r,i)&&i in n?r:n,i,a)}var ua={get:en(!1)},ca={get:en(!0)};function tn(e,t,r){const n=m(r);if(n!==r&&t.call(e,n)){const i=Gr(e);console.warn(`Reactive ${i} contains both the raw and reactive versions of the same object${i==="Map"?" as keys":""}, which can lead to inconsistencies. Avoid differentiating between the raw and reactive versions of an object and only use the reactive version if possible.`)}}var rn=new WeakMap,la=new WeakMap,nn=new WeakMap,fa=new WeakMap;function da(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function pa(e){return e.__v_skip||!Object.isExtensible(e)?0:da(Gr(e))}function It(e){return e&&e.__v_isReadonly?e:on(e,!1,na,ua,rn)}function an(e){return on(e,!0,ia,ca,nn)}function on(e,t,r,n,i){if(!ke(e))return console.warn(`value cannot be made reactive: ${String(e)}`),e;if(e.__v_raw&&!(t&&e.__v_isReactive))return e;const a=i.get(e);if(a)return a;const o=pa(e);if(o===0)return e;const s=new Proxy(e,o===2?n:r);return i.set(e,s),s}function m(e){return e&&m(e.__v_raw)||e}function ut(e){return!!(e&&e.__v_isRef===!0)}M("nextTick",()=>St);M("dispatch",e=>fe.bind(fe,e));M("watch",(e,{evaluateLater:t,cleanup:r})=>(n,i)=>{let a=t(n),s=rr(()=>{let u;return a(c=>u=c),u},i);r(s)});M("store",Ti);M("data",e=>lr(e));M("root",e=>Me(e));M("refs",e=>(e._x_refs_proxy||(e._x_refs_proxy=_e(ha(e))),e._x_refs_proxy));function ha(e){let t=[];return ne(e,r=>{r._x_refs&&t.push(r._x_refs)}),t}var qe={};function sn(e){return qe[e]||(qe[e]=0),++qe[e]}function _a(e,t){return ne(e,r=>{if(r._x_ids&&r._x_ids[t])return!0})}function va(e,t){e._x_ids||(e._x_ids={}),e._x_ids[t]||(e._x_ids[t]=sn(t))}M("id",(e,{cleanup:t})=>(r,n=null)=>{let i=`${r}${n?`-${n}`:""}`;return ga(e,i,t,()=>{let a=_a(e,r),o=a?a._x_ids[r]:sn(r);return n?`${r}-${o}-${n}`:`${r}-${o}`})});De((e,t)=>{e._x_id&&(t._x_id=e._x_id)});function ga(e,t,r,n){if(e._x_id||(e._x_id={}),e._x_id[t])return e._x_id[t];let i=n();return e._x_id[t]=i,r(()=>{delete e._x_id[t]}),i}M("el",e=>e);un("Focus","focus","focus");un("Persist","persist","persist");function un(e,t,r){M(t,n=>N(`You can't use [$${t}] without first installing the "${e}" plugin here: https://alpinejs.dev/plugins/${r}`,n))}A("modelable",(e,{expression:t},{effect:r,evaluateLater:n,cleanup:i})=>{let a=n(t),o=()=>{let l;return a(p=>l=p),l},s=n(`${t} = __placeholder`),u=l=>s(()=>{},{scope:{__placeholder:l}}),c=o();u(c),queueMicrotask(()=>{if(!e._x_model)return;e._x_removeModelListeners.default();let l=e._x_model.get,p=e._x_model.set,_=zr({get(){return l()},set(g){p(g)}},{get(){return o()},set(g){u(g)}});i(_)})});A("teleport",(e,{modifiers:t,expression:r},{cleanup:n})=>{e.tagName.toLowerCase()!=="template"&&N("x-teleport can only be used on a <template> tag",e);let i=zt(r),a=e.content.cloneNode(!0).firstElementChild;e._x_teleport=a,a._x_teleportBack=e,e.setAttribute("data-teleport-template",!0),a.setAttribute("data-teleport-target",!0),e._x_forwardEvents&&e._x_forwardEvents.forEach(s=>{a.addEventListener(s,u=>{u.stopPropagation(),e.dispatchEvent(new u.constructor(u.type,u))})}),he(a,{},e);let o=(s,u,c)=>{c.includes("prepend")?u.parentNode.insertBefore(s,u):c.includes("append")?u.parentNode.insertBefore(s,u.nextSibling):u.appendChild(s)};w(()=>{o(a,i,t),H(()=>{k(a)})()}),e._x_teleportPutBack=()=>{let s=zt(r);w(()=>{o(e._x_teleport,s,t)})},n(()=>w(()=>{a.remove(),ie(a)}))});var ba=document.createElement("div");function zt(e){let t=H(()=>document.querySelector(e),()=>ba)();return t||N(`Cannot find x-teleport element for selector: "${e}"`),t}var cn=()=>{};cn.inline=(e,{modifiers:t},{cleanup:r})=>{t.includes("self")?e._x_ignoreSelf=!0:e._x_ignore=!0,r(()=>{t.includes("self")?delete e._x_ignoreSelf:delete e._x_ignore})};A("ignore",cn);A("effect",H((e,{expression:t},{effect:r})=>{r(F(e,t))}));function ct(e,t,r,n){let i=e,a=u=>n(u),o={},s=(u,c)=>l=>c(u,l);if(r.includes("dot")&&(t=ya(t)),r.includes("camel")&&(t=ma(t)),r.includes("passive")&&(o.passive=!0),r.includes("capture")&&(o.capture=!0),r.includes("window")&&(i=window),r.includes("document")&&(i=document),r.includes("debounce")){let u=r[r.indexOf("debounce")+1]||"invalid-wait",c=Fe(u.split("ms")[0])?Number(u.split("ms")[0]):250;a=Kr(a,c)}if(r.includes("throttle")){let u=r[r.indexOf("throttle")+1]||"invalid-wait",c=Fe(u.split("ms")[0])?Number(u.split("ms")[0]):250;a=Hr(a,c)}return r.includes("prevent")&&(a=s(a,(u,c)=>{c.preventDefault(),u(c)})),r.includes("stop")&&(a=s(a,(u,c)=>{c.stopPropagation(),u(c)})),r.includes("once")&&(a=s(a,(u,c)=>{u(c),i.removeEventListener(t,a,o)})),(r.includes("away")||r.includes("outside"))&&(i=document,a=s(a,(u,c)=>{e.contains(c.target)||c.target.isConnected!==!1&&(e.offsetWidth<1&&e.offsetHeight<1||e._x_isShown!==!1&&u(c))})),r.includes("self")&&(a=s(a,(u,c)=>{c.target===e&&u(c)})),(wa(t)||ln(t))&&(a=s(a,(u,c)=>{Sa(c,r)||u(c)})),i.addEventListener(t,a,o),()=>{i.removeEventListener(t,a,o)}}function ya(e){return e.replace(/-/g,".")}function ma(e){return e.toLowerCase().replace(/-(\w)/g,(t,r)=>r.toUpperCase())}function Fe(e){return!Array.isArray(e)&&!isNaN(e)}function xa(e){return[" ","_"].includes(e)?e:e.replace(/([a-z])([A-Z])/g,"$1-$2").replace(/[_\s]/,"-").toLowerCase()}function wa(e){return["keydown","keyup"].includes(e)}function ln(e){return["contextmenu","click","mouse"].some(t=>e.includes(t))}function Sa(e,t){let r=t.filter(a=>!["window","document","prevent","stop","once","capture","self","away","outside","passive"].includes(a));if(r.includes("debounce")){let a=r.indexOf("debounce");r.splice(a,Fe((r[a+1]||"invalid-wait").split("ms")[0])?2:1)}if(r.includes("throttle")){let a=r.indexOf("throttle");r.splice(a,Fe((r[a+1]||"invalid-wait").split("ms")[0])?2:1)}if(r.length===0||r.length===1&&qt(e.key).includes(r[0]))return!1;const i=["ctrl","shift","alt","meta","cmd","super"].filter(a=>r.includes(a));return r=r.filter(a=>!i.includes(a)),!(i.length>0&&i.filter(o=>((o==="cmd"||o==="super")&&(o="meta"),e[`${o}Key`])).length===i.length&&(ln(e.type)||qt(e.key).includes(r[0])))}function qt(e){if(!e)return[];e=xa(e);let t={ctrl:"control",slash:"/",space:" ",spacebar:" ",cmd:"meta",esc:"escape",up:"arrow-up",down:"arrow-down",left:"arrow-left",right:"arrow-right",period:".",comma:",",equal:"=",minus:"-",underscore:"_"};return t[e]=e,Object.keys(t).map(r=>{if(t[r]===e)return r}).filter(r=>r)}A("model",(e,{modifiers:t,expression:r},{effect:n,cleanup:i})=>{let a=e;t.includes("parent")&&(a=e.parentNode);let o=F(a,r),s;typeof r=="string"?s=F(a,`${r} = __placeholder`):typeof r=="function"&&typeof r()=="string"?s=F(a,`${r()} = __placeholder`):s=()=>{};let u=()=>{let _;return o(g=>_=g),Wt(_)?_.get():_},c=_=>{let g;o(C=>g=C),Wt(g)?g.set(_):s(()=>{},{scope:{__placeholder:_}})};typeof r=="string"&&e.type==="radio"&&w(()=>{e.hasAttribute("name")||e.setAttribute("name",r)});var l=e.tagName.toLowerCase()==="select"||["checkbox","radio"].includes(e.type)||t.includes("lazy")?"change":"input";let p=B?()=>{}:ct(e,l,t,_=>{c(We(e,t,_,u()))});if(t.includes("fill")&&([void 0,null,""].includes(u())||At(e)&&Array.isArray(u())||e.tagName.toLowerCase()==="select"&&e.multiple)&&c(We(e,t,{target:e},u())),e._x_removeModelListeners||(e._x_removeModelListeners={}),e._x_removeModelListeners.default=p,i(()=>e._x_removeModelListeners.default()),e.form){let _=ct(e.form,"reset",[],g=>{St(()=>e._x_model&&e._x_model.set(We(e,t,{target:e},u())))});i(()=>_())}e._x_model={get(){return u()},set(_){c(_)}},e._x_forceModelUpdate=_=>{_===void 0&&typeof r=="string"&&r.match(/\./)&&(_=""),window.fromModel=!0,w(()=>$r(e,"value",_)),delete window.fromModel},n(()=>{let _=u();t.includes("unintrusive")&&document.activeElement.isSameNode(e)||e._x_forceModelUpdate(_)})});function We(e,t,r,n){return w(()=>{if(r instanceof CustomEvent&&r.detail!==void 0)return r.detail!==null&&r.detail!==void 0?r.detail:r.target.value;if(At(e))if(Array.isArray(n)){let i=null;return t.includes("number")?i=Ue(r.target.value):t.includes("boolean")?i=Ae(r.target.value):i=r.target.value,r.target.checked?n.includes(i)?n:n.concat([i]):n.filter(a=>!Ea(a,i))}else return r.target.checked;else{if(e.tagName.toLowerCase()==="select"&&e.multiple)return t.includes("number")?Array.from(r.target.selectedOptions).map(i=>{let a=i.value||i.text;return Ue(a)}):t.includes("boolean")?Array.from(r.target.selectedOptions).map(i=>{let a=i.value||i.text;return Ae(a)}):Array.from(r.target.selectedOptions).map(i=>i.value||i.text);{let i;return Br(e)?r.target.checked?i=r.target.value:i=n:i=r.target.value,t.includes("number")?Ue(i):t.includes("boolean")?Ae(i):t.includes("trim")?i.trim():i}}})}function Ue(e){let t=e?parseFloat(e):null;return Aa(t)?t:e}function Ea(e,t){return e==t}function Aa(e){return!Array.isArray(e)&&!isNaN(e)}function Wt(e){return e!==null&&typeof e=="object"&&typeof e.get=="function"&&typeof e.set=="function"}A("cloak",e=>queueMicrotask(()=>w(()=>e.removeAttribute(re("cloak")))));Fr(()=>`[${re("init")}]`);A("init",H((e,{expression:t},{evaluate:r})=>typeof t=="string"?!!t.trim()&&r(t,{},!1):r(t,{},!1)));A("text",(e,{expression:t},{effect:r,evaluateLater:n})=>{let i=n(t);r(()=>{i(a=>{w(()=>{e.textContent=a})})})});A("html",(e,{expression:t},{effect:r,evaluateLater:n})=>{let i=n(t);r(()=>{i(a=>{w(()=>{e.innerHTML=a,e._x_ignoreSelf=!0,k(e),delete e._x_ignoreSelf})})})});mt(mr(":",xr(re("bind:"))));var fn=(e,{value:t,modifiers:r,expression:n,original:i},{effect:a,cleanup:o})=>{if(!t){let u={};Fi(u),F(e,n)(l=>{Wr(e,l,i)},{scope:u});return}if(t==="key")return Oa(e,n);if(e._x_inlineBindings&&e._x_inlineBindings[t]&&e._x_inlineBindings[t].extract)return;let s=F(e,n);a(()=>s(u=>{u===void 0&&typeof n=="string"&&n.match(/\./)&&(u=""),w(()=>$r(e,t,u,r))})),o(()=>{e._x_undoAddedClasses&&e._x_undoAddedClasses(),e._x_undoAddedStyles&&e._x_undoAddedStyles()})};fn.inline=(e,{value:t,modifiers:r,expression:n})=>{t&&(e._x_inlineBindings||(e._x_inlineBindings={}),e._x_inlineBindings[t]={expression:n,extract:!1})};A("bind",fn);function Oa(e,t){e._x_keyExpression=t}Rr(()=>`[${re("data")}]`);A("data",(e,{expression:t},{cleanup:r})=>{if(Ca(e))return;t=t===""?"{}":t;let n={};Ze(n,e);let i={};Ii(i,n);let a=U(e,t,{scope:i});(a===void 0||a===!0)&&(a={}),Ze(a,e);let o=ee(a);fr(o);let s=he(e,o);o.init&&U(e,o.init),r(()=>{o.destroy&&U(e,o.destroy),s()})});De((e,t)=>{e._x_dataStack&&(t._x_dataStack=e._x_dataStack,t.setAttribute("data-has-alpine-state",!0))});function Ca(e){return B?at?!0:e.hasAttribute("data-has-alpine-state"):!1}A("show",(e,{modifiers:t,expression:r},{effect:n})=>{let i=F(e,r);e._x_doHide||(e._x_doHide=()=>{w(()=>{e.style.setProperty("display","none",t.includes("important")?"important":void 0)})}),e._x_doShow||(e._x_doShow=()=>{w(()=>{e.style.length===1&&e.style.display==="none"?e.removeAttribute("style"):e.style.removeProperty("display")})});let a=()=>{e._x_doHide(),e._x_isShown=!1},o=()=>{e._x_doShow(),e._x_isShown=!0},s=()=>setTimeout(o),u=nt(p=>p?o():a(),p=>{typeof e._x_toggleAndCascadeWithTransitions=="function"?e._x_toggleAndCascadeWithTransitions(e,p,o,a):p?s():a()}),c,l=!0;n(()=>i(p=>{!l&&p===c||(t.includes("immediate")&&(p?s():a()),u(p),c=p,l=!1)}))});A("for",(e,{expression:t},{effect:r,cleanup:n})=>{let i=Ra(t),a=F(e,i.items),o=F(e,e._x_keyExpression||"index");e._x_prevKeys=[],e._x_lookup={},r(()=>Ta(e,i,a,o)),n(()=>{Object.values(e._x_lookup).forEach(s=>w(()=>{ie(s),s.remove()})),delete e._x_prevKeys,delete e._x_lookup})});function Ta(e,t,r,n){let i=o=>typeof o=="object"&&!Array.isArray(o),a=e;r(o=>{Fa(o)&&o>=0&&(o=Array.from(Array(o).keys(),h=>h+1)),o===void 0&&(o=[]);let s=e._x_lookup,u=e._x_prevKeys,c=[],l=[];if(i(o))o=Object.entries(o).map(([h,b])=>{let x=Ut(t,b,h,o);n(O=>{l.includes(O)&&N("Duplicate key on x-for",e),l.push(O)},{scope:{index:h,...x}}),c.push(x)});else for(let h=0;h<o.length;h++){let b=Ut(t,o[h],h,o);n(x=>{l.includes(x)&&N("Duplicate key on x-for",e),l.push(x)},{scope:{index:h,...b}}),c.push(b)}let p=[],_=[],g=[],C=[];for(let h=0;h<u.length;h++){let b=u[h];l.indexOf(b)===-1&&g.push(b)}u=u.filter(h=>!g.includes(h));let P="template";for(let h=0;h<l.length;h++){let b=l[h],x=u.indexOf(b);if(x===-1)u.splice(h,0,b),p.push([P,h]);else if(x!==h){let O=u.splice(h,1)[0],T=u.splice(x-1,1)[0];u.splice(h,0,T),u.splice(x,0,O),_.push([O,T])}else C.push(b);P=b}for(let h=0;h<g.length;h++){let b=g[h];b in s&&(w(()=>{ie(s[b]),s[b].remove()}),delete s[b])}for(let h=0;h<_.length;h++){let[b,x]=_[h],O=s[b],T=s[x],y=document.createElement("div");w(()=>{T||N('x-for ":key" is undefined or invalid',a,x,s),T.after(y),O.after(T),T._x_currentIfEl&&T.after(T._x_currentIfEl),y.before(O),O._x_currentIfEl&&O.after(O._x_currentIfEl),y.remove()}),T._x_refreshXForScope(c[l.indexOf(x)])}for(let h=0;h<p.length;h++){let[b,x]=p[h],O=b==="template"?a:s[b];O._x_currentIfEl&&(O=O._x_currentIfEl);let T=c[x],y=l[x],f=document.importNode(a.content,!0).firstElementChild,d=ee(T);he(f,d,a),f._x_refreshXForScope=v=>{Object.entries(v).forEach(([E,S])=>{d[E]=S})},w(()=>{O.after(f),H(()=>k(f))()}),typeof y=="object"&&N("x-for key cannot be an object, it must be a string or an integer",a),s[y]=f}for(let h=0;h<C.length;h++)s[C[h]]._x_refreshXForScope(c[l.indexOf(C[h])]);a._x_prevKeys=l})}function Ra(e){let t=/,([^,\}\]]*)(?:,([^,\}\]]*))?$/,r=/^\s*\(|\)\s*$/g,n=/([\s\S]*?)\s+(?:in|of)\s+([\s\S]*)/,i=e.match(n);if(!i)return;let a={};a.items=i[2].trim();let o=i[1].replace(r,"").trim(),s=o.match(t);return s?(a.item=o.replace(t,"").trim(),a.index=s[1].trim(),s[2]&&(a.collection=s[2].trim())):a.item=o,a}function Ut(e,t,r,n){let i={};return/^\[.*\]$/.test(e.item)&&Array.isArray(t)?e.item.replace("[","").replace("]","").split(",").map(o=>o.trim()).forEach((o,s)=>{i[o]=t[s]}):/^\{.*\}$/.test(e.item)&&!Array.isArray(t)&&typeof t=="object"?e.item.replace("{","").replace("}","").split(",").map(o=>o.trim()).forEach(o=>{i[o]=t[o]}):i[e.item]=t,e.index&&(i[e.index]=r),e.collection&&(i[e.collection]=n),i}function Fa(e){return!Array.isArray(e)&&!isNaN(e)}function dn(){}dn.inline=(e,{expression:t},{cleanup:r})=>{let n=Me(e);n._x_refs||(n._x_refs={}),n._x_refs[t]=e,r(()=>delete n._x_refs[t])};A("ref",dn);A("if",(e,{expression:t},{effect:r,cleanup:n})=>{e.tagName.toLowerCase()!=="template"&&N("x-if can only be used on a <template> tag",e);let i=F(e,t),a=()=>{if(e._x_currentIfEl)return e._x_currentIfEl;let s=e.content.cloneNode(!0).firstElementChild;return he(s,{},e),w(()=>{e.after(s),H(()=>k(s))()}),e._x_currentIfEl=s,e._x_undoIf=()=>{w(()=>{ie(s),s.remove()}),delete e._x_currentIfEl},s},o=()=>{e._x_undoIf&&(e._x_undoIf(),delete e._x_undoIf)};r(()=>i(s=>{s?a():o()})),n(()=>e._x_undoIf&&e._x_undoIf())});A("id",(e,{expression:t},{evaluate:r})=>{r(t).forEach(i=>va(e,i))});De((e,t)=>{e._x_ids&&(t._x_ids=e._x_ids)});mt(mr("@",xr(re("on:"))));A("on",H((e,{value:t,modifiers:r,expression:n},{cleanup:i})=>{let a=n?F(e,n):()=>{};e.tagName.toLowerCase()==="template"&&(e._x_forwardEvents||(e._x_forwardEvents=[]),e._x_forwardEvents.includes(t)||e._x_forwardEvents.push(t));let o=ct(e,t,r,s=>{a(()=>{},{scope:{$event:s},params:[s]})});i(()=>o())}));je("Collapse","collapse","collapse");je("Intersect","intersect","intersect");je("Focus","trap","focus");je("Mask","mask","mask");function je(e,t,r){A(t,n=>N(`You can't use [x-${t}] without first installing the "${e}" plugin here: https://alpinejs.dev/plugins/${r}`,n))}ve.setEvaluator(vr);ve.setReactivityEngine({reactive:It,effect:Hi,release:zi,raw:m});var Na=ve,to=Na;function Ia(e){e.directive("collapse",t),t.inline=(r,{modifiers:n})=>{n.includes("min")&&(r._x_doShow=()=>{},r._x_doHide=()=>{})};function t(r,{modifiers:n}){let i=Vt(n,"duration",250)/1e3,a=Vt(n,"min",0),o=!n.includes("min");r._x_isShown||(r.style.height=`${a}px`),!r._x_isShown&&o&&(r.hidden=!0),r._x_isShown||(r.style.overflow="hidden");let s=(c,l)=>{let p=e.setStyles(c,l);return l.height?()=>{}:p},u={transitionProperty:"height",transitionDuration:`${i}s`,transitionTimingFunction:"cubic-bezier(0.4, 0.0, 0.2, 1)"};r._x_transition={in(c=()=>{},l=()=>{}){o&&(r.hidden=!1),o&&(r.style.display=null);let p=r.getBoundingClientRect().height;r.style.height="auto";let _=r.getBoundingClientRect().height;p===_&&(p=a),e.transition(r,e.setStyles,{during:u,start:{height:p+"px"},end:{height:_+"px"}},()=>r._x_isShown=!0,()=>{Math.abs(r.getBoundingClientRect().height-_)<1&&(r.style.overflow=null)})},out(c=()=>{},l=()=>{}){let p=r.getBoundingClientRect().height;e.transition(r,s,{during:u,start:{height:p+"px"},end:{height:a+"px"}},()=>r.style.overflow="hidden",()=>{r._x_isShown=!1,r.style.height==`${a}px`&&o&&(r.style.display="none",r.hidden=!0)})}}}}function Vt(e,t,r){if(e.indexOf(t)===-1)return r;const n=e[e.indexOf(t)+1];if(!n)return r;if(t==="duration"){let i=n.match(/([0-9]+)ms/);if(i)return i[1]}if(t==="min"){let i=n.match(/([0-9]+)px/);if(i)return i[1]}return n}var ro=Ia,pn=["input","select","textarea","a[href]","button","[tabindex]:not(slot)","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable="false"])',"details>summary:first-of-type","details"],Ne=pn.join(","),hn=typeof Element>"u",Y=hn?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,lt=!hn&&Element.prototype.getRootNode?function(e){return e.getRootNode()}:function(e){return e.ownerDocument},_n=function(t,r,n){var i=Array.prototype.slice.apply(t.querySelectorAll(Ne));return r&&Y.call(t,Ne)&&i.unshift(t),i=i.filter(n),i},vn=function e(t,r,n){for(var i=[],a=Array.from(t);a.length;){var o=a.shift();if(o.tagName==="SLOT"){var s=o.assignedElements(),u=s.length?s:o.children,c=e(u,!0,n);n.flatten?i.push.apply(i,c):i.push({scope:o,candidates:c})}else{var l=Y.call(o,Ne);l&&n.filter(o)&&(r||!t.includes(o))&&i.push(o);var p=o.shadowRoot||typeof n.getShadowRoot=="function"&&n.getShadowRoot(o),_=!n.shadowRootFilter||n.shadowRootFilter(o);if(p&&_){var g=e(p===!0?o.children:p.children,!0,n);n.flatten?i.push.apply(i,g):i.push({scope:o,candidates:g})}else a.unshift.apply(a,o.children)}}return i},gn=function(t,r){return t.tabIndex<0&&(r||/^(AUDIO|VIDEO|DETAILS)$/.test(t.tagName)||t.isContentEditable)&&isNaN(parseInt(t.getAttribute("tabindex"),10))?0:t.tabIndex},Ma=function(t,r){return t.tabIndex===r.tabIndex?t.documentOrder-r.documentOrder:t.tabIndex-r.tabIndex},bn=function(t){return t.tagName==="INPUT"},Pa=function(t){return bn(t)&&t.type==="hidden"},Da=function(t){var r=t.tagName==="DETAILS"&&Array.prototype.slice.apply(t.children).some(function(n){return n.tagName==="SUMMARY"});return r},$a=function(t,r){for(var n=0;n<t.length;n++)if(t[n].checked&&t[n].form===r)return t[n]},ka=function(t){if(!t.name)return!0;var r=t.form||lt(t),n=function(s){return r.querySelectorAll('input[type="radio"][name="'+s+'"]')},i;if(typeof window<"u"&&typeof window.CSS<"u"&&typeof window.CSS.escape=="function")i=n(window.CSS.escape(t.name));else try{i=n(t.name)}catch(o){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",o.message),!1}var a=$a(i,t.form);return!a||a===t},La=function(t){return bn(t)&&t.type==="radio"},ja=function(t){return La(t)&&!ka(t)},Gt=function(t){var r=t.getBoundingClientRect(),n=r.width,i=r.height;return n===0&&i===0},Ba=function(t,r){var n=r.displayCheck,i=r.getShadowRoot;if(getComputedStyle(t).visibility==="hidden")return!0;var a=Y.call(t,"details>summary:first-of-type"),o=a?t.parentElement:t;if(Y.call(o,"details:not([open]) *"))return!0;var s=lt(t).host,u=(s==null?void 0:s.ownerDocument.contains(s))||t.ownerDocument.contains(t);if(!n||n==="full"){if(typeof i=="function"){for(var c=t;t;){var l=t.parentElement,p=lt(t);if(l&&!l.shadowRoot&&i(l)===!0)return Gt(t);t.assignedSlot?t=t.assignedSlot:!l&&p!==t.ownerDocument?t=p.host:t=l}t=c}if(u)return!t.getClientRects().length}else if(n==="non-zero-area")return Gt(t);return!1},Ka=function(t){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(t.tagName))for(var r=t.parentElement;r;){if(r.tagName==="FIELDSET"&&r.disabled){for(var n=0;n<r.children.length;n++){var i=r.children.item(n);if(i.tagName==="LEGEND")return Y.call(r,"fieldset[disabled] *")?!0:!i.contains(t)}return!0}r=r.parentElement}return!1},Ie=function(t,r){return!(r.disabled||Pa(r)||Ba(r,t)||Da(r)||Ka(r))},ft=function(t,r){return!(ja(r)||gn(r)<0||!Ie(t,r))},Ha=function(t){var r=parseInt(t.getAttribute("tabindex"),10);return!!(isNaN(r)||r>=0)},za=function e(t){var r=[],n=[];return t.forEach(function(i,a){var o=!!i.scope,s=o?i.scope:i,u=gn(s,o),c=o?e(i.candidates):s;u===0?o?r.push.apply(r,c):r.push(s):n.push({documentOrder:a,tabIndex:u,item:i,isScope:o,content:c})}),n.sort(Ma).reduce(function(i,a){return a.isScope?i.push.apply(i,a.content):i.push(a.content),i},[]).concat(r)},qa=function(t,r){r=r||{};var n;return r.getShadowRoot?n=vn([t],r.includeContainer,{filter:ft.bind(null,r),flatten:!1,getShadowRoot:r.getShadowRoot,shadowRootFilter:Ha}):n=_n(t,r.includeContainer,ft.bind(null,r)),za(n)},yn=function(t,r){r=r||{};var n;return r.getShadowRoot?n=vn([t],r.includeContainer,{filter:Ie.bind(null,r),flatten:!0,getShadowRoot:r.getShadowRoot}):n=_n(t,r.includeContainer,Ie.bind(null,r)),n},we=function(t,r){if(r=r||{},!t)throw new Error("No node provided");return Y.call(t,Ne)===!1?!1:ft(r,t)},Wa=pn.concat("iframe").join(","),Oe=function(t,r){if(r=r||{},!t)throw new Error("No node provided");return Y.call(t,Wa)===!1?!1:Ie(r,t)};function Jt(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Yt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Jt(Object(r),!0).forEach(function(n){Ua(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Jt(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Ua(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Xt=function(){var e=[];return{activateTrap:function(r){if(e.length>0){var n=e[e.length-1];n!==r&&n.pause()}var i=e.indexOf(r);i===-1||e.splice(i,1),e.push(r)},deactivateTrap:function(r){var n=e.indexOf(r);n!==-1&&e.splice(n,1),e.length>0&&e[e.length-1].unpause()}}}(),Va=function(t){return t.tagName&&t.tagName.toLowerCase()==="input"&&typeof t.select=="function"},Ga=function(t){return t.key==="Escape"||t.key==="Esc"||t.keyCode===27},Ja=function(t){return t.key==="Tab"||t.keyCode===9},Zt=function(t){return setTimeout(t,0)},Qt=function(t,r){var n=-1;return t.every(function(i,a){return r(i)?(n=a,!1):!0}),n},ce=function(t){for(var r=arguments.length,n=new Array(r>1?r-1:0),i=1;i<r;i++)n[i-1]=arguments[i];return typeof t=="function"?t.apply(void 0,n):t},Se=function(t){return t.target.shadowRoot&&typeof t.composedPath=="function"?t.composedPath()[0]:t.target},Ya=function(t,r){var n=(r==null?void 0:r.document)||document,i=Yt({returnFocusOnDeactivate:!0,escapeDeactivates:!0,delayInitialFocus:!0},r),a={containers:[],containerGroups:[],tabbableGroups:[],nodeFocusedBeforeActivation:null,mostRecentlyFocusedNode:null,active:!1,paused:!1,delayInitialFocusTimer:void 0},o,s=function(f,d,v){return f&&f[d]!==void 0?f[d]:i[v||d]},u=function(f){return a.containerGroups.findIndex(function(d){var v=d.container,E=d.tabbableNodes;return v.contains(f)||E.find(function(S){return S===f})})},c=function(f){var d=i[f];if(typeof d=="function"){for(var v=arguments.length,E=new Array(v>1?v-1:0),S=1;S<v;S++)E[S-1]=arguments[S];d=d.apply(void 0,E)}if(d===!0&&(d=void 0),!d){if(d===void 0||d===!1)return d;throw new Error("`".concat(f,"` was specified but was not a node, or did not return a node"))}var R=d;if(typeof d=="string"&&(R=n.querySelector(d),!R))throw new Error("`".concat(f,"` as selector refers to no known node"));return R},l=function(){var f=c("initialFocus");if(f===!1)return!1;if(f===void 0)if(u(n.activeElement)>=0)f=n.activeElement;else{var d=a.tabbableGroups[0],v=d&&d.firstTabbableNode;f=v||c("fallbackFocus")}if(!f)throw new Error("Your focus-trap needs to have at least one focusable element");return f},p=function(){if(a.containerGroups=a.containers.map(function(f){var d=qa(f,i.tabbableOptions),v=yn(f,i.tabbableOptions);return{container:f,tabbableNodes:d,focusableNodes:v,firstTabbableNode:d.length>0?d[0]:null,lastTabbableNode:d.length>0?d[d.length-1]:null,nextTabbableNode:function(S){var R=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,$=v.findIndex(function(L){return L===S});if(!($<0))return R?v.slice($+1).find(function(L){return we(L,i.tabbableOptions)}):v.slice(0,$).reverse().find(function(L){return we(L,i.tabbableOptions)})}}}),a.tabbableGroups=a.containerGroups.filter(function(f){return f.tabbableNodes.length>0}),a.tabbableGroups.length<=0&&!c("fallbackFocus"))throw new Error("Your focus-trap must have at least one container with at least one tabbable node in it at all times")},_=function y(f){if(f!==!1&&f!==n.activeElement){if(!f||!f.focus){y(l());return}f.focus({preventScroll:!!i.preventScroll}),a.mostRecentlyFocusedNode=f,Va(f)&&f.select()}},g=function(f){var d=c("setReturnFocus",f);return d||(d===!1?!1:f)},C=function(f){var d=Se(f);if(!(u(d)>=0)){if(ce(i.clickOutsideDeactivates,f)){o.deactivate({returnFocus:i.returnFocusOnDeactivate&&!Oe(d,i.tabbableOptions)});return}ce(i.allowOutsideClick,f)||f.preventDefault()}},P=function(f){var d=Se(f),v=u(d)>=0;v||d instanceof Document?v&&(a.mostRecentlyFocusedNode=d):(f.stopImmediatePropagation(),_(a.mostRecentlyFocusedNode||l()))},h=function(f){var d=Se(f);p();var v=null;if(a.tabbableGroups.length>0){var E=u(d),S=E>=0?a.containerGroups[E]:void 0;if(E<0)f.shiftKey?v=a.tabbableGroups[a.tabbableGroups.length-1].lastTabbableNode:v=a.tabbableGroups[0].firstTabbableNode;else if(f.shiftKey){var R=Qt(a.tabbableGroups,function(Be){var Ke=Be.firstTabbableNode;return d===Ke});if(R<0&&(S.container===d||Oe(d,i.tabbableOptions)&&!we(d,i.tabbableOptions)&&!S.nextTabbableNode(d,!1))&&(R=E),R>=0){var $=R===0?a.tabbableGroups.length-1:R-1,L=a.tabbableGroups[$];v=L.lastTabbableNode}}else{var ae=Qt(a.tabbableGroups,function(Be){var Ke=Be.lastTabbableNode;return d===Ke});if(ae<0&&(S.container===d||Oe(d,i.tabbableOptions)&&!we(d,i.tabbableOptions)&&!S.nextTabbableNode(d))&&(ae=E),ae>=0){var xn=ae===a.tabbableGroups.length-1?0:ae+1,wn=a.tabbableGroups[xn];v=wn.firstTabbableNode}}}else v=c("fallbackFocus");v&&(f.preventDefault(),_(v))},b=function(f){if(Ga(f)&&ce(i.escapeDeactivates,f)!==!1){f.preventDefault(),o.deactivate();return}if(Ja(f)){h(f);return}},x=function(f){var d=Se(f);u(d)>=0||ce(i.clickOutsideDeactivates,f)||ce(i.allowOutsideClick,f)||(f.preventDefault(),f.stopImmediatePropagation())},O=function(){if(a.active)return Xt.activateTrap(o),a.delayInitialFocusTimer=i.delayInitialFocus?Zt(function(){_(l())}):_(l()),n.addEventListener("focusin",P,!0),n.addEventListener("mousedown",C,{capture:!0,passive:!1}),n.addEventListener("touchstart",C,{capture:!0,passive:!1}),n.addEventListener("click",x,{capture:!0,passive:!1}),n.addEventListener("keydown",b,{capture:!0,passive:!1}),o},T=function(){if(a.active)return n.removeEventListener("focusin",P,!0),n.removeEventListener("mousedown",C,!0),n.removeEventListener("touchstart",C,!0),n.removeEventListener("click",x,!0),n.removeEventListener("keydown",b,!0),o};return o={get active(){return a.active},get paused(){return a.paused},activate:function(f){if(a.active)return this;var d=s(f,"onActivate"),v=s(f,"onPostActivate"),E=s(f,"checkCanFocusTrap");E||p(),a.active=!0,a.paused=!1,a.nodeFocusedBeforeActivation=n.activeElement,d&&d();var S=function(){E&&p(),O(),v&&v()};return E?(E(a.containers.concat()).then(S,S),this):(S(),this)},deactivate:function(f){if(!a.active)return this;var d=Yt({onDeactivate:i.onDeactivate,onPostDeactivate:i.onPostDeactivate,checkCanReturnFocus:i.checkCanReturnFocus},f);clearTimeout(a.delayInitialFocusTimer),a.delayInitialFocusTimer=void 0,T(),a.active=!1,a.paused=!1,Xt.deactivateTrap(o);var v=s(d,"onDeactivate"),E=s(d,"onPostDeactivate"),S=s(d,"checkCanReturnFocus"),R=s(d,"returnFocus","returnFocusOnDeactivate");v&&v();var $=function(){Zt(function(){R&&_(g(a.nodeFocusedBeforeActivation)),E&&E()})};return R&&S?(S(g(a.nodeFocusedBeforeActivation)).then($,$),this):($(),this)},pause:function(){return a.paused||!a.active?this:(a.paused=!0,T(),this)},unpause:function(){return!a.paused||!a.active?this:(a.paused=!1,p(),O(),this)},updateContainerElements:function(f){var d=[].concat(f).filter(Boolean);return a.containers=d.map(function(v){return typeof v=="string"?n.querySelector(v):v}),a.active&&p(),this}},o.updateContainerElements(t),o};function Xa(e){let t,r;window.addEventListener("focusin",()=>{t=r,r=document.activeElement}),e.magic("focus",n=>{let i=n;return{__noscroll:!1,__wrapAround:!1,within(a){return i=a,this},withoutScrolling(){return this.__noscroll=!0,this},noscroll(){return this.__noscroll=!0,this},withWrapAround(){return this.__wrapAround=!0,this},wrap(){return this.withWrapAround()},focusable(a){return Oe(a)},previouslyFocused(){return t},lastFocused(){return t},focused(){return r},focusables(){return Array.isArray(i)?i:yn(i,{displayCheck:"none"})},all(){return this.focusables()},isFirst(a){let o=this.all();return o[0]&&o[0].isSameNode(a)},isLast(a){let o=this.all();return o.length&&o.slice(-1)[0].isSameNode(a)},getFirst(){return this.all()[0]},getLast(){return this.all().slice(-1)[0]},getNext(){let a=this.all(),o=document.activeElement;if(a.indexOf(o)!==-1)return this.__wrapAround&&a.indexOf(o)===a.length-1?a[0]:a[a.indexOf(o)+1]},getPrevious(){let a=this.all(),o=document.activeElement;if(a.indexOf(o)!==-1)return this.__wrapAround&&a.indexOf(o)===0?a.slice(-1)[0]:a[a.indexOf(o)-1]},first(){this.focus(this.getFirst())},last(){this.focus(this.getLast())},next(){this.focus(this.getNext())},previous(){this.focus(this.getPrevious())},prev(){return this.previous()},focus(a){a&&setTimeout(()=>{a.hasAttribute("tabindex")||a.setAttribute("tabindex","0"),a.focus({preventScroll:this.__noscroll})})}}}),e.directive("trap",e.skipDuringClone((n,{expression:i,modifiers:a},{effect:o,evaluateLater:s,cleanup:u})=>{let c=s(i),l=!1,p={escapeDeactivates:!1,allowOutsideClick:!0,fallbackFocus:()=>n};if(a.includes("noautofocus"))p.initialFocus=!1;else{let h=n.querySelector("[autofocus]");h&&(p.initialFocus=h)}let _=Ya(n,p),g=()=>{},C=()=>{};const P=()=>{g(),g=()=>{},C(),C=()=>{},_.deactivate({returnFocus:!a.includes("noreturn")})};o(()=>c(h=>{l!==h&&(h&&!l&&(a.includes("noscroll")&&(C=Za()),a.includes("inert")&&(g=er(n)),setTimeout(()=>{_.activate()},15)),!h&&l&&P(),l=!!h)})),u(P)},(n,{expression:i,modifiers:a},{evaluate:o})=>{a.includes("inert")&&o(i)&&er(n)}))}function er(e){let t=[];return mn(e,r=>{let n=r.hasAttribute("aria-hidden");r.setAttribute("aria-hidden","true"),t.push(()=>n||r.removeAttribute("aria-hidden"))}),()=>{for(;t.length;)t.pop()()}}function mn(e,t){e.isSameNode(document.body)||!e.parentNode||Array.from(e.parentNode.children).forEach(r=>{r.isSameNode(e)?mn(e.parentNode,t):t(r)})}function Za(){let e=document.documentElement.style.overflow,t=document.documentElement.style.paddingRight,r=window.innerWidth-document.documentElement.clientWidth;return document.documentElement.style.overflow="hidden",document.documentElement.style.paddingRight=`${r}px`,()=>{document.documentElement.style.overflow=e,document.documentElement.style.paddingRight=t}}var no=Xa;/*! Bundled license information:

tabbable/dist/index.esm.js:
  (*!
  * tabbable 5.3.3
  * @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE
  *)

focus-trap/dist/focus-trap.esm.js:
  (*!
  * focus-trap 6.9.4
  * @license MIT, https://github.com/focus-trap/focus-trap/blob/master/LICENSE
  *)
*/export{ro as a,no as b,to as m};

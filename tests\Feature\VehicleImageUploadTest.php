<?php

use App\Models\User;
use App\Models\File;
use App\Models\Vehicle;
use App\Models\VehicleType;
use App\Models\City;
use App\Models\VehicleImage;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Seed the database with required data
    $this->seed(\Database\Seeders\RoleSeeder::class);
    $this->seed(\Database\Seeders\PermissionSeeder::class);
    $this->seed(\Database\Seeders\LocationSeeder::class);
    $this->seed(\Database\Seeders\VehicleTypeSeeder::class);
    $this->seed(\Database\Seeders\DocumentTypeSeeder::class);
    
    // Create admin user
    $this->admin = User::create([
        'first_name' => 'Admin',
        'last_name' => 'User',
        'email' => '<EMAIL>',
        'password' => bcrypt('password'),
        'email_verified_at' => now(),
    ]);
    $this->admin->assignRole('admin');
    
    // Get references to seeded data
    $this->vehicleType = VehicleType::where('slug', 'sedan')->first();
    $this->city = City::where('name', 'New York City')->first();
    
    // Setup fake storage
    Storage::fake('public');
});

/**
 * Helper function to create a file in the database
 */
function createFile($filename, $fileType = 'image/jpeg', $fileSize = 1000)
{
    return File::create([
        'title' => $filename,
        'filename' => $filename,
        'url' => '/storage/uploads/' . $filename,
        'fileSize' => $fileSize,
        'fileType' => $fileType
    ]);
}

test('vehicle creation requires a primary image', function () {
    $this->actingAs($this->admin);
    
    // Create vehicle data without primary image
    $vehicleData = [
        'vehicle_type_id' => $this->vehicleType->id,
        'make' => 'Toyota',
        'model' => 'Camry',
        'year' => 2022,
        'license_plate' => 'TEST123',
        'color' => 'Blue',
        'mileage' => 15000,
        'transmission' => 'automatic',
        'fuel_type' => 'gasoline',
        'seats' => '5',
        'doors' => 4,
        'city_id' => $this->city->id,
        'daily_rate' => 50.00,
        // Missing primary_image
    ];
    
    $response = $this->post('/management/vehicles', $vehicleData);
    
    // Should return with validation errors
    $response->assertSessionHasErrors(['primary_image']);
    
    // No vehicle should be created
    $this->assertEquals(0, Vehicle::count());
});

test('vehicle can be created with primary image', function () {
    $this->actingAs($this->admin);
    
    // Create a file record in the database
    $primaryImageFile = createFile('primary_image.jpg');
    
    // Create vehicle data with primary image
    $vehicleData = [
        'vehicle_type_id' => $this->vehicleType->id,
        'make' => 'Toyota',
        'model' => 'Camry',
        'year' => 2022,
        'license_plate' => 'TEST123',
        'color' => 'Blue',
        'mileage' => 15000,
        'transmission' => 'automatic',
        'fuel_type' => 'gasoline',
        'seats' => '5',
        'doors' => 4,
        'city_id' => $this->city->id,
        'daily_rate' => 50.00,
        'primary_image' => $primaryImageFile->id,
    ];
    
    $response = $this->post('/management/vehicles', $vehicleData);
    
    // Check if redirected to vehicles index with success message
    $response->assertRedirect(route('vehicles.index'));
    $response->assertSessionHas('success');
    
    // Verify the vehicle was created
    $vehicle = Vehicle::where('license_plate', 'TEST123')->first();
    $this->assertNotNull($vehicle);
    
    // Verify the primary image was associated with the vehicle
    $vehicleImage = VehicleImage::where('vehicle_id', $vehicle->id)->first();
    $this->assertNotNull($vehicleImage);
    $this->assertEquals($primaryImageFile->id, $vehicleImage->file_id);
    $this->assertTrue($vehicleImage->is_primary);
});

test('vehicle can be created with primary image and gallery images', function () {
    $this->actingAs($this->admin);
    
    // Create file records in the database
    $primaryImageFile = createFile('primary_image.jpg');
    $galleryImage1 = createFile('gallery_image1.jpg');
    $galleryImage2 = createFile('gallery_image2.jpg');
    
    // Create a comma-separated list of gallery image IDs
    $galleryImageIds = $galleryImage1->id . ',' . $galleryImage2->id;
    
    // Create vehicle data with primary image and gallery images
    $vehicleData = [
        'vehicle_type_id' => $this->vehicleType->id,
        'make' => 'Toyota',
        'model' => 'Camry',
        'year' => 2022,
        'license_plate' => 'TEST123',
        'color' => 'Blue',
        'mileage' => 15000,
        'transmission' => 'automatic',
        'fuel_type' => 'gasoline',
        'seats' => '5',
        'doors' => 4,
        'city_id' => $this->city->id,
        'daily_rate' => 50.00,
        'primary_image' => $primaryImageFile->id,
        'images' => $galleryImageIds,
    ];
    
    $response = $this->post('/management/vehicles', $vehicleData);
    
    // Check if redirected to vehicles index with success message
    $response->assertRedirect(route('vehicles.index'));
    $response->assertSessionHas('success');
    
    // Verify the vehicle was created
    $vehicle = Vehicle::where('license_plate', 'TEST123')->first();
    $this->assertNotNull($vehicle);
    
    // Verify the primary image was associated with the vehicle
    $primaryVehicleImage = VehicleImage::where('vehicle_id', $vehicle->id)
        ->where('is_primary', true)
        ->first();
    $this->assertNotNull($primaryVehicleImage);
    $this->assertEquals($primaryImageFile->id, $primaryVehicleImage->file_id);
    
    // Verify the gallery images were associated with the vehicle
    $galleryImages = VehicleImage::where('vehicle_id', $vehicle->id)
        ->where('is_primary', false)
        ->get();
    $this->assertEquals(2, $galleryImages->count());
    
    // Check that the gallery image IDs match
    $galleryImageFileIds = $galleryImages->pluck('file_id')->toArray();
    $this->assertContains($galleryImage1->id, $galleryImageFileIds);
    $this->assertContains($galleryImage2->id, $galleryImageFileIds);
});

test('vehicle creation with document uploads', function () {
    $this->actingAs($this->admin);
    
    // Create file records in the database
    $primaryImageFile = createFile('primary_image.jpg');
    $ownershipDocFile = createFile('ownership_doc.pdf', 'application/pdf');
    $insuranceDocFile = createFile('insurance_doc.pdf', 'application/pdf');
    
    // Get document type IDs
    $ownershipDocTypeId = 2; // Vehicle Registration
    $insuranceDocTypeId = 3; // Vehicle Insurance
    
    // Create vehicle data with primary image and documents
    $vehicleData = [
        'vehicle_type_id' => $this->vehicleType->id,
        'make' => 'Toyota',
        'model' => 'Camry',
        'year' => 2022,
        'license_plate' => 'TEST123',
        'color' => 'Blue',
        'mileage' => 15000,
        'transmission' => 'automatic',
        'fuel_type' => 'gasoline',
        'seats' => '5',
        'doors' => 4,
        'city_id' => $this->city->id,
        'daily_rate' => 50.00,
        'primary_image' => $primaryImageFile->id,
        'documents' => [
            $ownershipDocTypeId => [
                'file_id' => $ownershipDocFile->id,
            ],
            $insuranceDocTypeId => [
                'file_id' => $insuranceDocFile->id,
                'expiry_date' => now()->addYear()->format('Y-m-d'),
            ],
        ],
    ];
    
    $response = $this->post('/management/vehicles', $vehicleData);
    
    // Check if redirected to vehicles index with success message
    $response->assertRedirect(route('vehicles.index'));
    $response->assertSessionHas('success');
    
    // Verify the vehicle was created
    $vehicle = Vehicle::where('license_plate', 'TEST123')->first();
    $this->assertNotNull($vehicle);
    
    // Verify the primary image was associated with the vehicle
    $vehicleImage = VehicleImage::where('vehicle_id', $vehicle->id)->first();
    $this->assertNotNull($vehicleImage);
    $this->assertEquals($primaryImageFile->id, $vehicleImage->file_id);
    $this->assertTrue($vehicleImage->is_primary);
    
    // Verify the documents were associated with the vehicle
    // Note: This would require checking the VehicleDocument table
    // which we would need to add to the test if needed
});

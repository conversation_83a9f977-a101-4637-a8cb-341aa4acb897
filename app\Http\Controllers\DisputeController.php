<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\Dispute;
use App\Models\DisputeMessage;
use App\Models\DisputeEvidence;
use App\Models\File;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class DisputeController extends Controller
{
    /**
     * Display a listing of the disputes.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $user = Auth::user();
        
        // Get disputes based on user role
        if ($user->hasRole('admin')) {
            // Ad<PERSON> can see all disputes
            $disputes = Dispute::with(['booking', 'user', 'againstUser'])
                ->orderBy('created_at', 'desc')
                ->paginate(10);
        } else {
            // Regular users can only see disputes they're involved in
            $disputes = Dispute::where('user_id', $user->id)
                ->orWhere('against_user_id', $user->id)
                ->with(['booking', 'user', 'againstUser'])
                ->orderBy('created_at', 'desc')
                ->paginate(10);
        }
        
        return view('disputes.index', compact('disputes'));
    }
    
    /**
     * Show the form for creating a new dispute.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function create(Request $request)
    {
        $bookingId = $request->input('booking_id');
        
        if ($bookingId) {
            $booking = Booking::with(['vehicle', 'vehicle.user', 'user', 'driver'])
                ->findOrFail($bookingId);
                
            // Check if user is involved in this booking
            if ($booking->user_id !== Auth::id() && $booking->vehicle->user_id !== Auth::id()) {
                abort(403, 'You cannot create a dispute for a booking you are not involved in');
            }
            
            // Determine against whom the dispute is being filed
            $againstUser = null;
            if ($booking->user_id === Auth::id()) {
                // Renter is filing against owner
                $againstUser = $booking->vehicle->user;
            } else {
                // Owner is filing against renter
                $againstUser = $booking->user;
            }
            
            return view('disputes.create', compact('booking', 'againstUser'));
        }
        
        // If no booking_id is provided, show a list of bookings to choose from
        $bookings = Booking::where(function($query) {
            $query->where('user_id', Auth::id()) // Bookings where user is the renter
                  ->orWhereHas('vehicle', function($q) {
                      $q->where('user_id', Auth::id()); // Bookings where user is the vehicle owner
                  });
        })
        ->whereIn('status', ['completed', 'in_progress', 'cancelled'])
        ->with(['vehicle', 'vehicle.user', 'user'])
        ->orderBy('created_at', 'desc')
        ->get();
        
        return view('disputes.select-booking', compact('bookings'));
    }
    
    /**
     * Store a newly created dispute.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'booking_id' => 'required|exists:bookings,id',
            'against_user_id' => 'required|exists:users,id',
            'type' => 'required|in:damage,cleanliness,late_return,no_show,extra_charges,incorrect_fuel,other',
            'description' => 'required|string|max:5000',
            'amount_claimed' => 'nullable|numeric|min:0',
            'evidence_files' => 'nullable|array',
            'evidence_files.*' => 'nullable|exists:files,id',
            'evidence_titles' => 'nullable|array',
            'evidence_titles.*' => 'nullable|string|max:255',
            'evidence_descriptions' => 'nullable|array',
            'evidence_descriptions.*' => 'nullable|string|max:1000',
        ]);
        
        $booking = Booking::findOrFail($validated['booking_id']);
        
        // Check if user is involved in this booking
        if ($booking->user_id !== Auth::id() && $booking->vehicle->user_id !== Auth::id()) {
            abort(403, 'You cannot create a dispute for a booking you are not involved in');
        }
        
        // Check if a dispute already exists for this booking by this user
        $existingDispute = Dispute::where('booking_id', $booking->id)
            ->where('user_id', Auth::id())
            ->first();
            
        if ($existingDispute) {
            return redirect()->route('disputes.show', $existingDispute->id)
                ->with('error', 'You already have an open dispute for this booking.');
        }
        
        try {
            DB::beginTransaction();
            
            // Create the dispute
            $dispute = Dispute::create([
                'booking_id' => $validated['booking_id'],
                'user_id' => Auth::id(),
                'against_user_id' => $validated['against_user_id'],
                'dispute_number' => Dispute::generateDisputeNumber(),
                'type' => $validated['type'],
                'description' => $validated['description'],
                'amount_claimed' => $validated['amount_claimed'],
                'status' => 'open',
            ]);
            
            // Create initial system message
            DisputeMessage::create([
                'dispute_id' => $dispute->id,
                'user_id' => Auth::id(),
                'message' => 'Dispute opened by ' . Auth::user()->first_name . ' ' . Auth::user()->last_name,
                'is_system_message' => true,
            ]);
            
            // Process evidence files if any
            if ($request->has('evidence_files') && is_array($request->evidence_files)) {
                foreach ($request->evidence_files as $index => $fileId) {
                    if (!$fileId) continue;
                    
                    $title = $request->evidence_titles[$index] ?? 'Evidence ' . ($index + 1);
                    $description = $request->evidence_descriptions[$index] ?? null;
                    
                    DisputeEvidence::create([
                        'dispute_id' => $dispute->id,
                        'user_id' => Auth::id(),
                        'file_id' => $fileId,
                        'title' => $title,
                        'description' => $description,
                    ]);
                }
            }
            
            DB::commit();
            
            return redirect()->route('disputes.show', $dispute->id)
                ->with('success', 'Dispute created successfully.');
                
        } catch (\Exception $e) {
            DB::rollBack();
            
            return back()->with('error', 'Failed to create dispute: ' . $e->getMessage())
                ->withInput();
        }
    }
    
    /**
     * Display the specified dispute.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $dispute = Dispute::with([
            'booking', 
            'booking.vehicle', 
            'booking.vehicle.user', 
            'booking.user', 
            'user', 
            'againstUser', 
            'messages.user', 
            'evidence.file', 
            'evidence.user'
        ])->findOrFail($id);
        
        // Check if user is involved in this dispute or is an admin
        $user = Auth::user();
        if ($dispute->user_id !== $user->id && $dispute->against_user_id !== $user->id && !$user->hasRole('admin')) {
            abort(403, 'You do not have access to this dispute');
        }
        
        return view('disputes.show', compact('dispute'));
    }
    
    /**
     * Add a message to a dispute.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function addMessage(Request $request, $id)
    {
        $validated = $request->validate([
            'message' => 'required|string|max:5000',
        ]);
        
        $dispute = Dispute::findOrFail($id);
        
        // Check if user is involved in this dispute or is an admin
        $user = Auth::user();
        if ($dispute->user_id !== $user->id && $dispute->against_user_id !== $user->id && !$user->hasRole('admin')) {
            abort(403, 'You do not have access to this dispute');
        }
        
        // Don't allow messages if dispute is closed
        if ($dispute->status === 'closed') {
            return back()->with('error', 'This dispute is closed and cannot be updated.');
        }
        
        $isAdminMessage = $user->hasRole('admin');
        
        DisputeMessage::create([
            'dispute_id' => $dispute->id,
            'user_id' => $user->id,
            'message' => $validated['message'],
            'is_admin_message' => $isAdminMessage,
        ]);
        
        return back()->with('success', 'Message added successfully.');
    }
    
    /**
     * Add evidence to a dispute.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function addEvidence(Request $request, $id)
    {
        $validated = $request->validate([
            'file_id' => 'required|exists:files,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
        ]);
        
        $dispute = Dispute::findOrFail($id);
        
        // Check if user is involved in this dispute or is an admin
        $user = Auth::user();
        if ($dispute->user_id !== $user->id && $dispute->against_user_id !== $user->id && !$user->hasRole('admin')) {
            abort(403, 'You do not have access to this dispute');
        }
        
        // Don't allow evidence if dispute is closed
        if ($dispute->status === 'closed') {
            return back()->with('error', 'This dispute is closed and cannot be updated.');
        }
        
        DisputeEvidence::create([
            'dispute_id' => $dispute->id,
            'user_id' => $user->id,
            'file_id' => $validated['file_id'],
            'title' => $validated['title'],
            'description' => $validated['description'],
        ]);
        
        // Add system message about evidence
        DisputeMessage::create([
            'dispute_id' => $dispute->id,
            'user_id' => $user->id,
            'message' => $user->first_name . ' ' . $user->last_name . ' added evidence: ' . $validated['title'],
            'is_system_message' => true,
        ]);
        
        return back()->with('success', 'Evidence added successfully.');
    }
    
    /**
     * Update the status of a dispute.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateStatus(Request $request, $id)
    {
        $validated = $request->validate([
            'status' => 'required|in:open,under_review,resolved,closed',
            'resolution_notes' => 'nullable|string|max:5000',
        ]);
        
        $dispute = Dispute::findOrFail($id);
        
        // Only admins can change status to resolved or closed
        $user = Auth::user();
        if (in_array($validated['status'], ['resolved', 'closed']) && !$user->hasRole('admin')) {
            abort(403, 'Only administrators can resolve or close disputes');
        }
        
        // Update dispute status
        $oldStatus = $dispute->status;
        $newStatus = $validated['status'];
        
        $updateData = [
            'status' => $newStatus,
        ];
        
        // If resolving or closing, add resolution details
        if (in_array($newStatus, ['resolved', 'closed'])) {
            $updateData['resolution_notes'] = $validated['resolution_notes'];
            $updateData['resolved_by'] = $user->id;
            $updateData['resolved_at'] = now();
        }
        
        $dispute->update($updateData);
        
        // Add system message about status change
        $statusMessage = 'Dispute status changed from ' . ucfirst($oldStatus) . ' to ' . ucfirst($newStatus);
        if ($newStatus === 'resolved' || $newStatus === 'closed') {
            $statusMessage .= ' by ' . $user->first_name . ' ' . $user->last_name;
        }
        
        DisputeMessage::create([
            'dispute_id' => $dispute->id,
            'user_id' => $user->id,
            'message' => $statusMessage,
            'is_system_message' => true,
            'is_admin_message' => $user->hasRole('admin'),
        ]);
        
        return back()->with('success', 'Dispute status updated successfully.');
    }
}

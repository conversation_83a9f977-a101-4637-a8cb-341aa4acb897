<!-- 
    File Card Component
    To use this as a standalone component, add:
    x-data="{ isSelected: false, showMenu: false }" to the div
-->
<div 
    class="file-card bg-white border-2 rounded-lg shadow-sm p-2 relative cursor-pointer transition duration-200 hover:shadow-md"
    id="file-card-<?php echo e($file->id); ?>"
    data-id="<?php echo e($file->id); ?>"
    :class="{
        'border-indigo-500 bg-indigo-50 ring-2 ring-indigo-300 shadow-md': isSelected,
        'border-gray-200': !isSelected
    }"
    aria-label="File: <?php echo e($file->original_name ?? $file->title); ?>"
    role="button"
    tabindex="0"
    @keydown.enter.stop="$dispatch('file-selected', { id: '<?php echo e($file->id); ?>' })"
    @keydown.space.prevent.stop="$dispatch('file-selected', { id: '<?php echo e($file->id); ?>' })"
>
    <input
        type="checkbox"
        class="absolute top-2 left-2 rounded text-indigo-600 focus:ring-indigo-500 border-gray-300 z-10"
        id="checkbox-<?php echo e($file->id); ?>"
        :checked="isSelected"
        @click.stop="$dispatch('file-selected', { id: '<?php echo e($file->id); ?>' })"
        aria-label="Select <?php echo e($file->original_name ?? $file->title); ?>"
    >
    
    <!-- Selected overlay -->
    <div
        x-show="isSelected"
        class="absolute inset-0 bg-indigo-100 bg-opacity-30 z-0"
    ></div>
    
    <div @click.stop="$dispatch('file-selected', { id: '<?php echo e($file->id); ?>' })" class="mt-2">
        <div class="file-image-container overflow-hidden rounded relative z-0">
            <?php if(Str::startsWith($file->mime_type ?? '', 'image/')): ?>
                <img 
                    src="<?php echo e($file->url); ?>" 
                    alt="<?php echo e($file->original_name ?? $file->title); ?>" 
                    class="w-full h-32 object-cover transition-transform duration-300"
                    loading="lazy"
                    onerror="this.onerror=null; this.src='/images/fallback-image.jpg'"
                >
            <?php else: ?>
                <div class="w-full h-32 flex items-center justify-center bg-gray-100">
                    <i class="fas fa-file text-gray-400 text-5xl"></i>
                </div>
            <?php endif; ?>
            
            <!-- Selected checkmark overlay -->
            <div 
                x-show="isSelected" 
                class="absolute top-0 right-0 bg-indigo-600 text-white rounded-bl-lg p-1 shadow-md"
            >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
            </div>
        </div>
        
        <div class="flex justify-between items-center mt-2 relative z-0">
            <span 
                class="text-sm font-medium truncate max-w-[70%]" 
                title="<?php echo e($file->original_name ?? $file->title); ?>"
            ><?php echo e($file->original_name ?? $file->title); ?></span>
            <span class="text-xs text-gray-500"><?php echo e(round(($file->size ?? $file->fileSize) / 1024)); ?> KB</span>
        </div>
    </div>
</div><?php /**PATH C:\laragon\www\carbnb\resources\views\components\file-management\file-card.blade.php ENDPATH**/ ?>
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Driver extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'bio',
        'license_number',
        'license_expiry',
        'experience_years',
        'languages',
        'hourly_rate',
        'daily_rate',
        'available_weekdays',
        'available_weekends',
        'status',
        'rejection_reason',
        'is_featured',
        'city_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'license_expiry' => 'date',
        'languages' => 'json',
        'hourly_rate' => 'decimal:2',
        'daily_rate' => 'decimal:2',
        'available_weekdays' => 'boolean',
        'available_weekends' => 'boolean',
        'is_featured' => 'boolean',
    ];

    /**
     * Get the user that owns the driver profile.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the driver's license.
     */
    public function license()
    {
        return $this->hasOne(DriverLicense::class);
    }

    /**
     * Get the driver's availability schedule.
     */
    public function availability()
    {
        return $this->hasMany(DriverAvailability::class);
    }

    /**
     * Get the driver's documents.
     */
    public function documents()
    {
        return $this->hasMany(Document::class, 'user_id', 'user_id');
    }

    /**
     * Get the city that the driver is associated with.
     */
    public function city()
    {
        return $this->belongsTo(City::class);
    }

    /**
     * Scope a query to only include active drivers.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include pending drivers.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope a query to only include featured drivers.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Get the bookings for this driver.
     */
    public function bookings()
    {
        return $this->hasMany(Booking::class);
    }

    /**
     * Check if the driver is available for a specific date range.
     *
     * @param \Carbon\Carbon $startDate
     * @param \Carbon\Carbon $endDate
     * @return bool
     */
    public function isAvailableForDateRange($startDate, $endDate)
    {
        // Check if driver has any bookings in this date range
        $hasBookings = $this->bookings()
            ->where(function($query) use ($startDate, $endDate) {
                $query->whereBetween('start_date', [$startDate, $endDate])
                    ->orWhereBetween('end_date', [$startDate, $endDate])
                    ->orWhere(function($query) use ($startDate, $endDate) {
                        $query->where('start_date', '<=', $startDate)
                            ->where('end_date', '>=', $endDate);
                    });
            })
            ->exists();

        if ($hasBookings) {
            return false;
        }

        // Check driver availability settings
        $dateRange = new \DatePeriod(
            $startDate->startOfDay(),
            new \DateInterval('P1D'),
            $endDate->endOfDay()
        );

        foreach ($dateRange as $date) {
            // Check custom availability first
            $customAvailability = $this->availability()
                ->where('date', $date->format('Y-m-d'))
                ->first();

            if ($customAvailability) {
                if (!$customAvailability->is_available) {
                    return false;
                }
                continue;
            }

            // Check default availability based on weekday/weekend
            $isWeekend = $date->isWeekend();
            $isAvailable = ($isWeekend && $this->available_weekends) ||
                          (!$isWeekend && $this->available_weekdays);

            if (!$isAvailable) {
                return false;
            }
        }

        return true;
    }
}

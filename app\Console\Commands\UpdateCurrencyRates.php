<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use App\Models\Currency;
use Illuminate\Support\Facades\Log;

class UpdateCurrencyRates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'currency:update';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update currency exchange rates';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Updating currency exchange rates...');

        try {
            // Get the base currency
            $baseCurrency = Currency::where('is_default', 1)->first();
            
            if (!$baseCurrency) {
                $this->error('Default currency not found!');
                return 1;
            }
            
            $this->info("Base currency: {$baseCurrency->code}");
            
            // Get all currencies except the base currency
            $currencies = Currency::where('code', '!=', $baseCurrency->code)->get();
            
            if ($currencies->isEmpty()) {
                $this->info('No additional currencies to update.');
                return 0;
            }
            
            // Get currency codes
            $currencyCodes = $currencies->pluck('code')->implode(',');
            
            // Call the API to get exchange rates
            $response = Http::get("https://api.exchangerate.host/latest", [
                'base' => $baseCurrency->code,
                'symbols' => $currencyCodes,
            ]);
            
            if (!$response->successful()) {
                $this->error('Failed to fetch exchange rates: ' . $response->body());
                return 1;
            }
            
            $data = $response->json();
            
            if (!isset($data['rates']) || empty($data['rates'])) {
                $this->error('No exchange rates found in the API response.');
                return 1;
            }
            
            $this->info('Exchange rates retrieved successfully.');
            
            // Update exchange rates
            $bar = $this->output->createProgressBar(count($currencies));
            $bar->start();
            
            foreach ($currencies as $currency) {
                if (isset($data['rates'][$currency->code])) {
                    $rate = $data['rates'][$currency->code];
                    $currency->exchange_rate = $rate;
                    $currency->updated_at = now();
                    $currency->save();
                    
                    $this->line("Updated {$currency->code}: {$rate}");
                } else {
                    $this->warn("Exchange rate for {$currency->code} not found in API response.");
                }
                
                $bar->advance();
            }
            
            $bar->finish();
            $this->newLine();
            $this->info('Currency exchange rates updated successfully!');
            
            return 0;
        } catch (\Exception $e) {
            $this->error('Error updating currency rates: ' . $e->getMessage());
            Log::error('Currency update error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            
            return 1;
        }
    }
}

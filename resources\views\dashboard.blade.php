<x-app-backend-layout>
    <x-slot name="header">
        <h1 class="h1">
            {{ backend_trans('dashboard') }}
        </h1>
    </x-slot>

    <!-- FullCalendar CSS -->
    <link href='https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.css' rel='stylesheet' />

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">


            <!-- Admin Dashboard -->
            @if($userRoles->contains('admin'))
                <div class="mb-8">
                    <h2 class="h2 mb-4">{{ backend_trans('admin_dashboard') }}</h2>
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div class="bg-white overflow-hidden shadow-sm rounded-lg p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-indigo-100 text-indigo-600">
                                    <svg class="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500">{{ backend_trans('total_users') }}</p>
                                    <p class="text-2xl font-semibold text-gray-800">{{ $stats['admin']['total_users'] }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white overflow-hidden shadow-sm rounded-lg p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-green-100 text-green-600">
                                    <svg class="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500">{{ backend_trans('total_bookings') }}</p>
                                    <p class="text-2xl font-semibold text-gray-800">{{ $stats['admin']['total_bookings'] }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white overflow-hidden shadow-sm rounded-lg p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                                    <svg class="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500">{{ __('Pending Approvals') }}</p>
                                    <p class="text-2xl font-semibold text-gray-800">{{ $stats['admin']['pending_approvals'] }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Booking Calendar -->
                    <div class="mt-6 bg-white overflow-hidden shadow-sm rounded-lg">
                        <div class="p-6">
                            <h3 class="h3 mb-4">{{ backend_trans('booking_calendar') }}</h3>
                            <div class="flex mb-4 space-x-2 flex-wrap">
                                <div class="flex items-center mr-4 mb-2">
                                    <span class="inline-block w-4 h-4 mr-2 bg-blue-500 rounded-full"></span>
                                    <span class="text-sm text-gray-600">Vehicle Booking</span>
                                </div>
                                <div class="flex items-center mr-4 mb-2">
                                    <span class="inline-block w-4 h-4 mr-2 bg-green-500 rounded-full"></span>
                                    <span class="text-sm text-gray-600">Vehicle with Driver</span>
                                </div>
                                <div class="flex items-center mr-4 mb-2">
                                    <span class="inline-block w-4 h-4 mr-2 border-2 border-yellow-500 rounded-full"></span>
                                    <span class="text-sm text-gray-600">Pending</span>
                                </div>
                                <div class="flex items-center mr-4 mb-2">
                                    <span class="inline-block w-4 h-4 mr-2 border-2 border-green-500 rounded-full"></span>
                                    <span class="text-sm text-gray-600">Confirmed</span>
                                </div>
                                <div class="flex items-center mr-4 mb-2">
                                    <span class="inline-block w-4 h-4 mr-2 border-2 border-purple-500 rounded-full"></span>
                                    <span class="text-sm text-gray-600">In Progress</span>
                                </div>
                                <div class="flex items-center mr-4 mb-2">
                                    <span class="inline-block w-4 h-4 mr-2 bg-red-500 rounded-full"></span>
                                    <span class="text-sm text-gray-600">Cancelled/Rejected</span>
                                </div>
                            </div>
                            <div id="booking-calendar" class="h-96"></div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Agent Dashboard -->
            @if($userRoles->contains('agent'))
                <div class="mb-8">
                    <h2 class="h2 mb-4">{{ backend_trans('agent_dashboard') }}</h2>
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div class="bg-white overflow-hidden shadow-sm rounded-lg p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                                    <svg class="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500">{{ backend_trans('managed_vehicles') }}</p>
                                    <p class="text-2xl font-semibold text-gray-800">{{ $stats['agent']['managed_vehicles'] }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white overflow-hidden shadow-sm rounded-lg p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-green-100 text-green-600">
                                    <svg class="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500">{{ __('Active Vehicles') }}</p>
                                    <p class="text-2xl font-semibold text-gray-800">{{ $stats['agent']['active_vehicles'] }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white overflow-hidden shadow-sm rounded-lg p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                                    <svg class="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500">{{ __('Pending Vehicles') }}</p>
                                    <p class="text-2xl font-semibold text-gray-800">{{ $stats['agent']['pending_vehicles'] }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="mt-6 bg-white overflow-hidden shadow-sm rounded-lg">
                        <div class="p-6">
                            <h3 class="h3 mb-4">{{ __('Quick Actions') }}</h3>
                            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                                <a href="{{ route('vehicles.create') }}" class="flex items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                                    <div class="p-2 rounded-full bg-blue-100 text-blue-600">
                                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-700">{{ __('Add New Vehicle') }}</p>
                                    </div>
                                </a>

                                <a href="{{ route('vehicles.index') }}" class="flex items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
                                    <div class="p-2 rounded-full bg-green-100 text-green-600">
                                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-700">{{ __('Manage Vehicles') }}</p>
                                    </div>
                                </a>

                                <a href="{{ route('revenue.dashboard') }}" class="flex items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
                                    <div class="p-2 rounded-full bg-purple-100 text-purple-600">
                                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-700">{{ __('View Revenue') }}</p>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Booking Calendar -->
                    <div class="mt-6 bg-white overflow-hidden shadow-sm rounded-lg">
                        <div class="p-6">
                            <h3 class="h3 mb-4">{{ backend_trans('booking_calendar') }}</h3>
                            <div class="flex mb-4 space-x-2 flex-wrap">
                                <div class="flex items-center mr-4 mb-2">
                                    <span class="inline-block w-4 h-4 mr-2 bg-blue-500 rounded-full"></span>
                                    <span class="text-sm text-gray-600">Vehicle Booking</span>
                                </div>
                                <div class="flex items-center mr-4 mb-2">
                                    <span class="inline-block w-4 h-4 mr-2 bg-green-500 rounded-full"></span>
                                    <span class="text-sm text-gray-600">Vehicle with Driver</span>
                                </div>
                                <div class="flex items-center mr-4 mb-2">
                                    <span class="inline-block w-4 h-4 mr-2 border-2 border-yellow-500 rounded-full"></span>
                                    <span class="text-sm text-gray-600">Pending</span>
                                </div>
                                <div class="flex items-center mr-4 mb-2">
                                    <span class="inline-block w-4 h-4 mr-2 border-2 border-green-500 rounded-full"></span>
                                    <span class="text-sm text-gray-600">Confirmed</span>
                                </div>
                                <div class="flex items-center mr-4 mb-2">
                                    <span class="inline-block w-4 h-4 mr-2 border-2 border-purple-500 rounded-full"></span>
                                    <span class="text-sm text-gray-600">In Progress</span>
                                </div>
                                <div class="flex items-center mr-4 mb-2">
                                    <span class="inline-block w-4 h-4 mr-2 bg-red-500 rounded-full"></span>
                                    <span class="text-sm text-gray-600">Cancelled/Rejected</span>
                                </div>
                            </div>
                            <div id="agent-booking-calendar" class="h-96"></div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Regular User Dashboard -->
            @if($userRoles->contains('user'))
                <div class="mb-8">
                    <h2 class="h2 mb-4">{{ backend_trans('my_dashboard') }}</h2>
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div class="bg-white overflow-hidden shadow-sm rounded-lg p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                                    <svg class="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500">{{ backend_trans('my_vehicles') }}</p>
                                    <p class="text-2xl font-semibold text-gray-800">{{ $stats['user']['my_vehicles'] }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white overflow-hidden shadow-sm rounded-lg p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-green-100 text-green-600">
                                    <svg class="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500">{{ backend_trans('my_bookings') }}</p>
                                    <p class="text-2xl font-semibold text-gray-800">{{ $stats['user']['my_bookings'] }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="mt-6 bg-white overflow-hidden shadow-sm rounded-lg">
                        <div class="p-6">
                            <h3 class="h3 mb-4">{{ __('Quick Actions') }}</h3>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <a href="{{ route('vehicles.create') }}" class="flex items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                                    <div class="p-2 rounded-full bg-blue-100 text-blue-600">
                                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-700">{{ __('List Your Vehicle') }}</p>
                                    </div>
                                </a>

                                <a href="{{ route('cars.listing') }}" class="flex items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
                                    <div class="p-2 rounded-full bg-green-100 text-green-600">
                                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-700">{{ __('Find a Vehicle') }}</p>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Booking Calendar -->
                    <div class="mt-6 bg-white overflow-hidden shadow-sm rounded-lg">
                        <div class="p-6">
                            <h3 class="h3 mb-4">{{ backend_trans('booking_calendar') }}</h3>
                            <div class="flex mb-4 space-x-2 flex-wrap">
                                <div class="flex items-center mr-4 mb-2">
                                    <span class="inline-block w-4 h-4 mr-2 bg-blue-500 rounded-full"></span>
                                    <span class="text-sm text-gray-600">Vehicle Booking</span>
                                </div>
                                <div class="flex items-center mr-4 mb-2">
                                    <span class="inline-block w-4 h-4 mr-2 bg-green-500 rounded-full"></span>
                                    <span class="text-sm text-gray-600">Vehicle with Driver</span>
                                </div>
                                <div class="flex items-center mr-4 mb-2">
                                    <span class="inline-block w-4 h-4 mr-2 border-2 border-yellow-500 rounded-full"></span>
                                    <span class="text-sm text-gray-600">Pending</span>
                                </div>
                                <div class="flex items-center mr-4 mb-2">
                                    <span class="inline-block w-4 h-4 mr-2 border-2 border-green-500 rounded-full"></span>
                                    <span class="text-sm text-gray-600">Confirmed</span>
                                </div>
                                <div class="flex items-center mr-4 mb-2">
                                    <span class="inline-block w-4 h-4 mr-2 border-2 border-purple-500 rounded-full"></span>
                                    <span class="text-sm text-gray-600">In Progress</span>
                                </div>
                                <div class="flex items-center mr-4 mb-2">
                                    <span class="inline-block w-4 h-4 mr-2 bg-red-500 rounded-full"></span>
                                    <span class="text-sm text-gray-600">Cancelled/Rejected</span>
                                </div>
                            </div>
                            <div id="user-booking-calendar" class="h-96"></div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- FullCalendar JS -->
    <script src='https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.js'></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Calendar configuration function
            function initCalendar(elementId) {
                const calendarEl = document.getElementById(elementId);
                if (!calendarEl) return null;

                const calendar = new FullCalendar.Calendar(calendarEl, {
                    initialView: 'dayGridMonth',
                    headerToolbar: {
                        left: 'prev,next today',
                        center: 'title',
                        right: 'dayGridMonth,timeGridWeek,listWeek'
                    },
                    events: @json($bookings),
                    eventTimeFormat: {
                        hour: '2-digit',
                        minute: '2-digit',
                        meridiem: 'short'
                    },
                    eventClick: function(info) {
                        // Prevent the default action (opening the URL)
                        if (info.event.url) {
                            window.open(info.event.url, '_blank');
                            info.jsEvent.preventDefault();
                        }
                    },
                    eventDidMount: function(info) {
                        // Add tooltip with booking details
                        const props = info.event.extendedProps;

                        // Create a detailed tooltip
                        let tooltipText = `Booking #${props.booking_number || 'N/A'}\n`;
                        tooltipText += `Vehicle: ${props.vehicle}\n`;
                        tooltipText += `Status: ${props.status}\n`;

                        if (props.has_driver) {
                            tooltipText += `Driver: ${props.driver}\n`;
                        }

                        tooltipText += `Renter: ${props.renter}\n`;
                        tooltipText += `Owner: ${props.owner}\n`;
                        tooltipText += `Amount: $${props.amount}\n`;
                        tooltipText += `Pickup: ${props.pickup || 'N/A'}\n`;
                        tooltipText += `Return: ${props.return || 'N/A'}`;

                        // Use browser's native tooltip
                        info.el.title = tooltipText;
                    }
                });

                calendar.render();
                return calendar;
            }

            // Initialize all calendars
            const adminCalendar = initCalendar('booking-calendar');
            const agentCalendar = initCalendar('agent-booking-calendar');
            const userCalendar = initCalendar('user-booking-calendar');
        });
    </script>
</x-app-backend-layout>

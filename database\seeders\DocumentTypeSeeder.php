<?php

namespace Database\Seeders;

use App\Models\DocumentType;
use Illuminate\Database\Seeder;

class DocumentTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $documentTypes = [
            [
                'name' => 'Driving License',
                'description' => 'A valid driving license issued by a government authority',
                'requires_verification' => true,
                'has_expiry' => true,
                'is_required' => true,
                'applies_to' => 'driver',
            ],
            [
                'name' => 'Vehicle Registration',
                'description' => 'Document proving ownership of the vehicle (registration certificate, title, etc.)',
                'requires_verification' => true,
                'has_expiry' => true,
                'is_required' => true,
                'applies_to' => 'vehicle',
            ],
            [
                'name' => 'Vehicle Insurance',
                'description' => 'Valid insurance policy document for the vehicle with coverage details',
                'requires_verification' => true,
                'has_expiry' => true,
                'is_required' => true,
                'applies_to' => 'vehicle',
            ],
            [
                'name' => 'Vehicle Inspection Certificate',
                'description' => 'Recent vehicle inspection certificate or report showing the vehicle is in good condition',
                'requires_verification' => true,
                'has_expiry' => true,
                'is_required' => true,
                'applies_to' => 'vehicle',
            ],
            [
                'name' => 'Proof of Address',
                'description' => 'Document proving your current residential address',
                'requires_verification' => true,
                'has_expiry' => false,
                'is_required' => true,
                'applies_to' => 'user',
            ],
        ];

        foreach ($documentTypes as $documentType) {
            DocumentType::updateOrCreate(
                ['name' => $documentType['name']],
                $documentType
            );
        }
    }
}

<div class="mb-5 content-card shadow-md hover:shadow-lg transition-shadow duration-300">
    <h2 class="text-lg font-semibold text-gray-800 mb-4">Search Vehicles</h2>

    <!-- Search Input -->
    <div class="mb-5">
        <div class="relative rounded-lg shadow-sm">
            <input
                id="vehicle-search"
                type="text"
                x-model="search"
                @keyup.enter="searchItems"
                placeholder="Search vehicles by make, model, or license plate..."
                class="form-input pl-10 border-gray-300 focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-20 rounded-lg transition-all duration-200 w-full"
                value=""
            >
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="fas fa-magnifying-glass text-gray-400"></i>
            </div>
            <button
                @click="searchItems"
                class="absolute inset-y-0 right-0 px-4 text-white bg-primary hover:bg-primary-hover rounded-r-lg focus:outline-none transition-colors duration-200"
            >
                Search
            </button>
        </div>
    </div>

    <h3 class="text-base font-medium text-gray-700 mb-3 border-b pb-2">Filters</h3>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- Vehicle Type Filter -->
        <div>
            <label for="vehicle-type-filter" class="block text-sm font-medium text-gray-600 mb-1">Vehicle Type</label>
            <select
                id="vehicle-type-filter"
                x-model="filters.vehicle_type_id"
                @change="applyFilters"
                class="form-input text-sm rounded-md border-gray-300 focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-20 w-full"
            >
                <option value="">All Vehicle Types</option>
                <option value="9">Convertible</option>
                <option value="8">Hatchback</option>
                <option value="10">Luxury</option>
                <option value="6">Sedan</option>
                <option value="7">SUV</option>
            </select>
        </div>

        <!-- City Filter -->
        <div>
            <label for="city-filter" class="block text-sm font-medium text-gray-600 mb-1">City</label>
            <select
                id="city-filter"
                x-model="filters.city_id"
                @change="applyFilters"
                class="form-input text-sm rounded-md border-gray-300 focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-20 w-full"
            >
                <option value="">All Cities</option>
                <option value="4">Los Angeles</option>
                <option value="3">New York City</option>
            </select>
        </div>

        <!-- Status Filter -->
        <div>
            <label for="status-filter" class="block text-sm font-medium text-gray-600 mb-1">Status</label>
            <select
                id="status-filter"
                x-model="filters.status"
                @change="applyFilters"
                class="form-input text-sm rounded-md border-gray-300 focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-20 w-full"
            >
                <option value="">All Statuses</option>
                <option value="draft">Draft</option>
                <option value="pending">Pending</option>
                <option value="active">Active</option>
                <option value="rejected">Rejected</option>
                <option value="inactive">Inactive</option>
            </select>
        </div>
    </div>

    <!-- Reset Button -->
    <div class="mt-4 flex justify-end">
        <button
            @click="resetFilters"
            class="flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all duration-200"
            title="Clear filters"
        >
            <i class="fas fa-arrow-rotate-left mr-2"></i>
            Reset Filters
        </button>
    </div>
</div>

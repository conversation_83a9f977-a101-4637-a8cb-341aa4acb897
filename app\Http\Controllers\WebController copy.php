<?php
// app\Http\Controllers\WebController.php

namespace App\Http\Controllers;

use App\Models\Page;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\View;

class WebController extends Controller
{
    /**
     * Get the view path for the current theme
     *
     * @param string $view
     * @return string
     */
    protected function getThemeView($view)
    {
        $theme = get_theme();
        $themeView = "front.{$theme}.{$view}";

        return $themeView;

        // Check if theme-specific view exists, otherwise fall back to default
//        return View::exists($themeView) ? $themeView : "front.{$view}";
    }

    public function index()
    {
        // // You might want to fetch a featured slider for the homepage
        // $featuredSlider = Slider::where('is_active', true)
        //                     ->with(['slides' => function($query) {
        //                         $query->orderBy('order');
        //                     }])
        //                     ->first();

        // Fetch featured products
        // $featuredProducts = Product::where('is_featured', true)
        //     ->where('only_offline', false) // Ensure only online products are shown
        //     ->with(['brand']) // Eager load related brand if needed
        //     ->take(3) // Limit to 3 products
        //     ->get();

        return view($this->getThemeView('index'));
    }

    public function products(Request $request)
    {
        $query = Product::query()->where('only_offline', false);

        // Search functionality
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('short_description', 'like', "%{$search}%");
            });
        }

        // Category filter - handle both array and string inputs
        if ($request->has('categories') && !empty($request->categories)) {
            $categories = $request->categories;

            // Ensure we have an array of category IDs
            if (!is_array($categories)) {
                $categories = [$categories];
            }

            // We're using a many-to-many relationship, so use whereHas to filter
            // This will find products that have ALL the selected categories
            foreach ($categories as $catId) {
                $query->whereHas('categories', function($q) use ($catId) {
                    $q->where('categories.id', $catId);
                });
            }
        }

        // Price filter
        if ($request->has('min_price') && $request->has('max_price')) {
            $minPrice = $request->min_price;
            $maxPrice = $request->max_price;

            if (is_numeric($minPrice) && is_numeric($maxPrice)) {
                $query->whereBetween('price', [$minPrice, $maxPrice]);
            }
        }

        // Sort functionality
        if ($request->has('sortBy') && !empty($request->sortBy)) {
            $sortParams = explode(';', $request->sortBy);
            if (count($sortParams) == 2) {
                $column = $sortParams[0];
                $direction = $sortParams[1];

                // Only allow specific columns to be sorted
                $allowedColumns = ['name', 'price', 'created_at'];
                if (in_array($column, $allowedColumns)) {
                    $query->orderBy($column, $direction);
                }
            }
        } else {
            // Default sorting
            $query->orderBy('created_at', 'desc');
        }

        // Get categories for filter
        $categories = Category::all();

        // Get products with pagination
        $products = $query->paginate(9);

        // Log query for debugging
        \Log::info('Products method query:', [
            'filter_categories' => $request->categories,
            'product_count' => $products->count(),
            'total_products' => $products->total()
        ]);

        return view($this->getThemeView('products'), compact('products', 'categories'));
    }

    public function productDetails(Request $request)
    {
        $product = null;

        if ($request->has('id')) {
            $product = Product::find($request->id);
        }

        if (!$product) {
            // Default product if none specified
            $product = Product::first();
        }

        // Check if we have a product at this point
        if ($product) {
            // First check if these column names exist in your database table
            $hasCategory = Schema::hasColumn('products', 'category_id');
            $hasBrand = Schema::hasColumn('products', 'brand_id');

            // Build query for related products
            $query = Product::where('id', '!=', $product->id);

            // Only add conditions for columns that exist
            if ($hasCategory && $hasBrand) {
                $query->where(function($query) use ($product) {
                    $query->when($product->category_id, function ($q) use ($product) {
                        return $q->where('category_id', $product->category_id);
                    })
                        ->when($product->brand_id, function ($q) use ($product) {
                            return $q->orWhere('brand_id', $product->brand_id);
                        });
                });
            } elseif ($hasCategory) {
                $query->when($product->category_id, function ($q) use ($product) {
                    return $q->where('category_id', $product->category_id);
                });
            } elseif ($hasBrand) {
                $query->when($product->brand_id, function ($q) use ($product) {
                    return $q->where('brand_id', $product->brand_id);
                });
            }

            $relatedProducts = $query->limit(4)->get();
        } else {
            $relatedProducts = collect(); // Empty collection if no product found
        }

        return view($this->getThemeView('product-details'), compact('product', 'relatedProducts'));
    }

    public function ajaxSearch(Request $request)
    {
        $search = $request->search;
        $query = Product::query()->where('only_offline', false);

        if (!empty($search)) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%")
                    ->orWhere('short_description', 'like', "%{$search}%");
            });
        }

        // Apply category filter if provided
        if ($request->has('categories') && !empty($request->categories)) {
            $categories = is_array($request->categories) ? $request->categories : [$request->categories];
            $query->whereIn('category_id', $categories);
        }

        // Apply sorting if provided
        if ($request->has('sortBy') && !empty($request->sortBy)) {
            $sortParams = explode(';', $request->sortBy);
            if (count($sortParams) == 2) {
                $column = $sortParams[0];
                $direction = $sortParams[1];
                $allowedColumns = ['name', 'price', 'created_at'];
                if (in_array($column, $allowedColumns)) {
                    $query->orderBy($column, $direction);
                }
            }
        } else {
            $query->orderBy('created_at', 'desc');
        }

        $products = $query->paginate(9);

        // Set the correct query parameters for pagination links
        $products->appends($request->except('page'));

        // Log AJAX request results
        \Log::info('AJAX search products count: ' . $products->count());
        \Log::info('AJAX search total products: ' . $products->total());

        $theme = get_theme();

        // Generate HTML for the grid of products
        $html = '';

        if ($products->count() > 0) {
            foreach ($products as $product) {
                $html .= view("front.{$theme}.partials.product-card", ['product' => $product])->render();
            }
        } else {
            $html = '<div class="col-span-full p-4 text-center"><p class="text-lg text-gray-500">No products found. Try adjusting your filters.</p></div>';
        }

        // Add the metadata span for JS to read
        if ($products->count() > 0) {
            $html .= '<span class="hidden" id="products-metadata"
                      data-current-page="' . $products->currentPage() . '"
                      data-last-page="' . $products->lastPage() . '"
                      data-from="' . ($products->firstItem() ?? 0) . '"
                      data-to="' . ($products->lastItem() ?? 0) . '"
                      data-total="' . $products->total() . '">
                      </span>';
        }

        return response()->json([
            'html' => $html,
            'pagination' => view("front.{$theme}.partials.pagination", compact('products'))->render(),
            'meta' => [
                'current_page' => $products->currentPage(),
                'last_page' => $products->lastPage(),
                'from' => $products->firstItem() ?? 0,
                'to' => $products->lastItem() ?? 0,
                'total' => $products->total()
            ]
        ]);
    }

    /**
     * Display the page on the frontend by slug.
     *
     * @param  string  $slug
     * @return \Illuminate\Http\Response
     */
    public function viewPage($slug)
    {
        $page = Page::where('slug', $slug)
                    ->where('status', 1)
                    ->firstOrFail();

        return view($this->getThemeView('page'), compact('page'));
    }

    /**
     * Display the posts
     */
    public function viewBlog()
    {
        $posts = Post::paginate(10);
        return view($this->getThemeView('blog'), compact('posts'));
    }

    /**
     * Display the specified post by slug.
     */
    public function viewPost($slug)
    {
        $post = Post::where('slug', $slug)->with('postCategory')->firstOrFail();
        return view($this->getThemeView('blog-post'), compact('post'));
    }

    /**
     * Display a specific slider by ID
     *
     * @param int $id
     * @return \Illuminate\View\View
     */
    public function showSlider($id)
    {
        $slider = Slider::where('is_active', true)
                     ->with(['slides' => function($query) {
                         $query->where('is_active', true)
                                ->orderBy('order');
                     }])
                     ->findOrFail($id);

        return view($this->getThemeView('slider'), compact('slider'));
    }

    public function getSliderData($id)
    {
        try {
            // Fetch the slider with its active slides
            $slider = Slider::with(['slides' => function($query) {
                $query->where('is_active', true)->orderBy('order');
            }])->findOrFail($id);

            // Transform slides to include full image URL
            $slides = $slider->slides->map(function($slide) {
                return [
                    'id' => $slide->id,
                    'title' => $slide->title,
                    'description' => $slide->description,
                    'image_path' => $slide->image_path
                        ? \Illuminate\Support\Facades\Storage::url($slide->image_path)
                        : null,
                    'link' => $slide->link
                ];
            });

            return response()->json([
                'id' => $slider->id,
                'name' => $slider->name,
                'slides' => $slides
            ]);
        } catch (\Exception $e) {
            \Log::error('Error fetching slider data', [
                'slider_id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'error' => 'Unable to fetch slider data',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function viewCategory($slug)
    {
        // Find the category by slug
        $category = Category::where('slug', $slug)->firstOrFail();

        // Start with a general product query
        $query = Product::query()->where('only_offline', false);

        // Get the selected categories from the request
        $selectedCategories = request()->input('categories', []);

        // Convert string of comma-separated categories to an array
        if (is_string($selectedCategories)) {
            $selectedCategories = explode(',', $selectedCategories);
        }

        // Convert array elements to integers and remove any non-numeric values
        $selectedCategories = array_filter(array_map('intval', $selectedCategories));

        // If no categories are selected, default to the current category
        if (empty($selectedCategories)) {
            $selectedCategories = [$category->id];
        }

        // Use whereHas with a more flexible approach to find products in ANY of the selected categories
        $query->whereHas('categories', function($q) use ($selectedCategories) {
            $q->whereIn('categories.id', $selectedCategories);
        });

        // Apply price filter if provided
        if (request()->has('min_price') && request()->has('max_price')) {
            $minPrice = request()->input('min_price');
            $maxPrice = request()->input('max_price');

            if (is_numeric($minPrice) && is_numeric($maxPrice)) {
                $query->where(function($q) use ($minPrice, $maxPrice) {
                    // For regular price
                    $q->whereBetween('price', [(float)$minPrice, (float)$maxPrice]);

                    // For sale price if it exists in your products table
                    if (Schema::hasColumn('products', 'sale_price')) {
                        $q->orWhere(function($subQ) use ($minPrice, $maxPrice) {
                            $subQ->whereNotNull('sale_price')
                                 ->whereBetween('sale_price', [(float)$minPrice, (float)$maxPrice]);
                        });
                    }
                });
            }
        }

        // Apply sort if provided
        if (request()->has('sortBy') && !empty(request()->input('sortBy'))) {
            $sortParams = explode(';', request()->input('sortBy'));
            if (count($sortParams) == 2) {
                $column = $sortParams[0];
                $direction = $sortParams[1];

                // Only allow specific columns to be sorted
                $allowedColumns = ['name', 'price', 'created_at'];
                if (in_array($column, $allowedColumns)) {
                    $query->orderBy($column, $direction);
                }
            }
        } else {
            // Default sorting
            $query->orderBy('created_at', 'desc');
        }

        // Get products with pagination
        $products = $query->paginate(9);

        // Make sure pagination links include all current query parameters
        $products->appends(request()->except('page'));

        // Get all categories for the filter sidebar
        $categories = Category::all();

        // Log for debugging
        \Log::info('Category Filtering Debug', [
            'current_category_id' => $category->id,
            'selected_categories' => $selectedCategories,
            'price_filter' => [
                'min_price' => request()->input('min_price'),
                'max_price' => request()->input('max_price')
            ],
            'sort_by' => request()->input('sortBy'),
            'total_products' => $products->total(),
        ]);

        return view($this->getThemeView('category'), compact('category', 'products', 'categories'));
    }

}

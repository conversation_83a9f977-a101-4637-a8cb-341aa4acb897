<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'menuItem' => null,
    'menuItems' => [],
    'routeNames' => [],
    'action',
    'method' => 'POST'
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'menuItem' => null,
    'menuItems' => [],
    'routeNames' => [],
    'action',
    'method' => 'POST'
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<form action="<?php echo e($action); ?>" method="POST" class="space-y-6">
    <?php echo csrf_field(); ?>
    <?php if($method === 'PUT'): ?>
        <?php echo method_field('PUT'); ?>
    <?php endif; ?>

    <!-- Name Field -->
    <div>
        <label for="name" class="block text-sm font-medium text-gray-700">Name</label>
        <input type="text" 
               name="name" 
               id="name" 
               value="<?php echo e(old('name', $menuItem->name ?? '')); ?>"
               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
               required>
        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
    </div>

    <!-- URL Field -->
    <div>
        <label for="url" class="block text-sm font-medium text-gray-700">URL</label>
        <input type="text" 
               name="url" 
               id="url" 
               value="<?php echo e(old('url', $menuItem->url ?? '')); ?>"
               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
               placeholder="e.g., /about-us">
        <?php $__errorArgs = ['url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
    </div>

    <!-- Route Name Field -->
    <div>
        <label for="route_name" class="block text-sm font-medium text-gray-700">Route Name</label>
        <select name="route_name" 
                id="route_name" 
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
            <option value="">Select a route (optional)</option>
            <?php $__currentLoopData = $routeNames; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $routeName): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option value="<?php echo e($routeName); ?>" 
                        <?php echo e(old('route_name', $menuItem->route_name ?? '') === $routeName ? 'selected' : ''); ?>>
                    <?php echo e($routeName); ?>

                </option>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </select>
        <?php $__errorArgs = ['route_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
    </div>

    <!-- Parent Menu Item -->
    <div>
        <label for="parent_id" class="block text-sm font-medium text-gray-700">Parent Menu Item</label>
        <select name="parent_id" 
                id="parent_id" 
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
            <option value="">No Parent (Top Level)</option>
            <?php $__currentLoopData = $menuItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option value="<?php echo e($item->id); ?>" 
                        <?php echo e(old('parent_id', $menuItem->parent_id ?? '') == $item->id ? 'selected' : ''); ?>>
                    <?php echo e($item->name); ?>

                </option>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </select>
        <?php $__errorArgs = ['parent_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
    </div>

    <!-- Active Status -->
    <div class="flex items-center">
        <input type="checkbox" 
               name="is_active" 
               id="is_active" 
               value="1"
               <?php echo e(old('is_active', $menuItem->is_active ?? true) ? 'checked' : ''); ?>

               class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
        <label for="is_active" class="ml-2 block text-sm text-gray-900">
            Active
        </label>
    </div>

    <!-- Submit Buttons -->
    <div class="flex items-center justify-end space-x-3">
        <a href="<?php echo e(route('menu-items.index')); ?>" 
           class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
            Cancel
        </a>
        <button type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            <?php echo e($menuItem ? 'Update' : 'Create'); ?> Menu Item
        </button>
    </div>
</form>
<?php /**PATH C:\laragon\www\carbnb\resources\views\components\menu-item-form.blade.php ENDPATH**/ ?>
<!DOCTYPE html>
<html :class="{ 'theme-dark': dark }" x-data="data()" lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo e(config('app.name', 'Laravel')); ?></title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- jQuery and Select2 Loaders -->
    <script src="<?php echo e(asset('js/jquery-loader.js')); ?>"></script>
    <script src="<?php echo e(asset('js/select2-loader.js')); ?>"></script>

    <!-- Scripts -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('css/quill-override.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('css/consistent-ui.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('css/custom-fixes.css')); ?>">

    <!-- Clean Sidebar CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('css/sidebar-clean.css')); ?>">

    <!-- Responsive Fixes CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('css/responsive-fixes.css')); ?>">

    <script src="<?php echo e(asset('js/gp_lib.js')); ?>"></script>
    <script src="<?php echo e(asset('js/gallery-helpers.js')); ?>" defer></script>
    <script src="<?php echo e(asset('js/pagination-utils.js')); ?>"></script>

    <!-- CSRF Token for AJAX Requests -->
    <script>
        window.addEventListener('DOMContentLoaded', function() {
            // Set up CSRF token for all AJAX requests
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

            // Add CSRF token to all fetch requests
            const originalFetch = window.fetch;
            window.fetch = function(url, options = {}) {
                options = options || {};
                options.headers = options.headers || {};

                // Only add for same-origin requests
                const isSameOrigin = url.startsWith('/') || url.startsWith(window.location.origin);
                if (isSameOrigin) {
                    options.headers['X-CSRF-TOKEN'] = csrfToken;
                }

                return originalFetch(url, options);
            };

            console.log('CSRF token initialized for AJAX requests');
        });
    </script>
</head>
<body>
<div class="flex h-screen bg-gray-100" x-data="{ sidebarOpen: false }">

    <!-- ===== Preloader Start ===== -->
    <div x-show="loaded" x-init="window.addEventListener('DOMContentLoaded', () => {setTimeout(() => loaded = false, 500)})" class="fixed left-0 top-0 z-[9999] flex h-screen w-screen items-center justify-center bg-white dark:bg-black" style="display: none;">
        <div class="h-16 w-16 animate-spin rounded-full border-4 border-solid border-blue-500 border-t-transparent"></div>
    </div>
    <!-- ===== Preloader End ===== -->

    <!-- ===== Mobile Sidebar Overlay ===== -->
    <div x-show="sidebarOpen" @click="sidebarOpen = false" class="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"></div>

    <!-- ===== Sidebar Start ===== -->
    <aside class="bg-gradient-to-b from-gray-800 to-gray-900 text-white w-64 min-h-screen shadow-xl transition-all duration-300 ease-in-out fixed left-0 top-0 h-full z-50 transform lg:translate-x-0"
           :class="{'translate-x-0': sidebarOpen, '-translate-x-full': !sidebarOpen}">
        <?php echo $__env->make('backend.layouts.partials.sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    </aside>
    <!-- ===== Sidebar End ===== -->

    <!-- Main content -->
    <div class="flex-1 flex flex-col overflow-hidden lg:ml-64">
        <!-- ===== Header Start ===== -->
        <div class="w-full sticky top-0 z-10">
            <?php echo $__env->make('backend.layouts.partials.header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        </div>
        <!-- ===== Header End ===== -->

        <!-- ===== Page Content Start ===== -->
        <div class="flex-1 overflow-y-auto p-4 w-full">
            <?php echo e($slot); ?>

        </div>
        <!-- ===== Page Content End ===== -->
    </div>

    <!-- Mobile menu toggle script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            if (mobileMenuButton) {
                mobileMenuButton.addEventListener('click', function() {
                    const sidebar = document.querySelector('aside');
                    if (sidebar.classList.contains('-translate-x-full')) {
                        sidebar.classList.remove('-translate-x-full');
                        sidebar.classList.add('translate-x-0');
                        // Add overlay
                        const overlay = document.createElement('div');
                        overlay.id = 'sidebar-overlay';
                        overlay.className = 'fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden';
                        overlay.addEventListener('click', function() {
                            sidebar.classList.remove('translate-x-0');
                            sidebar.classList.add('-translate-x-full');
                            this.remove();
                        });
                        document.body.appendChild(overlay);
                    } else {
                        sidebar.classList.remove('translate-x-0');
                        sidebar.classList.add('-translate-x-full');
                        // Remove overlay
                        const overlay = document.getElementById('sidebar-overlay');
                        if (overlay) overlay.remove();
                    }
                });
            }

            // Handle window resize
            window.addEventListener('resize', function() {
                const sidebar = document.querySelector('aside');
                if (window.innerWidth >= 1024) { // lg breakpoint
                    sidebar.classList.remove('-translate-x-full');
                    sidebar.classList.add('translate-x-0');
                    // Remove overlay
                    const overlay = document.getElementById('sidebar-overlay');
                    if (overlay) overlay.remove();
                } else {
                    sidebar.classList.remove('translate-x-0');
                    sidebar.classList.add('-translate-x-full');
                }
            });
        });
    </script>
</div>
</body>
</html>

<?php /**PATH C:\laragon\www\carbnb\resources\views\backend\layouts\app.blade.php ENDPATH**/ ?>
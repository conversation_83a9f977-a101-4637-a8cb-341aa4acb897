<x-app-layout>
    <!-- Vehicle Details Section -->
    <section class="py-12">
        <div class="container mx-auto px-4">
            <div class="flex flex-col lg:flex-row gap-8">
                <!-- Vehicle Images and Details -->
                <div class="lg:w-2/3">
                    <!-- Breadcrumbs -->
                    <nav class="flex mb-6 text-sm">
                        <a href="{{ route('home') }}" class="text-gray-500 hover:text-primary" data-lang-key="home">{{ frontend_trans('home') }}</a>
                        <span class="mx-2 text-gray-500">/</span>
                        <a href="{{ route('cars.listing') }}" class="text-gray-500 hover:text-primary" data-lang-key="cars">{{ frontend_trans('rent_car') }}</a>
                        <span class="mx-2 text-gray-500">/</span>
                        <span class="text-primary">{{ $vehicle->year }} {{ $vehicle->make }} {{ $vehicle->model }}</span>
                    </nav>

                    <!-- Vehicle Images Gallery -->
                    <div class="mb-8">
                        <div class="relative h-[400px] mb-4 rounded-lg overflow-hidden">
                            @if($vehicle->images->count() > 0)
                                @php
                                    $primaryImage = $vehicle->images->where('is_primary', true)->first();
                                @endphp
                                <img id="main-image" src="{{ $primaryImage->url }}" alt="{{ $vehicle->make }} {{ $vehicle->model }}" class="w-full h-full object-cover">
                            @else
                                <img src="https://via.placeholder.com/800x400?text=No+Image" alt="{{ $vehicle->make }} {{ $vehicle->model }}" class="w-full h-full object-cover">
                            @endif

                            @if($vehicle->is_featured)
                                <div class="absolute top-4 right-4 bg-primary text-white text-sm font-semibold py-1 px-3 rounded-full" data-lang-key="featured">
                                    {{ frontend_trans('featured') }}
                                </div>
                            @endif
                        </div>

                        @if($vehicle->images->count() > 1)
                            <div class="grid grid-cols-5 gap-2">
                                @foreach($vehicle->images as $image)
                                    <div class="h-20 rounded-lg overflow-hidden cursor-pointer thumbnail-image {{ $image->is_primary ? 'ring-2 ring-primary' : '' }}" data-image="{{ $image->url }}">
                                        <img src="{{ $image->url }}" alt="{{ $vehicle->make }} {{ $vehicle->model }}" class="w-full h-full object-cover">
                                    </div>
                                @endforeach
                            </div>
                        @endif
                    </div>

                    <!-- Vehicle Details -->
                    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                        <h1 class="text-3xl font-bold mb-2">{{ $vehicle->year }} {{ $vehicle->make }} {{ $vehicle->model }}</h1>
                        <div class="flex items-center text-gray-600 mb-4">
                            <i class="fas fa-map-marker-alt text-gray-400 mr-2"></i>
                            <span>{{ $vehicle->city->name }}</span>
                            <span class="mx-2">•</span>
                            <i class="fas fa-user text-gray-400 mr-2"></i>
                            <span>{{ $vehicle->user->name }}</span>
                        </div>

                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                            <div class="flex flex-col items-center p-3 bg-gray-50 rounded-lg">
                                <i class="fas fa-car text-primary text-xl mb-2"></i>
                                <span class="text-sm text-gray-500" data-lang-key="type">Type</span>
                                <span class="font-semibold">{{ $vehicle->vehicleType->name }}</span>
                            </div>
                            <div class="flex flex-col items-center p-3 bg-gray-50 rounded-lg">
                                @if($vehicle->fuel_type == 'electric')
                                    <i class="fas fa-bolt text-green-500 text-xl mb-2"></i>
                                @elseif($vehicle->fuel_type == 'hybrid' || $vehicle->fuel_type == 'plugin_hybrid')
                                    <i class="fas fa-leaf text-green-500 text-xl mb-2"></i>
                                @else
                                    <i class="fas fa-gas-pump text-blue-500 text-xl mb-2"></i>
                                @endif
                                <span class="text-sm text-gray-500" data-lang-key="fuel">Fuel</span>
                                <span class="font-semibold" data-lang-key="{{ $vehicle->fuel_type }}">{{ ucfirst(str_replace('_', ' ', $vehicle->fuel_type)) }}</span>
                            </div>
                            <div class="flex flex-col items-center p-3 bg-gray-50 rounded-lg">
                                <i class="fas fa-cog text-gray-500 text-xl mb-2"></i>
                                <span class="text-sm text-gray-500" data-lang-key="transmission">Transmission</span>
                                <span class="font-semibold" data-lang-key="{{ $vehicle->transmission }}">{{ ucfirst($vehicle->transmission) }}</span>
                            </div>
                            <div class="flex flex-col items-center p-3 bg-gray-50 rounded-lg">
                                <i class="fas fa-tachometer-alt text-gray-500 text-xl mb-2"></i>
                                <span class="text-sm text-gray-500" data-lang-key="mileage">Mileage</span>
                                <span class="font-semibold">{{ number_format($vehicle->mileage) }} km</span>
                            </div>
                        </div>

                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                            <div class="flex flex-col items-center p-3 bg-gray-50 rounded-lg">
                                <i class="fas fa-user-friends text-gray-500 text-xl mb-2"></i>
                                <span class="text-sm text-gray-500" data-lang-key="seats">Seats</span>
                                <span class="font-semibold">{{ $vehicle->seats }}</span>
                            </div>
                            <div class="flex flex-col items-center p-3 bg-gray-50 rounded-lg">
                                <i class="fas fa-door-open text-gray-500 text-xl mb-2"></i>
                                <span class="text-sm text-gray-500" data-lang-key="doors">Doors</span>
                                <span class="font-semibold">{{ $vehicle->doors }}</span>
                            </div>
                            <div class="flex flex-col items-center p-3 bg-gray-50 rounded-lg">
                                <i class="fas fa-palette text-gray-500 text-xl mb-2"></i>
                                <span class="text-sm text-gray-500" data-lang-key="color">Color</span>
                                <span class="font-semibold">{{ $vehicle->color }}</span>
                            </div>
                            <div class="flex flex-col items-center p-3 bg-gray-50 rounded-lg">
                                @if($vehicle->with_driver)
                                    <i class="fas fa-user-tie text-primary text-xl mb-2"></i>
                                    <span class="text-sm text-gray-500" data-lang-key="driver">Driver</span>
                                    <span class="font-semibold text-primary" data-lang-key="included">Included</span>
                                @else
                                    <i class="fas fa-user-tie text-gray-400 text-xl mb-2"></i>
                                    <span class="text-sm text-gray-500" data-lang-key="driver">Driver</span>
                                    <span class="font-semibold" data-lang-key="not_included">Not Included</span>
                                @endif
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="mb-6">
                            <h2 class="text-xl font-bold mb-3" data-lang-key="description">Description</h2>
                            <p class="text-gray-600">
                                {{ $vehicle->description ?? 'No description provided.' }}
                            </p>
                        </div>

                        <!-- Features -->
                        @php
                            $features = is_string($vehicle->features) ? json_decode($vehicle->features, true) : $vehicle->features;
                        @endphp
                        @if($features && is_array($features) && count($features) > 0)
                            <div class="mb-6">
                                <h2 class="text-xl font-bold mb-3" data-lang-key="features">Features</h2>
                                <div class="grid grid-cols-2 md:grid-cols-3 gap-2">
                                    @foreach($features as $feature)
                                        <div class="flex items-center">
                                            <i class="fas fa-check text-green-500 mr-2"></i>
                                            <span data-lang-key="{{ $feature }}">{{ ucwords(str_replace('_', ' ', $feature)) }}</span>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif

                        <!-- Location -->
                        <div>
                            <h2 class="text-xl font-bold mb-3" data-lang-key="location">Location</h2>
                            <p class="text-gray-600 mb-3">{{ $vehicle->address }}</p>
                            <div class="h-64 bg-gray-200 rounded-lg">
                                <!-- Map placeholder - would be replaced with actual map implementation -->
                                <div class="w-full h-full flex items-center justify-center">
                                    <span class="text-gray-500" data-lang-key="map_placeholder">Map would be displayed here</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Booking and Similar Cars -->
                <div class="lg:w-1/3">
                    <!-- Booking Card -->
                    <div class="bg-white rounded-lg shadow-md p-6 mb-8 sticky top-24">
                        <h2 class="text-2xl font-bold mb-4" data-lang-key="booking_details">Booking Details</h2>

                        <div class="flex justify-between items-center mb-6">
                            <span class="text-gray-600" data-lang-key="daily_rate">Daily Rate</span>
                            <span class="text-2xl font-bold text-primary">€{{ number_format($vehicle->daily_rate, 2) }}</span>
                        </div>

                        <form action="{{ route('vehicle.details', $vehicle->id) }}" method="GET">
                            <input type="hidden" name="vehicle_id" value="{{ $vehicle->id }}">

                            <div class="mb-4">
                                <label class="block text-gray-700 mb-2" data-lang-key="pickup_date">Pickup Date</label>
                                <input type="date" name="pickup_date" required class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none" min="{{ date('Y-m-d') }}">
                            </div>

                            <div class="mb-4">
                                <label class="block text-gray-700 mb-2" data-lang-key="return_date">Return Date</label>
                                <input type="date" name="return_date" required class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none" min="{{ date('Y-m-d') }}">
                            </div>

                            <div class="mb-6">
                                <div class="bg-blue-50 p-3 rounded-md">
                                    <p class="text-sm text-blue-700">{{ frontend_trans('add_insurance') }}</p>
                                </div>
                            </div>

                            <button type="submit" class="w-full bg-primary text-white py-3 rounded-md hover:bg-red-700 transition" data-lang-key="book_now">{{ frontend_trans('book_now') }}</button>
                        </form>

                        <div class="mt-6 text-center">
                            <p class="text-sm text-gray-500" data-lang-key="free_cancellation">Free cancellation up to 24 hours before pickup</p>
                        </div>

                        <div class="mt-6 flex justify-center space-x-4">
                            <a href="#" class="text-primary hover:text-red-700">
                                <i class="far fa-heart text-xl"></i>
                            </a>
                            <a href="#" class="text-primary hover:text-red-700">
                                <i class="far fa-share-square text-xl"></i>
                            </a>
                            <a href="#" class="text-primary hover:text-red-700">
                                <i class="far fa-flag text-xl"></i>
                            </a>
                        </div>
                    </div>

                    <!-- Owner Info -->
                    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                        <h2 class="text-xl font-bold mb-4" data-lang-key="car_owner">Car Owner</h2>

                        <div class="flex items-center mb-4">
                            <div class="w-16 h-16 bg-gray-200 rounded-full mr-4 flex items-center justify-center">
                                <i class="fas fa-user text-gray-400 text-2xl"></i>
                            </div>
                            <div>
                                <h3 class="font-bold">{{ $vehicle->user->name }}</h3>
                                <p class="text-gray-600">{{ frontend_trans('member_since') }} {{ $vehicle->user->created_at->format('M Y') }}</p>
                            </div>
                        </div>

                        <a href="#" class="block w-full bg-white border border-primary text-primary py-2 text-center rounded-md hover:bg-red-50 transition" data-lang-key="contact_owner">{{ frontend_trans('contact_owner') }}</a>
                    </div>

                    <!-- Similar Cars -->
                    @if($similarVehicles->count() > 0)
                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h2 class="text-xl font-bold mb-4" data-lang-key="similar_cars">Similar Cars</h2>

                            <div class="space-y-4">
                                @foreach($similarVehicles as $similarVehicle)
                                    <div class="flex border-b pb-4 last:border-b-0 last:pb-0">
                                        <div class="w-24 h-20 rounded-md overflow-hidden flex-shrink-0">
                                            @if($similarVehicle->primaryImage)
                                                <img src="{{ $similarVehicle->primaryImage->url }}" alt="{{ $similarVehicle->make }} {{ $similarVehicle->model }}" class="w-full h-full object-cover">
                                            @else
                                                <img src="https://via.placeholder.com/100x80?text=No+Image" alt="{{ $similarVehicle->make }} {{ $similarVehicle->model }}" class="w-full h-full object-cover">
                                            @endif
                                        </div>
                                        <div class="ml-4 flex-grow">
                                            <h3 class="font-semibold">{{ $similarVehicle->year }} {{ $similarVehicle->make }} {{ $similarVehicle->model }}</h3>
                                            <p class="text-primary font-bold">€{{ number_format($similarVehicle->daily_rate, 2) }}/day</p>
                                            <a href="{{ route('vehicle.details', $similarVehicle->id) }}" class="text-sm text-primary hover:text-red-700" data-lang-key="view_details">View Details</a>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </section>

    <!-- JavaScript for image gallery and date validation -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Image gallery functionality
            const thumbnails = document.querySelectorAll('.thumbnail-image');
            const mainImage = document.getElementById('main-image');

            thumbnails.forEach(thumbnail => {
                thumbnail.addEventListener('click', function() {
                    // Update main image
                    mainImage.src = this.getAttribute('data-image');

                    // Update active thumbnail
                    thumbnails.forEach(thumb => thumb.classList.remove('ring-2', 'ring-primary'));
                    this.classList.add('ring-2', 'ring-primary');
                });
            });

            // Date validation for booking form
            const pickupDateInput = document.querySelector('input[name="pickup_date"]');
            const returnDateInput = document.querySelector('input[name="return_date"]');
            const bookingForm = document.querySelector('form[action="{{ route('vehicle.details', $vehicle->id) }}"]');

            // Set minimum dates
            const today = new Date();
            const tomorrow = new Date(today);
            tomorrow.setDate(tomorrow.getDate() + 1);

            pickupDateInput.min = today.toISOString().split('T')[0];
            returnDateInput.min = tomorrow.toISOString().split('T')[0];

            // Update return date min value when pickup date changes
            pickupDateInput.addEventListener('change', function() {
                const pickupDate = new Date(this.value);
                const nextDay = new Date(pickupDate);
                nextDay.setDate(nextDay.getDate() + 1);

                returnDateInput.min = nextDay.toISOString().split('T')[0];

                // If return date is before pickup date, update it
                if (returnDateInput.value && new Date(returnDateInput.value) <= pickupDate) {
                    returnDateInput.value = nextDay.toISOString().split('T')[0];
                }
            });

            // Form validation
            bookingForm.addEventListener('submit', function(e) {
                const pickupDate = new Date(pickupDateInput.value);
                const returnDate = new Date(returnDateInput.value);

                if (returnDate <= pickupDate) {
                    e.preventDefault();
                    alert('Return date must be after pickup date');
                    return false;
                }

                // Calculate number of days
                const diffTime = Math.abs(returnDate - pickupDate);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                // Check if booking is within allowed range (e.g., max 30 days)
                if (diffDays > 30) {
                    e.preventDefault();
                    alert('Booking cannot exceed 30 days');
                    return false;
                }
            });
        });
    </script>
</x-app-layout>
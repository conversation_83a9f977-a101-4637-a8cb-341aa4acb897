import './bootstrap';

// Import Alpine.js and its plugins
import Alpine from 'alpinejs';
import collapse from '@alpinejs/collapse';
import focus from '@alpinejs/focus';
import alpineInit from './alpine-init';

// jQuery is loaded from CDN via jquery-loader.js

// Import Quill
import Quill from 'quill';
window.Quill = Quill;

// Import SweetAlert2
import Swal from 'sweetalert2';
window.Swal = Swal;

// Select2 is loaded and initialized via select2-loader.js

// Import Tagify
import Tagify from '@yaireo/tagify';
window.Tagify = Tagify;

// Dynamically import FilePond when needed
window.loadFilePond = function() {
    return import('filepond').then(module => {
        window.FilePond = module;
        return module;
    });
};

// Initialize FilePond if there are file upload elements on the page
if (document.querySelector('input[type="file"][data-filepond]')) {
    window.loadFilePond();
}

// Import Toastify
import Toastify from 'toastify-js';
window.Toastify = Toastify;

// Dynamically import html2pdf only when needed
window.loadHtml2pdf = function() {
    return import('html2pdf.js').then(module => {
        window.html2pdf = module.default;
        return module.default;
    });
};

// Import Font Awesome
import '@fortawesome/fontawesome-free/js/all.min.js';

// Dynamically import Swiper when needed
window.loadSwiper = function() {
    return Promise.all([
        import('swiper'),
        import('swiper/modules'),
        import('swiper/css'),
        import('swiper/css/navigation'),
        import('swiper/css/pagination')
    ]).then(([swiperModule, modulesModule]) => {
        window.Swiper = swiperModule.default;
        window.SwiperModules = {
            Navigation: modulesModule.Navigation,
            Pagination: modulesModule.Pagination,
            Autoplay: modulesModule.Autoplay
        };
        return swiperModule.default;
    });
};

// Initialize Swiper if there are slider elements on the page
if (document.querySelector('.swiper') || document.querySelector('[data-swiper]')) {
    window.loadSwiper();
}

// Dynamically import LightGallery when needed
window.loadLightGallery = function() {
    return Promise.all([
        import('lightgallery'),
        import('lightgallery/css/lightgallery.css')
    ]).then(([module]) => {
        window.lightGallery = module.default;
        return module.default;
    });
};

// Initialize LightGallery if there are gallery elements on the page
if (document.querySelector('[data-gallery]')) {
    window.loadLightGallery();
}

// Register Alpine plugins
Alpine.plugin(collapse);
Alpine.plugin(focus);

// Make Alpine available globally
window.Alpine = Alpine;
window.data = alpineInit;

// Import dynamic imports helper
import './dynamic-imports';

// Start Alpine
Alpine.start();

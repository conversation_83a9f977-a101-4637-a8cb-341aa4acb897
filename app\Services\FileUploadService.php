<?php

namespace App\Services;

use App\Models\File;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class FileUploadService
{
    /**
     * Upload a file and create a database record
     *
     * @param UploadedFile $file
     * @param string $directory
     * @param array $additionalData
     * @return File
     */
    public function uploadFile(UploadedFile $file, string $directory = 'uploads', array $additionalData = []): File
    {
        // Generate a unique filename
        $filename = time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();

        // Store the file in the public disk
        $path = $file->storeAs($directory, $filename, 'public');

        // Create a database record
        $fileModel = new File();
        $fileModel->title = $file->getClientOriginalName();
        $fileModel->filename = $filename;
        $fileModel->url = $path; // Store the relative path
        $fileModel->fileSize = $file->getSize();
        $fileModel->fileType = $file->getMimeType();

        // Add any additional data
        foreach ($additionalData as $key => $value) {
            $fileModel->{$key} = $value;
        }

        $fileModel->save();

        return $fileModel;
    }

    /**
     * Get the URL for a file
     *
     * @param File $file
     * @return string
     */
    public function getFileUrl(File $file): string
    {
        // If the URL already starts with http:// or https://, return it as is
        if (Str::startsWith($file->url, ['http://', 'https://'])) {
            return $file->url;
        }

        // Use Laravel's Storage facade to generate the URL
        return Storage::disk('public')->url($file->url);
    }

    /**
     * Delete a file and its database record
     *
     * @param File $file
     * @return bool
     */
    public function deleteFile(File $file): bool
    {
        // Delete the file from storage
        if (Storage::disk('public')->exists($file->url)) {
            Storage::disk('public')->delete($file->url);
        }

        // Delete the database record
        return $file->delete();
    }
}

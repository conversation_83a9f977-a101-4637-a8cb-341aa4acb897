<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <!-- Hero Section -->
    <section class="relative h-[300px] overflow-hidden">
        <div class="absolute inset-0">
            <img src="https://images.unsplash.com/photo-1494976388531-d1058494cdd8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80" alt="Cars" class="w-full h-full object-cover brightness-50">
        </div>
        <div class="relative container mx-auto px-4 h-full flex flex-col justify-center">
            <h1 class="text-4xl md:text-5xl font-bold text-white mb-4" data-lang-key="available_cars">Available Cars</h1>
            <p class="text-xl text-white mb-8 max-w-xl" data-lang-key="find_perfect_car">Find the perfect car for your next trip. Filter by type, price, and features.</p>
        </div>
    </section>

    <!-- Filter and Cars Section -->
    <section class="py-12">
        <div class="container mx-auto px-4">
            <div class="flex flex-col lg:flex-row gap-8">
                <!-- Filter Sidebar -->
                <div class="lg:w-1/4">
                    <div class="bg-white p-6 rounded-lg shadow-md sticky top-24">
                        <h2 class="text-xl font-bold mb-6 border-b pb-2" data-lang-key="filter_cars">Filter Cars</h2>

                        <form id="filter-form" action="<?php echo e(route('cars.listing')); ?>" method="GET">
                            <!-- Vehicle Type Filter -->
                            <div class="mb-6">
                                <h3 class="font-semibold mb-3" data-lang-key="car_type">Car Type</h3>
                                <select name="vehicle_type" class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none">
                                    <option value="" data-lang-key="all_types">All Types</option>
                                    <?php $__currentLoopData = $vehicleTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($type->id); ?>" <?php echo e(request('vehicle_type') == $type->id ? 'selected' : ''); ?>>
                                            <?php echo e($type->name); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>

                            <!-- City Filter -->
                            <div class="mb-6">
                                <h3 class="font-semibold mb-3" data-lang-key="location">Location</h3>
                                <select name="city_id" class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none">
                                    <option value="" data-lang-key="all_locations">All Locations</option>
                                    <?php $__currentLoopData = $cities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $city): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($city->id); ?>" <?php echo e(request('city_id') == $city->id ? 'selected' : ''); ?>>
                                            <?php echo e($city->name); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>

                            <!-- Price Range Filter -->
                            <div class="mb-6">
                                <h3 class="font-semibold mb-3" data-lang-key="price_range">Price Range (€/day)</h3>
                                <div class="flex items-center gap-2">
                                    <input type="number" name="min_price" placeholder="Min" value="<?php echo e(request('min_price')); ?>" min="0" class="w-1/2 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none">
                                    <span>-</span>
                                    <input type="number" name="max_price" placeholder="Max" value="<?php echo e(request('max_price')); ?>" min="0" class="w-1/2 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none">
                                </div>
                            </div>

                            <!-- Transmission Filter -->
                            <div class="mb-6">
                                <h3 class="font-semibold mb-3" data-lang-key="transmission">Transmission</h3>
                                <div class="space-y-2">
                                    <label class="flex items-center">
                                        <input type="radio" name="transmission" value="" <?php echo e(request('transmission') == '' ? 'checked' : ''); ?> class="mr-2">
                                        <span data-lang-key="any_transmission">Any</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="transmission" value="automatic" <?php echo e(request('transmission') == 'automatic' ? 'checked' : ''); ?> class="mr-2">
                                        <span data-lang-key="automatic">Automatic</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="transmission" value="manual" <?php echo e(request('transmission') == 'manual' ? 'checked' : ''); ?> class="mr-2">
                                        <span data-lang-key="manual">Manual</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Fuel Type Filter -->
                            <div class="mb-6">
                                <h3 class="font-semibold mb-3" data-lang-key="fuel_type">Fuel Type</h3>
                                <select name="fuel_type" class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none">
                                    <option value="" data-lang-key="any_fuel">Any</option>
                                    <option value="gasoline" <?php echo e(request('fuel_type') == 'gasoline' ? 'selected' : ''); ?> data-lang-key="gasoline">Gasoline</option>
                                    <option value="diesel" <?php echo e(request('fuel_type') == 'diesel' ? 'selected' : ''); ?> data-lang-key="diesel">Diesel</option>
                                    <option value="hybrid" <?php echo e(request('fuel_type') == 'hybrid' ? 'selected' : ''); ?> data-lang-key="hybrid">Hybrid</option>
                                    <option value="electric" <?php echo e(request('fuel_type') == 'electric' ? 'selected' : ''); ?> data-lang-key="electric">Electric</option>
                                    <option value="plugin_hybrid" <?php echo e(request('fuel_type') == 'plugin_hybrid' ? 'selected' : ''); ?> data-lang-key="plugin_hybrid">Plug-in Hybrid</option>
                                </select>
                            </div>

                            <!-- Seats Filter -->
                            <div class="mb-6">
                                <h3 class="font-semibold mb-3" data-lang-key="min_seats">Minimum Seats</h3>
                                <select name="seats" class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none">
                                    <option value="" data-lang-key="any_seats">Any</option>
                                    <option value="2" <?php echo e(request('seats') == '2' ? 'selected' : ''); ?>>2+</option>
                                    <option value="4" <?php echo e(request('seats') == '4' ? 'selected' : ''); ?>>4+</option>
                                    <option value="5" <?php echo e(request('seats') == '5' ? 'selected' : ''); ?>>5+</option>
                                    <option value="6" <?php echo e(request('seats') == '6' ? 'selected' : ''); ?>>6+</option>
                                    <option value="7" <?php echo e(request('seats') == '7' ? 'selected' : ''); ?>>7+</option>
                                    <option value="8+" <?php echo e(request('seats') == '8+' ? 'selected' : ''); ?>>8+</option>
                                </select>
                            </div>

                            <!-- With Driver Option -->
                            <div class="mb-6">
                                <label class="flex items-center">
                                    <input type="checkbox" name="with_driver" value="1" <?php echo e(request('with_driver') == '1' ? 'checked' : ''); ?> class="mr-2">
                                    <span data-lang-key="with_driver">With Driver/Chauffeur</span>
                                </label>
                            </div>

                            <!-- Sort By -->
                            <div class="mb-6">
                                <h3 class="font-semibold mb-3" data-lang-key="sort_by">Sort By</h3>
                                <select name="sort_by" class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none">
                                    <option value="created_at:desc" <?php echo e(request('sort_by') == 'created_at:desc' ? 'selected' : ''); ?> data-lang-key="newest">Newest First</option>
                                    <option value="daily_rate:asc" <?php echo e(request('sort_by') == 'daily_rate:asc' ? 'selected' : ''); ?> data-lang-key="price_low_high">Price: Low to High</option>
                                    <option value="daily_rate:desc" <?php echo e(request('sort_by') == 'daily_rate:desc' ? 'selected' : ''); ?> data-lang-key="price_high_low">Price: High to Low</option>
                                    <option value="year:desc" <?php echo e(request('sort_by') == 'year:desc' ? 'selected' : ''); ?> data-lang-key="newest_models">Newest Models</option>
                                    <option value="year:asc" <?php echo e(request('sort_by') == 'year:asc' ? 'selected' : ''); ?> data-lang-key="oldest_models">Oldest Models</option>
                                </select>
                            </div>

                            <button type="submit" class="w-full bg-primary text-white py-3 rounded-md hover:bg-red-700 transition" data-lang-key="apply_filters">Apply Filters</button>

                            <a href="<?php echo e(route('cars.listing')); ?>" class="block text-center mt-3 text-primary hover:text-red-700" data-lang-key="clear_filters">Clear Filters</a>
                        </form>
                    </div>
                </div>

                <!-- Cars Grid -->
                <div class="lg:w-3/4">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-xl font-bold" data-lang-key="found_cars">
                            <?php echo e($vehicles->total()); ?> <?php echo e($vehicles->total() == 1 ? frontend_trans('car_found') : frontend_trans('cars_found')); ?>

                        </h2>

                        <div class="flex items-center">
                            <span class="mr-2 text-gray-600" data-lang-key="language">Language:</span>
                            <a href="<?php echo e(route('change.locale', 'en')); ?>" class="mr-2 <?php echo e(app()->getLocale() == 'en' ? 'font-bold text-primary' : 'text-gray-600'); ?>">EN</a>
                            <a href="<?php echo e(route('change.locale', 'pt')); ?>" class="<?php echo e(app()->getLocale() == 'pt' ? 'font-bold text-primary' : 'text-gray-600'); ?>">PT</a>
                        </div>
                    </div>

                    <?php if($vehicles->count() > 0): ?>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <?php $__currentLoopData = $vehicles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vehicle): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition">
                                    <div class="relative h-48">
                                        <?php if($vehicle->primaryImage): ?>
                                            <img src="<?php echo e($vehicle->primaryImage->url); ?>" alt="<?php echo e($vehicle->make); ?> <?php echo e($vehicle->model); ?>" class="w-full h-full object-cover">
                                        <?php else: ?>
                                            <img src="https://via.placeholder.com/400x300?text=No+Image" alt="<?php echo e($vehicle->make); ?> <?php echo e($vehicle->model); ?>" class="w-full h-full object-cover">
                                        <?php endif; ?>

                                        <?php if($vehicle->is_featured): ?>
                                            <div class="absolute top-4 right-4 bg-primary text-white text-sm font-semibold py-1 px-3 rounded-full" data-lang-key="featured">
                                                <?php echo e(frontend_trans('featured')); ?>

                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="p-6">
                                        <div class="flex justify-between items-center mb-3">
                                            <h3 class="text-xl font-bold"><?php echo e($vehicle->year); ?> <?php echo e($vehicle->make); ?> <?php echo e($vehicle->model); ?></h3>
                                            <p class="text-primary font-bold">€<?php echo e(number_format($vehicle->daily_rate, 2)); ?>/day</p>
                                        </div>
                                        <div class="flex items-center text-gray-600 mb-2">
                                            <i class="fas fa-map-marker-alt text-gray-400 mr-2"></i>
                                            <span><?php echo e($vehicle->city->name); ?></span>
                                        </div>
                                        <div class="grid grid-cols-2 gap-4 mb-6">
                                            <div class="flex items-center">
                                                <?php if($vehicle->fuel_type == 'electric'): ?>
                                                    <i class="fas fa-bolt text-green-500 mr-2"></i>
                                                    <span data-lang-key="electric">Electric</span>
                                                <?php elseif($vehicle->fuel_type == 'hybrid' || $vehicle->fuel_type == 'plugin_hybrid'): ?>
                                                    <i class="fas fa-leaf text-green-500 mr-2"></i>
                                                    <span data-lang-key="<?php echo e($vehicle->fuel_type); ?>"><?php echo e(ucfirst(str_replace('_', ' ', $vehicle->fuel_type))); ?></span>
                                                <?php else: ?>
                                                    <i class="fas fa-gas-pump text-blue-500 mr-2"></i>
                                                    <span data-lang-key="<?php echo e($vehicle->fuel_type); ?>"><?php echo e(ucfirst($vehicle->fuel_type)); ?></span>
                                                <?php endif; ?>
                                            </div>
                                            <div class="flex items-center">
                                                <i class="fas fa-cog text-gray-400 mr-2"></i>
                                                <span data-lang-key="<?php echo e($vehicle->transmission); ?>"><?php echo e(ucfirst($vehicle->transmission)); ?></span>
                                            </div>
                                            <div class="flex items-center">
                                                <i class="fas fa-user-friends text-gray-400 mr-2"></i>
                                                <span><?php echo e($vehicle->seats); ?> <?php echo e(frontend_trans('seats')); ?></span>
                                            </div>
                                            <div class="flex items-center">
                                                <i class="fas fa-door-open text-gray-400 mr-2"></i>
                                                <span><?php echo e($vehicle->doors); ?> <?php echo e(frontend_trans('doors')); ?></span>
                                            </div>
                                        </div>
                                        <a href="<?php echo e(route('vehicle.details', $vehicle->id)); ?>" class="block w-full bg-primary text-white py-2 text-center rounded-md hover:bg-red-700 transition" data-lang-key="view_details">View Details</a>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>

                        <!-- Pagination -->
                        <div class="mt-8">
                            <?php echo e($vehicles->links()); ?>

                        </div>
                    <?php else: ?>
                        <div class="bg-white p-8 rounded-lg shadow-md text-center">
                            <i class="fas fa-car-side text-5xl text-gray-300 mb-4"></i>
                            <h3 class="text-xl font-bold mb-2" data-lang-key="no_cars_found">No Cars Found</h3>
                            <p class="text-gray-600 mb-4" data-lang-key="try_adjusting">Try adjusting your filters or search criteria.</p>
                            <a href="<?php echo e(route('cars.listing')); ?>" class="inline-block bg-primary text-white px-6 py-2 rounded-md hover:bg-red-700 transition" data-lang-key="clear_filters">Clear Filters</a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?><?php /**PATH C:\laragon\www\carbnb\resources\views\front\default\cars-listing.blade.php ENDPATH**/ ?>
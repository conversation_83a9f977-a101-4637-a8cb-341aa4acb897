<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<div class="container py-8 payment-container">
    <div class="mb-6">
        <a href="<?php echo e(route('booking.show', $booking->id)); ?>" class="text-primary hover:text-primary-dark flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clip-rule="evenodd" />
            </svg>
            Back to Booking Details
        </a>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                    <h1 class="text-2xl font-bold text-gray-800">Complete Your Payment</h1>
                </div>

                <div class="p-6">
                    <div class="mb-8">
                        <h2 class="text-xl font-semibold mb-4 text-gray-700">Payment Methods</h2>

                        <div class="grid grid-cols-1 gap-4">
                            <!-- PagSeguro Payment Option -->
                            <div class="border border-gray-200 rounded-lg p-4 hover:border-primary cursor-pointer payment-option active" data-payment-method="pagseguro">
                                <div class="flex items-center">
                                    <input type="radio" name="payment_method" id="pagseguro" value="pagseguro" class="h-4 w-4 text-primary focus:ring-primary border-gray-300" checked>
                                    <label for="pagseguro" class="ml-2 flex-1">
                                        <div class="flex items-center">
                                            <span class="font-medium text-gray-700">Pay with PagSeguro</span>
                                            <img src="https://assets.pagseguro.com.br/ps-bootstrap/v6.71.0/img/logos/pagbank/pagbank-logo.svg" alt="PagSeguro" class="h-6 ml-2">
                                        </div>
                                        <span class="block text-sm text-gray-500 mt-1">Secure payment via credit/debit card, PIX, boleto, and more</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Razorpay Payment Option -->
                            <div class="border border-gray-200 rounded-lg p-4 hover:border-primary cursor-pointer payment-option" data-payment-method="razorpay">
                                <div class="flex items-center">
                                    <input type="radio" name="payment_method" id="razorpay" value="razorpay" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                                    <label for="razorpay" class="ml-2 flex-1">
                                        <div class="flex items-center">
                                            <span class="font-medium text-gray-700">Pay with Razorpay</span>
                                            <img src="https://razorpay.com/assets/razorpay-logo.svg" alt="Razorpay" class="h-6 ml-2">
                                        </div>
                                        <span class="block text-sm text-gray-500 mt-1">Secure payment via credit/debit card, UPI, net banking, and more</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Manual Payment Option (for testing) -->
                            <div class="border border-gray-200 rounded-lg p-4 hover:border-primary cursor-pointer payment-option" data-payment-method="manual">
                                <div class="flex items-center">
                                    <input type="radio" name="payment_method" id="manual" value="manual" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                                    <label for="manual" class="ml-2 flex-1">
                                        <span class="font-medium text-gray-700">Manual Payment (Testing Only)</span>
                                        <span class="block text-sm text-gray-500 mt-1">Use this option for testing the payment flow</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- PagSeguro Payment Form -->
                    <div id="pagseguro_payment_form" class="payment-form">
                        <div class="bg-blue-50 p-4 rounded-md mb-6">
                            <div class="flex">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                </svg>
                                <div>
                                    <p class="text-sm text-blue-700">You'll be redirected to PagSeguro's secure payment page to complete your payment.</p>
                                </div>
                            </div>
                        </div>

                        <div class="mt-8 flex justify-end">
                            <button type="button" id="pagseguro_pay_button" class="inline-flex items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                Pay R$<?php echo e(number_format($booking->total_amount, 2)); ?>

                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- Razorpay Payment Form -->
                    <div id="razorpay_payment_form" class="payment-form hidden">
                        <div class="bg-blue-50 p-4 rounded-md mb-6">
                            <div class="flex">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                </svg>
                                <div>
                                    <p class="text-sm text-blue-700">You'll be redirected to Razorpay's secure payment page to complete your payment.</p>
                                </div>
                            </div>
                        </div>

                        <div class="mt-8 flex justify-end">
                            <button type="button" id="razorpay_pay_button" class="inline-flex items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                Pay $<?php echo e(number_format($booking->total_amount, 2)); ?>

                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- Manual Payment Form (for testing) -->
                    <div id="manual_payment_form" class="payment-form hidden">
                        <div class="bg-yellow-50 p-4 rounded-md mb-6">
                            <div class="flex">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                </svg>
                                <div>
                                    <p class="text-sm text-yellow-700">This is a test payment option for development purposes only.</p>
                                </div>
                            </div>
                        </div>

                        <?php if(session('error')): ?>
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
                            <strong>Error:</strong> <?php echo e(session('error')); ?>

                        </div>
                        <?php endif; ?>

                        <form action="<?php echo e(route('booking.manual-payment', $booking->id)); ?>" method="POST" id="manual-payment-form">
                            <?php echo csrf_field(); ?>
                            <div class="mt-8 flex justify-end">
                                <button type="submit" id="manual-payment-button" class="inline-flex items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                    Process Test Payment
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-md overflow-hidden sticky top-24">
                <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                    <h2 class="text-xl font-bold text-gray-800">Booking Summary</h2>
                </div>

                <div class="p-6">
                    <div class="flex items-center mb-6">
                        <?php if($booking->vehicle->primaryImage): ?>
                            <img src="<?php echo e(asset('storage/' . $booking->vehicle->primaryImage->file->path)); ?>" alt="<?php echo e($booking->vehicle->make); ?> <?php echo e($booking->vehicle->model); ?>" class="w-20 h-20 object-cover rounded-md mr-4">
                        <?php else: ?>
                            <div class="w-20 h-20 bg-gray-200 rounded-md mr-4 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                            </div>
                        <?php endif; ?>
                        <div>
                            <h3 class="font-bold text-gray-800"><?php echo e($booking->vehicle->make); ?> <?php echo e($booking->vehicle->model); ?></h3>
                            <p class="text-gray-600 text-sm"><?php echo e($booking->vehicle->year); ?></p>
                            <p class="text-gray-600 text-sm">Booking #<?php echo e($booking->booking_number); ?></p>
                        </div>
                    </div>

                    <div class="border-t border-gray-200 pt-4 mb-4">
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-600">Pickup Date:</span>
                            <span class="font-medium"><?php echo e($booking->start_date->format('M d, Y')); ?></span>
                        </div>
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-600">Return Date:</span>
                            <span class="font-medium"><?php echo e($booking->end_date->format('M d, Y')); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Duration:</span>
                            <span class="font-medium"><?php echo e($booking->duration_in_days); ?> days</span>
                        </div>
                    </div>

                    <div class="border-t border-gray-200 pt-4 mb-4">
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-600">Vehicle:</span>
                            <span class="font-medium">$<?php echo e(number_format($booking->vehicle_price, 2)); ?></span>
                        </div>

                        <?php if($booking->driver_price > 0): ?>
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-600">Driver:</span>
                            <span class="font-medium">$<?php echo e(number_format($booking->driver_price, 2)); ?></span>
                        </div>
                        <?php endif; ?>

                        <?php if($booking->discount_amount > 0): ?>
                        <div class="flex justify-between mb-2 text-green-600">
                            <span>Discount:</span>
                            <span>-$<?php echo e(number_format($booking->discount_amount, 2)); ?></span>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="border-t border-gray-200 pt-4 mb-4">
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-600">Subtotal:</span>
                            <span class="font-medium">$<?php echo e(number_format($booking->subtotal, 2)); ?></span>
                        </div>
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-600">Tax (10%):</span>
                            <span class="font-medium">$<?php echo e(number_format($booking->tax_amount, 2)); ?></span>
                        </div>
                    </div>

                    <div class="border-t border-gray-200 pt-4 mb-4">
                        <div class="flex justify-between">
                            <span class="text-lg font-bold">Total:</span>
                            <span class="text-lg font-bold">$<?php echo e(number_format($booking->total_amount, 2)); ?></span>
                        </div>
                    </div>

                    <?php if($booking->security_deposit > 0): ?>
                    <div class="bg-blue-50 p-4 rounded-md mt-4">
                        <div class="flex">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                            </svg>
                            <div>
                                <p class="text-sm text-blue-700">A security deposit of $<?php echo e(number_format($booking->security_deposit, 2)); ?> will be collected at pickup.</p>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- PagSeguro Payment Form (Hidden) -->
<form action="<?php echo e(route('pagseguro.payment')); ?>" method="POST" id="pagseguro_hidden_form">
    <?php echo csrf_field(); ?>
    <input type="hidden" name="pagseguro_order_id" id="pagseguro_order_id">
    <input type="hidden" name="pagseguro_payment_id" id="pagseguro_payment_id">
    <input type="hidden" name="booking_id" value="<?php echo e($booking->id); ?>">
</form>

<!-- Razorpay Payment Form (Hidden) -->
<form action="<?php echo e(route('razorpay.payment')); ?>" method="POST" id="razorpay_hidden_form">
    <?php echo csrf_field(); ?>
    <input type="hidden" name="razorpay_payment_id" id="razorpay_payment_id">
    <input type="hidden" name="razorpay_order_id" id="razorpay_order_id">
    <input type="hidden" name="razorpay_signature" id="razorpay_signature">
    <input type="hidden" name="booking_id" value="<?php echo e($booking->id); ?>">
</form>

<?php $__env->startPush('scripts'); ?>
<script src="https://checkout.razorpay.com/v1/checkout.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Payment method selection
        const paymentOptions = document.querySelectorAll('.payment-option');
        const paymentForms = document.querySelectorAll('.payment-form');

        paymentOptions.forEach(option => {
            option.addEventListener('click', function() {
                // Update radio button
                const radio = this.querySelector('input[type="radio"]');
                radio.checked = true;

                // Update active class
                paymentOptions.forEach(opt => opt.classList.remove('active'));
                this.classList.add('active');

                // Show/hide payment forms
                const paymentMethod = this.getAttribute('data-payment-method');
                paymentForms.forEach(form => {
                    form.classList.add('hidden');
                });
                document.getElementById(paymentMethod + '_payment_form').classList.remove('hidden');
            });
        });

        // Manual payment integration
        const manualPaymentForm = document.getElementById('manual-payment-form');
        const manualPaymentButton = document.getElementById('manual-payment-button');

        if (manualPaymentForm && manualPaymentButton) {
            manualPaymentForm.addEventListener('submit', function(e) {
                // Show loading state
                manualPaymentButton.disabled = true;
                manualPaymentButton.innerHTML = `
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Processing...
                `;
            });
        }

        // PagSeguro integration
        const pagseguroButton = document.getElementById('pagseguro_pay_button');

        pagseguroButton.addEventListener('click', function() {
            // Show loading state
            pagseguroButton.disabled = true;
            pagseguroButton.innerHTML = `
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Processing...
            `;

            // Create PagSeguro order
            fetch('<?php echo e(route('pagseguro.create-order', $booking->id)); ?>')
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(errorData => {
                            throw new Error(errorData.error || 'Failed to create payment order');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.error) {
                        // Handle error
                        console.error(data.error);

                        // Show error message in a more user-friendly way
                        const errorDiv = document.createElement('div');
                        errorDiv.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4';
                        errorDiv.innerHTML = `<strong>Error:</strong> ${data.error}`;
                        document.querySelector('.payment-container').prepend(errorDiv);

                        // If API keys are not configured, switch to manual payment
                        if (data.use_manual) {
                            // Select the manual payment option
                            document.querySelector('[data-payment-method="manual"]').click();

                            // Add a note about using manual payment
                            const noteDiv = document.createElement('div');
                            noteDiv.className = 'bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative mb-4';
                            noteDiv.innerHTML = `<strong>Note:</strong> PagSeguro payment is not available. Please use the Manual Payment option for testing.`;
                            document.querySelector('#manual_payment_form').prepend(noteDiv);
                        }

                        // Reset button
                        pagseguroButton.disabled = false;
                        pagseguroButton.innerHTML = `
                            Pay R$<?php echo e(number_format($booking->total_amount, 2)); ?>

                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                        `;
                        return;
                    }

                    // If there's a checkout URL, redirect to PagSeguro checkout page
                    if (data.checkout_url) {
                        // Set form values
                        document.getElementById('pagseguro_order_id').value = data.order_id;
                        document.getElementById('pagseguro_payment_id').value = 'pending';

                        // Redirect to PagSeguro checkout
                        window.location.href = data.checkout_url;
                    } else {
                        // Handle direct payment flow (if needed)
                        document.getElementById('pagseguro_order_id').value = data.order_id;
                        document.getElementById('pagseguro_payment_id').value = data.payment_id || 'direct';

                        // Submit the form
                        document.getElementById('pagseguro_hidden_form').submit();
                    }
                })
                .catch(error => {
                    console.error('Error:', error);

                    // Show error message in a more user-friendly way
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4';
                    errorDiv.innerHTML = `<strong>Error:</strong> ${error.message || 'An error occurred while processing your payment. Please try again.'}`;
                    document.querySelector('.payment-container').prepend(errorDiv);

                    // If the error is related to authentication, suggest using manual payment
                    if (error.message && (error.message.includes('Authentication') || error.message.includes('API key'))) {
                        // Select the manual payment option
                        document.querySelector('[data-payment-method="manual"]').click();

                        // Add a note about using manual payment
                        const noteDiv = document.createElement('div');
                        noteDiv.className = 'bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative mb-4';
                        noteDiv.innerHTML = `<strong>Note:</strong> PagSeguro payment is not available. Please use the Manual Payment option for testing.`;
                        document.querySelector('#manual_payment_form').prepend(noteDiv);
                    }

                    // Reset button
                    pagseguroButton.disabled = false;
                    pagseguroButton.innerHTML = `
                        Pay R$<?php echo e(number_format($booking->total_amount, 2)); ?>

                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    `;
                });
        });

        // Razorpay integration
        const razorpayButton = document.getElementById('razorpay_pay_button');

        razorpayButton.addEventListener('click', function() {
            // Show loading state
            razorpayButton.disabled = true;
            razorpayButton.innerHTML = `
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Processing...
            `;

            // Create Razorpay order
            fetch('<?php echo e(route('razorpay.create-order', $booking->id)); ?>')
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(errorData => {
                            throw new Error(errorData.error || 'Failed to create payment order');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.error) {
                        // Handle error
                        console.error(data.error);

                        // Show error message in a more user-friendly way
                        const errorDiv = document.createElement('div');
                        errorDiv.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4';
                        errorDiv.innerHTML = `<strong>Error:</strong> ${data.error}`;
                        document.querySelector('.payment-container').prepend(errorDiv);

                        // If API keys are not configured, switch to manual payment
                        if (data.use_manual) {
                            // Select the manual payment option
                            document.querySelector('[data-payment-method="manual"]').click();

                            // Add a note about using manual payment
                            const noteDiv = document.createElement('div');
                            noteDiv.className = 'bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative mb-4';
                            noteDiv.innerHTML = `<strong>Note:</strong> Razorpay payment is not available. Please use the Manual Payment option for testing.`;
                            document.querySelector('#manual_payment_form').prepend(noteDiv);
                        }

                        // Reset button
                        razorpayButton.disabled = false;
                        razorpayButton.innerHTML = `
                            Pay $<?php echo e(number_format($booking->total_amount, 2)); ?>

                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                        `;
                        return;
                    }

                    // Initialize Razorpay
                    const options = {
                        key: '<?php echo e(config('razorpay.key')); ?>',
                        amount: data.amount,
                        currency: data.currency,
                        name: 'Car Rental',
                        description: 'Booking #<?php echo e($booking->booking_number); ?>',
                        order_id: data.order_id,
                        handler: function(response) {
                            // Set form values
                            document.getElementById('razorpay_payment_id').value = response.razorpay_payment_id;
                            document.getElementById('razorpay_order_id').value = response.razorpay_order_id;
                            document.getElementById('razorpay_signature').value = response.razorpay_signature;

                            // Submit the form
                            document.getElementById('razorpay_hidden_form').submit();
                        },
                        prefill: {
                            name: '<?php echo e(Auth::user()->name); ?>',
                            email: '<?php echo e(Auth::user()->email); ?>',
                        },
                        theme: {
                            color: '#3B82F6',
                        },
                        modal: {
                            ondismiss: function() {
                                // Reset button
                                razorpayButton.disabled = false;
                                razorpayButton.innerHTML = `
                                    Pay $<?php echo e(number_format($booking->total_amount, 2)); ?>

                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                `;
                            }
                        }
                    };

                    const razorpay = new Razorpay(options);
                    razorpay.open();
                })
                .catch(error => {
                    console.error('Error:', error);

                    // Show error message in a more user-friendly way
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4';
                    errorDiv.innerHTML = `<strong>Error:</strong> ${error.message || 'An error occurred while processing your payment. Please try again.'}`;
                    document.querySelector('.payment-container').prepend(errorDiv);

                    // If the error is related to authentication, suggest using manual payment
                    if (error.message && (error.message.includes('Authentication') || error.message.includes('API key'))) {
                        // Select the manual payment option
                        document.querySelector('[data-payment-method="manual"]').click();

                        // Add a note about using manual payment
                        const noteDiv = document.createElement('div');
                        noteDiv.className = 'bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative mb-4';
                        noteDiv.innerHTML = `<strong>Note:</strong> Razorpay payment is not available. Please use the Manual Payment option for testing.`;
                        document.querySelector('#manual_payment_form').prepend(noteDiv);
                    }

                    // Reset button
                    razorpayButton.disabled = false;
                    razorpayButton.innerHTML = `
                        Pay $<?php echo e(number_format($booking->total_amount, 2)); ?>

                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    `;
                });
        });
    });
</script>
<?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\carbnb\resources\views\front\bookings\payment.blade.php ENDPATH**/ ?>
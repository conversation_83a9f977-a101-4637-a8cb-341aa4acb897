<?php if (isset($component)) { $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742 = $attributes; } ?>
<?php $component = App\View\Components\AppBackendLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-backend-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppBackendLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            <?php echo e(__('Driver Management')); ?>

        </h2>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="mb-6 flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900"><?php echo e(__('Drivers')); ?></h3>

                        <div class="flex space-x-2">
                            <a href="<?php echo e(route('admin.drivers.index', ['status' => 'pending'])); ?>"
                               class="px-3 py-2 text-sm font-medium rounded-md <?php echo e($status === 'pending' ? 'bg-indigo-100 text-indigo-700' : 'text-gray-500 hover:text-gray-700'); ?>">
                                <?php echo e(__('Pending')); ?>

                            </a>
                            <a href="<?php echo e(route('admin.drivers.index', ['status' => 'active'])); ?>"
                               class="px-3 py-2 text-sm font-medium rounded-md <?php echo e($status === 'active' ? 'bg-indigo-100 text-indigo-700' : 'text-gray-500 hover:text-gray-700'); ?>">
                                <?php echo e(__('Active')); ?>

                            </a>
                            <a href="<?php echo e(route('admin.drivers.index', ['status' => 'rejected'])); ?>"
                               class="px-3 py-2 text-sm font-medium rounded-md <?php echo e($status === 'rejected' ? 'bg-indigo-100 text-indigo-700' : 'text-gray-500 hover:text-gray-700'); ?>">
                                <?php echo e(__('Rejected')); ?>

                            </a>
                            <a href="<?php echo e(route('admin.drivers.index', ['status' => 'inactive'])); ?>"
                               class="px-3 py-2 text-sm font-medium rounded-md <?php echo e($status === 'inactive' ? 'bg-indigo-100 text-indigo-700' : 'text-gray-500 hover:text-gray-700'); ?>">
                                <?php echo e(__('Inactive')); ?>

                            </a>
                        </div>
                    </div>

                    <?php if($drivers->count() > 0): ?>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            <?php echo e(__('Driver')); ?>

                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            <?php echo e(__('License')); ?>

                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            <?php echo e(__('Experience')); ?>

                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            <?php echo e(__('City')); ?>

                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            <?php echo e(__('Status')); ?>

                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            <?php echo e(__('Featured')); ?>

                                        </th>
                                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            <?php echo e(__('Actions')); ?>

                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <?php $__currentLoopData = $drivers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $driver): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="flex items-center">
                                                    <div class="flex-shrink-0 h-10 w-10">
                                                        <?php if($driver->user->profile_image): ?>
                                                            <img class="h-10 w-10 rounded-full" src="<?php echo e($driver->user->profile_image); ?>" alt="<?php echo e($driver->user->first_name); ?>">
                                                        <?php else: ?>
                                                            <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                                                <span class="text-gray-600"><?php echo e(substr($driver->user->first_name, 0, 1)); ?></span>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div class="ml-4">
                                                        <div class="text-sm font-medium text-gray-900">
                                                            <?php echo e($driver->user->first_name); ?> <?php echo e($driver->user->last_name); ?>

                                                        </div>
                                                        <div class="text-sm text-gray-500">
                                                            <?php echo e($driver->user->email); ?>

                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900"><?php echo e($driver->license->license_number); ?></div>
                                                <div class="text-sm text-gray-500">Expires: <?php echo e($driver->license->expiry_date->format('M d, Y')); ?></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900"><?php echo e($driver->experience_years); ?> <?php echo e(Str::plural('year', $driver->experience_years)); ?></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900"><?php echo e($driver->city->name ?? 'N/A'); ?></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                                    <?php if($driver->status === 'active'): ?> bg-green-100 text-green-800
                                                    <?php elseif($driver->status === 'pending'): ?> bg-yellow-100 text-yellow-800
                                                    <?php elseif($driver->status === 'rejected'): ?> bg-red-100 text-red-800
                                                    <?php else: ?> bg-gray-100 text-gray-800 <?php endif; ?>">
                                                    <?php echo e(ucfirst($driver->status)); ?>

                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                <?php if($driver->is_featured): ?>
                                                    <span class="text-green-600"><i class="fas fa-check"></i></span>
                                                <?php else: ?>
                                                    <span class="text-red-600"><i class="fas fa-times"></i></span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                <a href="<?php echo e(route('admin.drivers.show', $driver)); ?>" class="text-indigo-600 hover:text-indigo-900 mr-3"><?php echo e(__('View')); ?></a>

                                                <?php if($driver->status === 'pending'): ?>
                                                    <form method="POST" action="<?php echo e(route('admin.drivers.approve', $driver)); ?>" class="inline">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('PUT'); ?>
                                                        <button type="submit" class="text-green-600 hover:text-green-900 mr-3"><?php echo e(__('Approve')); ?></button>
                                                    </form>

                                                    <button type="button"
                                                            onclick="openRejectModal(<?php echo e($driver->id); ?>)"
                                                            class="text-red-600 hover:text-red-900">
                                                        <?php echo e(__('Reject')); ?>

                                                    </button>
                                                <?php endif; ?>

                                                <?php if($driver->status === 'active'): ?>
                                                    <form method="POST" action="<?php echo e(route('admin.drivers.toggle-featured', $driver)); ?>" class="inline">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('PUT'); ?>
                                                        <button type="submit" class="text-amber-600 hover:text-amber-900">
                                                            <?php echo e($driver->is_featured ? __('Unfeature') : __('Feature')); ?>

                                                        </button>
                                                    </form>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>

                        <div class="mt-4">
                            <?php echo e($drivers->links()); ?>

                        </div>
                    <?php else: ?>
                        <div class="text-center py-8">
                            <p class="text-gray-500"><?php echo e(__('No drivers found.')); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Reject Modal -->
    <div id="reject-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden z-50">
        <div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
            <h3 class="text-lg font-medium text-gray-900 mb-4"><?php echo e(__('Reject Driver Application')); ?></h3>

            <form id="reject-form" method="POST" action="">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>

                <div class="mb-4">
                    <label for="rejection_reason" class="block text-sm font-medium text-gray-700"><?php echo e(__('Reason for Rejection')); ?></label>
                    <textarea id="rejection_reason" name="rejection_reason" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required></textarea>
                </div>

                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeRejectModal()" class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <?php echo e(__('Cancel')); ?>

                    </button>
                    <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                        <?php echo e(__('Reject')); ?>

                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function openRejectModal(driverId) {
            document.getElementById('reject-form').action = `/admin/drivers/${driverId}/reject`;
            document.getElementById('reject-modal').classList.remove('hidden');
        }

        function closeRejectModal() {
            document.getElementById('reject-modal').classList.add('hidden');
        }
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $attributes = $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $component = $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\carbnb\resources\views\admin\drivers\index.blade.php ENDPATH**/ ?>
@props([
    'menuItem' => null,
    'menuItems' => [],
    'routeNames' => [],
    'action',
    'method' => 'POST'
])

<form action="{{ $action }}" method="POST" class="space-y-6">
    @csrf
    @if($method === 'PUT')
        @method('PUT')
    @endif

    <!-- Name Field -->
    <div>
        <label for="name" class="block text-sm font-medium text-gray-700">Name</label>
        <input type="text" 
               name="name" 
               id="name" 
               value="{{ old('name', $menuItem->name ?? '') }}"
               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
               required>
        @error('name')
            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
        @enderror
    </div>

    <!-- URL Field -->
    <div>
        <label for="url" class="block text-sm font-medium text-gray-700">URL</label>
        <input type="text" 
               name="url" 
               id="url" 
               value="{{ old('url', $menuItem->url ?? '') }}"
               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
               placeholder="e.g., /about-us">
        @error('url')
            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
        @enderror
    </div>

    <!-- Route Name Field -->
    <div>
        <label for="route_name" class="block text-sm font-medium text-gray-700">Route Name</label>
        <select name="route_name" 
                id="route_name" 
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
            <option value="">Select a route (optional)</option>
            @foreach($routeNames as $routeName)
                <option value="{{ $routeName }}" 
                        {{ old('route_name', $menuItem->route_name ?? '') === $routeName ? 'selected' : '' }}>
                    {{ $routeName }}
                </option>
            @endforeach
        </select>
        @error('route_name')
            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
        @enderror
    </div>

    <!-- Parent Menu Item -->
    <div>
        <label for="parent_id" class="block text-sm font-medium text-gray-700">Parent Menu Item</label>
        <select name="parent_id" 
                id="parent_id" 
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
            <option value="">No Parent (Top Level)</option>
            @foreach($menuItems as $item)
                <option value="{{ $item->id }}" 
                        {{ old('parent_id', $menuItem->parent_id ?? '') == $item->id ? 'selected' : '' }}>
                    {{ $item->name }}
                </option>
            @endforeach
        </select>
        @error('parent_id')
            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
        @enderror
    </div>

    <!-- Active Status -->
    <div class="flex items-center">
        <input type="checkbox" 
               name="is_active" 
               id="is_active" 
               value="1"
               {{ old('is_active', $menuItem->is_active ?? true) ? 'checked' : '' }}
               class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
        <label for="is_active" class="ml-2 block text-sm text-gray-900">
            Active
        </label>
    </div>

    <!-- Submit Buttons -->
    <div class="flex items-center justify-end space-x-3">
        <a href="{{ route('menu-items.index') }}" 
           class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
            Cancel
        </a>
        <button type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            {{ $menuItem ? 'Update' : 'Create' }} Menu Item
        </button>
    </div>
</form>

/* Quill Editor Overrides */

/* Fix for strike-through in headings within Quill editor */
.ql-editor h1, .ql-editor h2, .ql-editor h3, .ql-editor h4, .ql-editor h5, .ql-editor h6 {
    text-decoration: none !important;
    text-decoration-line: none !important;
    text-decoration-style: none !important;
    text-decoration-color: transparent !important;
    text-decoration-thickness: 0 !important;
}

/* Make Quill editor more prominent */
.ql-container {
    min-height: 200px;
    border-bottom-left-radius: 0.5rem;
    border-bottom-right-radius: 0.5rem;
    font-family: inherit;
}

.ql-toolbar {
    border-top-left-radius: 0.5rem;
    border-top-right-radius: 0.5rem;
    background-color: #f9fafb;
}

/* Fix for Quill editor in dark mode */
.dark .ql-toolbar {
    background-color: #374151;
    border-color: #4b5563;
}

.dark .ql-container {
    border-color: #4b5563;
}

.dark .ql-editor {
    color: #e5e7eb;
}

/* Fix for Quill editor in forms */
form .ql-container {
    margin-bottom: 1rem;
}

/* Fix for Quill editor in modals */
.modal .ql-container {
    max-height: 300px;
    overflow-y: auto;
}

/* Fix for Quill editor toolbar */
.ql-toolbar button {
    height: 24px;
}

/* Fix for Quill editor in responsive layouts */
@media (max-width: 640px) {
    .ql-toolbar {
        flex-wrap: wrap;
    }
    
    .ql-formats {
        margin-right: 0;
    }
}

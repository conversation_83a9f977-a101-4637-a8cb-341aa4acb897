<?php if (isset($component)) { $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742 = $attributes; } ?>
<?php $component = App\View\Components\AppBackendLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-backend-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppBackendLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container mx-auto mt-5 p-6 bg-white rounded-lg shadow-md">
        <h1 class="text-2xl font-semibold mb-4 text-gray-800">Manage Permissions for Role: <?php echo e($role->name); ?></h1>

        <?php if(session('success')): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-md mb-4" role="alert">
                <strong class="font-bold"><?php echo e(session('success')); ?></strong>
            </div>
        <?php endif; ?>

        <form action="<?php echo e(route('roles.permissions.store', $role)); ?>" method="POST" class="bg-gray-50 p-4 rounded-lg shadow">
            <?php echo csrf_field(); ?>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <?php $__currentLoopData = $permissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="flex items-center space-x-2 bg-white p-2 rounded-md shadow-sm border">
                        <input type="checkbox" name="permissions[]" value="<?php echo e($permission->id); ?>" id="permission_<?php echo e($permission->id); ?>"
                               class="w-5 h-5 text-blue-600 border-gray-300 rounded focus:ring focus:ring-blue-300"
                            <?php echo e(in_array($permission->id, $rolePermissions) ? 'checked' : ''); ?>>
                        <label for="permission_<?php echo e($permission->id); ?>" class="text-gray-700"><?php echo e($permission->name); ?></label>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            <div class="mt-4 flex justify-end">
                <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition">Assign Permissions</button>
            </div>
        </form>

        <h2 class="text-xl font-semibold mt-8 text-gray-800">Assigned Permissions</h2>
        <div class="overflow-x-auto mt-4">
            <table class="w-full bg-white border rounded-lg shadow-md">
                <thead class="bg-gray-100">
                <tr>
                    <th class="px-4 py-3 border text-left text-gray-700">#</th>
                    <th class="px-4 py-3 border text-left text-gray-700">Name</th>
                    <th class="px-4 py-3 border text-left text-gray-700">Actions</th>
                </tr>
                </thead>
                <tbody>
                <?php $__currentLoopData = $role->permissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr class="hover:bg-gray-50">
                        <td class="border px-4 py-3 text-gray-800"><?php echo e($permission->id); ?></td>
                        <td class="border px-4 py-3 text-gray-800"><?php echo e($permission->name); ?></td>
                        <td class="border px-4 py-3">
                            <form action="<?php echo e(route('roles.permissions.destroy', [$role, $permission])); ?>" method="POST" class="inline-block">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit" class="text-red-500 hover:text-red-700 transition" onclick="return confirm('Are you sure?')">Revoke</button>
                            </form>
                        </td>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $attributes = $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $component = $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\carbnb\resources\views\backend\access-control\roles-permissions.blade.php ENDPATH**/ ?>
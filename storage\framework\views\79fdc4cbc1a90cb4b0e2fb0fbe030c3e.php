<?php if (isset($component)) { $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742 = $attributes; } ?>
<?php $component = App\View\Components\AppBackendLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-backend-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppBackendLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container bg-white p-5 mx-auto">
        <h1 class="text-2xl mb-4">Edit Category</h1>

        <form action="<?php echo e(route('admin.categories.update', $category)); ?>" method="POST">
            <?php echo csrf_field(); ?>
            <?php echo method_field('PUT'); ?>

            <div class="mb-4">
                <label for="name" class="block text-sm font-bold">Name</label>
                <input type="text" name="name" id="name" value="<?php echo e(old('name', $category->name)); ?>" class="mt-1 block w-full border border-gray-300 rounded p-2" required>
            </div>

            <div class="mb-4">
                <label for="description" class="block text-sm font-bold">Description</label>
                <input type="text" name="description" id="description" value="<?php echo e(old('description', $category->description)); ?>" class="mt-1 block w-full border border-gray-300 rounded p-2">
            </div>

            <div class="mb-4">
                <label for="status" class="block text-sm font-bold">Status</label>
                <select name="status" id="status" class="mt-1 block w-full border border-gray-300 rounded">
                    <option value="1" <?php echo e($category->status ? 'selected' : ''); ?>>Active</option>
                    <option value="0" <?php echo e(!$category->status ? 'selected' : ''); ?>>Inactive</option>
                </select>
            </div>

            <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded">Update Category</button>
        </form>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $attributes = $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $component = $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\carbnb\resources\views\backend\categories\edit.blade.php ENDPATH**/ ?>
<x-app-backend-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                {{ __('Conversation with') }} {{ $otherUser->first_name }} {{ $otherUser->last_name }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('messages.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-500 active:bg-gray-700 focus:outline-none focus:border-gray-700 focus:ring ring-gray-300 disabled:opacity-25 transition ease-in-out duration-150">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    {{ __('Back to Messages') }}
                </a>
                @if($conversation->booking)
                    <a href="{{ route('booking.show', $conversation->booking->id) }}" class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-500 active:bg-green-700 focus:outline-none focus:border-green-700 focus:ring ring-green-300 disabled:opacity-25 transition ease-in-out duration-150">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                        {{ __('View Booking') }}
                    </a>
                @endif
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    @if(session('success'))
                        <div class="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
                            {{ session('error') }}
                        </div>
                    @endif

                    @if($conversation->booking)
                        <div class="mb-4 p-4 bg-blue-50 border border-blue-200 text-blue-700 rounded flex items-center">
                            <i class="fas fa-info-circle mr-2 text-blue-500"></i>
                            <div>
                                <p class="font-medium">This conversation is related to booking #{{ $conversation->booking->booking_number }}</p>
                                <p class="text-sm">{{ $conversation->booking->vehicle->make }} {{ $conversation->booking->vehicle->model }} • {{ $conversation->booking->start_date->format('M d, Y') }} - {{ $conversation->booking->end_date->format('M d, Y') }}</p>
                            </div>
                        </div>
                    @endif

                    <div class="flex flex-col h-[600px]">
                        <!-- Message List -->
                        <div class="flex-1 overflow-y-auto mb-4 p-4 border border-gray-200 rounded-lg">
                            <div class="space-y-4">
                                @if($messages->isEmpty())
                                    <div class="text-center py-8">
                                        <p class="text-gray-500">{{ __('No messages yet. Start the conversation!') }}</p>
                                    </div>
                                @else
                                    @foreach($messages->sortBy('created_at') as $message)
                                        <div class="flex {{ $message->sender_id === Auth::id() ? 'justify-end' : 'justify-start' }}">
                                            <div class="max-w-[70%] {{ $message->sender_id === Auth::id() ? 'bg-blue-100 text-blue-900' : 'bg-gray-100 text-gray-900' }} rounded-lg px-4 py-2 shadow">
                                                <div class="text-sm">
                                                    {{ $message->message }}
                                                </div>
                                                <div class="text-xs text-gray-500 mt-1 text-right">
                                                    {{ $message->created_at->format('M d, Y g:i A') }}
                                                    @if($message->sender_id === Auth::id())
                                                        @if($message->is_read)
                                                            <span class="ml-1 text-blue-600">✓✓</span>
                                                        @else
                                                            <span class="ml-1 text-gray-400">✓</span>
                                                        @endif
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                @endif
                            </div>
                        </div>

                        <!-- Reply Form -->
                        <div class="mt-auto">
                            <form action="{{ route('messages.reply', $conversation->id) }}" method="POST">
                                @csrf
                                <div class="flex items-start space-x-4">
                                    <div class="min-w-0 flex-1">
                                        <div class="relative">
                                            <textarea id="message" name="message" rows="3" class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6" placeholder="{{ __('Type your message...') }}" required></textarea>
                                        </div>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <button type="submit" class="inline-flex items-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600">
                                            <svg class="-ml-0.5 mr-1.5 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                            </svg>
                                            {{ __('Send') }}
                                        </button>
                                    </div>
                                </div>
                                @error('message')
                                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Auto-scroll to bottom of messages on page load
        document.addEventListener('DOMContentLoaded', function() {
            const messageContainer = document.querySelector('.overflow-y-auto');
            messageContainer.scrollTop = messageContainer.scrollHeight;
        });
    </script>
</x-app-backend-layout>

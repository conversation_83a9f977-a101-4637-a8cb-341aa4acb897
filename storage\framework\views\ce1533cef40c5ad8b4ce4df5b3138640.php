<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <!-- Hero Section -->
    <section class="relative h-[600px] overflow-hidden bg-gradient-to-r from-gray-900 to-red-900">
        <div class="absolute inset-0 opacity-40">
            <img src="https://images.unsplash.com/photo-1580273916550-e323be2ae537?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80" alt="Car keys handover" class="w-full h-full object-cover">
        </div>
        <div class="absolute inset-0 bg-gradient-to-b from-transparent to-black opacity-40"></div>
        <div class="relative container mx-auto px-4 h-full flex flex-col justify-center">
            <div class="max-w-xl">
                <span class="inline-block px-4 py-1 bg-white/20 backdrop-blur-sm text-white rounded-full mb-4 font-medium animate-pulse">Start earning today</span>
                <h1 class="text-5xl md:text-6xl font-extrabold text-white mb-6 leading-tight" data-lang-key="turn_car_income">Turn Your Car Into <span class="text-primary">Income</span></h1>
                <p class="text-xl text-white/90 mb-10 leading-relaxed" data-lang-key="share_car_earn">Share your car when you're not using it and earn an average of €500+ per month. Join thousands of car owners already earning with CARBNB.</p>
                <div class="flex flex-col sm:flex-row gap-4">
                    <a href="<?php echo e(route('quick-car-registration')); ?>" class="inline-block px-8 py-4 bg-primary text-white font-bold rounded-full hover:bg-red-700 transition transform hover:scale-105 shadow-lg" data-lang-key="list_car_now">List Your Car Now</a>
                    <a href="#how-it-works" class="inline-block px-8 py-4 bg-white/10 backdrop-blur-sm text-white font-bold rounded-full hover:bg-white/20 transition">Learn More</a>
                </div>
                <p class="text-white/80 text-sm mt-6" data-lang-key="free_to_list">✓ Free to list. Only pay when you earn.</p>
            </div>
        </div>
        <div class="absolute bottom-0 left-0 right-0">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 120" class="w-full h-auto">
                <path fill="#ffffff" fill-opacity="1" d="M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z"></path>
            </svg>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="py-12 bg-white relative z-10">
        <div class="container mx-auto px-4">
            <div class="max-w-6xl mx-auto bg-white rounded-2xl shadow-xl p-8 border border-gray-100">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
                    <div class="p-4 rounded-xl hover:bg-red-50 transition duration-300">
                        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-euro-sign text-2xl text-primary"></i>
                        </div>
                        <p class="text-4xl font-extrabold text-primary mb-1">€500<span class="text-2xl">+</span></p>
                        <p class="text-gray-600 font-medium" data-lang-key="avg_monthly_earnings">Avg. Monthly Earnings</p>
                    </div>
                    <div class="p-4 rounded-xl hover:bg-red-50 transition duration-300">
                        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-users text-2xl text-primary"></i>
                        </div>
                        <p class="text-4xl font-extrabold text-primary mb-1">10k<span class="text-2xl">+</span></p>
                        <p class="text-gray-600 font-medium" data-lang-key="car_owners">Car Owners</p>
                    </div>
                    <div class="p-4 rounded-xl hover:bg-red-50 transition duration-300">
                        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-shield-alt text-2xl text-primary"></i>
                        </div>
                        <p class="text-4xl font-extrabold text-primary mb-1">€1M</p>
                        <p class="text-gray-600 font-medium" data-lang-key="insurance_coverage">Insurance Coverage</p>
                    </div>
                    <div class="p-4 rounded-xl hover:bg-red-50 transition duration-300">
                        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-headset text-2xl text-primary"></i>
                        </div>
                        <p class="text-4xl font-extrabold text-primary mb-1">24/7</p>
                        <p class="text-gray-600 font-medium" data-lang-key="customer_support">Customer Support</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section id="how-it-works" class="py-24 bg-gradient-to-b from-white to-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <span class="inline-block px-4 py-1 bg-red-100 text-primary rounded-full mb-4 font-medium">Simple Process</span>
                <h2 class="text-4xl font-extrabold text-gray-900 mb-4" data-lang-key="how_car_sharing_works">How Car Sharing Works</h2>
                <p class="text-gray-600 text-center max-w-2xl mx-auto" data-lang-key="sharing_simple">Sharing your car on CARBNB is simple, safe, and profitable. Here's how to get started:</p>
            </div>

            <!-- Video Section -->
            <div class="max-w-4xl mx-auto mb-16 rounded-2xl overflow-hidden shadow-2xl video-section">
                <div class="relative pb-[56.25%] h-0">
                    <video class="absolute inset-0 w-full h-full object-cover" controls poster="<?php echo e(asset('video/videoPoster.png')); ?>">
                        <source src="<?php echo e(asset('video/carbnbVideo.mp4')); ?>" type="video/mp4">
                        Your browser does not support the video tag.
                    </video>
                    <div class="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center group transition-opacity duration-500">
                        <button class="w-20 h-20 bg-primary bg-opacity-80 rounded-full flex items-center justify-center transition-transform transform group-hover:scale-110 focus:outline-none">
                            <i class="fas fa-play text-white text-3xl pl-1"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-12 max-w-6xl mx-auto">
                <div class="bg-white p-8 rounded-2xl shadow-xl text-center transform transition duration-500 hover:scale-105 border border-gray-100 relative">
                    <div class="absolute -top-6 left-1/2 transform -translate-x-1/2">
                        <div class="w-20 h-20 bg-primary rounded-full flex items-center justify-center mx-auto shadow-lg">
                            <i class="fas fa-car text-2xl text-white"></i>
                        </div>
                    </div>
                    <div class="mt-8">
                        <h3 class="text-2xl font-bold mb-4 text-gray-900" data-lang-key="list_your_car_step">1. List Your Car</h3>
                        <p class="text-gray-600 mb-6 leading-relaxed" data-lang-key="create_free_listing">Create a free listing with photos and details about your car. Set your own pricing and availability.</p>
                        <div class="h-1 w-20 bg-primary mx-auto"></div>
                    </div>
                </div>

                <div class="bg-white p-8 rounded-2xl shadow-xl text-center transform transition duration-500 hover:scale-105 border border-gray-100 relative">
                    <div class="absolute -top-6 left-1/2 transform -translate-x-1/2">
                        <div class="w-20 h-20 bg-primary rounded-full flex items-center justify-center mx-auto shadow-lg">
                            <i class="fas fa-calendar-check text-2xl text-white"></i>
                        </div>
                    </div>
                    <div class="mt-8">
                        <h3 class="text-2xl font-bold mb-4 text-gray-900" data-lang-key="accept_bookings_step">2. Accept Bookings</h3>
                        <p class="text-gray-600 mb-6 leading-relaxed" data-lang-key="review_approve">Review and approve trip requests from verified renters. Manage your own schedule.</p>
                        <div class="h-1 w-20 bg-primary mx-auto"></div>
                    </div>
                </div>

                <div class="bg-white p-8 rounded-2xl shadow-xl text-center transform transition duration-500 hover:scale-105 border border-gray-100 relative">
                    <div class="absolute -top-6 left-1/2 transform -translate-x-1/2">
                        <div class="w-20 h-20 bg-primary rounded-full flex items-center justify-center mx-auto shadow-lg">
                            <i class="fas fa-wallet text-2xl text-white"></i>
                        </div>
                    </div>
                    <div class="mt-8">
                        <h3 class="text-2xl font-bold mb-4 text-gray-900" data-lang-key="get_paid_step">3. Get Paid</h3>
                        <p class="text-gray-600 mb-6 leading-relaxed" data-lang-key="earnings_deposited">Earnings are automatically deposited into your account after each completed trip.</p>
                        <div class="h-1 w-20 bg-primary mx-auto"></div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-16">
                <a href="<?php echo e(route('quick-car-registration')); ?>" class="inline-block px-8 py-4 bg-primary text-white font-bold rounded-full hover:bg-red-700 transition transform hover:scale-105 shadow-lg" data-lang-key="start_earning_now">Start Earning Now</a>
            </div>
        </div>
    </section>

    <!-- Income Calculator -->
    <section class="py-24 bg-gradient-to-r from-red-50 to-red-100 relative overflow-hidden">
        <!-- Background decorative elements -->
        <div class="absolute top-0 right-0 w-64 h-64 bg-red-200 rounded-full opacity-30 -mr-32 -mt-32"></div>
        <div class="absolute bottom-0 left-0 w-96 h-96 bg-red-200 rounded-full opacity-30 -ml-48 -mb-48"></div>

        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center mb-16">
                <span class="inline-block px-4 py-1 bg-white text-primary rounded-full mb-4 font-medium">Earnings Calculator</span>
                <h2 class="text-4xl font-extrabold text-gray-900 mb-4" data-lang-key="how_much_earn">How Much Can You Earn?</h2>
                <p class="text-gray-700 text-center max-w-2xl mx-auto" data-lang-key="calculator_desc">Use our calculator to estimate your potential earnings based on your car and location.</p>
            </div>

            <div class="max-w-4xl mx-auto bg-white rounded-2xl shadow-xl p-8 border border-red-100">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                    <div>
                        <label class="block text-gray-700 font-medium mb-2" data-lang-key="car_make">Car Make</label>
                        <div class="relative">
                            <select id="car-make" class="w-full p-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:outline-none appearance-none bg-white">
                                <option>Toyota</option>
                                <option>Honda</option>
                                <option>Ford</option>
                                <option>BMW</option>
                                <option>Tesla</option>
                                <option>Other</option>
                            </select>
                            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                <i class="fas fa-chevron-down text-gray-400"></i>
                            </div>
                        </div>
                    </div>
                    <div>
                        <label class="block text-gray-700 font-medium mb-2" data-lang-key="car_year">Car Year</label>
                        <div class="relative">
                            <select id="car-year" class="w-full p-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:outline-none appearance-none bg-white">
                                <option>2023</option>
                                <option>2022</option>
                                <option>2021</option>
                                <option>2020</option>
                                <option>2019</option>
                                <option>2018</option>
                                <option>2017</option>
                                <option>2016</option>
                                <option>2015</option>
                                <option>Older</option>
                            </select>
                            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                <i class="fas fa-chevron-down text-gray-400"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-8">
                    <label class="block text-gray-700 font-medium mb-2" data-lang-key="your_location">Your Location</label>
                    <div class="relative">
                        <i class="fas fa-map-marker-alt absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        <input type="text" placeholder="Enter your city" class="w-full p-4 pl-12 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:outline-none">
                    </div>
                </div>

                <div class="mb-10">
                    <label class="block text-gray-700 font-medium mb-3" data-lang-key="days_available">Days Available Per Month</label>
                    <div class="flex flex-col">
                        <div class="flex justify-between text-xs text-gray-500 mb-2 px-1">
                            <span>1 day</span>
                            <span>15 days</span>
                            <span>30 days</span>
                        </div>
                        <div class="flex items-center">
                            <input type="range" min="1" max="30" value="15" class="w-full h-3 bg-gray-200 rounded-full appearance-none cursor-pointer" id="days-slider">
                        </div>
                        <div class="flex justify-center mt-4">
                            <span class="px-6 py-2 bg-red-100 rounded-full text-lg font-semibold text-primary" id="days-value">15 days</span>
                        </div>
                    </div>
                </div>

                <div class="text-center">
                    <button id="calculate-btn" class="px-10 py-4 bg-primary text-white font-bold rounded-full hover:bg-red-700 transition transform hover:scale-105 shadow-lg" data-lang-key="calculate_earnings">
                        <i class="fas fa-calculator mr-2"></i> Calculate Earnings
                    </button>
                </div>

                <div id="earnings-result" class="mt-10 p-8 bg-gradient-to-r from-red-50 to-red-100 rounded-xl hidden">
                    <h3 class="text-2xl font-bold text-center mb-4" data-lang-key="estimated_earnings">Your Estimated Monthly Earnings</h3>
                    <div class="flex justify-center items-center">
                        <div class="w-24 h-24 bg-white rounded-full flex items-center justify-center shadow-lg mr-4">
                            <i class="fas fa-euro-sign text-4xl text-primary"></i>
                        </div>
                        <p class="text-center text-5xl font-extrabold text-primary"><span id="earnings-amount">0</span></p>
                    </div>
                    <p class="text-center text-gray-600 mt-4 max-w-md mx-auto" data-lang-key="results_vary">Results vary based on your car's demand, location, and availability. Many owners earn even more during peak seasons.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Benefits Section -->
    <section class="py-24 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <span class="inline-block px-4 py-1 bg-red-100 text-primary rounded-full mb-4 font-medium">Why Choose CARBNB</span>
                <h2 class="text-4xl font-extrabold text-gray-900 mb-4" data-lang-key="benefits_sharing">Benefits of Sharing Your Car</h2>
                <p class="text-gray-600 text-center max-w-2xl mx-auto" data-lang-key="join_thousands_owners">Join thousands of car owners who are earning extra income and helping their community.</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
                <div class="bg-white p-8 rounded-2xl shadow-lg border border-gray-100 hover:border-red-200 transition duration-300 transform hover:-translate-y-2">
                    <div class="w-16 h-16 bg-gradient-to-br from-green-400 to-green-600 rounded-2xl flex items-center justify-center mb-6 shadow-md">
                        <i class="fas fa-money-bill-wave text-2xl text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-gray-900" data-lang-key="earn_extra_income">Earn Extra Income</h3>
                    <p class="text-gray-600 leading-relaxed" data-lang-key="turn_expense_income">Turn your car from an expense into an income source. Many owners cover their car payments entirely.</p>
                </div>

                <div class="bg-white p-8 rounded-2xl shadow-lg border border-gray-100 hover:border-red-200 transition duration-300 transform hover:-translate-y-2">
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-400 to-blue-600 rounded-2xl flex items-center justify-center mb-6 shadow-md">
                        <i class="fas fa-shield-alt text-2xl text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-gray-900" data-lang-key="insurance_protection_title">Insurance Protection</h3>
                    <p class="text-gray-600 leading-relaxed" data-lang-key="every_trip_includes">Every trip includes €1M in liability insurance and comprehensive collision coverage.</p>
                </div>

                <div class="bg-white p-8 rounded-2xl shadow-lg border border-gray-100 hover:border-red-200 transition duration-300 transform hover:-translate-y-2">
                    <div class="w-16 h-16 bg-gradient-to-br from-purple-400 to-purple-600 rounded-2xl flex items-center justify-center mb-6 shadow-md">
                        <i class="fas fa-calendar-alt text-2xl text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-gray-900" data-lang-key="flexible_schedule">Flexible Schedule</h3>
                    <p class="text-gray-600 leading-relaxed" data-lang-key="control_availability">You control when your car is available. Share only when it works for you.</p>
                </div>

                <div class="bg-white p-8 rounded-2xl shadow-lg border border-gray-100 hover:border-red-200 transition duration-300 transform hover:-translate-y-2">
                    <div class="w-16 h-16 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-2xl flex items-center justify-center mb-6 shadow-md">
                        <i class="fas fa-hands-helping text-2xl text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-gray-900" data-lang-key="vetted_renters">Vetted Renters</h3>
                    <p class="text-gray-600 leading-relaxed" data-lang-key="all_renters_screened">All renters are screened and verified. You can review profiles before accepting bookings.</p>
                </div>

                <div class="bg-white p-8 rounded-2xl shadow-lg border border-gray-100 hover:border-red-200 transition duration-300 transform hover:-translate-y-2">
                    <div class="w-16 h-16 bg-gradient-to-br from-red-400 to-red-600 rounded-2xl flex items-center justify-center mb-6 shadow-md">
                        <i class="fas fa-headset text-2xl text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-gray-900" data-lang-key="support_title">24/7 Support</h3>
                    <p class="text-gray-600 leading-relaxed" data-lang-key="team_available">Our team is always available to help with any questions or issues that arise.</p>
                </div>

                <div class="bg-white p-8 rounded-2xl shadow-lg border border-gray-100 hover:border-red-200 transition duration-300 transform hover:-translate-y-2">
                    <div class="w-16 h-16 bg-gradient-to-br from-teal-400 to-teal-600 rounded-2xl flex items-center justify-center mb-6 shadow-md">
                        <i class="fas fa-leaf text-2xl text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-gray-900" data-lang-key="reduce_carbon">Reduce Carbon Footprint</h3>
                    <p class="text-gray-600 leading-relaxed" data-lang-key="car_sharing_helps">Car sharing helps reduce the number of vehicles on the road, benefiting the environment.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials -->
    <section class="py-24 bg-gradient-to-b from-white to-red-50 relative overflow-hidden">
        <!-- Background decorative elements -->
        <div class="absolute top-0 left-0 w-64 h-64 bg-red-200 rounded-full opacity-20 -ml-32 -mt-32"></div>
        <div class="absolute bottom-0 right-0 w-96 h-96 bg-red-200 rounded-full opacity-20 -mr-48 -mb-48"></div>

        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center mb-16">
                <span class="inline-block px-4 py-1 bg-red-100 text-primary rounded-full mb-4 font-medium">Success Stories</span>
                <h2 class="text-4xl font-extrabold text-gray-900 mb-4" data-lang-key="what_car_owners_say">What Car Owners Say</h2>
                <p class="text-gray-600 text-center max-w-2xl mx-auto" data-lang-key="hear_from_owners">Hear from other car owners who are already earning with CARBNB.</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-10 max-w-6xl mx-auto">
                <div class="bg-white p-8 rounded-2xl shadow-xl border border-gray-100 relative">
                    <div class="absolute -top-6 right-8">
                        <div class="bg-yellow-400 text-white px-4 py-2 rounded-full shadow-md">
                            <div class="flex items-center">
                                <i class="fas fa-star mr-1"></i>
                                <span class="font-bold">5.0</span>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col items-center mb-6">
                        <div class="w-24 h-24 mb-4 relative">
                            <img src="<?php echo e(asset('images/testimonials/owner1.jpg')); ?>" alt="Car Owner" class="w-full h-full object-cover rounded-full border-4 border-red-100">
                            <div class="absolute bottom-0 right-0 bg-primary text-white rounded-full w-8 h-8 flex items-center justify-center shadow-md">
                                <i class="fas fa-quote-right"></i>
                            </div>
                        </div>
                        <h4 class="font-bold text-xl text-gray-900">Miguel Santos</h4>
                        <p class="text-gray-500">Tesla Model 3 Owner</p>
                    </div>

                    <p class="text-gray-600 leading-relaxed mb-6 italic" data-lang-key="testimonial_miguel">"I was skeptical at first, but CARBNB has been amazing. My Tesla is booked almost every weekend, and I've made over €12,000 in the past year. The process is seamless, and the support team is excellent."</p>

                    <div class="bg-gradient-to-r from-red-500 to-red-600 text-white py-3 px-6 rounded-xl inline-block shadow-md">
                        <p class="font-bold" data-lang-key="earning_1200">Earning €1,200/month</p>
                    </div>
                </div>

                <div class="bg-white p-8 rounded-2xl shadow-xl border border-gray-100 relative md:mt-8">
                    <div class="absolute -top-6 right-8">
                        <div class="bg-yellow-400 text-white px-4 py-2 rounded-full shadow-md">
                            <div class="flex items-center">
                                <i class="fas fa-star mr-1"></i>
                                <span class="font-bold">4.5</span>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col items-center mb-6">
                        <div class="w-24 h-24 mb-4 relative">
                            <img src="<?php echo e(asset('images/testimonials/owner2.jpg')); ?>" alt="Car Owner" class="w-full h-full object-cover rounded-full border-4 border-red-100">
                            <div class="absolute bottom-0 right-0 bg-primary text-white rounded-full w-8 h-8 flex items-center justify-center shadow-md">
                                <i class="fas fa-quote-right"></i>
                            </div>
                        </div>
                        <h4 class="font-bold text-xl text-gray-900">Sofia Martins</h4>
                        <p class="text-gray-500">Toyota RAV4 Owner</p>
                    </div>

                    <p class="text-gray-600 leading-relaxed mb-6 italic" data-lang-key="testimonial_sofia">"I work from home and barely use my car during the week. Now it's making me money instead of just sitting in the driveway. The insurance coverage gives me peace of mind, and the renters have all been respectful."</p>

                    <div class="bg-gradient-to-r from-red-500 to-red-600 text-white py-3 px-6 rounded-xl inline-block shadow-md">
                        <p class="font-bold" data-lang-key="earning_650">Earning €650/month</p>
                    </div>
                </div>

                <div class="bg-white p-8 rounded-2xl shadow-xl border border-gray-100 relative">
                    <div class="absolute -top-6 right-8">
                        <div class="bg-yellow-400 text-white px-4 py-2 rounded-full shadow-md">
                            <div class="flex items-center">
                                <i class="fas fa-star mr-1"></i>
                                <span class="font-bold">5.0</span>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col items-center mb-6">
                        <div class="w-24 h-24 mb-4 relative">
                            <img src="<?php echo e(asset('images/testimonials/owner3.jpg')); ?>" alt="Car Owner" class="w-full h-full object-cover rounded-full border-4 border-red-100">
                            <div class="absolute bottom-0 right-0 bg-primary text-white rounded-full w-8 h-8 flex items-center justify-center shadow-md">
                                <i class="fas fa-quote-right"></i>
                            </div>
                        </div>
                        <h4 class="font-bold text-xl text-gray-900">João Ferreira</h4>
                        <p class="text-gray-500">Ford F-150 Owner</p>
                    </div>

                    <p class="text-gray-600 leading-relaxed mb-6 italic" data-lang-key="testimonial_joao">"My truck is in high demand for weekend moves and projects. I've had such a positive experience with CARBNB that I'm considering getting a second vehicle just to share on the platform."</p>

                    <div class="bg-gradient-to-r from-red-500 to-red-600 text-white py-3 px-6 rounded-xl inline-block shadow-md">
                        <p class="font-bold" data-lang-key="earning_850">Earning €850/month</p>
                    </div>
                </div>
            </div>

            <div class="text-center mt-16">
                <a href="<?php echo e(route('quick-car-registration')); ?>" class="inline-block px-8 py-4 bg-primary text-white font-bold rounded-full hover:bg-red-700 transition transform hover:scale-105 shadow-lg">
                    <i class="fas fa-car mr-2"></i> Join Them Today
                </a>
            </div>
        </div>
    </section>

    <!-- Common Questions -->
    <section class="py-24 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <span class="inline-block px-4 py-1 bg-red-100 text-primary rounded-full mb-4 font-medium">FAQ</span>
                <h2 class="text-4xl font-extrabold text-gray-900 mb-4" data-lang-key="common_questions">Common Questions</h2>
                <p class="text-gray-600 text-center max-w-2xl mx-auto" data-lang-key="everything_need_know">Everything you need to know about sharing your car on CARBNB.</p>
            </div>

            <div class="max-w-4xl mx-auto">
                <div class="mb-6 transform transition duration-500 hover:scale-[1.01]">
                    <button class="flex justify-between items-center w-full bg-white p-6 rounded-xl shadow-lg hover:shadow-xl border border-gray-100 transition focus:outline-none faq-toggle group">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-4 group-hover:bg-primary group-hover:text-white transition-colors">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <span class="text-xl font-bold text-gray-900" data-lang-key="insurance_work">How does the insurance work?</span>
                        </div>
                        <i class="fas fa-chevron-down text-primary transition-transform duration-300"></i>
                    </button>
                    <div class="bg-white px-6 pb-6 pt-4 rounded-b-xl hidden faq-content shadow-lg border-x border-b border-gray-100">
                        <p class="text-gray-600 leading-relaxed" data-lang-key="insurance_answer">Every trip is covered by our €1 million insurance policy. This includes liability insurance and physical damage protection up to the actual cash value of your car. Your personal insurance is never affected, and you're never out of pocket for damage caused during a rental.</p>
                    </div>
                </div>

                <div class="mb-6 transform transition duration-500 hover:scale-[1.01]">
                    <button class="flex justify-between items-center w-full bg-white p-6 rounded-xl shadow-lg hover:shadow-xl border border-gray-100 transition focus:outline-none faq-toggle group">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-4 group-hover:bg-primary group-hover:text-white transition-colors">
                                <i class="fas fa-car"></i>
                            </div>
                            <span class="text-xl font-bold text-gray-900" data-lang-key="cars_qualify">What types of cars qualify?</span>
                        </div>
                        <i class="fas fa-chevron-down text-primary transition-transform duration-300"></i>
                    </button>
                    <div class="bg-white px-6 pb-6 pt-4 rounded-b-xl hidden faq-content shadow-lg border-x border-b border-gray-100">
                        <p class="text-gray-600 leading-relaxed" data-lang-key="cars_qualify_answer">Most cars less than 12 years old with fewer than 150,000 kilometers qualify. Exotic cars, commercial vehicles, and cars with salvage titles are generally not eligible. During registration, we'll help determine if your vehicle qualifies.</p>
                    </div>
                </div>

                <div class="mb-6 transform transition duration-500 hover:scale-[1.01]">
                    <button class="flex justify-between items-center w-full bg-white p-6 rounded-xl shadow-lg hover:shadow-xl border border-gray-100 transition focus:outline-none faq-toggle group">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-4 group-hover:bg-primary group-hover:text-white transition-colors">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <span class="text-xl font-bold text-gray-900" data-lang-key="how_get_paid">How do I get paid?</span>
                        </div>
                        <i class="fas fa-chevron-down text-primary transition-transform duration-300"></i>
                    </button>
                    <div class="bg-white px-6 pb-6 pt-4 rounded-b-xl hidden faq-content shadow-lg border-x border-b border-gray-100">
                        <p class="text-gray-600 leading-relaxed" data-lang-key="get_paid_answer">You'll receive payments via direct deposit to your bank account. Payments are processed within 3 business days after each trip is completed. You can track your earnings in real-time through the CARBNB dashboard.</p>
                    </div>
                </div>

                <div class="mb-6 transform transition duration-500 hover:scale-[1.01]">
                    <button class="flex justify-between items-center w-full bg-white p-6 rounded-xl shadow-lg hover:shadow-xl border border-gray-100 transition focus:outline-none faq-toggle group">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-4 group-hover:bg-primary group-hover:text-white transition-colors">
                                <i class="fas fa-tools"></i>
                            </div>
                            <span class="text-xl font-bold text-gray-900" data-lang-key="wear_tear">What about wear and tear on my car?</span>
                        </div>
                        <i class="fas fa-chevron-down text-primary transition-transform duration-300"></i>
                    </button>
                    <div class="bg-white px-6 pb-6 pt-4 rounded-b-xl hidden faq-content shadow-lg border-x border-b border-gray-100">
                        <p class="text-gray-600 leading-relaxed" data-lang-key="wear_tear_answer">Normal wear and tear is expected, but our pricing structure is designed to account for this. Many hosts find that the income they earn far outweighs the additional wear and tear. For maintenance issues that arise directly from a trip, our support team can help determine appropriate compensation.</p>
                    </div>
                </div>

                <div class="mb-6 transform transition duration-500 hover:scale-[1.01]">
                    <button class="flex justify-between items-center w-full bg-white p-6 rounded-xl shadow-lg hover:shadow-xl border border-gray-100 transition focus:outline-none faq-toggle group">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-4 group-hover:bg-primary group-hover:text-white transition-colors">
                                <i class="fas fa-ticket-alt"></i>
                            </div>
                            <span class="text-xl font-bold text-gray-900" data-lang-key="tickets_tolls">What if a renter gets a ticket or tolls?</span>
                        </div>
                        <i class="fas fa-chevron-down text-primary transition-transform duration-300"></i>
                    </button>
                    <div class="bg-white px-6 pb-6 pt-4 rounded-b-xl hidden faq-content shadow-lg border-x border-b border-gray-100">
                        <p class="text-gray-600 leading-relaxed" data-lang-key="tickets_tolls_answer">Renters are responsible for all tickets, tolls, and violations incurred during their trip. If you receive a notice for a violation that occurred during a rental period, you can submit it through our platform and we'll handle the reimbursement process with the renter.</p>
                    </div>
                </div>
            </div>

            <div class="text-center mt-12">
                <a href="#faqs" class="inline-flex items-center px-6 py-3 bg-white text-primary font-bold rounded-full hover:bg-red-50 transition shadow-md border border-red-100" data-lang-key="view_all_faqs">
                    View all FAQs
                    <i class="fas fa-arrow-right ml-2"></i>
                </a>
            </div>
        </div>
    </section>


    <?php $__env->startPush('scripts'); ?>
        <!-- SweetAlert2 -->
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

        <script>
            $(document).ready(function() {
                // FAQ toggles
                $('.faq-toggle').click(function() {
                    const icon = $(this).find('i.fas.fa-chevron-down');
                    const content = $(this).next('.faq-content');

                    // Toggle the clicked FAQ
                    icon.toggleClass('transform rotate-180');
                    content.slideToggle(300);

                    // Close other FAQs
                    $('.faq-content').not(content).slideUp(300);
                    $('.faq-toggle i.fas.fa-chevron-down').not(icon).removeClass('transform rotate-180');
                });

                // Make sure FAQs are closed on page load
                $('.faq-content').hide();

                // Days slider
                $('#days-slider').on('input', function() {
                    $('#days-value').text($(this).val() + ' days');
                });

                // Calculate earnings button with animation
                $('#calculate-btn').click(function() {
                    const carMake = $('#car-make').val();
                    const carYear = $('#car-year').val();
                    const daysAvailable = $('#days-slider').val();

                    // Simple calculation formula
                    let baseRate = 35;

                    // Adjust for premium brands
                    if (['BMW', 'Tesla', 'Mercedes'].includes(carMake)) {
                        baseRate = 65;
                    }

                    // Adjust for newer cars
                    if (['2020', '2021', '2022', '2023'].includes(carYear)) {
                        baseRate += 15;
                    }

                    const estimatedEarnings = baseRate * daysAvailable;

                    // Show loading animation
                    $('#calculate-btn').html('<i class="fas fa-spinner fa-spin mr-2"></i> Calculating...');
                    $('#calculate-btn').prop('disabled', true);

                    // Simulate calculation delay for better UX
                    setTimeout(function() {
                        // Display results with counter animation
                        $('#earnings-result').removeClass('hidden');

                        // Animate the counter
                        $({ Counter: 0 }).animate({
                            Counter: estimatedEarnings
                        }, {
                            duration: 1000,
                            easing: 'swing',
                            step: function() {
                                $('#earnings-amount').text(Math.ceil(this.Counter));
                            },
                            complete: function() {
                                $('#earnings-amount').text(estimatedEarnings);
                            }
                        });

                        // Reset button
                        $('#calculate-btn').html('<i class="fas fa-calculator mr-2"></i> Calculate Earnings');
                        $('#calculate-btn').prop('disabled', false);

                        // Scroll to results
                        $('html, body').animate({
                            scrollTop: $('#earnings-result').offset().top - 100
                        }, 500);
                    }, 800);
                });

                // Enhanced photo upload handling
                $('.photo-upload').click(function() {
                    $(this).find('input[type="file"]').click();
                });

                $('.photo-upload input[type="file"]').change(function() {
                    const file = this.files[0];
                    if (file) {
                        const reader = new FileReader();
                        const photoUpload = $(this).parent();

                        // Show loading indicator
                        photoUpload.find('div').html('<i class="fas fa-spinner fa-spin text-gray-400 text-2xl"></i>');

                        reader.onload = function(e) {
                            // Create image element to check dimensions
                            const img = new Image();
                            img.onload = function() {
                                photoUpload.css('background-image', `url(${e.target.result})`);
                                photoUpload.css('background-size', 'cover');
                                photoUpload.css('background-position', 'center');
                                photoUpload.find('div').addClass('hidden');

                                // Add a "change" button
                                const changeBtn = $('<div class="absolute bottom-2 right-2 bg-white p-1 rounded-full shadow-md cursor-pointer"><i class="fas fa-sync text-primary"></i></div>');
                                photoUpload.append(changeBtn);

                                changeBtn.click(function(e) {
                                    e.stopPropagation();
                                    photoUpload.find('input[type="file"]').click();
                                });
                            };
                            img.src = e.target.result;
                        }

                        reader.readAsDataURL(file);
                    }
                });

                // Enhanced form submission with SweetAlert
                $('#car-listing-form').submit(function(e) {
                    e.preventDefault();

                    // Form validation with smooth scroll to error
                    const form = this;
                    const requiredFields = $(form).find('input[required], select[required], textarea[required]');
                    let valid = true;
                    let firstError = null;

                    requiredFields.each(function() {
                        if (!$(this).val()) {
                            valid = false;
                            $(this).addClass('border-red-500');
                            $(this).parent().addClass('shake-animation');

                            // Store the first error element for scrolling
                            if (!firstError) {
                                firstError = $(this);
                            }

                            // Remove shake animation after it completes
                            setTimeout(() => {
                                $(this).parent().removeClass('shake-animation');
                            }, 500);
                        } else {
                            $(this).removeClass('border-red-500');
                        }
                    });

                    if (!valid) {
                        // Scroll to the first error
                        if (firstError) {
                            $('html, body').animate({
                                scrollTop: firstError.offset().top - 100
                            }, 500);
                        }

                        Swal.fire({
                            title: 'Validation Error',
                            text: 'Please fill in all required fields.',
                            icon: 'error',
                            confirmButtonText: 'OK',
                            confirmButtonColor: '#FF0000',
                            customClass: {
                                popup: 'swal-wide',
                                title: 'text-xl font-bold',
                                confirmButton: 'px-6 py-3 rounded-full'
                            }
                        });
                        return;
                    }

                    // Show loading state with custom design
                    Swal.fire({
                        title: 'Submitting Your Car',
                        html: `
                            <div class="flex flex-col items-center">
                                <div class="w-16 h-16 mb-4 relative">
                                    <div class="absolute inset-0 rounded-full border-4 border-gray-200"></div>
                                    <div class="absolute inset-0 rounded-full border-4 border-t-primary border-r-transparent border-b-transparent border-l-transparent animate-spin"></div>
                                </div>
                                <p>Please wait while we process your listing...</p>
                            </div>
                        `,
                        showConfirmButton: false,
                        allowOutsideClick: false,
                        customClass: {
                            popup: 'rounded-xl'
                        }
                    });

                    // Create FormData and submit
                    const formData = new FormData(form);

                    // AJAX submission
                    $.ajax({
                        url: $(form).attr('action'),
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function(response) {
                            Swal.fire({
                                title: 'Success!',
                                html: `
                                    <div class="flex flex-col items-center">
                                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                                            <i class="fas fa-check-circle text-3xl text-green-500"></i>
                                        </div>
                                        <p>Your car listing has been submitted successfully. Our team will review it shortly.</p>
                                    </div>
                                `,
                                icon: false,
                                confirmButtonText: 'Great!',
                                confirmButtonColor: '#FF0000',
                                customClass: {
                                    popup: 'rounded-xl',
                                    confirmButton: 'px-6 py-3 rounded-full'
                                }
                            }).then((result) => {
                                // Redirect to the seller dashboard
                                window.location.href = "<?php echo e(url('/')); ?>";
                            });
                        },
                        error: function(xhr, status, error) {
                            // Parse the error response
                            let errorMessage = 'An error occurred while submitting your listing.';

                            if (xhr.responseJSON && xhr.responseJSON.errors) {
                                const errors = xhr.responseJSON.errors;
                                const firstError = Object.values(errors)[0];
                                if (firstError && firstError.length > 0) {
                                    errorMessage = firstError[0];
                                }
                            }

                            Swal.fire({
                                title: 'Error',
                                html: `
                                    <div class="flex flex-col items-center">
                                        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
                                            <i class="fas fa-exclamation-circle text-3xl text-red-500"></i>
                                        </div>
                                        <p>${errorMessage}</p>
                                    </div>
                                `,
                                icon: false,
                                confirmButtonText: 'Try Again',
                                confirmButtonColor: '#FF0000',
                                customClass: {
                                    popup: 'rounded-xl',
                                    confirmButton: 'px-6 py-3 rounded-full'
                                }
                            });
                        }
                    });
                });

                // Add smooth scrolling for anchor links
                $('a[href^="#"]').on('click', function(e) {
                    e.preventDefault();

                    const target = $(this.getAttribute('href'));
                    if (target.length) {
                        $('html, body').animate({
                            scrollTop: target.offset().top - 50
                        }, 800);
                    }
                });

                // Add CSS for animations
                $('<style>')
                    .prop('type', 'text/css')
                    .html(`
                        @keyframes shake {
                            0%, 100% { transform: translateX(0); }
                            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
                            20%, 40%, 60%, 80% { transform: translateX(5px); }
                        }
                        .shake-animation {
                            animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
                        }

                        /* FAQ toggle icon transition */
                        .faq-toggle i.fas.fa-chevron-down {
                            transition: transform 0.3s ease;
                        }

                        .transform.rotate-180 {
                            transform: rotate(180deg);
                        }
                    `)
                    .appendTo('head');
            });
        </script>
    <?php $__env->stopPush(); ?>
<?php $__env->startPush('scripts'); ?>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Video player functionality
            const videoContainer = document.querySelector('.video-section');
            const video = document.querySelector('.video-section video');
            const playButton = document.querySelector('.video-section .group button');

            if (video && playButton) {
                // Make sure video is loaded
                video.addEventListener('loadedmetadata', function() {
                    console.log('Video metadata loaded');
                });

                // Play button click handler
                playButton.addEventListener('click', function() {
                    console.log('Play button clicked');
                    try {
                        const playPromise = video.play();

                        if (playPromise !== undefined) {
                            playPromise.then(_ => {
                                // Video playback started successfully
                                console.log('Video playback started');
                                this.parentElement.classList.add('opacity-0');
                                setTimeout(() => {
                                    this.parentElement.style.display = 'none';
                                }, 500);
                            })
                            .catch(error => {
                                // Auto-play was prevented
                                console.log('Playback error:', error);
                            });
                        }
                    } catch (e) {
                        console.error('Error playing video:', e);
                    }
                });

                // Handle video pause
                video.addEventListener('pause', function() {
                    console.log('Video paused');
                    playButton.parentElement.style.display = 'flex';
                    setTimeout(() => {
                        playButton.parentElement.classList.remove('opacity-0');
                    }, 10);
                });

                // Handle video end
                video.addEventListener('ended', function() {
                    console.log('Video ended');
                    playButton.parentElement.style.display = 'flex';
                    setTimeout(() => {
                        playButton.parentElement.classList.remove('opacity-0');
                    }, 10);
                });
            }
        });
    </script>
<?php $__env->stopPush(); ?>

 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\carbnb\resources\views\front\default\list-your-car.blade.php ENDPATH**/ ?>
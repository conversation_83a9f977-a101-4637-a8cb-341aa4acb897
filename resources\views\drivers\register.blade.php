<x-app-backend-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Driver Registration') }}
        </h2>
    </x-slot>

    <!-- Alpine.js script for step functionality -->
    <script>
        document.addEventListener('alpine:init', () => {
            Alpine.data('registrationForm', () => ({
                currentStep: 1,
                totalSteps: 4,
                isSubmitting: false,
                
                nextStep() {
                    if (this.currentStep < this.totalSteps) {
                        this.currentStep++;
                        window.scrollTo({ top: 0, behavior: 'smooth' });
                    }
                },
                
                prevStep() {
                    if (this.currentStep > 1) {
                        this.currentStep--;
                        window.scrollTo({ top: 0, behavior: 'smooth' });
                    }
                },
                
                goToStep(step) {
                    if (step >= 1 && step <= this.totalSteps) {
                        this.currentStep = step;
                        window.scrollTo({ top: 0, behavior: 'smooth' });
                    }
                },
                
                submitForm() {
                    this.isSubmitting = true;
                    document.getElementById('driver-registration-form').submit();
                }
            }));
        });
    </script>

    <div class="py-8">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Welcome Banner -->
            <div class="bg-gradient-to-r from-blue-600 to-indigo-700 rounded-xl shadow-2xl mb-8 overflow-hidden transform transition-all hover:scale-[1.01] duration-300">
                <div class="md:flex items-center">
                    <div class="p-8 md:w-2/3">
                        <h2 class="text-3xl font-bold text-white mb-3">{{ __('Become a Driver Partner') }}</h2>
                        <p class="text-blue-100 text-lg mb-6">{{ __('Join our platform and start earning by driving passengers to their destinations.') }}</p>
                        <div class="flex flex-wrap gap-6 text-sm text-white">
                            <div class="flex items-center bg-white/10 p-3 rounded-lg">
                                <i class="fas fa-money-bill-wave text-2xl mr-2 text-yellow-300"></i>
                                <span>{{ __('Competitive Earnings') }}</span>
                            </div>
                            <div class="flex items-center bg-white/10 p-3 rounded-lg">
                                <i class="fas fa-clock text-2xl mr-2 text-green-300"></i>
                                <span>{{ __('Flexible Hours') }}</span>
                            </div>
                            <div class="flex items-center bg-white/10 p-3 rounded-lg">
                                <i class="fas fa-shield-alt text-2xl mr-2 text-red-300"></i>
                                <span>{{ __('Secure Platform') }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="hidden md:flex md:w-1/3 p-8 items-center justify-center">
                        <div class="bg-white/20 p-6 rounded-full">
                            <img src="https://cdn-icons-png.flaticon.com/512/4175/4175166.png" alt="Driver Illustration" class="h-48 mx-auto animate-pulse">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Registration Steps -->
            <div class="bg-white overflow-hidden shadow-xl rounded-xl" x-data="registrationForm">
                <div class="p-8 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-white">
                    <h3 class="text-2xl font-bold text-gray-800 mb-6">{{ __('Complete Your Registration') }}</h3>

                    <!-- Progress Steps -->
                    <div class="mb-10">
                        <div class="w-full flex items-center">
                            <template x-for="step in 4" :key="step">
                                <div class="flex items-center relative">
                                    <!-- Step Circle -->
                                    <button @click="goToStep(step)" 
                                        class="w-14 h-14 rounded-full flex items-center justify-center transition-all duration-500 ease-in-out relative z-10"
                                        :class="{
                                            'bg-teal-600 text-white shadow-lg': currentStep >= step,
                                            'bg-white text-gray-400 border-2 border-gray-300': currentStep < step
                                        }">
                                        <span x-show="step === 1" class="text-xl"><i class="fas fa-user"></i></span>
                                        <span x-show="step === 2" class="text-xl"><i class="fas fa-id-card"></i></span>
                                        <span x-show="step === 3" class="text-xl"><i class="fas fa-file-alt"></i></span>
                                        <span x-show="step === 4" class="text-xl"><i class="fas fa-check"></i></span>
                                    </button>
                                    
                                    <!-- Step Label -->
                                    <div class="absolute top-16 -ml-6 text-center w-24 text-xs font-medium"
                                        :class="{
                                            'text-teal-600 font-semibold': currentStep >= step,
                                            'text-gray-500': currentStep < step
                                        }">
                                        <span x-show="step === 1">{{ __('Personal Info') }}</span>
                                        <span x-show="step === 2">{{ __('License Info') }}</span>
                                        <span x-show="step === 3">{{ __('Documents') }}</span>
                                        <span x-show="step === 4">{{ __('Review') }}</span>
                                    </div>
                                    
                                    <!-- Connector Line (except for last step) -->
                                    <div x-show="step < 4" class="flex-auto border-t-2 transition duration-500 ease-in-out w-24 mx-2"
                                        :class="{
                                            'border-teal-600': currentStep > step,
                                            'border-gray-300': currentStep <= step
                                        }">
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>

                    <form method="POST" action="{{ route('drivers.store-registration') }}" enctype="multipart/form-data" id="driver-registration-form" class="space-y-8">
                        @csrf

                        <!-- Step 1: Personal Information Section -->
                        <div x-show="currentStep === 1" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 transform -translate-x-4" x-transition:enter-end="opacity-100 transform translate-x-0">
                            <div class="bg-gradient-to-r from-teal-50 to-blue-50 p-6 rounded-xl shadow-sm border border-teal-100">
                                <div class="flex items-center mb-4">
                                    <div class="bg-teal-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-3">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <h4 class="text-lg font-semibold text-gray-900">{{ __('Personal Information') }}</h4>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <!-- Bio -->
                                    <div class="col-span-2">
                                        <x-input-label for="bio" :value="__('Bio')" />
                                        <textarea id="bio" name="bio" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">{{ old('bio') }}</textarea>
                                        <p class="mt-1 text-sm text-gray-500">{{ __('Tell us about yourself and your driving experience.') }}</p>
                                        <x-input-error :messages="$errors->get('bio')" class="mt-2" />
                                    </div>

                                    <!-- Experience Years -->
                                    <div>
                                        <x-input-label for="experience_years" :value="__('Years of Driving Experience')" />
                                        <select id="experience_years" name="experience_years" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                            <option value="">{{ __('Select years of experience') }}</option>
                                            @for ($i = 1; $i <= 20; $i++)
                                                <option value="{{ $i }}" {{ old('experience_years') == $i ? 'selected' : '' }}>{{ $i }} {{ $i == 1 ? __('year') : __('years') }}</option>
                                            @endfor
                                            <option value="20+">{{ __('More than 20 years') }}</option>
                                        </select>
                                        <x-input-error :messages="$errors->get('experience_years')" class="mt-2" />
                                    </div>

                                    <!-- Languages -->
                                    <div>
                                        <x-input-label for="languages" :value="__('Languages Spoken')" />
                                        <div class="mt-1 grid grid-cols-2 gap-2">
                                            @php
                                                $languages = ['English', 'Spanish', 'French', 'German', 'Italian', 'Portuguese', 'Arabic', 'Chinese', 'Japanese', 'Russian'];
                                                $oldLanguages = old('languages', []);
                                            @endphp

                                            @foreach ($languages as $language)
                                                <div class="flex items-center">
                                                    <input type="checkbox" id="language_{{ $loop->index }}" name="languages[]" value="{{ $language }}"
                                                        class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                                                        {{ in_array($language, $oldLanguages) ? 'checked' : '' }}>
                                                    <label for="language_{{ $loop->index }}" class="ml-2 text-sm text-gray-700">{{ $language }}</label>
                                                </div>
                                            @endforeach
                                        </div>
                                        <x-input-error :messages="$errors->get('languages')" class="mt-2" />
                                    </div>

                                    <!-- Hourly Rate -->
                                    <div>
                                        <x-input-label for="hourly_rate" :value="__('Hourly Rate (Optional)')" />
                                        <div class="mt-1 relative rounded-md shadow-sm">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <span class="text-gray-500 sm:text-sm">$</span>
                                            </div>
                                            <input type="number" step="0.01" min="0" id="hourly_rate" name="hourly_rate" value="{{ old('hourly_rate') }}"
                                                class="block w-full pl-7 pr-12 border-gray-300 rounded-md focus:border-indigo-500 focus:ring-indigo-500">
                                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                                <span class="text-gray-500 sm:text-sm">/hour</span>
                                            </div>
                                        </div>
                                        <x-input-error :messages="$errors->get('hourly_rate')" class="mt-2" />
                                    </div>

                                    <!-- Daily Rate -->
                                    <div>
                                        <x-input-label for="daily_rate" :value="__('Daily Rate')" />
                                        <div class="mt-1 relative rounded-md shadow-sm">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <span class="text-gray-500 sm:text-sm">$</span>
                                            </div>
                                            <input type="number" step="0.01" min="0" id="daily_rate" name="daily_rate" value="{{ old('daily_rate') }}"
                                                class="block w-full pl-7 pr-12 border-gray-300 rounded-md focus:border-indigo-500 focus:ring-indigo-500" required>
                                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                                <span class="text-gray-500 sm:text-sm">/day</span>
                                            </div>
                                        </div>
                                        <x-input-error :messages="$errors->get('daily_rate')" class="mt-2" />
                                    </div>

                                    <!-- Availability -->
                                    <div>
                                        <x-input-label :value="__('Availability')" />
                                        <div class="mt-2 space-y-2">
                                            <div class="flex items-center">
                                                <input type="checkbox" id="available_weekdays" name="available_weekdays" value="1"
                                                    class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                                                    {{ old('available_weekdays', true) ? 'checked' : '' }}>
                                                <label for="available_weekdays" class="ml-2 text-sm text-gray-700">{{ __('Available on weekdays') }}</label>
                                            </div>
                                            <div class="flex items-center">
                                                <input type="checkbox" id="available_weekends" name="available_weekends" value="1"
                                                    class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                                                    {{ old('available_weekends', true) ? 'checked' : '' }}>
                                                <label for="available_weekends" class="ml-2 text-sm text-gray-700">{{ __('Available on weekends') }}</label>
                                            </div>
                                        </div>
                                        <x-input-error :messages="$errors->get('available_weekdays')" class="mt-2" />
                                        <x-input-error :messages="$errors->get('available_weekends')" class="mt-2" />
                                    </div>

                                    <!-- City -->
                                    <div>
                                        <x-input-label for="city_id" :value="__('City')" />
                                        <select id="city_id" name="city_id" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                            <option value="">{{ __('Select your city') }}</option>
                                            @foreach($cities as $city)
                                                <option value="{{ $city->id }}" {{ old('city_id') == $city->id ? 'selected' : '' }}>{{ $city->name }}</option>
                                            @endforeach
                                        </select>
                                        <x-input-error :messages="$errors->get('city_id')" class="mt-2" />
                                    </div>

                                    <!-- Profile Image -->
                                    <div>
                                        <x-file-upload
                                            name="profile_image_id"
                                            label="Profile Image"
                                            :multiple="false"
                                            accept="image/*"
                                            :required="true"
                                            :fileIds="old('profile_image_id', '')"
                                        />
                                        <p class="mt-1 text-sm text-gray-500">{{ __('Upload a professional photo of yourself.') }}</p>
                                        <x-input-error :messages="$errors->get('profile_image_id')" class="mt-2" />
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flex justify-between mt-8">
                                <div></div> <!-- Empty div for spacing -->
                                <button type="button" @click="nextStep" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition-all duration-300">
                                    {{ __('Next Step') }}
                                    <i class="fas fa-arrow-right ml-2"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Step 2: Driver's License Section -->
                        <div x-show="currentStep === 2" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 transform translate-x-4" x-transition:enter-end="opacity-100 transform translate-x-0">
                            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-xl shadow-sm border border-blue-100">
                                <div class="flex items-center mb-4">
                                    <div class="bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-3">
                                        <i class="fas fa-id-card"></i>
                                    </div>
                                    <h4 class="text-lg font-semibold text-gray-900">{{ __('Driver\'s License Information') }}</h4>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <!-- License Number -->
                                    <div>
                                        <x-input-label for="license_number" :value="__('License Number')" />
                                        <input type="text" id="license_number" name="license_number" value="{{ old('license_number') }}"
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                        <x-input-error :messages="$errors->get('license_number')" class="mt-2" />
                                    </div>

                                    <!-- License Expiry -->
                                    <div>
                                        <x-input-label for="license_expiry" :value="__('License Expiry Date')" />
                                        <input type="date" id="license_expiry" name="license_expiry" value="{{ old('license_expiry') }}"
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                        <x-input-error :messages="$errors->get('license_expiry')" class="mt-2" />
                                    </div>

                                    <!-- License Front Image -->
                                    <div>
                                        <x-file-upload
                                            name="license_front_image_id"
                                            label="License Front Image"
                                            :multiple="false"
                                            accept="image/*"
                                            :required="true"
                                            :fileIds="old('license_front_image_id', '')"
                                        />
                                        <x-input-error :messages="$errors->get('license_front_image_id')" class="mt-2" />
                                    </div>

                                    <!-- License Back Image -->
                                    <div>
                                        <x-file-upload
                                            name="license_back_image_id"
                                            label="License Back Image"
                                            :multiple="false"
                                            accept="image/*"
                                            :required="false"
                                            :fileIds="old('license_back_image_id', '')"
                                        />
                                        <x-input-error :messages="$errors->get('license_back_image_id')" class="mt-2" />
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flex justify-between mt-8">
                                <button type="button" @click="prevStep" class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-300">
                                    <i class="fas fa-arrow-left mr-2"></i>
                                    {{ __('Previous') }}
                                </button>
                                <button type="button" @click="nextStep" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-300">
                                    {{ __('Next Step') }}
                                    <i class="fas fa-arrow-right ml-2"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Step 3: Required Documents Section -->
                        <div x-show="currentStep === 3" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 transform translate-x-4" x-transition:enter-end="opacity-100 transform translate-x-0">
                            <div class="bg-gradient-to-r from-indigo-50 to-purple-50 p-6 rounded-xl shadow-sm border border-indigo-100">
                                <div class="flex items-center mb-4">
                                    <div class="bg-indigo-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-3">
                                        <i class="fas fa-file-alt"></i>
                                    </div>
                                    <h4 class="text-lg font-semibold text-gray-900">{{ __('Required Documents') }}</h4>
                                </div>
                                
                                <div class="bg-white p-4 rounded-lg mb-6 border border-indigo-100">
                                    <p class="text-sm text-gray-600">
                                        <i class="fas fa-info-circle text-indigo-500 mr-2"></i>
                                        {{ __('Please upload the following required documents to complete your registration.') }}
                                    </p>
                                </div>

                                <div class="space-y-6">
                                    <!-- Proof of Address -->
                                    <div class="bg-white p-5 rounded-lg shadow-sm border border-gray-200 hover:border-indigo-300 transition-all duration-300">
                                        <x-file-upload
                                            name="proof_of_address_id"
                                            label="Proof of Address"
                                            :multiple="false"
                                            accept="image/*,.pdf"
                                            :required="true"
                                            :fileIds="old('proof_of_address_id', '')"
                                        />
                                        <p class="mt-3 text-sm text-gray-500">
                                            <i class="fas fa-lightbulb text-yellow-500 mr-1"></i>
                                            {{ __('Upload a utility bill, bank statement, or government-issued document showing your current address.') }}
                                        </p>
                                        <x-input-error :messages="$errors->get('proof_of_address_id')" class="mt-2" />
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flex justify-between mt-8">
                                <button type="button" @click="prevStep" class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-300">
                                    <i class="fas fa-arrow-left mr-2"></i>
                                    {{ __('Previous') }}
                                </button>
                                <button type="button" @click="nextStep" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-300">
                                    {{ __('Review Application') }}
                                    <i class="fas fa-arrow-right ml-2"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Step 4: Review and Submit -->
                        <div x-show="currentStep === 4" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 transform translate-x-4" x-transition:enter-end="opacity-100 transform translate-x-0">
                            <div class="bg-gradient-to-r from-green-50 to-teal-50 p-6 rounded-xl shadow-sm border border-green-100">
                                <div class="flex items-center mb-4">
                                    <div class="bg-green-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-3">
                                        <i class="fas fa-check"></i>
                                    </div>
                                    <h4 class="text-lg font-semibold text-gray-900">{{ __('Review Your Application') }}</h4>
                                </div>
                                
                                <div class="bg-white p-5 rounded-lg shadow-sm border border-gray-200 mb-6">
                                    <p class="text-gray-700">
                                        <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                                        {{ __('Please review all the information you\'ve provided. Once submitted, our team will review your application and get back to you within 2-3 business days.') }}
                                    </p>
                                </div>

                                <!-- Terms and Conditions -->
                                <div class="bg-white p-5 rounded-lg shadow-sm border border-gray-200 hover:border-green-300 transition-all duration-300">
                                    <div class="flex items-start">
                                        <div class="flex items-center h-5 mt-1">
                                            <input id="terms" name="terms" type="checkbox" class="h-5 w-5 rounded border-gray-300 text-green-600 focus:ring-green-500" required>
                                        </div>
                                        <div class="ml-3 text-sm">
                                            <label for="terms" class="font-medium text-gray-700">{{ __('I agree to the terms and conditions') }}</label>
                                            <p class="text-gray-500 mt-1">{{ __('By checking this box, you agree to our Terms of Service and Privacy Policy. You also confirm that all information provided is accurate and that you have the legal right to work as a driver.') }}</p>
                                        </div>
                                    </div>
                                    <x-input-error :messages="$errors->get('terms')" class="mt-2" />
                                </div>
                            </div>
                            
                            <div class="flex justify-between mt-8">
                                <button type="button" @click="prevStep" class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-300">
                                    <i class="fas fa-arrow-left mr-2"></i>
                                    {{ __('Previous') }}
                                </button>
                                <button type="submit" 
                                    class="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-300"
                                    :disabled="isSubmitting"
                                    @click="submitForm">
                                    <span x-show="!isSubmitting">{{ __('Submit Application') }}</span>
                                    <span x-show="isSubmitting" class="flex items-center">
                                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        {{ __('Submitting...') }}
                                    </span>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Include File Management Modal -->
    @include('components.file-management.file-management-modal')
</x-app-backend-layout>

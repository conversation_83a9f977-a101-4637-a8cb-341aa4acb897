<x-app-backend-layout>
    <div class="p-6">
        <div class="mb-6">
            <div class="flex justify-between items-center">
                <h1 class="text-2xl font-bold text-gray-800">
                    {{ $vehicle->year }} {{ $vehicle->make }} {{ $vehicle->model }}
                </h1>
                <div class="flex space-x-2">
                    <a href="{{ route('vehicles.index') }}" class="btn-secondary">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Vehicles
                    </a>
                    <a href="{{ route('vehicles.edit', $vehicle->id) }}" class="btn-primary">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Vehicle
                    </a>
                </div>
            </div>
            <p class="text-gray-600">License Plate: {{ $vehicle->license_plate }}</p>
        </div>

        <!-- Status Badge -->
        <div class="mb-6">
            <span class="px-3 py-1 rounded-full text-sm font-semibold
                @if($vehicle->status === 'active') bg-green-100 text-green-800
                @elseif($vehicle->status === 'pending') bg-yellow-100 text-yellow-800
                @elseif($vehicle->status === 'rejected') bg-red-100 text-red-800
                @elseif($vehicle->status === 'draft') bg-blue-100 text-blue-800
                @else bg-gray-100 text-gray-800 @endif">
                {{ ucfirst($vehicle->status) }}
            </span>

            @if($vehicle->is_featured)
                <span class="ml-2 px-3 py-1 rounded-full text-sm font-semibold bg-purple-100 text-purple-800">
                    Featured
                </span>
            @endif

            @if($vehicle->status === 'rejected' && $vehicle->rejection_reason)
                <div class="mt-2 p-3 bg-red-50 border border-red-200 rounded-md">
                    <h3 class="text-sm font-semibold text-red-800">Rejection Reason:</h3>
                    <p class="text-sm text-red-700">{{ $vehicle->rejection_reason }}</p>
                </div>
            @endif
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Left Column: Vehicle Images -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="p-4 border-b border-gray-200">
                        <h2 class="text-lg font-semibold text-gray-800">Vehicle Images</h2>
                    </div>

                    <div class="p-4">
                        @if($vehicle->images->count() > 0)
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                @foreach($vehicle->images as $image)
                                    <div class="relative rounded-lg overflow-hidden border border-gray-200 @if($image->is_primary) ring-2 ring-blue-500 @endif">
                                        <img src="{{ $image->url }}"
                                             alt="{{ $vehicle->make }} {{ $vehicle->model }}"
                                             class="w-full h-48 object-cover">

                                        @if($image->is_primary)
                                            <div class="absolute top-2 left-2 bg-blue-500 text-white text-xs px-2 py-1 rounded">
                                                Primary Image
                                            </div>
                                        @endif

                                        <div class="absolute bottom-2 right-2 bg-gray-800 bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                                            {{ ucfirst($image->type) }}
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-8 text-gray-500">
                                <i class="fas fa-image text-4xl mb-2"></i>
                                <p>No images available</p>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Vehicle Documents -->
                <div class="mt-6 bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="p-4 border-b border-gray-200">
                        <h2 class="text-lg font-semibold text-gray-800">Vehicle Documents</h2>
                    </div>

                    <div class="p-4">
                        @if($vehicle->documents->count() > 0)
                            <div class="space-y-4">
                                @foreach($vehicle->documents as $document)
                                    <div class="border rounded-lg overflow-hidden">
                                        <div class="flex justify-between items-center p-4 bg-gray-50 border-b">
                                            <div>
                                                <h3 class="font-medium text-gray-900">{{ $document->documentType->name }}</h3>
                                                <p class="text-sm text-gray-500">{{ $document->documentType->description }}</p>
                                            </div>
                                            <div>
                                                <span class="px-2 py-1 rounded-full text-xs font-semibold
                                                    @if($document->status === 'approved') bg-green-100 text-green-800
                                                    @elseif($document->status === 'pending') bg-yellow-100 text-yellow-800
                                                    @else bg-red-100 text-red-800 @endif">
                                                    {{ ucfirst($document->status) }}
                                                </span>
                                            </div>
                                        </div>

                                        <div class="p-4">
                                            @if($document->file)
                                                <div class="flex items-center justify-between">
                                                    <div class="flex items-center">
                                                        <i class="fas fa-file-alt text-blue-500 text-2xl mr-3"></i>
                                                        <div>
                                                            <p class="font-medium">{{ $document->file_name }}</p>
                                                            @if($document->expiry_date)
                                                                <p class="text-sm text-gray-500">Expires: {{ $document->expiry_date->format('M d, Y') }}</p>
                                                            @endif
                                                        </div>
                                                    </div>
                                                    <a href="{{ asset('storage/' . $document->file_path) }}" target="_blank" class="btn-secondary text-sm">
                                                        <i class="fas fa-eye mr-1"></i> View
                                                    </a>
                                                </div>

                                                @if($document->status === 'rejected' && $document->rejection_reason)
                                                    <div class="mt-3 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                                                        <strong>Rejection reason:</strong> {{ $document->rejection_reason }}
                                                    </div>
                                                @endif

                                                @if(auth()->user()->hasRole(['admin', 'agent']) && $document->status === 'pending')
                                                    <div class="mt-4 flex space-x-2 border-t pt-4">
                                                        <form method="POST" action="{{ route('vehicle-documents.approve', $document->id) }}" class="inline">
                                                            @csrf
                                                            @method('PUT')
                                                            <button type="submit" class="btn-primary bg-green-600 hover:bg-green-700 text-sm">
                                                                <i class="fas fa-check mr-1"></i> Approve
                                                            </button>
                                                        </form>

                                                        <button type="button"
                                                                onclick="openRejectModal('{{ $document->id }}', '{{ $document->documentType->name }}')"
                                                                class="btn-primary bg-red-600 hover:bg-red-700 text-sm">
                                                            <i class="fas fa-times mr-1"></i> Reject
                                                        </button>
                                                    </div>
                                                @endif
                                            @else
                                                <div class="text-center py-4 text-gray-500">
                                                    <p>File not found</p>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-8 text-gray-500">
                                <i class="fas fa-file-alt text-4xl mb-2"></i>
                                <p>No documents available</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Right Column: Vehicle Details -->
            <div>
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="p-4 border-b border-gray-200">
                        <h2 class="text-lg font-semibold text-gray-800">Vehicle Details</h2>
                    </div>

                    <div class="p-4">
                        <dl class="space-y-4">
                            <div class="grid grid-cols-2 gap-4">
                                <dt class="text-sm font-medium text-gray-500">Owner</dt>
                                <dd class="text-sm text-gray-900">{{ $vehicle->user->name }}</dd>
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <dt class="text-sm font-medium text-gray-500">Vehicle Type</dt>
                                <dd class="text-sm text-gray-900">{{ $vehicle->vehicleType->name }}</dd>
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <dt class="text-sm font-medium text-gray-500">Make & Model</dt>
                                <dd class="text-sm text-gray-900">{{ $vehicle->make }} {{ $vehicle->model }}</dd>
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <dt class="text-sm font-medium text-gray-500">Year</dt>
                                <dd class="text-sm text-gray-900">{{ $vehicle->year }}</dd>
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <dt class="text-sm font-medium text-gray-500">Color</dt>
                                <dd class="text-sm text-gray-900">{{ $vehicle->color }}</dd>
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <dt class="text-sm font-medium text-gray-500">Mileage</dt>
                                <dd class="text-sm text-gray-900">{{ number_format($vehicle->mileage) }} km</dd>
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <dt class="text-sm font-medium text-gray-500">Transmission</dt>
                                <dd class="text-sm text-gray-900">{{ ucfirst($vehicle->transmission) }}</dd>
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <dt class="text-sm font-medium text-gray-500">Fuel Type</dt>
                                <dd class="text-sm text-gray-900">{{ ucfirst(str_replace('_', ' ', $vehicle->fuel_type)) }}</dd>
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <dt class="text-sm font-medium text-gray-500">Seats</dt>
                                <dd class="text-sm text-gray-900">{{ $vehicle->seats }}</dd>
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <dt class="text-sm font-medium text-gray-500">Doors</dt>
                                <dd class="text-sm text-gray-900">{{ $vehicle->doors }}</dd>
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <dt class="text-sm font-medium text-gray-500">Daily Rate</dt>
                                <dd class="text-sm text-gray-900">${{ number_format($vehicle->daily_rate, 2) }}</dd>
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <dt class="text-sm font-medium text-gray-500">With Driver</dt>
                                <dd class="text-sm text-gray-900">{{ $vehicle->with_driver ? 'Yes' : 'No' }}</dd>
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <dt class="text-sm font-medium text-gray-500">Location</dt>
                                <dd class="text-sm text-gray-900">{{ $vehicle->address }}</dd>
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <dt class="text-sm font-medium text-gray-500">City</dt>
                                <dd class="text-sm text-gray-900">{{ $vehicle->city->name }}</dd>
                            </div>
                        </dl>
                    </div>
                </div>

                <!-- Features -->
                <div class="mt-6 bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="p-4 border-b border-gray-200">
                        <h2 class="text-lg font-semibold text-gray-800">Features</h2>
                    </div>

                    <div class="p-4">
                        @if(is_array($vehicle->features) && count($vehicle->features) > 0)
                            <ul class="grid grid-cols-1 md:grid-cols-2 gap-2">
                                @foreach($vehicle->features as $feature)
                                    <li class="flex items-center">
                                        <i class="fas fa-check text-green-500 mr-2"></i>
                                        <span class="text-sm">{{ ucwords(str_replace('_', ' ', $feature)) }}</span>
                                    </li>
                                @endforeach
                            </ul>
                        @else
                            <div class="text-center py-4 text-gray-500">
                                <p>No features specified</p>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Description -->
                <div class="mt-6 bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="p-4 border-b border-gray-200">
                        <h2 class="text-lg font-semibold text-gray-800">Description</h2>
                    </div>

                    <div class="p-4">
                        @if($vehicle->description)
                            <p class="text-sm text-gray-700">{{ $vehicle->description }}</p>
                        @else
                            <div class="text-center py-4 text-gray-500">
                                <p>No description provided</p>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Admin Actions -->
                @if(auth()->user()->hasRole(['admin', 'agent']))
                    <div class="mt-6 bg-white rounded-lg shadow-md overflow-hidden">
                        <div class="p-4 border-b border-gray-200">
                            <h2 class="text-lg font-semibold text-gray-800">Admin Actions</h2>
                        </div>

                        <div class="p-4">
                            <form method="POST" action="{{ route('vehicles.update-status', $vehicle->id) }}" id="statusForm">
                                @csrf
                                <div class="space-y-4">
                                    <div>
                                        <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                                        <select id="status" name="status" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                                            <option value="pending" {{ $vehicle->status === 'pending' ? 'selected' : '' }}>Pending</option>
                                            <option value="active" {{ $vehicle->status === 'active' ? 'selected' : '' }}>Active</option>
                                            <option value="rejected" {{ $vehicle->status === 'rejected' ? 'selected' : '' }}>Rejected</option>
                                            <option value="inactive" {{ $vehicle->status === 'inactive' ? 'selected' : '' }}>Inactive</option>
                                        </select>
                                    </div>

                                    <div id="rejectionReasonContainer" class="{{ $vehicle->status !== 'rejected' ? 'hidden' : '' }}">
                                        <label for="rejection_reason" class="block text-sm font-medium text-gray-700">Rejection Reason</label>
                                        <textarea id="rejection_reason" name="rejection_reason" rows="3" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">{{ $vehicle->rejection_reason }}</textarea>
                                    </div>

                                    <div class="flex justify-end">
                                        <button type="submit" class="btn-primary">
                                            <i class="fas fa-save mr-2"></i>
                                            Update Status
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Document Rejection Modal -->
    <div id="rejectModal" class="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50 z-50 hidden">
        <div class="bg-white p-6 rounded-lg shadow-lg w-[500px] max-w-full">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Reject Document</h3>

            <form method="POST" id="rejectForm" action="">
                @csrf
                @method('PUT')

                <div class="mb-4">
                    <p class="text-sm text-gray-600">You are rejecting: <span id="documentName" class="font-medium"></span></p>
                </div>

                <div class="mb-4">
                    <label for="modal_rejection_reason" class="block text-sm font-medium text-gray-700">Rejection Reason</label>
                    <textarea id="modal_rejection_reason" name="rejection_reason" rows="3" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required></textarea>
                </div>

                <div class="flex justify-end space-x-2">
                    <button type="button" onclick="closeRejectModal()" class="btn-secondary">
                        Cancel
                    </button>
                    <button type="submit" class="btn-primary bg-red-600 hover:bg-red-700">
                        <i class="fas fa-times mr-1"></i> Reject Document
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Show/hide rejection reason based on status
        document.getElementById('status').addEventListener('change', function() {
            const rejectionContainer = document.getElementById('rejectionReasonContainer');
            if (this.value === 'rejected') {
                rejectionContainer.classList.remove('hidden');
            } else {
                rejectionContainer.classList.add('hidden');
            }
        });

        // Document rejection modal functions
        function openRejectModal(documentId, documentName) {
            document.getElementById('rejectForm').action = "{{ url('management/vehicle-documents') }}/" + documentId + "/reject";
            document.getElementById('documentName').textContent = documentName;
            document.getElementById('rejectModal').classList.remove('hidden');
        }

        function closeRejectModal() {
            document.getElementById('rejectModal').classList.add('hidden');
            document.getElementById('modal_rejection_reason').value = '';
        }
    </script>
</x-app-backend-layout>

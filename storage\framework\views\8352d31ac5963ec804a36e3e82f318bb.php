<!-- File Management Modal with Alpine.js -->
<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['name' => '', 'label' => 'File', 'multiple' => false, 'accept' => '', 'required' => false, 'fileIds' => '', 'primaryFileId' => '']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['name' => '', 'label' => 'File', 'multiple' => false, 'accept' => '', 'required' => false, 'fileIds' => '', 'primaryFileId' => '']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div
    x-data="{
        openFileManager() {
            console.log('Opening file manager for <?php echo e($name); ?>');
            window.dispatchEvent(new CustomEvent('open-file-manager', {
                detail: {
                    name: '<?php echo e($name); ?>',
                    isMultiple: <?php echo e($multiple ? 'true' : 'false'); ?>,
                    accept: '<?php echo e($accept); ?>',
                    fileIds: '<?php echo e($fileIds); ?>'
                }
            }));
        }
    }"
>
    <div class="mb-4" x-data="{
        selectedFileIds: '<?php echo e($fileIds); ?>',
        selectedFiles: [],

        init() {
            // Listen for file selection events
            window.addEventListener('file-ids-updated', (event) => {
                if (event.detail.name === '<?php echo e($name); ?>') {
                    this.selectedFileIds = event.detail.value;
                    this.selectedFiles = event.detail.files || [];
                }
            });

            // If we have initial file IDs, fetch the file details
            if (this.selectedFileIds) {
                this.fetchSelectedFiles();
            }
        },

        fetchSelectedFiles() {
            const fileIds = this.selectedFileIds.split(',').filter(id => id.trim() !== '');
            if (fileIds.length === 0) return;

            // Fetch file details from the API
            fetch(`/api/files?ids=${fileIds.join(',')}`)
                .then(response => response.json())
                .then(data => {
                    this.selectedFiles = data.data || [];
                })
                .catch(error => {
                    console.error('Error fetching file details:', error);
                });
        },

        removeFile(fileId) {
            // Remove the file from the selected files
            this.selectedFiles = this.selectedFiles.filter(file => file.id.toString() !== fileId.toString());

            // Update the selected file IDs
            const fileIds = this.selectedFiles.map(file => file.id);
            this.selectedFileIds = fileIds.join(',');

            // Update the hidden input value
            document.getElementById('<?php echo e($name); ?>').value = this.selectedFileIds;

            // Dispatch event for Alpine.js components to listen for
            window.dispatchEvent(new CustomEvent('file-ids-updated', {
                detail: {
                    name: '<?php echo e($name); ?>',
                    value: this.selectedFileIds,
                    files: this.selectedFiles
                }
            }));
        }
    }">
        <?php if($label): ?>
            <label for="<?php echo e($name); ?>" class="block text-sm font-medium text-gray-700 mb-1">
                <?php echo e($label); ?> <?php if($required): ?><span class="text-red-500">*</span><?php endif; ?>
            </label>
        <?php endif; ?>

        <div class="flex items-center space-x-2">
            <button
                type="button"
                @click="openFileManager()"
                class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
                <i class="fas fa-folder-open mr-2"></i>
                <?php echo e($multiple ? 'Select Files' : 'Select File'); ?>

            </button>

            <input
                type="hidden"
                name="<?php echo e($name); ?>"
                id="<?php echo e($name); ?>"
                :value="selectedFileIds"
                <?php echo e($required ? 'required' : ''); ?>

            >

            <span class="text-sm text-gray-500" x-show="selectedFileIds !== ''">
                <span x-text="selectedFiles.length + ' <?php echo e($multiple ? 'files' : 'file'); ?> selected'"></span>
            </span>
        </div>

        <!-- Selected files preview -->
        <div class="mt-2" x-show="selectedFiles.length > 0">
            <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2">
                <template x-for="file in selectedFiles" :key="file.id">
                    <div class="relative group">
                        <!-- Image preview -->
                        <div class="border border-gray-200 rounded overflow-hidden h-20">
                            <template x-if="file.mime_type && file.mime_type.startsWith('image/')">
                                <img :src="file.url" :alt="file.original_name" class="w-full h-full object-cover">
                            </template>
                            <template x-if="!file.mime_type || !file.mime_type.startsWith('image/')">
                                <div class="w-full h-full flex items-center justify-center bg-gray-100">
                                    <i class="fas fa-file text-gray-400 text-xl"></i>
                                </div>
                            </template>
                        </div>

                        <!-- File name -->
                        <div class="text-xs truncate mt-1" :title="file.original_name" x-text="file.original_name"></div>

                        <!-- Remove button -->
                        <button
                            @click="removeFile(file.id)"
                            class="absolute top-0 right-0 bg-red-500 text-white rounded-bl p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                            title="Remove file"
                        >
                            <i class="fas fa-times text-xs"></i>
                        </button>
                    </div>
                </template>
            </div>
        </div>
    </div>
</div>

<div
    x-data="fileManager()"
    x-init="initialize()"
    id="file-management-modal"
    class="fixed inset-0 z-50 flex items-center justify-center bg-gray-900 bg-opacity-50 overflow-auto"
    x-show="isOpen"
    @keydown.escape.window="close()"
    role="dialog"
    aria-modal="true"
    aria-labelledby="modal-title"
>
    <div
        class="bg-white p-6 rounded-lg w-5/6 lg:w-3/4 xl:w-2/3 max-w-5xl relative overflow-hidden my-8 max-h-[90vh] flex flex-col"
        @click.outside="close()"
        x-transition:enter="transition ease-out duration-300"
        x-transition:enter-start="opacity-0 transform scale-95"
        x-transition:enter-end="opacity-100 transform scale-100"
        x-transition:leave="transition ease-in duration-200"
        x-transition:leave-start="opacity-100 transform scale-100"
        x-transition:leave-end="opacity-0 transform scale-95"
    >
        <!-- Header -->
        <div class="flex justify-between items-center border-b border-gray-200 pb-4">
            <h2 id="modal-title" class="text-xl font-bold">File Management</h2>
            <button
                @click="close()"
                class="text-gray-500 hover:text-gray-800 focus:outline-none focus:ring-2 focus:ring-indigo-300 rounded-full p-1"
                aria-label="Close modal"
            >
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <!-- Tabs -->
        <div class="mb-4 border-b border-gray-200">
            <ul class="flex flex-wrap -mb-px text-sm font-medium" role="tablist">
                <li class="me-2" role="presentation">
                    <button
                        @click="activeTab = 'files'; loadFiles(1)"
                        :class="{'bg-indigo-600 text-white': activeTab === 'files', 'text-gray-700 hover:text-indigo-600 hover:border-indigo-300': activeTab !== 'files'}"
                        class="inline-block p-4 rounded-t-lg border-b-2 border-transparent transition-all duration-200"
                        role="tab"
                        aria-controls="files-tab"
                        :aria-selected="activeTab === 'files'"
                        id="files-tab-button"
                    >
                        All Files
                    </button>
                </li>
                <li class="me-2" role="presentation">
                    <button
                        @click="activeTab = 'upload'"
                        :class="{'bg-indigo-600 text-white': activeTab === 'upload', 'text-gray-700 hover:text-indigo-600 hover:border-indigo-300': activeTab !== 'upload'}"
                        class="inline-block p-4 rounded-t-lg border-b-2 border-transparent transition-all duration-200"
                        role="tab"
                        aria-controls="upload-tab"
                        :aria-selected="activeTab === 'upload'"
                        id="upload-tab-button"
                    >
                        Upload File
                    </button>
                </li>
            </ul>
        </div>

        <!-- Action Buttons and Search (only visible on Files tab) -->
        <div class="flex flex-col sm:flex-row justify-between gap-4 my-4" x-show="activeTab === 'files'" id="files-tab" role="tabpanel" aria-labelledby="files-tab-button">
            <div class="flex flex-wrap gap-2">
                <button
                    @click="close()"
                    class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded focus:outline-none focus:ring-2 focus:ring-gray-300 transition-all"
                >
                    Cancel
                </button>
                <button
                    @click="selectFiles()"
                    class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded focus:outline-none focus:ring-2 focus:ring-indigo-300 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
                    :disabled="selectedFiles.length === 0"
                >
                    <span x-text="selectedFiles.length > 0 ? `Select (${selectedFiles.length})` : 'Select Files'"></span>
                </button>

                <button
                    @click="selectedFiles = []; selectedFileObjects = []"
                    x-show="selectedFiles.length > 0"
                    class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded focus:outline-none focus:ring-2 focus:ring-red-300 transition-all"
                >
                    Clear Selection
                </button>

            </div>
            <div class="relative">
                <input
                    type="text"
                    id="file-search"
                    x-model="searchQuery"
                    @input="handleSearch()"
                    placeholder="Search files..."
                    class="form-control pl-10 pr-4 py-2 border border-gray-300 rounded w-full focus:outline-none focus:ring-2 focus:ring-indigo-300 focus:border-indigo-300"
                    aria-label="Search files"
                >
                <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Files Tab Content -->
        <div
            x-show="activeTab === 'files'"
            class="flex-grow overflow-hidden flex flex-col"
            id="files-content"
        >
            <!-- Loading Indicator with improved feedback -->
            <div x-show="isLoading" class="flex flex-col justify-center items-center h-64">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mb-4"></div>
                <p class="text-gray-500" x-text="searchQuery ? 'Searching files...' : 'Loading files...'"></p>
            </div>

            <!-- Status message for screen readers -->
            <div aria-live="polite" class="sr-only" x-text="ariaAnnouncement"></div>

            <!-- Selection status indicator -->
            <div
                x-show="selectedFiles.length > 0"
                class="sticky top-0 bg-indigo-100 p-2 rounded mb-2 text-sm font-medium text-indigo-700 flex justify-between items-center"
            >
                <span x-text="`${selectedFiles.length} ${selectedFiles.length === 1 ? 'file' : 'files'} selected`"></span>
                <button @click="selectedFiles = []; selectedFileObjects = []" class="text-indigo-500 hover:text-indigo-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Selection utilities -->
            <div class="flex flex-wrap gap-2 mb-2" x-show="files && files.length > 0 && currentContext?.isMultiple">
                <button
                    @click="selectAllFiles()"
                    class="text-sm text-indigo-600 hover:text-indigo-800"
                    x-show="files && files.length > selectedFiles.length"
                >
                    <i class="fas fa-check-square mr-1"></i> Select All
                </button>
                <button
                    @click="selectedFiles = []; selectedFileObjects = []"
                    class="text-sm text-indigo-600 hover:text-indigo-800"
                    x-show="selectedFiles.length > 0"
                >
                    <i class="fas fa-square mr-1"></i> Deselect All
                </button>
            </div>

            <!-- Updated File Grid with extracted component -->
            <div
                x-show="!isLoading && files && files.length > 0"
                class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 overflow-y-auto p-2"
                id="file-grid"
                role="grid"
                aria-label="File selection grid"
                @keydown.arrow-right="focusNextFile('right')"
                @keydown.arrow-left="focusNextFile('left')"
                @keydown.arrow-up="focusNextFile('up')"
                @keydown.arrow-down="focusNextFile('down')"
            >
                <!-- File card template using the extracted component -->
                <template x-for="file in files" :key="file.id">
                    <?php echo $__env->make('components.file-management._file-card', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                </template>
            </div>

            <!-- No Files Message with Font Awesome -->
            <div
                x-show="!isLoading && (!files || files.length === 0)"
                class="flex flex-col items-center justify-center h-64 text-gray-500 text-center p-4"
            >
                <i class="fas fa-file-image text-6xl mb-4 text-gray-400"></i>
                <p>No files found</p>
                <p x-show="searchQuery" class="text-sm mt-2">Try adjusting your search criteria</p>
            </div>

            <!-- Improved Pagination with Page Numbers -->
            <div
                x-show="!isLoading && totalPages > 1"
                class="flex justify-center items-center space-x-2 mt-4 p-2"
            >
                <button
                    @click="loadFiles(1)"
                    :disabled="currentPage === 1"
                    class="px-2 py-1 rounded border border-gray-300"
                    :class="{'bg-gray-200 text-gray-400': currentPage === 1, 'hover:bg-gray-100': currentPage !== 1}"
                    aria-label="First page"
                >
                    <i class="fas fa-angle-double-left"></i>
                </button>

                <button
                    @click="loadFiles(currentPage - 1)"
                    :disabled="currentPage === 1"
                    class="px-2 py-1 rounded border border-gray-300"
                    :class="{'bg-gray-200 text-gray-400': currentPage === 1, 'hover:bg-gray-100': currentPage !== 1}"
                    aria-label="Previous page"
                >
                    <i class="fas fa-angle-left"></i>
                </button>

                <!-- Page numbers - limited to avoid clutter -->
                <template x-for="page in getPaginationRange()" :key="page">
                    <button
                        @click="loadFiles(page)"
                        class="px-3 py-1 rounded border"
                        :class="{
                            'bg-indigo-600 text-white border-indigo-600': currentPage === page,
                            'border-gray-300 hover:bg-gray-100': currentPage !== page
                        }"
                        x-text="page"
                    ></button>
                </template>

                <button
                    @click="loadFiles(currentPage + 1)"
                    :disabled="currentPage === totalPages"
                    class="px-2 py-1 rounded border border-gray-300"
                    :class="{'bg-gray-200 text-gray-400': currentPage === totalPages, 'hover:bg-gray-100': currentPage !== totalPages}"
                    aria-label="Next page"
                >
                    <i class="fas fa-angle-right"></i>
                </button>

                <button
                    @click="loadFiles(totalPages)"
                    :disabled="currentPage === totalPages"
                    class="px-2 py-1 rounded border border-gray-300"
                    :class="{'bg-gray-200 text-gray-400': currentPage === totalPages, 'hover:bg-gray-100': currentPage !== totalPages}"
                    aria-label="Last page"
                >
                    <i class="fas fa-angle-double-right"></i>
                </button>
            </div>
        </div>

        <!-- Upload Tab -->
        <div
            x-show="activeTab === 'upload'"
            class="tab-content flex-grow overflow-y-auto"
            id="upload-tab"
            role="tabpanel"
            aria-labelledby="upload-tab-button"
        >
            <div x-data="fileUploader()">
            <div
                class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center transition-colors duration-300"
                :class="{
                    'border-indigo-500 bg-indigo-50': isDragOver,
                    'hover:border-indigo-300 hover:bg-indigo-50': !isDragOver
                }"
                @dragover.prevent="isDragOver = true"
                @dragleave.prevent="isDragOver = false"
                @drop.prevent="handleFileDrop($event)"
            >
                <input
                    type="file"
                    id="file-upload"
                    class="hidden"
                    multiple
                    @change="handleFileSelect($event)"
                    x-ref="fileInput"
                    accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx"
                >
                <div class="flex flex-col items-center justify-center space-y-4">
                    <svg class="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>

                    <!-- Upload progress -->
                    <div x-show="uploadingFiles && uploadingFiles.length > 0" class="w-full">
                        <div class="mb-2 flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-700">Uploading files...</span>
                            <div class="flex items-center gap-2">
                                <span class="text-sm text-gray-500" x-text="uploadingFiles.length + ' file(s) - ' + formatFileSize(uploadingFiles.reduce((total, file) => total + file.size, 0))"></span>
                                <button
                                    @click="uploadingFiles = []"
                                    class="text-xs text-red-500 hover:text-red-700"
                                    title="Cancel all uploads"
                                >
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>

                        <template x-for="file in uploadingFiles" :key="file.id">
                            <div class="mb-2">
                                <div class="flex justify-between mb-1">
                                    <span class="text-xs font-medium text-gray-700 truncate max-w-[60%]" x-text="file.name"></span>
                                    <div class="flex items-center gap-2">
                                        <span class="text-xs text-gray-500" x-text="formatFileSize(file.size)"></span>
                                        <button
                                            @click="removeUploadingFile(file.id)"
                                            class="text-xs text-gray-400 hover:text-red-500"
                                            title="Cancel upload"
                                        >
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2.5">
                                    <div
                                        class="bg-indigo-600 h-2.5 rounded-full progress-bar transition-all duration-300"
                                        :style="{ width: file.progress + '%' }"
                                        :class="{ 'bg-red-500': file.error }"
                                    ></div>
                                </div>
                                <p x-show="file.error" x-text="file.error" class="text-xs text-red-500 mt-1"></p>
                                <p x-show="file.progress === 100 && !file.error" class="text-xs text-green-500 mt-1">Upload complete</p>
                            </div>
                        </template>
                    </div>

                    <p x-show="!uploadingFiles || uploadingFiles.length === 0" class="text-gray-600">
                        Drag and drop files here or
                        <button
                            type="button"
                            @click="$refs.fileInput.click()"
                            class="text-indigo-600 hover:text-indigo-800 underline focus:outline-none"
                        >
                            browse
                        </button>
                    </p>
                    <p class="text-xs text-gray-500">
                        Supports: JPG, PNG, GIF, PDF, DOC, XLSX (Max 10MB per file)
                    </p>
                </div>
            </div>

            <!-- File Preview List -->
            <div x-show="uploadingFiles && uploadingFiles.length > 0" class="mt-6">
                <h3 class="text-lg font-semibold mb-4">Files to Upload</h3>
                <div class="space-y-4">
                    <template x-for="(file, index) in uploadingFiles" :key="file.name">
                        <div class="flex items-center justify-between bg-gray-100 p-4 rounded-lg">
                            <div class="flex items-center space-x-4">
                                <div class="w-12 h-12 flex-shrink-0 bg-gray-200 rounded-lg flex items-center justify-center">
                                    <svg x-show="file.type && file.type.startsWith('image/')" class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    <svg x-show="!file.type || !file.type.startsWith('image/')" class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <p x-text="file.name" class="font-medium truncate max-w-xs"></p>
                                    <p x-text="formatFileSize(file.size)" class="text-xs text-gray-500"></p>
                                </div>
                            </div>
                            <button
                                @click="uploadingFiles.splice(index, 1)"
                                class="text-red-500 hover:text-red-700 focus:outline-none"
                                aria-label="Remove file"
                            >
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    </template>
                </div>

                <!-- Upload Progress and Submit -->
                <div class="mt-6 flex justify-between items-center">
                    <div class="text-sm text-gray-600">
                        Total Files: <span x-text="uploadingFiles ? uploadingFiles.length : 0"></span>
                        | Total Size: <span x-text="formatFileSize(uploadingFiles ? uploadingFiles.reduce((total, file) => total + file.size, 0) : 0)"></span>
                    </div>
                    <div class="flex space-x-4">
                        <button
                            @click="uploadingFiles = []"
                            class="bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded transition-colors"
                        >
                            Clear All
                        </button>
                        <button
                            @click="activeTab = 'files'; loadFiles(1)"
                            :disabled="!uploadingFiles || uploadingFiles.length === 0"
                            class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            Upload Files
                        </button>
                    </div>
                </div>
            </div>
            </div>
        </div>


    </div>
</div>

<style>
    [x-cloak] { display: none !important; }

    .file-card.selected {
        border-color: #4f46e5;
        background-color: #e0e7ff;
    }

    .progress-bar {
        transition: width 0.3s ease-in-out;
    }

    .file-image-container {
        overflow: hidden;
        position: relative;
    }

    .file-image-container img {
        transition: transform 0.3s ease;
    }

    .file-image-container:hover img {
        transform: scale(1.05);
    }
</style>

<!-- Include the JavaScript file -->
<?php $__env->startPush('scripts'); ?>
<script>
    // Utility functions
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function fileManager() {
        return {
            // State variables
            isOpen: false,
            activeTab: 'files',
            files: [],
            isLoading: false,
            searchQuery: '',
            selectedFiles: [],
            selectedFileObjects: [],
            currentPage: 1,
            totalPages: 1,
            perPage: 20,
            ariaAnnouncement: '',
            currentContext: null,

            // Initialize the component
            initialize() {
                console.log('Initializing file manager');
                // Listen for open events
                window.addEventListener('open-file-manager', (event) => {
                    console.log('Received open-file-manager event', event.detail);
                    this.open(event.detail);
                });

                // Listen for file upload complete events
                window.addEventListener('file-upload-complete', (event) => {
                    if (this.activeTab === 'files') {
                        this.loadFiles(1);
                    }
                });
            },

            // Open the file manager
            open(context = null) {
                console.log('Opening file manager with context', context);
                this.currentContext = context;
                this.isOpen = true;
                this.activeTab = 'files';
                this.selectedFiles = [];
                this.selectedFileObjects = [];

                // If we have pre-selected file IDs, set them
                if (context && context.fileIds) {
                    const fileIdArray = context.fileIds.split(',').filter(id => id.trim() !== '');
                    this.selectedFiles = fileIdArray;
                }

                // Load files
                this.loadFiles(1);

                // Announce for screen readers
                this.ariaAnnouncement = 'File manager opened';

                // Prevent body scrolling
                document.body.style.overflow = 'hidden';
            },

            // Close the file manager
            close() {
                this.isOpen = false;
                this.currentContext = null;

                // Restore body scrolling
                document.body.style.overflow = '';

                // Announce for screen readers
                this.ariaAnnouncement = 'File manager closed';
            },

            // Load files from the server
            loadFiles(page = 1) {
                this.isLoading = true;
                this.currentPage = page;

                // Prepare the URL with query parameters
                const url = new URL('/api/files', window.location.origin);
                url.searchParams.append('page', page);
                url.searchParams.append('per_page', this.perPage);

                if (this.searchQuery) {
                    url.searchParams.append('search', this.searchQuery);
                }

                // Add filter for allowed types if specified
                if (this.currentContext && this.currentContext.accept) {
                    url.searchParams.append('types', this.currentContext.accept);
                }

                // Fetch the files
                fetch(url)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Failed to load files');
                        }
                        return response.json();
                    })
                    .then(data => {
                        this.files = data.data;
                        this.totalPages = data.meta.last_page;
                        this.currentPage = data.meta.current_page;

                        // Update selected file objects
                        this.updateSelectedFileObjects();

                        // Announce for screen readers
                        this.ariaAnnouncement = `Loaded ${this.files.length} files, page ${this.currentPage} of ${this.totalPages}`;
                    })
                    .catch(error => {
                        console.error('Error loading files:', error);
                        // Show error message
                        Toastify({
                            text: 'Failed to load files. Please try again.',
                            duration: 3000,
                            close: true,
                            gravity: 'top',
                            position: 'right',
                            backgroundColor: '#f44336',
                        }).showToast();
                    })
                    .finally(() => {
                        this.isLoading = false;
                    });
            },

            // Handle search input
            handleSearch() {
                // Debounce the search
                clearTimeout(this._searchTimeout);
                this._searchTimeout = setTimeout(() => {
                    this.loadFiles(1);
                }, 300);
            },

            // Toggle file selection
            toggleFileSelection(file) {
                const fileId = file.id.toString();
                const index = this.selectedFiles.indexOf(fileId);

                // If multiple selection is not allowed, clear previous selection
                if (!this.currentContext?.isMultiple && index === -1) {
                    this.selectedFiles = [];
                    this.selectedFileObjects = [];
                }

                // Toggle selection
                if (index === -1) {
                    // Add to selection
                    this.selectedFiles.push(fileId);
                    this.selectedFileObjects.push(file);

                    // Announce for screen readers
                    this.ariaAnnouncement = `Selected file: ${file.original_name}`;
                } else {
                    // Remove from selection
                    this.selectedFiles.splice(index, 1);
                    this.selectedFileObjects = this.selectedFileObjects.filter(f => f.id.toString() !== fileId);

                    // Announce for screen readers
                    this.ariaAnnouncement = `Deselected file: ${file.original_name}`;
                }
            },

            // Check if a file is selected
            isFileSelected(fileId) {
                return this.selectedFiles.includes(fileId.toString());
            },

            // Select all files
            selectAllFiles() {
                // Only select files that match the current context's accept filter
                this.selectedFiles = this.files.map(file => file.id.toString());
                this.selectedFileObjects = [...this.files];

                // Announce for screen readers
                this.ariaAnnouncement = `Selected all ${this.files.length} files`;
            },

            // Update selected file objects based on selected IDs
            updateSelectedFileObjects() {
                // Create a map of file IDs to file objects
                const fileMap = this.files.reduce((map, file) => {
                    map[file.id.toString()] = file;
                    return map;
                }, {});

                // Update selected file objects
                this.selectedFileObjects = this.selectedFiles
                    .map(id => fileMap[id])
                    .filter(file => file !== undefined);
            },

            // Select files and close the modal
            selectFiles() {
                if (this.selectedFiles.length === 0) {
                    return;
                }

                // Prepare the selected file IDs
                const fileIds = this.selectedFiles.join(',');

                // Dispatch event with selected file IDs
                if (this.currentContext && this.currentContext.name) {
                    // Update the hidden input value
                    const inputElement = document.getElementById(this.currentContext.name);
                    if (inputElement) {
                        inputElement.value = fileIds;
                    }

                    // Dispatch event for Alpine.js components to listen for
                    window.dispatchEvent(new CustomEvent('file-ids-updated', {
                        detail: {
                            name: this.currentContext.name,
                            value: fileIds,
                            files: this.selectedFileObjects
                        }
                    }));
                }

                // Close the modal
                this.close();

                // Announce for screen readers
                this.ariaAnnouncement = `Selected ${this.selectedFiles.length} files and closed file manager`;

                // Show success message
                Toastify({
                    text: `Selected ${this.selectedFiles.length} ${this.selectedFiles.length === 1 ? 'file' : 'files'}`,
                    duration: 3000,
                    close: true,
                    gravity: 'top',
                    position: 'right',
                    backgroundColor: '#4caf50',
                }).showToast();
            },

            // Get pagination range
            getPaginationRange() {
                const range = [];
                const maxVisiblePages = 5;

                if (this.totalPages <= maxVisiblePages) {
                    // Show all pages
                    for (let i = 1; i <= this.totalPages; i++) {
                        range.push(i);
                    }
                } else {
                    // Show a subset of pages
                    let start = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
                    let end = Math.min(this.totalPages, start + maxVisiblePages - 1);

                    // Adjust start if end is at max
                    if (end === this.totalPages) {
                        start = Math.max(1, end - maxVisiblePages + 1);
                    }

                    for (let i = start; i <= end; i++) {
                        range.push(i);
                    }
                }

                return range;
            },

            // Focus next file in grid
            focusNextFile(direction) {
                const grid = document.getElementById('file-grid');
                const cards = Array.from(grid.querySelectorAll('.file-card'));
                const currentFocus = document.activeElement;

                // Find the index of the currently focused card
                const currentIndex = cards.findIndex(card => card === currentFocus || card.contains(currentFocus));

                if (currentIndex === -1) {
                    // No card is focused, focus the first one
                    if (cards.length > 0) {
                        cards[0].focus();
                    }
                    return;
                }

                // Calculate the number of columns
                const gridComputedStyle = window.getComputedStyle(grid);
                const gridTemplateColumns = gridComputedStyle.getPropertyValue('grid-template-columns');
                const columnCount = gridTemplateColumns.split(' ').length;

                // Calculate the next index based on direction
                let nextIndex = currentIndex;

                switch (direction) {
                    case 'right':
                        nextIndex = Math.min(cards.length - 1, currentIndex + 1);
                        break;
                    case 'left':
                        nextIndex = Math.max(0, currentIndex - 1);
                        break;
                    case 'up':
                        nextIndex = Math.max(0, currentIndex - columnCount);
                        break;
                    case 'down':
                        nextIndex = Math.min(cards.length - 1, currentIndex + columnCount);
                        break;
                }

                // Focus the next card
                if (nextIndex !== currentIndex && cards[nextIndex]) {
                    cards[nextIndex].focus();
                }
            }
        };
    }

    function fileUploader() {
        return {
            isDragOver: false,
            uploadProgress: {},
            uploadingFiles: [],

            // Handle file drop
            handleFileDrop(event) {
                this.isDragOver = false;
                const files = event.dataTransfer.files;
                this.uploadFiles(files);
            },

            // Handle file select from input
            handleFileSelect(event) {
                const files = event.target.files;
                this.uploadFiles(files);
            },

            // Upload files to the server
            uploadFiles(files) {
                if (!files || files.length === 0) {
                    return;
                }

                // Convert FileList to array
                const fileArray = Array.from(files);
                this.uploadingFiles = [...this.uploadingFiles, ...fileArray.map(file => ({
                    name: file.name,
                    size: file.size,
                    type: file.type,
                    progress: 0,
                    error: null,
                    id: Math.random().toString(36).substring(2, 11)
                }))];

                // Upload each file
                fileArray.forEach((file, index) => {
                    const fileId = this.uploadingFiles[this.uploadingFiles.length - fileArray.length + index].id;
                    this.uploadFile(file, fileId);
                });
            },

            // Upload a single file
            uploadFile(file, fileId) {
                const formData = new FormData();
                formData.append('file', file);

                // Add CSRF token
                const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
                if (csrfToken) {
                    formData.append('_token', csrfToken);
                }

                // Create upload request
                const xhr = new XMLHttpRequest();
                xhr.open('POST', '/api/files/upload', true);

                // Track upload progress
                xhr.upload.onprogress = (event) => {
                    if (event.lengthComputable) {
                        const progress = Math.round((event.loaded / event.total) * 100);
                        this.updateFileProgress(fileId, progress);
                    }
                };

                // Handle response
                xhr.onload = () => {
                    if (xhr.status >= 200 && xhr.status < 300) {
                        // Success
                        const response = JSON.parse(xhr.responseText);
                        this.updateFileProgress(fileId, 100);

                        // Remove file from uploading list after a delay
                        setTimeout(() => {
                            this.removeUploadingFile(fileId);

                            // Dispatch event to notify that a file upload is complete
                            window.dispatchEvent(new CustomEvent('file-upload-complete', {
                                detail: {
                                    file: response.data
                                }
                            }));

                            // Show success message
                            Toastify({
                                text: 'File uploaded successfully',
                                duration: 3000,
                                close: true,
                                gravity: 'top',
                                position: 'right',
                                backgroundColor: '#4caf50',
                            }).showToast();
                        }, 1000);
                    } else {
                        // Error
                        let errorMessage = 'Upload failed';
                        try {
                            const response = JSON.parse(xhr.responseText);
                            errorMessage = response.message || errorMessage;
                        } catch (e) {
                            console.error('Error parsing response:', e);
                        }

                        this.updateFileError(fileId, errorMessage);

                        // Show error message
                        Toastify({
                            text: errorMessage,
                            duration: 3000,
                            close: true,
                            gravity: 'top',
                            position: 'right',
                            backgroundColor: '#f44336',
                        }).showToast();
                    }
                };

                // Handle network errors
                xhr.onerror = () => {
                    this.updateFileError(fileId, 'Network error occurred');

                    // Show error message
                    Toastify({
                        text: 'Network error occurred',
                        duration: 3000,
                        close: true,
                        gravity: 'top',
                        position: 'right',
                        backgroundColor: '#f44336',
                    }).showToast();
                };

                // Send the request
                xhr.send(formData);
            },

            // Update file progress
            updateFileProgress(fileId, progress) {
                const index = this.uploadingFiles.findIndex(file => file.id === fileId);
                if (index !== -1) {
                    this.uploadingFiles[index].progress = progress;
                }
            },

            // Update file error
            updateFileError(fileId, error) {
                const index = this.uploadingFiles.findIndex(file => file.id === fileId);
                if (index !== -1) {
                    this.uploadingFiles[index].error = error;
                }
            },

            // Remove uploading file
            removeUploadingFile(fileId) {
                this.uploadingFiles = this.uploadingFiles.filter(file => file.id !== fileId);
            },

            // Format file size
            formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';

                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));

                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            },

            // Open file input
            openFileInput() {
                this.$refs.fileInput.click();
            }
        };
    }
</script>
<?php $__env->stopPush(); ?><?php /**PATH C:\laragon\www\carbnb\resources\views\components\file-management\file-management-modal.blade.php ENDPATH**/ ?>
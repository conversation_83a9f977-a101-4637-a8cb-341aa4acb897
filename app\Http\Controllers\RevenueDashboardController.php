<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Booking;
use App\Models\Vehicle;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class RevenueDashboardController extends Controller
{
    /**
     * Display the revenue dashboard.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // In a real application, you would fetch actual data from the database
        // For now, we'll just return the view with sample data that will be populated via JavaScript
        
        return view('front.default.revenue-dashboard');
    }

    /**
     * Get revenue statistics for the authenticated user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getRevenueStats(Request $request)
    {
        $user = Auth::user();
        $period = $request->input('period', 'month'); // Default to month
        
        // Initialize data structure
        $data = [
            'totalEarnings' => 0,
            'periodEarnings' => 0,
            'periodComparison' => 0,
            'completedBookings' => 0,
            'occupancyRate' => 0,
            'recentBookings' => [],
            'chartData' => []
        ];
        
        // Total earnings
        $data['totalEarnings'] = Booking::where('vehicle_id', function($query) use ($user) {
                $query->select('id')
                    ->from('vehicles')
                    ->where('user_id', $user->id);
            })
            ->where('status', 'completed')
            ->sum('total_amount');
            
        // Period earnings (month/week/year)
        $startDate = null;
        $endDate = Carbon::now();
        
        switch($period) {
            case 'week':
                $startDate = Carbon::now()->subWeek();
                break;
            case 'month':
                $startDate = Carbon::now()->subMonth();
                break;
            case 'year':
                $startDate = Carbon::now()->subYear();
                break;
        }
        
        $data['periodEarnings'] = Booking::where('vehicle_id', function($query) use ($user) {
                $query->select('id')
                    ->from('vehicles')
                    ->where('user_id', $user->id);
            })
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('total_amount');
            
        // Previous period for comparison
        $prevStartDate = $startDate->copy()->subDays($startDate->diffInDays($endDate));
        $prevEndDate = $startDate;
        
        $prevPeriodEarnings = Booking::where('vehicle_id', function($query) use ($user) {
                $query->select('id')
                    ->from('vehicles')
                    ->where('user_id', $user->id);
            })
            ->where('status', 'completed')
            ->whereBetween('created_at', [$prevStartDate, $prevEndDate])
            ->sum('total_amount');
            
        if ($prevPeriodEarnings > 0) {
            $data['periodComparison'] = (($data['periodEarnings'] - $prevPeriodEarnings) / $prevPeriodEarnings) * 100;
        }
        
        // Completed bookings
        $data['completedBookings'] = Booking::where('vehicle_id', function($query) use ($user) {
                $query->select('id')
                    ->from('vehicles')
                    ->where('user_id', $user->id);
            })
            ->where('status', 'completed')
            ->count();
            
        // Occupancy rate calculation
        $userVehicles = Vehicle::where('user_id', $user->id)->get();
        $totalAvailableDays = 0;
        $totalBookedDays = 0;
        
        foreach ($userVehicles as $vehicle) {
            // Calculate days since vehicle was added or last 90 days, whichever is less
            $vehicleAddedDays = $vehicle->created_at->diffInDays(Carbon::now());
            $availableDays = min($vehicleAddedDays, 90); // Consider last 90 days maximum
            $totalAvailableDays += $availableDays;
            
            // Calculate booked days for this vehicle
            $bookedDays = Booking::where('vehicle_id', $vehicle->id)
                ->where('status', 'completed')
                ->whereDate('end_date', '>=', Carbon::now()->subDays(90))
                ->sum(DB::raw('DATEDIFF(end_date, start_date) + 1')); // +1 to include both start and end dates
                
            $totalBookedDays += $bookedDays;
        }
        
        if ($totalAvailableDays > 0) {
            $data['occupancyRate'] = round(($totalBookedDays / $totalAvailableDays) * 100);
        }
        
        // Recent bookings
        $data['recentBookings'] = Booking::whereIn('vehicle_id', function($query) use ($user) {
                $query->select('id')
                    ->from('vehicles')
                    ->where('user_id', $user->id);
            })
            ->with(['vehicle'])
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get()
            ->map(function($booking) {
                return [
                    'id' => $booking->id,
                    'car' => $booking->vehicle->make . ' ' . $booking->vehicle->model,
                    'dates' => $booking->start_date->format('M d') . '-' . $booking->end_date->format('d, Y'),
                    'amount' => $booking->total_amount,
                    'status' => $booking->status
                ];
            });
            
        // Chart data
        switch($period) {
            case 'week':
                $data['chartData'] = $this->getWeeklyChartData($user->id);
                break;
            case 'month':
                $data['chartData'] = $this->getMonthlyChartData($user->id);
                break;
            case 'year':
                $data['chartData'] = $this->getYearlyChartData($user->id);
                break;
        }
        
        return response()->json($data);
    }
    
    /**
     * Get weekly chart data for revenue visualization.
     *
     * @param  int  $userId
     * @return array
     */
    private function getWeeklyChartData($userId)
    {
        $startDate = Carbon::now()->startOfWeek();
        $endDate = Carbon::now()->endOfWeek();
        
        $dailyRevenue = Booking::whereIn('vehicle_id', function($query) use ($userId) {
                $query->select('id')
                    ->from('vehicles')
                    ->where('user_id', $userId);
            })
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('SUM(total_amount) as amount')
            )
            ->groupBy('date')
            ->get()
            ->keyBy('date');
            
        $chartData = [];
        $dayLabels = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
        
        for ($i = 0; $i < 7; $i++) {
            $date = $startDate->copy()->addDays($i);
            $dateString = $date->format('Y-m-d');
            
            $chartData[] = [
                'label' => $dayLabels[$i],
                'amount' => isset($dailyRevenue[$dateString]) ? $dailyRevenue[$dateString]->amount : 0
            ];
        }
        
        return $chartData;
    }
    
    /**
     * Get monthly chart data for revenue visualization.
     *
     * @param  int  $userId
     * @return array
     */
    private function getMonthlyChartData($userId)
    {
        $startDate = Carbon::now()->startOfYear();
        $endDate = Carbon::now()->endOfYear();
        
        $monthlyRevenue = Booking::whereIn('vehicle_id', function($query) use ($userId) {
                $query->select('id')
                    ->from('vehicles')
                    ->where('user_id', $userId);
            })
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->select(
                DB::raw('MONTH(created_at) as month'),
                DB::raw('SUM(total_amount) as amount')
            )
            ->groupBy('month')
            ->get()
            ->keyBy('month');
            
        $chartData = [];
        $monthLabels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        
        for ($i = 1; $i <= 12; $i++) {
            $chartData[] = [
                'month' => $monthLabels[$i-1],
                'amount' => isset($monthlyRevenue[$i]) ? $monthlyRevenue[$i]->amount : 0
            ];
        }
        
        return $chartData;
    }
    
    /**
     * Get yearly chart data for revenue visualization.
     *
     * @param  int  $userId
     * @return array
     */
    private function getYearlyChartData($userId)
    {
        $startYear = Carbon::now()->subYears(5)->year;
        $endYear = Carbon::now()->year;
        
        $yearlyRevenue = Booking::whereIn('vehicle_id', function($query) use ($userId) {
                $query->select('id')
                    ->from('vehicles')
                    ->where('user_id', $userId);
            })
            ->where('status', 'completed')
            ->where('created_at', '>=', Carbon::createFromDate($startYear, 1, 1))
            ->select(
                DB::raw('YEAR(created_at) as year'),
                DB::raw('SUM(total_amount) as amount')
            )
            ->groupBy('year')
            ->get()
            ->keyBy('year');
            
        $chartData = [];
        
        for ($year = $startYear; $year <= $endYear; $year++) {
            $chartData[] = [
                'year' => (string)$year,
                'amount' => isset($yearlyRevenue[$year]) ? $yearlyRevenue[$year]->amount : 0
            ];
        }
        
        return $chartData;
    }
    
    /**
     * Get vehicles owned by the authenticated user.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserVehicles()
    {
        $user = Auth::user();
        
        $vehicles = Vehicle::where('user_id', $user->id)
            ->where('status', 'active')
            ->select('id', 'make', 'model', 'year', 'daily_rate')
            ->get();
            
        return response()->json($vehicles);
    }
}
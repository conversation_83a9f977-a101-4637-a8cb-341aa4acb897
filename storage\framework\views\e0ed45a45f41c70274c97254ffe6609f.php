
<div 
    class="file-card bg-white border-2 rounded-lg shadow-sm p-2 relative cursor-pointer transition duration-200 hover:shadow-md"
    :id="'file-card-' + file.id"
    :data-id="file.id"
    @click="toggleFileSelection(file)"
    :class="{
        'border-indigo-500 bg-indigo-50 ring-2 ring-indigo-300 shadow-md': isFileSelected(file.id),
        'border-gray-200': !isFileSelected(file.id)
    }"
    role="gridcell"
    tabindex="0"
    @keydown.space.prevent="toggleFileSelection(file)"
    @keydown.enter="toggleFileSelection(file)"
    :aria-selected="isFileSelected(file.id)"
>
    <input
        type="checkbox"
        class="absolute top-2 left-2 rounded text-indigo-600 focus:ring-indigo-500 border-gray-300 z-10"
        :id="'checkbox-' + file.id"
        :checked="isFileSelected(file.id)"
        @click.stop="toggleFileSelection(file)"
        :aria-label="'Select ' + file.original_name"
    >
    
    <!-- Selected overlay -->
    <div
        x-show="isFileSelected(file.id)"
        class="absolute inset-0 bg-indigo-100 bg-opacity-30 z-0"
    ></div>
    
    <div class="file-image-container overflow-hidden rounded relative z-0">
        <!-- Image files -->
        <template x-if="file.mime_type && file.mime_type.startsWith('image/')">
            <img 
                :src="file.url" 
                :alt="file.original_name" 
                class="w-full h-32 object-cover transition-transform duration-300"
                loading="lazy"
                onerror="this.onerror=null; this.src='/images/fallback-image.jpg'"
            >
        </template>
        
        <!-- Non-image files - show appropriate icon -->
        <template x-if="!file.mime_type || !file.mime_type.startsWith('image/')">
            <div class="w-full h-32 flex items-center justify-center bg-gray-100">
                <i 
                    class="fas fa-file text-gray-400 text-5xl"
                ></i>
            </div>
        </template>
        
        <!-- Selected checkmark overlay -->
        <div 
            x-show="isFileSelected(file.id)" 
            class="absolute top-0 right-0 bg-indigo-600 text-white rounded-bl-lg p-1 shadow-md"
        >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
        </div>
    </div>
    
    <div class="flex justify-between items-center mt-2 relative z-0">
        <span 
            class="text-sm font-medium truncate max-w-[70%]" 
            :title="file.original_name"
            x-text="file.original_name"
        ></span>
        <span 
            class="text-xs text-gray-500" 
            x-text="Math.round(file.size / 1024) + ' KB'"
        ></span>
    </div>
</div><?php /**PATH C:\laragon\www\carbnb\resources\views\components\file-management\_file-card.blade.php ENDPATH**/ ?>
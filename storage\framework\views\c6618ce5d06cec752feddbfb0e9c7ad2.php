<?php if (isset($component)) { $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742 = $attributes; } ?>
<?php $component = App\View\Components\AppBackendLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-backend-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppBackendLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                <?php echo e(__('Dispute')); ?> #<?php echo e($dispute->dispute_number); ?>

            </h2>
            <div class="flex space-x-2">
                <a href="<?php echo e(route('disputes.index')); ?>" class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-500 active:bg-gray-700 focus:outline-none focus:border-gray-700 focus:ring ring-gray-300 disabled:opacity-25 transition ease-in-out duration-150">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    <?php echo e(__('Back to Disputes')); ?>

                </a>
                <a href="<?php echo e(route('booking.show', $dispute->booking->id)); ?>" class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-500 active:bg-green-700 focus:outline-none focus:border-green-700 focus:ring ring-green-300 disabled:opacity-25 transition ease-in-out duration-150">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                    <?php echo e(__('View Booking')); ?>

                </a>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <?php if(session('success')): ?>
                <div class="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded">
                    <?php echo e(session('success')); ?>

                </div>
            <?php endif; ?>

            <?php if(session('error')): ?>
                <div class="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
                    <?php echo e(session('error')); ?>

                </div>
            <?php endif; ?>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Dispute Details -->
                <div class="md:col-span-1">
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4"><?php echo e(__('Dispute Details')); ?></h3>
                            
                            <div class="space-y-4">
                                <div>
                                    <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo e(__('Status')); ?></p>
                                    <div class="mt-1">
                                        <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full 
                                            <?php if($dispute->status == 'open'): ?> bg-blue-100 text-blue-800
                                            <?php elseif($dispute->status == 'under_review'): ?> bg-yellow-100 text-yellow-800
                                            <?php elseif($dispute->status == 'resolved'): ?> bg-green-100 text-green-800
                                            <?php elseif($dispute->status == 'closed'): ?> bg-gray-100 text-gray-800
                                            <?php endif; ?>">
                                            <?php echo e(ucfirst(str_replace('_', ' ', $dispute->status))); ?>

                                        </span>
                                    </div>
                                </div>
                                
                                <div>
                                    <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo e(__('Type')); ?></p>
                                    <p class="font-medium text-gray-900 dark:text-gray-100"><?php echo e(ucfirst(str_replace('_', ' ', $dispute->type))); ?></p>
                                </div>
                                
                                <div>
                                    <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo e(__('Filed By')); ?></p>
                                    <p class="font-medium text-gray-900 dark:text-gray-100"><?php echo e($dispute->user->first_name); ?> <?php echo e($dispute->user->last_name); ?></p>
                                </div>
                                
                                <div>
                                    <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo e(__('Filed Against')); ?></p>
                                    <p class="font-medium text-gray-900 dark:text-gray-100"><?php echo e($dispute->againstUser->first_name); ?> <?php echo e($dispute->againstUser->last_name); ?></p>
                                </div>
                                
                                <div>
                                    <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo e(__('Date Filed')); ?></p>
                                    <p class="font-medium text-gray-900 dark:text-gray-100"><?php echo e($dispute->created_at->format('M d, Y g:i A')); ?></p>
                                </div>
                                
                                <?php if($dispute->amount_claimed): ?>
                                    <div>
                                        <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo e(__('Amount Claimed')); ?></p>
                                        <p class="font-medium text-gray-900 dark:text-gray-100">$<?php echo e(number_format($dispute->amount_claimed, 2)); ?></p>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if($dispute->status === 'resolved' || $dispute->status === 'closed'): ?>
                                    <div>
                                        <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo e(__('Resolved By')); ?></p>
                                        <p class="font-medium text-gray-900 dark:text-gray-100"><?php echo e($dispute->resolvedBy->first_name); ?> <?php echo e($dispute->resolvedBy->last_name); ?></p>
                                    </div>
                                    
                                    <div>
                                        <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo e(__('Resolved On')); ?></p>
                                        <p class="font-medium text-gray-900 dark:text-gray-100"><?php echo e($dispute->resolved_at->format('M d, Y g:i A')); ?></p>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <?php if(Auth::user()->hasRole('admin') && $dispute->status !== 'closed'): ?>
                                <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                                    <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2"><?php echo e(__('Update Status')); ?></h4>
                                    
                                    <form action="<?php echo e(route('disputes.update-status', $dispute->id)); ?>" method="POST">
                                        <?php echo csrf_field(); ?>
                                        <div class="space-y-4">
                                            <div>
                                                <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300"><?php echo e(__('Status')); ?></label>
                                                <select id="status" name="status" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md" required>
                                                    <option value="open" <?php echo e($dispute->status === 'open' ? 'selected' : ''); ?>><?php echo e(__('Open')); ?></option>
                                                    <option value="under_review" <?php echo e($dispute->status === 'under_review' ? 'selected' : ''); ?>><?php echo e(__('Under Review')); ?></option>
                                                    <option value="resolved" <?php echo e($dispute->status === 'resolved' ? 'selected' : ''); ?>><?php echo e(__('Resolved')); ?></option>
                                                    <option value="closed" <?php echo e($dispute->status === 'closed' ? 'selected' : ''); ?>><?php echo e(__('Closed')); ?></option>
                                                </select>
                                            </div>
                                            
                                            <div>
                                                <label for="resolution_notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300"><?php echo e(__('Resolution Notes')); ?></label>
                                                <textarea id="resolution_notes" name="resolution_notes" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" placeholder="<?php echo e(__('Add resolution notes...')); ?>"><?php echo e($dispute->resolution_notes); ?></textarea>
                                            </div>
                                            
                                            <div>
                                                <button type="submit" class="w-full inline-flex justify-center items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-500 active:bg-blue-700 focus:outline-none focus:border-blue-700 focus:ring ring-blue-300 disabled:opacity-25 transition ease-in-out duration-150">
                                                    <?php echo e(__('Update Status')); ?>

                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Booking Details -->
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4"><?php echo e(__('Booking Details')); ?></h3>
                            
                            <div class="space-y-4">
                                <div>
                                    <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo e(__('Booking Number')); ?></p>
                                    <p class="font-medium text-gray-900 dark:text-gray-100"><?php echo e($dispute->booking->booking_number); ?></p>
                                </div>
                                
                                <div>
                                    <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo e(__('Vehicle')); ?></p>
                                    <p class="font-medium text-gray-900 dark:text-gray-100"><?php echo e($dispute->booking->vehicle->make); ?> <?php echo e($dispute->booking->vehicle->model); ?> (<?php echo e($dispute->booking->vehicle->year); ?>)</p>
                                </div>
                                
                                <div>
                                    <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo e(__('Booking Dates')); ?></p>
                                    <p class="font-medium text-gray-900 dark:text-gray-100"><?php echo e($dispute->booking->start_date->format('M d, Y')); ?> - <?php echo e($dispute->booking->end_date->format('M d, Y')); ?></p>
                                </div>
                                
                                <div>
                                    <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo e(__('Renter')); ?></p>
                                    <p class="font-medium text-gray-900 dark:text-gray-100"><?php echo e($dispute->booking->user->first_name); ?> <?php echo e($dispute->booking->user->last_name); ?></p>
                                </div>
                                
                                <div>
                                    <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo e(__('Owner')); ?></p>
                                    <p class="font-medium text-gray-900 dark:text-gray-100"><?php echo e($dispute->booking->vehicle->user->first_name); ?> <?php echo e($dispute->booking->vehicle->user->last_name); ?></p>
                                </div>
                                
                                <div>
                                    <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo e(__('Total Amount')); ?></p>
                                    <p class="font-medium text-gray-900 dark:text-gray-100">$<?php echo e(number_format($dispute->booking->total_amount, 2)); ?></p>
                                </div>
                                
                                <div>
                                    <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo e(__('Status')); ?></p>
                                    <p class="font-medium text-gray-900 dark:text-gray-100"><?php echo e(ucfirst($dispute->booking->status)); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Evidence -->
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100"><?php echo e(__('Evidence')); ?></h3>
                                
                                <?php if($dispute->status !== 'closed'): ?>
                                    <button type="button" onclick="toggleAddEvidenceForm()" class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs leading-4 font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                        <svg class="-ml-0.5 mr-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                        <?php echo e(__('Add Evidence')); ?>

                                    </button>
                                <?php endif; ?>
                            </div>
                            
                            <!-- Add Evidence Form (Hidden by default) -->
                            <div id="add-evidence-form" class="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hidden">
                                <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3"><?php echo e(__('Add New Evidence')); ?></h4>
                                
                                <form action="<?php echo e(route('disputes.add-evidence', $dispute->id)); ?>" method="POST">
                                    <?php echo csrf_field(); ?>
                                    <div class="space-y-4">
                                        <div>
                                            <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300"><?php echo e(__('Title')); ?></label>
                                            <input type="text" name="title" id="title" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" required>
                                        </div>
                                        
                                        <div>
                                            <label for="file_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300"><?php echo e(__('File')); ?></label>
                                            <div class="mt-1 flex items-center">
                                                <input type="hidden" name="file_id" id="evidence_file_id">
                                                <button type="button" id="file_selector" onclick="openFileManager()" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                                    <svg class="-ml-1 mr-2 h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                                                    </svg>
                                                    <?php echo e(__('Select File')); ?>

                                                </button>
                                                <span id="file_name" class="ml-3 text-sm text-gray-500"></span>
                                            </div>
                                        </div>
                                        
                                        <div>
                                            <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300"><?php echo e(__('Description')); ?></label>
                                            <textarea id="description" name="description" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"></textarea>
                                        </div>
                                        
                                        <div class="flex justify-end space-x-3">
                                            <button type="button" onclick="toggleAddEvidenceForm()" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                                <?php echo e(__('Cancel')); ?>

                                            </button>
                                            <button type="submit" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                                <?php echo e(__('Add Evidence')); ?>

                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            
                            <?php if($dispute->evidence->isEmpty()): ?>
                                <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                                    <?php echo e(__('No evidence has been submitted yet.')); ?>

                                </div>
                            <?php else: ?>
                                <div class="space-y-4">
                                    <?php $__currentLoopData = $dispute->evidence; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $evidence): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                                            <div class="flex justify-between items-start">
                                                <div>
                                                    <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100"><?php echo e($evidence->title); ?></h4>
                                                    <p class="text-xs text-gray-500 dark:text-gray-400">
                                                        <?php echo e(__('Added by')); ?> <?php echo e($evidence->user->first_name); ?> <?php echo e($evidence->user->last_name); ?> 
                                                        <?php echo e($evidence->created_at->diffForHumans()); ?>

                                                    </p>
                                                </div>
                                                <a href="<?php echo e(Storage::url('uploads/' . $evidence->file->filename)); ?>" target="_blank" class="inline-flex items-center px-2 py-1 text-xs text-blue-700 hover:text-blue-900">
                                                    <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                                                    </svg>
                                                    <?php echo e(__('Download')); ?>

                                                </a>
                                            </div>
                                            
                                            <?php if($evidence->description): ?>
                                                <div class="mt-2 text-sm text-gray-600 dark:text-gray-300">
                                                    <?php echo e($evidence->description); ?>

                                                </div>
                                            <?php endif; ?>
                                            
                                            <?php if(Str::contains($evidence->file->mime_type, 'image')): ?>
                                                <div class="mt-3">
                                                    <img src="<?php echo e(Storage::url('uploads/' . $evidence->file->filename)); ?>" alt="<?php echo e($evidence->title); ?>" class="max-w-full h-auto rounded">
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Dispute Description and Messages -->
                <div class="md:col-span-2">
                    <!-- Dispute Description -->
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4"><?php echo e(__('Dispute Description')); ?></h3>
                            <div class="prose max-w-none dark:prose-invert">
                                <?php echo e($dispute->description); ?>

                            </div>
                            
                            <?php if($dispute->resolution_notes && ($dispute->status === 'resolved' || $dispute->status === 'closed')): ?>
                                <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                                    <h4 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-2"><?php echo e(__('Resolution Notes')); ?></h4>
                                    <div class="prose max-w-none dark:prose-invert">
                                        <?php echo e($dispute->resolution_notes); ?>

                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Messages -->
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4"><?php echo e(__('Messages')); ?></h3>
                            
                            <div class="space-y-4 mb-6 max-h-[600px] overflow-y-auto p-2">
                                <?php $__currentLoopData = $dispute->messages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $message): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="p-4 rounded-lg <?php echo e($message->is_system_message ? 'bg-gray-100 dark:bg-gray-700' : ($message->is_admin_message ? 'bg-yellow-50 dark:bg-yellow-900' : ($message->user_id === Auth::id() ? 'bg-blue-50 dark:bg-blue-900' : 'bg-green-50 dark:bg-green-900'))); ?>">
                                        <div class="flex justify-between items-start">
                                            <div class="flex items-start">
                                                <div class="flex-shrink-0">
                                                    <div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center text-gray-600 font-semibold text-sm">
                                                        <?php echo e(substr($message->user->first_name, 0, 1)); ?><?php echo e(substr($message->user->last_name, 0, 1)); ?>

                                                    </div>
                                                </div>
                                                <div class="ml-3">
                                                    <p class="text-sm font-medium <?php echo e($message->is_system_message ? 'text-gray-700 dark:text-gray-300' : ($message->is_admin_message ? 'text-yellow-800 dark:text-yellow-200' : 'text-gray-900 dark:text-gray-100')); ?>">
                                                        <?php echo e($message->user->first_name); ?> <?php echo e($message->user->last_name); ?>

                                                        <?php if($message->is_admin_message): ?>
                                                            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100">
                                                                <?php echo e(__('Admin')); ?>

                                                            </span>
                                                        <?php endif; ?>
                                                        <?php if($message->is_system_message): ?>
                                                            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100">
                                                                <?php echo e(__('System')); ?>

                                                            </span>
                                                        <?php endif; ?>
                                                    </p>
                                                    <p class="text-xs text-gray-500 dark:text-gray-400">
                                                        <?php echo e($message->created_at->format('M d, Y g:i A')); ?>

                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <?php if(!$message->is_system_message): ?>
                                            <div class="mt-2 text-sm <?php echo e($message->is_admin_message ? 'text-yellow-800 dark:text-yellow-200' : 'text-gray-700 dark:text-gray-300'); ?>">
                                                <?php echo e($message->message); ?>

                                            </div>
                                        <?php else: ?>
                                            <div class="mt-2 text-sm text-gray-600 dark:text-gray-400 italic">
                                                <?php echo e($message->message); ?>

                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                            
                            <?php if($dispute->status !== 'closed'): ?>
                                <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                                    <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3"><?php echo e(__('Add Message')); ?></h4>
                                    
                                    <form action="<?php echo e(route('disputes.add-message', $dispute->id)); ?>" method="POST">
                                        <?php echo csrf_field(); ?>
                                        <div>
                                            <textarea id="message" name="message" rows="4" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" placeholder="<?php echo e(__('Type your message here...')); ?>" required></textarea>
                                        </div>
                                        
                                        <div class="mt-3 flex justify-end">
                                            <button type="submit" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-500 active:bg-blue-700 focus:outline-none focus:border-blue-700 focus:ring ring-blue-300 disabled:opacity-25 transition ease-in-out duration-150">
                                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                                </svg>
                                                <?php echo e(__('Send Message')); ?>

                                            </button>
                                        </div>
                                    </form>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleAddEvidenceForm() {
            const form = document.getElementById('add-evidence-form');
            form.classList.toggle('hidden');
        }
        
        function openFileManager() {
            const fileIdInput = document.getElementById('evidence_file_id');
            const fileNameSpan = document.getElementById('file_name');
            
            // Open file manager modal or popup here
            // This is a placeholder - you'll need to implement your file manager integration
            window.fileManagerCallback = function(fileId, fileName) {
                fileIdInput.value = fileId;
                fileNameSpan.textContent = fileName;
            };
            
            // Example: Open a modal or redirect to file manager
            window.open('/admin/file-manager?callback=fileManagerCallback&single=true', 'fileManager', 'width=900,height=600');
        }
        
        // Auto-scroll to bottom of messages on page load
        document.addEventListener('DOMContentLoaded', function() {
            const messageContainer = document.querySelector('.max-h-\\[600px\\]');
            messageContainer.scrollTop = messageContainer.scrollHeight;
        });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $attributes = $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $component = $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\carbnb\resources\views\disputes\show.blade.php ENDPATH**/ ?>
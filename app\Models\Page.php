<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Page extends Model
{
    // Define status constants
    const STATUS_PUBLISHED = 1;
    const STATUS_DRAFT = 0;
    
    protected $fillable = [
        'title',
        'slug',
        'content',
        'status',
    ];
    
    /**
     * Convert string status to integer when saving to database
     */
    public function setStatusAttribute($value)
    {
        if (is_string($value)) {
            $this->attributes['status'] = $value === 'published' ? self::STATUS_PUBLISHED : self::STATUS_DRAFT;
        } else {
            $this->attributes['status'] = $value;
        }
    }
    
    /**
     * Convert integer status to string when retrieving from database
     */
    public function getStatusAttribute($value)
    {
        return $value == self::STATUS_PUBLISHED ? 'published' : 'draft';
    }

    /**
     * Generate a unique slug
     * 
     * @param string $title
     * @return string
     */
    public static function createUniqueSlug($title)
    {
        $slug = Str::slug($title);
        
        // Check if the slug already exists
        $count = static::whereRaw("slug = ? OR slug LIKE ?", [$slug, $slug.'-%'])->count();
        
        // If we have a match, append the count to the end to make it unique
        return $count ? "{$slug}-{$count}" : $slug;
    }

    public static function boot()
    {
        parent::boot();

        static::creating(function ($page) {
            // Generate a unique slug
            $page->slug = self::createUniqueSlug($page->title);
        });
        
        static::updating(function ($page) {
            // If the title has changed, regenerate the slug
            if ($page->isDirty('title')) {
                $page->slug = self::createUniqueSlug($page->title);
            }
        });
    }
}
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Booking extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'booking_number',
        'user_id',
        'vehicle_id',
        'driver_id',
        'start_date',
        'end_date',
        'pickup_location',
        'return_location',
        'vehicle_price',
        'driver_price',
        'insurance_price',
        'extra_services_price',
        'discount_amount',
        'subtotal',
        'tax_amount',
        'total_amount',
        'security_deposit',
        'status',
        'cancellation_reason',
        'special_requests',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'vehicle_price' => 'decimal:2',
        'driver_price' => 'decimal:2',
        'insurance_price' => 'decimal:2',
        'extra_services_price' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'security_deposit' => 'decimal:2',
    ];

    /**
     * Get the user (renter) who made the booking.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the vehicle that was booked.
     */
    public function vehicle()
    {
        return $this->belongsTo(Vehicle::class);
    }

    /**
     * Get the driver assigned to the booking, if any.
     */
    public function driver()
    {
        return $this->belongsTo(Driver::class);
    }

    /**
     * Get the owner of the vehicle.
     */
    public function owner()
    {
        return $this->vehicle->user();
    }

    /**
     * Get the payments associated with this booking.
     */
    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get the status history for this booking.
     */
    public function statusHistory()
    {
        return $this->hasMany(BookingStatus::class);
    }

    /**
     * Get the items included in this booking.
     */
    public function items()
    {
        return $this->hasMany(BookingItem::class);
    }

    /**
     * Get the reviews for this booking.
     */
    public function reviews()
    {
        return $this->hasMany(Review::class);
    }

    /**
     * Generate a unique booking number.
     *
     * @return string
     */
    public static function generateBookingNumber()
    {
        $prefix = 'BK';
        $timestamp = now()->format('YmdHis');
        $random = rand(1000, 9999);

        return $prefix . $timestamp . $random;
    }

    /**
     * Calculate the duration of the booking in days.
     *
     * @return int
     */
    public function getDurationInDaysAttribute()
    {
        return $this->start_date->diffInDays($this->end_date) + 1; // Including the start day
    }

    /**
     * Check if the booking is active (confirmed or in_progress).
     *
     * @return bool
     */
    public function getIsActiveAttribute()
    {
        return in_array($this->status, ['confirmed', 'in_progress']);
    }

    /**
     * Check if the booking is completed.
     *
     * @return bool
     */
    public function getIsCompletedAttribute()
    {
        return $this->status === 'completed';
    }

    /**
     * Check if the booking is cancelled or rejected.
     *
     * @return bool
     */
    public function getIsCancelledAttribute()
    {
        return in_array($this->status, ['cancelled', 'rejected']);
    }
}

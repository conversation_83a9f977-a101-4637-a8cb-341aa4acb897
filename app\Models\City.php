<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class City extends Model
{
    protected $fillable = [
        'name',
        'address',
        'state_id',
        'latitude',
        'longitude',
        'zip_code',
        'cost',
        'is_active' // Using is_active to match the migration
    ];

    protected $casts = [
        'latitude' => 'decimal:7',
        'longitude' => 'decimal:7',
        'is_active' => 'boolean'
    ];

    public function state()
    {
        return $this->belongsTo(State::class);
    }

    public function country()
    {
        return $this->hasOneThrough(Country::class, State::class, 'id', 'id', 'state_id', 'country_id');
    }

    // Get all vehicles in this city
    public function vehicles()
    {
        return $this->hasMany(Vehicle::class);
    }

    // Get all drivers in this city
    public function drivers()
    {
        return $this->hasMany(Driver::class);
    }
}
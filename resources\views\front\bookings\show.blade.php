<x-app-layout>
<div class="container py-8">
    <div class="mb-6">
        @if(Auth::id() === $booking->user_id)
            <a href="{{ route('booking.index') }}" class="text-primary hover:text-primary-dark flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                </svg>
                Back to My Bookings
            </a>
        @else
            <a href="{{ route('booking.owner') }}" class="text-primary hover:text-primary-dark flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                </svg>
                Back to Booking Requests
            </a>
        @endif
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
                <div class="bg-gray-50 px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h1 class="text-2xl font-bold text-gray-800">Booking Details</h1>
                    <div>
                        @php
                            $statusColors = [
                                'pending' => 'bg-yellow-100 text-yellow-800',
                                'confirmed' => 'bg-blue-100 text-blue-800',
                                'in_progress' => 'bg-purple-100 text-purple-800',
                                'completed' => 'bg-green-100 text-green-800',
                                'cancelled' => 'bg-red-100 text-red-800',
                                'rejected' => 'bg-gray-100 text-gray-800',
                            ];
                            $statusColor = $statusColors[$booking->status] ?? 'bg-gray-100 text-gray-800';
                        @endphp
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium {{ $statusColor }}">
                            {{ ucfirst($booking->status) }}
                        </span>
                    </div>
                </div>

                <div class="p-6">
                    <div class="mb-8">
                        <div class="flex items-center mb-4">
                            @if($booking->vehicle->primaryImage)
                                <img src="{{ asset('storage/' . $booking->vehicle->primaryImage->file->path) }}" alt="{{ $booking->vehicle->make }} {{ $booking->vehicle->model }}" class="w-24 h-24 object-cover rounded-md mr-4">
                            @else
                                <div class="w-24 h-24 bg-gray-200 rounded-md mr-4 flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                </div>
                            @endif
                            <div>
                                <h3 class="font-bold text-gray-800 text-lg">{{ $booking->vehicle->make }} {{ $booking->vehicle->model }}</h3>
                                <p class="text-gray-600">{{ $booking->vehicle->year }} · {{ $booking->vehicle->vehicleType->name }}</p>
                                <p class="text-gray-600 mt-1">Hosted by {{ $booking->vehicle->user->first_name }} {{ $booking->vehicle->user->last_name }}</p>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                            <div>
                                <h3 class="font-medium text-gray-700 mb-2">Booking Information</h3>
                                <p class="text-gray-800 mb-1"><span class="text-gray-600">Booking Number:</span> {{ $booking->booking_number }}</p>
                                <p class="text-gray-800 mb-1"><span class="text-gray-600">Booked by:</span> {{ $booking->user->name }}</p>
                                <p class="text-gray-800 mb-1"><span class="text-gray-600">Booked on:</span> {{ $booking->created_at->format('M d, Y') }}</p>
                            </div>

                            <div>
                                <h3 class="font-medium text-gray-700 mb-2">Trip Details</h3>
                                <p class="text-gray-800 mb-1"><span class="text-gray-600">Pickup:</span> {{ $booking->start_date->format('M d, Y') }}</p>
                                <p class="text-gray-800 mb-1"><span class="text-gray-600">Return:</span> {{ $booking->end_date->format('M d, Y') }}</p>
                                <p class="text-gray-800 mb-1"><span class="text-gray-600">Duration:</span> {{ $booking->duration_in_days }} days</p>
                            </div>

                            <div>
                                <h3 class="font-medium text-gray-700 mb-2">Pickup Location</h3>
                                <p class="text-gray-800">{{ $booking->pickup_location }}</p>
                            </div>

                            <div>
                                <h3 class="font-medium text-gray-700 mb-2">Return Location</h3>
                                <p class="text-gray-800">{{ $booking->return_location }}</p>
                            </div>
                        </div>
                    </div>

                    @if($booking->driver)
                    <div class="mb-8">
                        <h2 class="text-xl font-semibold mb-4 text-gray-700">Driver Details</h2>

                        <div class="flex items-center p-4 bg-gray-50 rounded-lg">
                            <div class="w-12 h-12 bg-gray-200 rounded-full mr-4 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                            </div>
                            <div>
                                <h3 class="font-bold text-gray-800">{{ $booking->driver->user->name }}</h3>
                                <p class="text-gray-600 text-sm">{{ $booking->driver->experience_years }} years experience</p>
                                <p class="text-gray-600 text-sm">Daily Rate: ${{ number_format($booking->driver->daily_rate, 2) }}</p>
                            </div>
                        </div>
                    </div>
                    @endif

                    @if(!empty($booking->special_requests))
                    <div class="mb-8">
                        <h2 class="text-xl font-semibold mb-4 text-gray-700">Special Requests</h2>

                        <div class="bg-gray-50 p-4 rounded-md">
                            <p class="text-gray-700">{{ $booking->special_requests }}</p>
                        </div>
                    </div>
                    @endif

                    @if($booking->status === 'cancelled' || $booking->status === 'rejected')
                    <div class="mb-8">
                        <h2 class="text-xl font-semibold mb-4 text-gray-700">Cancellation Reason</h2>

                        <div class="bg-red-50 p-4 rounded-md">
                            <p class="text-red-700">{{ $booking->cancellation_reason }}</p>
                        </div>
                    </div>
                    @endif

                    @if($booking->statusHistory->count() > 0)
                    <div class="mb-8">
                        <h2 class="text-xl font-semibold mb-4 text-gray-700">Status History</h2>

                        <div class="bg-gray-50 p-4 rounded-md">
                            <ul class="space-y-4">
                                @foreach($booking->statusHistory->sortByDesc('created_at') as $status)
                                <li class="flex items-start">
                                    <div class="flex-shrink-0 h-5 w-5 rounded-full bg-blue-500 flex items-center justify-center mt-1">
                                        <div class="h-2 w-2 rounded-full bg-white"></div>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-900">
                                            Status changed to <span class="font-bold">{{ ucfirst($status->status) }}</span>
                                        </p>
                                        <p class="text-sm text-gray-500">
                                            {{ $status->created_at->format('M d, Y h:i A') }} by {{ $status->updatedBy->name }}
                                        </p>
                                        @if($status->notes)
                                        <p class="text-sm text-gray-700 mt-1">
                                            {{ $status->notes }}
                                        </p>
                                        @endif
                                    </div>
                                </li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                    @endif

                    <!-- Action Buttons -->
                    <div class="mt-8 border-t border-gray-200 pt-6">
                        <h2 class="text-xl font-semibold mb-4 text-gray-700">Actions</h2>

                        <div class="flex flex-wrap gap-4">
                            @if(Auth::id() === $booking->user_id && in_array($booking->status, ['confirmed', 'in_progress']))
                                <!-- Message the vehicle owner -->
                                <a href="{{ route('messages.create', ['recipient_id' => $booking->vehicle->user_id, 'booking_id' => $booking->id]) }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <i class="fas fa-envelope mr-2"></i> Message Owner
                                </a>
                            @endif

                            @if(Auth::id() === $booking->vehicle->user_id && in_array($booking->status, ['confirmed', 'in_progress']))
                                <!-- Message the renter -->
                                <a href="{{ route('messages.create', ['recipient_id' => $booking->user_id, 'booking_id' => $booking->id]) }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <i class="fas fa-envelope mr-2"></i> Message Renter
                                </a>
                            @endif

                            @if(Auth::id() === $booking->vehicle->user_id)
                                <!-- Owner Actions -->
                                @if($booking->status === 'pending')
                                <form action="{{ route('booking.update-status', $booking->id) }}" method="POST" class="confirm-action" data-message="Are you sure you want to confirm this booking?">
                                    @csrf
                                    <input type="hidden" name="status" value="confirmed">
                                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                        Confirm Booking
                                    </button>
                                </form>

                                <button type="button" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500" onclick="showRejectModal()">
                                    Reject Booking
                                </button>
                                @endif

                                @if($booking->status === 'confirmed')
                                <form action="{{ route('booking.update-status', $booking->id) }}" method="POST" class="confirm-action" data-message="Are you sure you want to mark this booking as in progress?">
                                    @csrf
                                    <input type="hidden" name="status" value="in_progress">
                                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                        Mark as In Progress
                                    </button>
                                </form>
                                @endif

                                @if($booking->status === 'in_progress')
                                <form action="{{ route('booking.update-status', $booking->id) }}" method="POST" class="confirm-action" data-message="Are you sure you want to mark this booking as completed?">
                                    @csrf
                                    <input type="hidden" name="status" value="completed">
                                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                        Mark as Completed
                                    </button>
                                </form>
                                @endif
                            @endif

                            @if(Auth::id() === $booking->user_id)
                                <!-- Renter Actions -->
                                @if($booking->status === 'pending' || $booking->status === 'confirmed')
                                <button type="button" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500" onclick="showCancelModal()">
                                    Cancel Booking
                                </button>
                                @endif

                                @if($booking->status === 'pending' && $booking->payments->isEmpty())
                                <a href="{{ route('booking.payment', $booking->id) }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                    Make Payment
                                </a>
                                @endif
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-md overflow-hidden sticky top-24">
                <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                    <h2 class="text-xl font-bold text-gray-800">Price Details</h2>
                </div>

                <div class="p-6">
                    <div class="mb-4">
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-600">${{ number_format($booking->vehicle_price / $booking->duration_in_days, 2) }} × {{ $booking->duration_in_days }} days</span>
                            <span class="font-medium">${{ number_format($booking->vehicle_price, 2) }}</span>
                        </div>

                        @if($booking->discount_amount > 0)
                        <div class="flex justify-between mb-2 text-green-600">
                            <span>Discount</span>
                            <span>-${{ number_format($booking->discount_amount, 2) }}</span>
                        </div>
                        @endif

                        @if($booking->driver_price > 0)
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-600">Driver</span>
                            <span class="font-medium">${{ number_format($booking->driver_price, 2) }}</span>
                        </div>
                        @endif
                    </div>

                    <div class="border-t border-gray-200 pt-4 mb-4">
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-600">Subtotal</span>
                            <span class="font-medium">${{ number_format($booking->subtotal, 2) }}</span>
                        </div>
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-600">Tax (10%)</span>
                            <span class="font-medium">${{ number_format($booking->tax_amount, 2) }}</span>
                        </div>
                    </div>

                    <div class="border-t border-gray-200 pt-4 mb-4">
                        <div class="flex justify-between">
                            <span class="text-lg font-bold">Total</span>
                            <span class="text-lg font-bold">${{ number_format($booking->total_amount, 2) }}</span>
                        </div>
                    </div>

                    @if($booking->security_deposit > 0)
                    <div class="border-t border-gray-200 pt-4 mb-4">
                        <div class="flex justify-between text-gray-600">
                            <span>Security deposit</span>
                            <span>${{ number_format($booking->security_deposit, 2) }}</span>
                        </div>
                        <p class="text-sm text-gray-500 mt-1">Refundable if no damage occurs</p>
                    </div>
                    @endif

                    <div class="border-t border-gray-200 pt-4">
                        <h3 class="font-medium text-gray-700 mb-2">Payment Status</h3>

                        @if($booking->payments->isNotEmpty())
                            @php
                                $payment = $booking->payments->first();
                                $paymentStatusColors = [
                                    'pending' => 'bg-yellow-100 text-yellow-800',
                                    'completed' => 'bg-green-100 text-green-800',
                                    'failed' => 'bg-red-100 text-red-800',
                                    'refunded' => 'bg-blue-100 text-blue-800',
                                ];
                                $paymentStatusColor = $paymentStatusColors[$payment->status] ?? 'bg-gray-100 text-gray-800';
                            @endphp
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">{{ ucfirst($payment->payment_method) }}</span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $paymentStatusColor }}">
                                    {{ ucfirst($payment->status) }}
                                </span>
                            </div>
                            <p class="text-sm text-gray-500 mt-1">Transaction ID: {{ $payment->transaction_id }}</p>
                            <p class="text-sm text-gray-500">Date: {{ $payment->created_at->format('M d, Y') }}</p>
                        @else
                            <p class="text-sm text-yellow-600">Payment pending</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reject Booking Modal -->
<div id="reject-modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Reject Booking</h3>
        </div>
        <form action="{{ route('booking.update-status', $booking->id) }}" method="POST">
            @csrf
            <input type="hidden" name="status" value="rejected">
            <div class="px-6 py-4">
                <div class="mb-4">
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Reason for rejection</label>
                    <textarea name="notes" id="notes" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50" required></textarea>
                </div>
            </div>
            <div class="px-6 py-3 bg-gray-50 flex justify-end space-x-3 rounded-b-lg">
                <button type="button" onclick="hideRejectModal()" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    Cancel
                </button>
                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                    Reject Booking
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Cancel Booking Modal -->
<div id="cancel-modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Cancel Booking</h3>
        </div>
        <form action="{{ route('booking.update-status', $booking->id) }}" method="POST">
            @csrf
            <input type="hidden" name="status" value="cancelled">
            <div class="px-6 py-4">
                <div class="mb-4">
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Reason for cancellation</label>
                    <textarea name="notes" id="notes" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50" required></textarea>
                </div>
                <div class="bg-yellow-50 p-4 rounded-md">
                    <div class="flex">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                        </svg>
                        <div>
                            <p class="text-sm text-yellow-700">Cancellation may be subject to fees depending on the timing. Please review the cancellation policy.</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="px-6 py-3 bg-gray-50 flex justify-end space-x-3 rounded-b-lg">
                <button type="button" onclick="hideCancelModal()" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    Go Back
                </button>
                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                    Cancel Booking
                </button>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script>
    // Modal functions
    function showRejectModal() {
        document.getElementById('reject-modal').classList.remove('hidden');
    }

    function hideRejectModal() {
        document.getElementById('reject-modal').classList.add('hidden');
    }

    function showCancelModal() {
        document.getElementById('cancel-modal').classList.remove('hidden');
    }

    function hideCancelModal() {
        document.getElementById('cancel-modal').classList.add('hidden');
    }

    // Confirmation for actions
    document.addEventListener('DOMContentLoaded', function() {
        const confirmForms = document.querySelectorAll('.confirm-action');

        confirmForms.forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();

                const message = this.getAttribute('data-message') || 'Are you sure you want to perform this action?';

                if (confirm(message)) {
                    this.submit();
                }
            });
        });

        // Close modals when clicking outside
        window.addEventListener('click', function(e) {
            const rejectModal = document.getElementById('reject-modal');
            const cancelModal = document.getElementById('cancel-modal');

            if (e.target === rejectModal) {
                hideRejectModal();
            }

            if (e.target === cancelModal) {
                hideCancelModal();
            }
        });
    });
</script>
@endpush
</x-app-layout>

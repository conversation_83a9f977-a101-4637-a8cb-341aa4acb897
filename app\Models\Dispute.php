<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Dispute extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'booking_id',
        'user_id',
        'against_user_id',
        'dispute_number',
        'type',
        'description',
        'amount_claimed',
        'status',
        'resolution_notes',
        'resolved_by',
        'resolved_at'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'amount_claimed' => 'decimal:2',
        'resolved_at' => 'datetime',
    ];

    /**
     * Get the booking associated with the dispute.
     */
    public function booking()
    {
        return $this->belongsTo(Booking::class);
    }

    /**
     * Get the user who opened the dispute.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user against whom the dispute is opened.
     */
    public function againstUser()
    {
        return $this->belongsTo(User::class, 'against_user_id');
    }

    /**
     * Get the user who resolved the dispute.
     */
    public function resolvedBy()
    {
        return $this->belongsTo(User::class, 'resolved_by');
    }

    /**
     * Get the messages associated with this dispute.
     */
    public function messages()
    {
        return $this->hasMany(DisputeMessage::class)->orderBy('created_at', 'asc');
    }

    /**
     * Get the evidence files associated with this dispute.
     */
    public function evidence()
    {
        return $this->hasMany(DisputeEvidence::class);
    }

    /**
     * Generate a unique dispute number.
     *
     * @return string
     */
    public static function generateDisputeNumber()
    {
        $prefix = 'DSP';
        $random = mt_rand(1000000, 9999999);
        $number = $prefix . $random;

        // Ensure the number is unique
        while (self::where('dispute_number', $number)->exists()) {
            $random = mt_rand(1000000, 9999999);
            $number = $prefix . $random;
        }

        return $number;
    }
}

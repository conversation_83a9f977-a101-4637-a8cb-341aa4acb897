/**
 * Shared pagination functionality for Alpine.js components
 * This utility provides a reusable pagination system for data tables
 */

window.createPagination = function(options = {}) {
    const defaults = {
        fetchUrl: '',
        initialPage: 1,
        perPage: 10,
        searchParams: {},
        autoInit: true,
        successMessage: ''
    };

    // Merge options with defaults
    const config = { ...defaults, ...options };

    return {
        // Pagination state
        items: [],
        currentPage: config.initialPage,
        totalPages: 1,
        totalRecords: 0,
        perPage: config.perPage,
        loading: false,
        search: '',
        filters: { ...config.searchParams },
        successMessage: config.successMessage,

        // Initialize data
        init() {
            // Set up URL parameters from the current URL if autoInit is true
            if (config.autoInit) {
                const urlParams = new URLSearchParams(window.location.search);
                
                // Set search from URL if present
                if (urlParams.has('search')) {
                    this.search = urlParams.get('search');
                }
                
                // Set filters from URL if present
                for (const key in this.filters) {
                    if (urlParams.has(key)) {
                        this.filters[key] = urlParams.get(key);
                    }
                }
            }
            
            // Show success message if present
            if (this.successMessage) {
                // If notification system is available, use it
                if (window.notification) {
                    window.notification.success(this.successMessage);
                } else if (window.Toastify) {
                    // Fallback to Toastify if available
                    Toastify({
                        text: this.successMessage,
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        backgroundColor: "#48bb78",
                    }).showToast();
                }
                // Clear the success message to prevent showing it again
                this.successMessage = '';
            }
        },

        // Calculate pagination array for UI
        get paginationArray() {
            let pages = [];
            let startPage = Math.max(1, this.currentPage - 2);
            let endPage = Math.min(this.totalPages, this.currentPage + 2);

            if (startPage > 1) {
                pages.push(1);
                if (startPage > 2) pages.push('...');
            }

            for (let i = startPage; i <= endPage; i++) {
                pages.push(i);
            }

            if (endPage < this.totalPages) {
                if (endPage < this.totalPages - 1) pages.push('...');
                pages.push(this.totalPages);
            }

            return pages;
        },

        // Get serial number for table rows
        getSerialNumber(index) {
            return (this.currentPage - 1) * this.perPage + index + 1;
        },

        // Navigation methods
        goToPage(page) {
            if (page !== this.currentPage && page >= 1 && page <= this.totalPages) {
                this.currentPage = page;
                this.fetchItems();
            }
        },

        nextPage() {
            if (this.currentPage < this.totalPages) {
                this.currentPage++;
                this.fetchItems();
            }
        },

        prevPage() {
            if (this.currentPage > 1) {
                this.currentPage--;
                this.fetchItems();
            }
        },

        // Search and filter methods
        searchItems() {
            this.currentPage = 1; // Reset to first page
            this.fetchItems();
        },

        resetFilters() {
            this.search = '';
            this.filters = {};
            this.currentPage = 1;
            this.fetchItems();
        },

        // Apply filters and update URL
        applyFilters() {
            // Build query parameters
            const params = new URLSearchParams();
            
            // Add search if present
            if (this.search) {
                params.append('search', this.search);
            }
            
            // Add filters if present
            for (const key in this.filters) {
                if (this.filters[key]) {
                    params.append(key, this.filters[key]);
                }
            }
            
            // Add page if not first page
            if (this.currentPage > 1) {
                params.append('page', this.currentPage);
            }
            
            // Update URL without reloading the page
            const newUrl = window.location.pathname + (params.toString() ? '?' + params.toString() : '');
            window.history.pushState({ path: newUrl }, '', newUrl);
            
            // Fetch items with new filters
            this.fetchItems();
        },

        // Fetch items from server
        fetchItems() {
            this.loading = true;
            
            // Build query parameters
            const params = new URLSearchParams();
            
            // Add search if present
            if (this.search) {
                params.append('search', this.search);
            }
            
            // Add filters if present
            for (const key in this.filters) {
                if (this.filters[key]) {
                    params.append(key, this.filters[key]);
                }
            }
            
            // Add page
            params.append('page', this.currentPage);
            
            // Fetch data from server
            fetch(`${config.fetchUrl}?${params.toString()}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    this.items = data.data;
                    this.totalPages = data.last_page;
                    this.totalRecords = data.total;
                    this.currentPage = data.current_page;
                    this.loading = false;
                })
                .catch(error => {
                    console.error('Error fetching data:', error);
                    this.loading = false;
                    
                    // Show error notification if available
                    if (window.notification) {
                        window.notification.error('Error loading data. Please try again.');
                    } else if (window.Toastify) {
                        Toastify({
                            text: 'Error loading data. Please try again.',
                            duration: 3000,
                            close: true,
                            gravity: "top",
                            position: "right",
                            backgroundColor: "#e53e3e",
                        }).showToast();
                    }
                });
        },

        // Delete item
        deleteItem(id, url, confirmMessage = 'Are you sure you want to delete this item?') {
            if (confirm(confirmMessage)) {
                this.loading = true;
                
                // Send delete request
                fetch(`${url}/${id}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    // Show success message
                    if (window.notification) {
                        window.notification.success(data.message || 'Item deleted successfully');
                    } else if (window.Toastify) {
                        Toastify({
                            text: data.message || 'Item deleted successfully',
                            duration: 3000,
                            close: true,
                            gravity: "top",
                            position: "right",
                            backgroundColor: "#48bb78",
                        }).showToast();
                    }
                    
                    // Refresh data
                    this.fetchItems();
                })
                .catch(error => {
                    console.error('Error deleting item:', error);
                    this.loading = false;
                    
                    // Show error notification
                    if (window.notification) {
                        window.notification.error('Error deleting item. Please try again.');
                    } else if (window.Toastify) {
                        Toastify({
                            text: 'Error deleting item. Please try again.',
                            duration: 3000,
                            close: true,
                            gravity: "top",
                            position: "right",
                            backgroundColor: "#e53e3e",
                        }).showToast();
                    }
                });
            }
        },

        // Process delete with SweetAlert2 (if available)
        processDelete(id, url) {
            this.loading = true;
            
            // Send delete request
            fetch(`${url}/${id}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                // Show success message
                if (window.notification) {
                    window.notification.success(data.message || 'Item deleted successfully');
                } else if (window.Swal) {
                    Swal.fire({
                        title: 'Success!',
                        text: data.message || 'Item deleted successfully',
                        icon: 'success',
                        confirmButtonColor: '#3085d6'
                    });
                }
                
                // Refresh data
                this.fetchItems();
            })
            .catch(error => {
                console.error('Error deleting item:', error);
                this.loading = false;
                
                // Show error notification
                if (window.notification) {
                    window.notification.error('Error deleting item. Please try again.');
                } else if (window.Swal) {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Error deleting item. Please try again.',
                        icon: 'error',
                        confirmButtonColor: '#3085d6'
                    });
                }
            });
        }
    };
};

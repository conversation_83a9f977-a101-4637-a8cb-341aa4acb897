<?php

namespace Tests\Unit;

use App\Models\User;
use Spatie\Permission\Traits\HasRoles;
use Tests\Unit\TestCase;

test('user has correct fillable attributes', function () {
    $user = new User();

    $fillable = [
        'first_name',
        'last_name',
        'email',
        'password',
    ];

    expect($user->getFillable())->toBe($fillable);
});

test('user has correct hidden attributes', function () {
    $user = new User();

    $hidden = [
        'password',
        'remember_token',
    ];

    expect($user->getHidden())->toBe($hidden);
});

test('user has correct casts', function () {
    $user = new User();

    $expectedCasts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
    ];

    expect($user->getCasts())->toMatchArray($expectedCasts);
});

test('user uses HasRoles trait', function () {
    $user = new User();

    expect(in_array(HasRoles::class, class_uses_recursive($user)))->toBeTrue();
});

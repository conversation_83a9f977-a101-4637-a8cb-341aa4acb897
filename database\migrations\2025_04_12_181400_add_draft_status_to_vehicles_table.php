<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // We need to use DB::statement to modify an enum field
        DB::statement("ALTER TABLE vehicles MODIFY COLUMN status ENUM('draft', 'pending', 'active', 'rejected', 'inactive') DEFAULT 'draft'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert back to the original enum without 'draft' status
        DB::statement("ALTER TABLE vehicles MODIFY COLUMN status ENUM('pending', 'active', 'rejected', 'inactive') DEFAULT 'pending'");
    }
};

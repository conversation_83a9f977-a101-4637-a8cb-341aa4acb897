<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class DisputeEvidence extends Model
{
    use HasFactory, SoftDeletes;
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'dispute_id',
        'user_id',
        'file_id',
        'title',
        'description'
    ];
    
    /**
     * Get the dispute this evidence belongs to.
     */
    public function dispute()
    {
        return $this->belongsTo(Dispute::class);
    }
    
    /**
     * Get the user who uploaded this evidence.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
    
    /**
     * Get the file associated with this evidence.
     */
    public function file()
    {
        return $this->belongsTo(File::class);
    }
}

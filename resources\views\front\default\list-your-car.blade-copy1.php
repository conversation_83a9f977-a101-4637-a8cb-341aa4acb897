<x-app-layout>
    <!-- Hero Section -->
    <section class="relative h-[500px] overflow-hidden">
        <div class="absolute inset-0">
            <img src="https://images.unsplash.com/photo-1580273916550-e323be2ae537?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80" alt="Car keys handover" class="w-full h-full object-cover brightness-75">
        </div>
        <div class="relative container mx-auto px-4 h-full flex flex-col justify-center">
            <div class="max-w-xl">
                <h1 class="text-4xl md:text-5xl font-bold text-white mb-4" data-lang-key="turn_car_income">Turn Your Car Into Income</h1>
                <p class="text-xl text-white mb-8" data-lang-key="share_car_earn">Share your car when you're not using it and earn an average of €500+ per month. Join thousands of car owners already earning with CARBNB.</p>
                <a href="#list-your-car" class="inline-block px-8 py-4 bg-primary text-white font-bold rounded-lg hover:bg-red-700 transition" data-lang-key="list_car_now">List Your Car Now</a>
                <p class="text-white text-sm mt-4" data-lang-key="free_to_list">Free to list. Only pay when you earn.</p>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="py-10 bg-white shadow-md rounded-lg -mt-16 relative z-10 max-w-6xl mx-auto">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
                <div>
                    <p class="text-3xl font-bold text-primary">€500+</p>
                    <p class="text-gray-600" data-lang-key="avg_monthly_earnings">Avg. Monthly Earnings</p>
                </div>
                <div>
                    <p class="text-3xl font-bold text-primary">10,000+</p>
                    <p class="text-gray-600" data-lang-key="car_owners">Car Owners</p>
                </div>
                <div>
                    <p class="text-3xl font-bold text-primary">€1M</p>
                    <p class="text-gray-600" data-lang-key="insurance_coverage">Insurance Coverage</p>
                </div>
                <div>
                    <p class="text-3xl font-bold text-primary">24/7</p>
                    <p class="text-gray-600" data-lang-key="customer_support">Customer Support</p>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section class="py-16">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-4" data-lang-key="how_car_sharing_works">How Car Sharing Works</h2>
            <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12" data-lang-key="sharing_simple">Sharing your car on CARBNB is simple, safe, and profitable. Here's how to get started:</p>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="bg-white p-6 rounded-lg shadow-md text-center">
                    <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-car text-2xl text-primary"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3" data-lang-key="list_your_car_step">1. List Your Car</h3>
                    <p class="text-gray-600 mb-4" data-lang-key="create_free_listing">Create a free listing with photos and details about your car. Set your own pricing and availability.</p>
                    <div class="h-1 w-20 bg-primary mx-auto"></div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-md text-center">
                    <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-calendar-check text-2xl text-primary"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3" data-lang-key="accept_bookings_step">2. Accept Bookings</h3>
                    <p class="text-gray-600 mb-4" data-lang-key="review_approve">Review and approve trip requests from verified renters. Manage your own schedule.</p>
                    <div class="h-1 w-20 bg-primary mx-auto"></div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-md text-center">
                    <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-wallet text-2xl text-primary"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3" data-lang-key="get_paid_step">3. Get Paid</h3>
                    <p class="text-gray-600 mb-4" data-lang-key="earnings_deposited">Earnings are automatically deposited into your account after each completed trip.</p>
                    <div class="h-1 w-20 bg-primary mx-auto"></div>
                </div>
            </div>

            <div class="text-center mt-12">
                <a href="#list-your-car" class="inline-block px-6 py-3 bg-primary text-white font-bold rounded-lg hover:bg-red-700 transition" data-lang-key="start_earning_now">Start Earning Now</a>
            </div>
        </div>
    </section>

    <!-- Income Calculator -->
    <section class="py-16 bg-red-50">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-4" data-lang-key="how_much_earn">How Much Can You Earn?</h2>
            <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12" data-lang-key="calculator_desc">Use our calculator to estimate your potential earnings based on your car and location.</p>

            <div class="max-w-3xl mx-auto bg-white rounded-lg shadow-md p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label class="block text-gray-700 mb-2" data-lang-key="car_make">Car Make</label>
                        <select id="car-make" class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none">
                            <option>Toyota</option>
                            <option>Honda</option>
                            <option>Ford</option>
                            <option>BMW</option>
                            <option>Tesla</option>
                            <option>Other</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-gray-700 mb-2" data-lang-key="car_year">Car Year</label>
                        <select id="car-year" class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none">
                            <option>2023</option>
                            <option>2022</option>
                            <option>2021</option>
                            <option>2020</option>
                            <option>2019</option>
                            <option>2018</option>
                            <option>2017</option>
                            <option>2016</option>
                            <option>2015</option>
                            <option>Older</option>
                        </select>
                    </div>
                </div>
                <div class="mb-6">
                    <label class="block text-gray-700 mb-2" data-lang-key="your_location">Your Location</label>
                    <input type="text" placeholder="Enter your city" class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none">
                </div>
                <div class="mb-6">
                    <label class="block text-gray-700 mb-2" data-lang-key="days_available">Days Available Per Month</label>
                    <div class="flex items-center">
                        <input type="range" min="1" max="30" value="15" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer" id="days-slider">
                        <span class="ml-4 text-lg font-semibold text-primary" id="days-value">15</span>
                    </div>
                </div>
                <div class="text-center">
                    <button id="calculate-btn" class="px-6 py-3 bg-primary text-white font-bold rounded-lg hover:bg-red-700 transition" data-lang-key="calculate_earnings">Calculate Earnings</button>
                </div>

                <div id="earnings-result" class="mt-6 p-4 bg-red-100 rounded-lg hidden">
                    <h3 class="text-xl font-bold text-center mb-3" data-lang-key="estimated_earnings">Your Estimated Monthly Earnings</h3>
                    <p class="text-center text-3xl font-bold text-primary">€<span id="earnings-amount">0</span></p>
                    <p class="text-center text-sm text-gray-600 mt-2" data-lang-key="results_vary">Results vary based on your car's demand, location, and availability.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Benefits Section -->
    <section class="py-16">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-4" data-lang-key="benefits_sharing">Benefits of Sharing Your Car</h2>
            <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12" data-lang-key="join_thousands_owners">Join thousands of car owners who are earning extra income and helping their community.</p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-money-bill-wave text-xl text-green-600"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3" data-lang-key="earn_extra_income">Earn Extra Income</h3>
                    <p class="text-gray-600" data-lang-key="turn_expense_income">Turn your car from an expense into an income source. Many owners cover their car payments entirely.</p>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-shield-alt text-xl text-blue-600"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3" data-lang-key="insurance_protection_title">Insurance Protection</h3>
                    <p class="text-gray-600" data-lang-key="every_trip_includes">Every trip includes €1M in liability insurance and comprehensive collision coverage.</p>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-calendar-alt text-xl text-purple-600"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3" data-lang-key="flexible_schedule">Flexible Schedule</h3>
                    <p class="text-gray-600" data-lang-key="control_availability">You control when your car is available. Share only when it works for you.</p>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-hands-helping text-xl text-yellow-600"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3" data-lang-key="vetted_renters">Vetted Renters</h3>
                    <p class="text-gray-600" data-lang-key="all_renters_screened">All renters are screened and verified. You can review profiles before accepting bookings.</p>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-headset text-xl text-red-600"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3" data-lang-key="support_title">24/7 Support</h3>
                    <p class="text-gray-600" data-lang-key="team_available">Our team is always available to help with any questions or issues that arise.</p>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <div class="w-12 h-12 bg-teal-100 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-leaf text-xl text-teal-600"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3" data-lang-key="reduce_carbon">Reduce Carbon Footprint</h3>
                    <p class="text-gray-600" data-lang-key="car_sharing_helps">Car sharing helps reduce the number of vehicles on the road, benefiting the environment.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials -->
    <section class="py-16 bg-red-50">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-4" data-lang-key="what_car_owners_say">What Car Owners Say</h2>
            <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12" data-lang-key="hear_from_owners">Hear from other car owners who are already earning with CARBNB.</p>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <div class="flex items-center mb-4">
                        <img src="{{ asset('images/testimonials/owner1.jpg') }}" alt="Car Owner" class="w-16 h-16 rounded-full mr-4">
                        <div>
                            <h4 class="font-bold text-lg">Miguel Santos</h4>
                            <p class="text-gray-600">Tesla Model 3 Owner</p>
                        </div>
                    </div>
                    <div class="flex text-yellow-400 mb-3">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <p class="text-gray-600" data-lang-key="testimonial_miguel">"I was skeptical at first, but CARBNB has been amazing. My Tesla is booked almost every weekend, and I've made over €12,000 in the past year. The process is seamless, and the support team is excellent."</p>
                    <p class="text-primary font-semibold mt-4" data-lang-key="earning_1200">Earning €1,200/month</p>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-md">
                    <div class="flex items-center mb-4">
                        <img src="{{ asset('images/testimonials/owner2.jpg') }}" alt="Car Owner" class="w-16 h-16 rounded-full mr-4">
                        <div>
                            <h4 class="font-bold text-lg">Sofia Martins</h4>
                            <p class="text-gray-600">Toyota RAV4 Owner</p>
                        </div>
                    </div>
                    <div class="flex text-yellow-400 mb-3">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star-half-alt"></i>
                    </div>
                    <p class="text-gray-600" data-lang-key="testimonial_sofia">"I work from home and barely use my car during the week. Now it's making me money instead of just sitting in the driveway. The insurance coverage gives me peace of mind, and the renters have all been respectful."</p>
                    <p class="text-primary font-semibold mt-4" data-lang-key="earning_650">Earning €650/month</p>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-md">
                    <div class="flex items-center mb-4">
                        <img src="{{ asset('images/testimonials/owner3.jpg') }}" alt="Car Owner" class="w-16 h-16 rounded-full mr-4">
                        <div>
                            <h4 class="font-bold text-lg">João Ferreira</h4>
                            <p class="text-gray-600">Ford F-150 Owner</p>
                        </div>
                    </div>
                    <div class="flex text-yellow-400 mb-3">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <p class="text-gray-600" data-lang-key="testimonial_joao">"My truck is in high demand for weekend moves and projects. I've had such a positive experience with CARBNB that I'm considering getting a second vehicle just to share on the platform."</p>
                    <p class="text-primary font-semibold mt-4" data-lang-key="earning_850">Earning €850/month</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Common Questions -->
    <section class="py-16">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-4" data-lang-key="common_questions">Common Questions</h2>
            <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12" data-lang-key="everything_need_know">Everything you need to know about sharing your car on CARBNB.</p>

            <div class="max-w-3xl mx-auto">
                <div class="mb-6">
                    <button class="flex justify-between items-center w-full bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition focus:outline-none faq-toggle">
                        <span class="text-lg font-semibold" data-lang-key="insurance_work">How does the insurance work?</span>
                        <i class="fas fa-chevron-down text-primary transition-transform"></i>
                    </button>
                    <div class="bg-white px-4 pb-4 rounded-b-lg hidden faq-content">
                        <p class="text-gray-600 pt-2" data-lang-key="insurance_answer">Every trip is covered by our €1 million insurance policy. This includes liability insurance and physical damage protection up to the actual cash value of your car. Your personal insurance is never affected, and you're never out of pocket for damage caused during a rental.</p>
                    </div>
                </div>

                <div class="mb-6">
                    <button class="flex justify-between items-center w-full bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition focus:outline-none faq-toggle">
                        <span class="text-lg font-semibold" data-lang-key="cars_qualify">What types of cars qualify?</span>
                        <i class="fas fa-chevron-down text-primary transition-transform"></i>
                    </button>
                    <div class="bg-white px-4 pb-4 rounded-b-lg hidden faq-content">
                        <p class="text-gray-600 pt-2" data-lang-key="cars_qualify_answer">Most cars less than 12 years old with fewer than 150,000 kilometers qualify. Exotic cars, commercial vehicles, and cars with salvage titles are generally not eligible. During registration, we'll help determine if your vehicle qualifies.</p>
                    </div>
                </div>

                <div class="mb-6">
                    <button class="flex justify-between items-center w-full bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition focus:outline-none faq-toggle">
                        <span class="text-lg font-semibold" data-lang-key="how_get_paid">How do I get paid?</span>
                        <i class="fas fa-chevron-down text-primary transition-transform"></i>
                    </button>
                    <div class="bg-white px-4 pb-4 rounded-b-lg hidden faq-content">
                        <p class="text-gray-600 pt-2" data-lang-key="get_paid_answer">You'll receive payments via direct deposit to your bank account. Payments are processed within 3 business days after each trip is completed. You can track your earnings in real-time through the CARBNB dashboard.</p>
                    </div>
                </div>

                <div class="mb-6">
                    <button class="flex justify-between items-center w-full bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition focus:outline-none faq-toggle">
                        <span class="text-lg font-semibold" data-lang-key="wear_tear">What about wear and tear on my car?</span>
                        <i class="fas fa-chevron-down text-primary transition-transform"></i>
                    </button>
                    <div class="bg-white px-4 pb-4 rounded-b-lg hidden faq-content">
                        <p class="text-gray-600 pt-2" data-lang-key="wear_tear_answer">Normal wear and tear is expected, but our pricing structure is designed to account for this. Many hosts find that the income they earn far outweighs the additional wear and tear. For maintenance issues that arise directly from a trip, our support team can help determine appropriate compensation.</p>
                    </div>
                </div>

                <div class="mb-6">
                    <button class="flex justify-between items-center w-full bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition focus:outline-none faq-toggle">
                        <span class="text-lg font-semibold" data-lang-key="tickets_tolls">What if a renter gets a ticket or tolls?</span>
                        <i class="fas fa-chevron-down text-primary transition-transform"></i>
                    </button>
                    <div class="bg-white px-4 pb-4 rounded-b-lg hidden faq-content">
                        <p class="text-gray-600 pt-2" data-lang-key="tickets_tolls_answer">Renters are responsible for all tickets, tolls, and violations incurred during their trip. If you receive a notice for a violation that occurred during a rental period, you can submit it through our platform and we'll handle the reimbursement process with the renter.</p>
                    </div>
                </div>
            </div>

            <div class="text-center mt-8">
                <a href="#faqs" class="text-primary font-semibold hover:text-red-800 transition" data-lang-key="view_all_faqs">View all FAQs <i class="fas fa-arrow-right ml-1"></i></a>
            </div>
        </div>
    </section>

    <!-- List Your Car Form -->
    <section id="list-your-car" class="py-16 bg-red-100">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-4" data-lang-key="list_your_car_form">List Your Car</h2>
            <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12" data-lang-key="takes_10_minutes">It only takes about 10 minutes to create your listing and start earning.</p>

            <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-8">
                <form id="car-listing-form" action="{{ route('vehicles.store') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    <!-- Owner Information -->
                    <h3 class="text-xl font-bold mb-4 border-b pb-2" data-lang-key="owner_information">Owner Information</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                        <div>
                            <label class="block text-gray-700 mb-2" data-lang-key="first_name">First Name *</label>
                            <input type="text" name="first_name" required class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none" value="{{ auth()->user()->name ?? '' }}">
                        </div>
                        <div>
                            <label class="block text-gray-700 mb-2" data-lang-key="last_name">Last Name *</label>
                            <input type="text" name="last_name" required class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none">
                        </div>
                        <div>
                            <label class="block text-gray-700 mb-2" data-lang-key="email_address">Email Address *</label>
                            <input type="email" name="email" required class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none" value="{{ auth()->user()->email ?? '' }}">
                        </div>
                        <div>
                            <label class="block text-gray-700 mb-2" data-lang-key="phone_number">Phone Number *</label>
                            <input type="tel" name="phone" required class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none">
                        </div>
                    </div>

                    <!-- Vehicle Information -->
                    <h3 class="text-xl font-bold mb-4 border-b pb-2" data-lang-key="vehicle_information">Vehicle Information</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                        <div>
                            <label class="block text-gray-700 mb-2" data-lang-key="car_make_required">Car Make *</label>
                            <select name="make" required class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none">
                                <option value="">Select Make</option>
                                <option>Toyota</option>
                                <option>Honda</option>
                                <option>Ford</option>
                                <option>Chevrolet</option>
                                <option>BMW</option>
                                <option>Mercedes-Benz</option>
                                <option>Audi</option>
                                <option>Tesla</option>
                                <option>Nissan</option>
                                <option>Subaru</option>
                                <option>Other</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-gray-700 mb-2" data-lang-key="car_model">Car Model *</label>
                            <input type="text" name="model" required placeholder="e.g. Camry, Civic, F-150" class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none">
                        </div>
                        <div>
                            <label class="block text-gray-700 mb-2" data-lang-key="year">Year *</label>
                            <select name="year" required class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none">
                                <option value="">Select Year</option>
                                <option>2023</option>
                                <option>2022</option>
                                <option>2021</option>
                                <option>2020</option>
                                <option>2019</option>
                                <option>2018</option>
                                <option>2017</option>
                                <option>2016</option>
                                <option>2015</option>
                                <option>2014</option>
                                <option>2013</option>
                                <option>2012</option>
                                <option>Older</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-gray-700 mb-2" data-lang-key="license_plate">License Plate (Last 4 Digits) *</label>
                            <input type="text" name="license_plate" required maxlength="4" placeholder="Last 4 digits only" class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none">
                        </div>
                    </div>

                    <!-- Continued from previous section -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                        <div>
                            <label class="block text-gray-700 mb-2" data-lang-key="color">Color *</label>
                            <input type="text" name="color" required class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none">
                        </div>
                        <div>
                            <label class="block text-gray-700 mb-2" data-lang-key="mileage">Mileage *</label>
                            <input type="number" name="mileage" required placeholder="Current odometer reading" class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none">
                        </div>
                        <div>
                            <label class="block text-gray-700 mb-2" data-lang-key="transmission">Transmission *</label>
                            <select name="transmission" required class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none">
                                <option value="">Select Transmission</option>
                                <option value="automatic">Automatic</option>
                                <option value="manual">Manual</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-6">
                        <label class="block text-gray-700 mb-2" data-lang-key="city">City *</label>
                        <select name="city_id" required class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none">
                            <option value="">Select City</option>
                            @foreach($cities as $city)
                                <option value="{{ $city->id }}">{{ $city->name }}</option>
                            @endforeach
                        </select>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                        <div>
                            <label class="block text-gray-700 mb-2" data-lang-key="fuel_type">Fuel Type *</label>
                            <select name="fuel_type" required class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none">
                                <option value="">Select Fuel Type</option>
                                <option value="gasoline">Gasoline</option>
                                <option value="diesel">Diesel</option>
                                <option value="hybrid">Hybrid</option>
                                <option value="electric">Electric</option>
                                <option value="plugin_hybrid">Plug-in Hybrid</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-gray-700 mb-2" data-lang-key="seats">Number of Seats *</label>
                            <select name="seats" required class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none">
                                <option value="">Select Seats</option>
                                <option value="2">2</option>
                                <option value="4">4</option>
                                <option value="5">5</option>
                                <option value="6">6</option>
                                <option value="7">7</option>
                                <option value="8">8+</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-8">
                        <label class="block text-gray-700 mb-2" data-lang-key="features">Features (select all that apply)</label>
                        <div class="grid grid-cols-2 md:grid-cols-3 gap-x-4 gap-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" name="features[]" value="gps" class="mr-2">
                                <span data-lang-key="gps">GPS Navigation</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="features[]" value="bluetooth" class="mr-2">
                                <span data-lang-key="bluetooth">Bluetooth</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="features[]" value="backup_camera" class="mr-2">
                                <span data-lang-key="backup_camera">Backup Camera</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="features[]" value="sunroof" class="mr-2">
                                <span data-lang-key="sunroof">Sunroof</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="features[]" value="heated_seats" class="mr-2">
                                <span data-lang-key="heated_seats">Heated Seats</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="features[]" value="child_seat" class="mr-2">
                                <span data-lang-key="child_seat">Child Seat</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="features[]" value="apple_carplay" class="mr-2">
                                <span data-lang-key="apple_carplay">Apple CarPlay</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="features[]" value="android_auto" class="mr-2">
                                <span data-lang-key="android_auto">Android Auto</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="features[]" value="premium_audio" class="mr-2">
                                <span data-lang-key="premium_audio">Premium Audio</span>
                            </label>
                        </div>
                    </div>

                    <!-- Photos and Description -->
                    <h3 class="text-xl font-bold mb-4 border-b pb-2" data-lang-key="photos_description">Photos & Description</h3>
                    <div class="mb-6">
                        <label class="block text-gray-700 mb-2" data-lang-key="upload_photos">Upload Photos (at least 5 recommended) *</label>
                        <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
                            <div class="border-2 border-dashed border-gray-300 rounded-lg h-32 flex items-center justify-center cursor-pointer hover:bg-gray-50 transition photo-upload">
                                <div class="text-center">
                                    <i class="fas fa-plus text-gray-400 text-2xl mb-1"></i>
                                    <p class="text-sm text-gray-500" data-lang-key="front">Front</p>
                                </div>
                                <input type="file" name="photos[]" accept="image/*" class="hidden" required>
                            </div>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg h-32 flex items-center justify-center cursor-pointer hover:bg-gray-50 transition photo-upload">
                                <div class="text-center">
                                    <i class="fas fa-plus text-gray-400 text-2xl mb-1"></i>
                                    <p class="text-sm text-gray-500" data-lang-key="back">Back</p>
                                </div>
                                <input type="file" name="photos[]" accept="image/*" class="hidden" required>
                            </div>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg h-32 flex items-center justify-center cursor-pointer hover:bg-gray-50 transition photo-upload">
                                <div class="text-center">
                                    <i class="fas fa-plus text-gray-400 text-2xl mb-1"></i>
                                    <p class="text-sm text-gray-500" data-lang-key="interior">Interior</p>
                                </div>
                                <input type="file" name="photos[]" accept="image/*" class="hidden" required>
                            </div>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg h-32 flex items-center justify-center cursor-pointer hover:bg-gray-50 transition photo-upload">
                                <div class="text-center">
                                    <i class="fas fa-plus text-gray-400 text-2xl mb-1"></i>
                                    <p class="text-sm text-gray-500" data-lang-key="side">Side</p>
                                </div>
                                <input type="file" name="photos[]" accept="image/*" class="hidden" required>
                            </div>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg h-32 flex items-center justify-center cursor-pointer hover:bg-gray-50 transition photo-upload">
                                <div class="text-center">
                                    <i class="fas fa-plus text-gray-400 text-2xl mb-1"></i>
                                    <p class="text-sm text-gray-500" data-lang-key="additional">Additional</p>
                                </div>
                                <input type="file" name="photos[]" accept="image/*" class="hidden">
                            </div>
                        </div>
                        <p class="text-sm text-gray-500 mt-2" data-lang-key="high_quality_photos">High-quality photos increase your booking rate by up to 35%</p>
                    </div>

                    <div class="mb-8">
                        <label class="block text-gray-700 mb-2" data-lang-key="vehicle_description">Vehicle Description *</label>
                        <textarea name="description" rows="4" required placeholder="Describe your car. What makes it special? Why would renters enjoy driving it?" class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none"></textarea>
                    </div>

                    <!-- Availability & Pricing -->
                    <h3 class="text-xl font-bold mb-4 border-b pb-2" data-lang-key="availability_pricing">Availability & Pricing</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                        <div>
                            <label class="block text-gray-700 mb-2" data-lang-key="availability">Availability *</label>
                            <select name="availability" required class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none">
                                <option value="">Select Availability</option>
                                <option value="weekends">Weekends Only</option>
                                <option value="weekdays">Weekdays Only</option>
                                <option value="everyday">Everyday</option>
                                <option value="custom">Custom Schedule</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-gray-700 mb-2" data-lang-key="advance_notice">Advance Notice *</label>
                            <select name="advance_notice" required class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none">
                                <option value="">Select Advance Notice</option>
                                <option value="instant">Instant Book (No Notice)</option>
                                <option value="1_hour">1 Hour</option>
                                <option value="3_hours">3 Hours</option>
                                <option value="6_hours">6 Hours</option>
                                <option value="12_hours">12 Hours</option>
                                <option value="24_hours">1 Day</option>
                            </select>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                        <div>
                            <label class="block text-gray-700 mb-2" data-lang-key="daily_rate">Daily Rate (EUR) *</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span class="text-gray-500">€</span>
                                </div>
                                <input type="number" name="daily_rate" min="1" required placeholder="e.g. 45" class="w-full pl-8 p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none">
                            </div>
                            <p class="text-sm text-gray-500 mt-1" data-lang-key="rate_suggested">Suggested: €35-150 based on your car</p>
                        </div>
                        <div>
                            <label class="block text-gray-700 mb-2" data-lang-key="weekly_discount">Weekly Discount</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span class="text-gray-500">%</span>
                                </div>
                                <input type="number" name="weekly_discount" min="0" max="50" placeholder="e.g. 10" class="w-full pl-8 p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none">
                            </div>
                            <p class="text-sm text-gray-500 mt-1" data-lang-key="weekly_recommended">Recommended: 10-15%</p>
                        </div>
                        <div>
                            <label class="block text-gray-700 mb-2" data-lang-key="monthly_discount">Monthly Discount</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span class="text-gray-500">%</span>
                                </div>
                                <input type="number" name="monthly_discount" min="0" max="70" placeholder="e.g. 25" class="w-full pl-8 p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none">
                            </div>
                            <p class="text-sm text-gray-500 mt-1" data-lang-key="monthly_recommended">Recommended: 20-30%</p>
                        </div>
                    </div>

                    <div class="mb-8">
                        <label class="block text-gray-700 mb-2" data-lang-key="pickup_location">Pickup & Return Location *</label>
                        <input type="text" name="location" required placeholder="Enter address" class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:outline-none mb-2">
                        <p class="text-sm text-gray-500" data-lang-key="location_safety">For safety, this will only be shown to confirmed renters</p>
                    </div>

                    <!-- Terms and Conditions -->
                    <div class="mb-8">
                        <label class="flex items-start">
                            <input type="checkbox" name="confirm_ownership" required class="mt-1 mr-2">
                            <span class="text-gray-700" data-lang-key="confirm_ownership">I confirm that I own this vehicle or am authorized to list it, and that it meets CARBNB eligibility requirements including valid registration and insurance. *</span>
                        </label>
                    </div>

                    <div class="mb-8">
                        <label class="flex items-start">
                            <input type="checkbox" name="agree_terms" required class="mt-1 mr-2">
                            <span class="text-gray-700" data-lang-key="agree_terms">I agree to the <a href="/terms" class="text-primary hover:underline">Terms of Service</a> and <a href="/privacy" class="text-primary hover:underline">Privacy Policy</a>. *</span>
                        </label>
                    </div>

                    <div class="text-center">
                        <button type="submit" id="submit-listing" class="px-8 py-4 bg-primary text-white font-bold rounded-lg hover:bg-red-700 transition" data-lang-key="submit_listing">Submit Your Listing</button>
                        <p class="text-sm text-gray-500 mt-4" data-lang-key="team_review">After submission, our team will review your listing within 24 hours.</p>
                    </div>
                </form>
            </div>
        </div>
    </section>

    @push('scripts')
        <!-- SweetAlert2 -->
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

        <script>
            $(document).ready(function() {
                // FAQ toggles
                $('.faq-toggle').click(function() {
                    $(this).find('i').toggleClass('rotate-180');
                    $(this).next('.faq-content').slideToggle();
                });

                // Days slider
                $('#days-slider').on('input', function() {
                    $('#days-value').text($(this).val());
                });

                // Calculate earnings button
                $('#calculate-btn').click(function() {
                    const carMake = $('#car-make').val();
                    const carYear = $('#car-year').val();
                    const daysAvailable = $('#days-slider').val();

                    // Simple calculation formula
                    let baseRate = 35;

                    // Adjust for premium brands
                    if (['BMW', 'Tesla', 'Mercedes'].includes(carMake)) {
                        baseRate = 65;
                    }

                    // Adjust for newer cars
                    if (['2020', '2021', '2022', '2023'].includes(carYear)) {
                        baseRate += 15;
                    }

                    const estimatedEarnings = baseRate * daysAvailable;

                    // Display results
                    $('#earnings-amount').text(estimatedEarnings);
                    $('#earnings-result').removeClass('hidden');
                });

                // Photo upload handling
                $('.photo-upload').click(function() {
                    $(this).find('input[type="file"]').click();
                });

                $('.photo-upload input[type="file"]').change(function() {
                    const file = this.files[0];
                    if (file) {
                        const reader = new FileReader();
                        const photoUpload = $(this).parent();

                        reader.onload = function(e) {
                            photoUpload.css('background-image', `url(${e.target.result})`);
                            photoUpload.css('background-size', 'cover');
                            photoUpload.css('background-position', 'center');
                            photoUpload.find('div').addClass('hidden');
                        }

                        reader.readAsDataURL(file);
                    }
                });

                // Form submission with SweetAlert
                $('#car-listing-form').submit(function(e) {
                    e.preventDefault();

                    // Form validation
                    const form = this;
                    const requiredFields = $(form).find('input[required], select[required], textarea[required]');
                    let valid = true;

                    requiredFields.each(function() {
                        if (!$(this).val()) {
                            valid = false;
                            $(this).addClass('border-red-500');
                        } else {
                            $(this).removeClass('border-red-500');
                        }
                    });

                    if (!valid) {
                        Swal.fire({
                            title: 'Validation Error',
                            text: 'Please fill in all required fields.',
                            icon: 'error',
                            confirmButtonText: 'OK',
                            confirmButtonColor: '#FF0000'
                        });
                        return;
                    }

                    // Show loading state
                    Swal.fire({
                        title: 'Submitting...',
                        html: 'Please wait while we process your listing.',
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    // Create FormData and submit
                    const formData = new FormData(form);

                    // AJAX submission
                    $.ajax({
                        url: $(form).attr('action'),
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function(response) {
                            Swal.fire({
                                title: 'Success!',
                                text: 'Your car listing has been submitted successfully. Our team will review it shortly.',
                                icon: 'success',
                                confirmButtonText: 'Great!',
                                confirmButtonColor: '#FF0000'
                            }).then((result) => {
                                // Redirect to the seller dashboard
                                window.location.href = "{{ url('/') }}";
                            });
                        },
                        error: function(xhr, status, error) {
                            // Parse the error response
                            let errorMessage = 'An error occurred while submitting your listing.';

                            if (xhr.responseJSON && xhr.responseJSON.errors) {
                                const errors = xhr.responseJSON.errors;
                                const firstError = Object.values(errors)[0];
                                if (firstError && firstError.length > 0) {
                                    errorMessage = firstError[0];
                                }
                            }

                            Swal.fire({
                                title: 'Error',
                                text: errorMessage,
                                icon: 'error',
                                confirmButtonText: 'Try Again',
                                confirmButtonColor: '#FF0000'
                            });
                        }
                    });
                });
            });
        </script>
    @endpush
</x-app-layout>

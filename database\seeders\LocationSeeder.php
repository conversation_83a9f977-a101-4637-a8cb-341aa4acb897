<?php

namespace Database\Seeders;

use App\Models\Country;
use App\Models\State;
use App\Models\City;
use Illuminate\Database\Seeder;

class LocationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create a country
        $country = Country::firstOrCreate(
            ['code' => 'US'],
            [
                'name' => 'United States',
                'zone_id' => 1,
                'status' => Country::STATUS_ACTIVE,
            ]
        );

        // Create a state
        $state = State::firstOrCreate(
            ['name' => 'New York', 'country_id' => $country->id],
            [
                'status' => 1,
            ]
        );

        // Create a city
        City::firstOrCreate(
            ['name' => 'New York City', 'state_id' => $state->id],
            [
                'latitude' => 40.7128,
                'longitude' => -74.0060,
                'cost' => 0,
                'is_active' => true,
            ]
        );

        // Create another state
        $state = State::firstOrCreate(
            ['name' => 'California', 'country_id' => $country->id],
            [
                'status' => 1,
            ]
        );

        // Create a city
        City::firstOrCreate(
            ['name' => 'Los Angeles', 'state_id' => $state->id],
            [
                'latitude' => 34.0522,
                'longitude' => -118.2437,
                'cost' => 0,
                'is_active' => true,
            ]
        );
    }
}

<!-- Header -->
<header class="bg-white shadow-md sticky top-0 z-50">
    <div class="container mx-auto px-4 py-3">
        <div class="flex justify-between items-center">
            <div class="flex items-center">
                <img src="{{ asset('/images/logo.png') }}" alt="CARBNB Logo" class="h-10">
    {{--            <span class="ml-2 text-2xl font-bold text-primary">CARBNB</span>--}}
            </div>
            <!-- Mobile menu button -->
            <button id="mobile-menu-toggle" class="md:hidden p-2 rounded-full hover:bg-gray-100 text-gray-600 hover:text-primary focus:outline-none">
                <i class="fas fa-bars text-xl"></i>
            </button>
            <nav class="hidden md:flex space-x-8">
                <a href="{{ route('home') }}" class="text-primary font-medium" data-lang-key="home">{{ frontend_trans('home') }}</a>
                <a href="{{ route('cars.listing') }}" class="text-gray-600 hover:text-primary transition" data-lang-key="rent_car">{{ frontend_trans('rent_car') }}</a>
                <a href="{{ route('list-car') }}" class="text-gray-600 hover:text-primary transition" data-lang-key="list_car">{{ frontend_trans('list_car') }}</a>
                <a href="{{ route('quick-driver-registration') }}" class="text-gray-600 hover:text-primary transition" data-lang-key="become_driver">{{ frontend_trans('become_driver') }}</a>
                <a href="{{ route('home') }}#how-it-works" class="text-gray-600 hover:text-primary transition" data-lang-key="how_works">{{ frontend_trans('how_works') }}</a>
                <a href="{{ route('home') }}#contact" class="text-gray-600 hover:text-primary transition" data-lang-key="contact">{{ frontend_trans('contact') }}</a>
            </nav>
            <div class="hidden md:flex items-center space-x-4">
            <!-- Language Selector -->
            <div class="relative mr-4">
                <button id="language-button" class="flex items-center text-gray-600 hover:text-primary">
                    <i class="fas fa-globe mr-1"></i>
                    <span id="current-language">EN</span>
                    <i class="fas fa-chevron-down ml-1 text-xs"></i>
                </button>
                <div id="language-dropdown" class="absolute right-0 mt-2 w-24 bg-white rounded-md shadow-lg hidden z-50">
                    <a href="{{ route('change.locale', 'en') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" data-lang="en">English</a>
                    <a href="{{ route('change.locale', 'pt') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" data-lang="pt">Português</a>
                </div>
            </div>
@auth
    @if(auth()->user()->hasRole(['admin', 'agent']))
    <a href="{{ route('revenue.dashboard') }}" class="hidden md:inline-block btn-outline-primary">
        <i class="fas fa-chart-line mr-1"></i> {{ frontend_trans('earnings') }}
    </a>
    @endif
    <a href="{{ route('dashboard') }}" class="btn-primary">
        <i class="fas fa-user mr-1"></i> {{ frontend_trans('dashboard') }}
    </a>
@else
    <a href="{{ route('login') }}" class="hidden md:inline-block btn-outline-primary" data-lang-key="login">{{ frontend_trans('login') }}</a>
    <a href="{{ route('register') }}" class="btn-primary" data-lang-key="signup">{{ frontend_trans('signup') }}</a>
@endauth
            </div>
        </div>
    </div>
    <!-- Mobile Menu -->
    <div id="mobile-menu" class="md:hidden hidden bg-white border-t">
        <a href="{{ route('home') }}" class="block py-3 px-4 text-primary font-medium border-b" data-lang-key="home">{{ frontend_trans('home') }}</a>
        <a href="{{ route('cars.listing') }}" class="block py-3 px-4 text-gray-600 border-b" data-lang-key="rent_car">{{ frontend_trans('rent_car') }}</a>
        <a href="{{ route('list-car') }}" class="block py-3 px-4 text-gray-600 border-b" data-lang-key="list_car">{{ frontend_trans('list_car') }}</a>
        <a href="{{ route('quick-driver-registration') }}" class="block py-3 px-4 text-gray-600 border-b" data-lang-key="become_driver">{{ frontend_trans('become_driver') }}</a>
        <a href="{{ route('home') }}#how-it-works" class="block py-3 px-4 text-gray-600 border-b" data-lang-key="how_works">{{ frontend_trans('how_works') }}</a>
        <a href="{{ route('home') }}#contact" class="block py-3 px-4 text-gray-600 border-b" data-lang-key="contact">{{ frontend_trans('contact') }}</a>

        @auth
            @if(auth()->user()->hasRole(['admin', 'agent']))
            <a href="{{ route('revenue.dashboard') }}" class="block py-3 px-4 text-gray-600 border-b">
                <i class="fas fa-chart-line mr-1"></i> {{ frontend_trans('earnings_dashboard') }}
            </a>
            @endif
            <a href="{{ route('dashboard') }}" class="block py-3 px-4 text-gray-600 border-b">
                <i class="fas fa-user mr-1"></i> {{ frontend_trans('my_dashboard') }}
            </a>
        @else
            <a href="{{ route('login') }}" class="block py-3 px-4 text-gray-600 border-b" data-lang-key="login">{{ frontend_trans('login') }}</a>
        @endauth
        <!-- Language selection for mobile -->
        <div class="py-3 px-4 text-gray-600 flex items-center border-b">
            <span data-lang-key="language">{{ frontend_trans('language') }}:</span>
            <a href="{{ route('change.locale', 'en') }}" class="ml-3 px-2" data-lang="en">EN</a>
            <span>|</span>
            <a href="{{ route('change.locale', 'pt') }}" class="px-2" data-lang="pt">PT</a>
        </div>
    </div>
</header>

<script>
    // Mobile menu toggle
    document.addEventListener('DOMContentLoaded', function() {
        const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
        const mobileMenu = document.getElementById('mobile-menu');
        const languageButton = document.getElementById('language-button');
        const languageDropdown = document.getElementById('language-dropdown');

        if (mobileMenuToggle && mobileMenu) {
            mobileMenuToggle.addEventListener('click', function() {
                mobileMenu.classList.toggle('hidden');
            });
        }

        if (languageButton && languageDropdown) {
            languageButton.addEventListener('click', function() {
                languageDropdown.classList.toggle('hidden');
            });

            // Close language dropdown when clicking outside
            document.addEventListener('click', function(event) {
                if (!languageButton.contains(event.target) && !languageDropdown.contains(event.target)) {
                    languageDropdown.classList.add('hidden');
                }
            });
        }
    });
</script>

<?php

use App\Models\User;
use App\Models\Vehicle;
use App\Models\VehicleType;
use App\Models\City;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Create vehicle type
    $this->vehicleType = VehicleType::create([
        'name' => 'Sedan',
        'slug' => 'sedan',
        'description' => 'A standard sedan',
        'is_active' => true
    ]);

    // Create city
    $this->city = City::create([
        'name' => 'New York',
        'state_id' => 1,
        'latitude' => 40.7128,
        'longitude' => -74.0060,
        'is_active' => true
    ]);

    // Create user
    $this->user = User::factory()->create([
        'first_name' => 'Test',
        'last_name' => 'User',
        'email' => '<EMAIL>',
    ]);

    // Create some vehicles
    $this->vehicles = [];

    // Create an active vehicle
    $this->vehicles[] = Vehicle::create([
        'user_id' => $this->user->id,
        'vehicle_type_id' => $this->vehicleType->id,
        'make' => 'Toyota',
        'model' => 'Camry',
        'year' => 2022,
        'license_plate' => 'ABC123',
        'color' => 'Blue',
        'mileage' => 15000,
        'transmission' => 'automatic',
        'fuel_type' => 'gasoline',
        'seats' => '5',
        'doors' => 4,
        'features' => ['bluetooth', 'air_conditioning'],
        'description' => 'A comfortable sedan for your journey',
        'daily_rate' => 50.00,
        'city_id' => $this->city->id,
        'status' => 'active',
        'is_featured' => true,
        'latitude' => 40.7128,
        'longitude' => -74.0060,
    ]);

    // Create a pending vehicle
    $this->vehicles[] = Vehicle::create([
        'user_id' => $this->user->id,
        'vehicle_type_id' => $this->vehicleType->id,
        'make' => 'Honda',
        'model' => 'Accord',
        'year' => 2021,
        'license_plate' => 'DEF456',
        'color' => 'Red',
        'mileage' => 20000,
        'transmission' => 'automatic',
        'fuel_type' => 'hybrid',
        'seats' => '5',
        'doors' => 4,
        'features' => ['bluetooth', 'air_conditioning', 'gps'],
        'description' => 'A hybrid sedan for eco-friendly travel',
        'daily_rate' => 60.00,
        'city_id' => $this->city->id,
        'status' => 'pending',
        'is_featured' => false,
        'latitude' => 40.7128,
        'longitude' => -74.0060,
    ]);

    // Create a vehicle with driver
    $this->vehicles[] = Vehicle::create([
        'user_id' => $this->user->id,
        'vehicle_type_id' => $this->vehicleType->id,
        'make' => 'Mercedes',
        'model' => 'S-Class',
        'year' => 2023,
        'license_plate' => 'GHI789',
        'color' => 'Black',
        'mileage' => 5000,
        'transmission' => 'automatic',
        'fuel_type' => 'gasoline',
        'seats' => '5',
        'doors' => 4,
        'features' => ['bluetooth', 'air_conditioning', 'leather_seats', 'sunroof'],
        'description' => 'A luxury sedan with chauffeur service',
        'daily_rate' => 150.00,
        'city_id' => $this->city->id,
        'status' => 'active',
        'is_featured' => true,
        'with_driver' => true,
        'latitude' => 40.7128,
        'longitude' => -74.0060,
    ]);
});

test('home page shows featured vehicles', function () {
    $response = $this->get('/');

    $response->assertStatus(200);
    $response->assertSee('Toyota Camry');
    $response->assertSee('Mercedes S-Class');
    $response->assertDontSee('Honda Accord'); // Pending, should not be visible
});

test('cars listing page shows all active vehicles', function () {
    $response = $this->get('/cars');

    $response->assertStatus(200);
    $response->assertSee('Toyota Camry');
    $response->assertSee('Mercedes S-Class');
    $response->assertDontSee('Honda Accord'); // Pending, should not be visible
});

test('vehicle details page shows correct vehicle information', function () {
    $vehicle = $this->vehicles[0]; // Toyota Camry

    $response = $this->get("/vehicle/{$vehicle->id}");

    $response->assertStatus(200);
    $response->assertSee('Toyota');
    $response->assertSee('Camry');
    $response->assertSee('2022');
    $response->assertSee('$50.00');
});

test('cars listing can filter by vehicle type', function () {
    // Create a new vehicle type
    $suvType = VehicleType::create([
        'name' => 'SUV',
        'slug' => 'suv',
        'description' => 'Sport Utility Vehicle',
        'is_active' => true
    ]);

    // Create an SUV vehicle
    Vehicle::create([
        'user_id' => $this->user->id,
        'vehicle_type_id' => $suvType->id,
        'make' => 'Toyota',
        'model' => 'RAV4',
        'year' => 2022,
        'license_plate' => 'SUV123',
        'color' => 'Green',
        'mileage' => 10000,
        'transmission' => 'automatic',
        'fuel_type' => 'gasoline',
        'seats' => '5',
        'doors' => 5,
        'features' => ['bluetooth', 'air_conditioning'],
        'description' => 'A compact SUV for adventure',
        'daily_rate' => 70.00,
        'city_id' => $this->city->id,
        'status' => 'active',
        'is_featured' => false,
        'latitude' => 40.7128,
        'longitude' => -74.0060,
    ]);

    // Filter by sedan type
    $response = $this->get("/cars?vehicle_type={$this->vehicleType->id}");

    $response->assertStatus(200);
    $response->assertSee('Toyota Camry');
    $response->assertSee('Mercedes S-Class');
    $response->assertDontSee('Toyota RAV4');

    // Filter by SUV type
    $response = $this->get("/cars?vehicle_type={$suvType->id}");

    $response->assertStatus(200);
    $response->assertSee('Toyota RAV4');
    $response->assertDontSee('Toyota Camry');
    $response->assertDontSee('Mercedes S-Class');
});

test('cars listing can filter by with driver option', function () {
    $response = $this->get('/cars?with_driver=1');

    $response->assertStatus(200);
    $response->assertSee('Mercedes S-Class');
    $response->assertDontSee('Toyota Camry');
});

test('cars listing can filter by price range', function () {
    $response = $this->get('/cars?min_price=100&max_price=200');

    $response->assertStatus(200);
    $response->assertSee('Mercedes S-Class');
    $response->assertDontSee('Toyota Camry');

    $response = $this->get('/cars?min_price=40&max_price=60');

    $response->assertStatus(200);
    $response->assertSee('Toyota Camry');
    $response->assertDontSee('Mercedes S-Class');
});

test('cars listing can search by location', function () {
    // Create a different city
    $losAngeles = City::create([
        'name' => 'Los Angeles',
        'state_id' => 2,
        'latitude' => 34.0522,
        'longitude' => -118.2437,
        'is_active' => true
    ]);

    // Create a vehicle in Los Angeles
    Vehicle::create([
        'user_id' => $this->user->id,
        'vehicle_type_id' => $this->vehicleType->id,
        'make' => 'Ford',
        'model' => 'Mustang',
        'year' => 2022,
        'license_plate' => 'LA123',
        'color' => 'Yellow',
        'mileage' => 5000,
        'transmission' => 'automatic',
        'fuel_type' => 'gasoline',
        'seats' => '4',
        'doors' => 2,
        'features' => ['bluetooth', 'air_conditioning'],
        'description' => 'A sporty car for cruising LA',
        'daily_rate' => 80.00,
        'city_id' => $losAngeles->id,
        'status' => 'active',
        'is_featured' => false,
        'latitude' => 34.0522,
        'longitude' => -118.2437,
    ]);

    // Search for New York vehicles
    $response = $this->get("/cars?city_id={$this->city->id}");

    $response->assertStatus(200);
    $response->assertSee('Toyota Camry');
    $response->assertSee('Mercedes S-Class');
    $response->assertDontSee('Ford Mustang');

    // Search for Los Angeles vehicles
    $response = $this->get("/cars?city_id={$losAngeles->id}");

    $response->assertStatus(200);
    $response->assertSee('Ford Mustang');
    $response->assertDontSee('Toyota Camry');
    $response->assertDontSee('Mercedes S-Class');
});

<?php if (isset($component)) { $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742 = $attributes; } ?>
<?php $component = App\View\Components\AppBackendLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-backend-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppBackendLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container mx-auto p-6">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-2xl font-bold text-gray-800">Manage Permissions</h2>
            <button id="create-permission-btn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg shadow-md transition">Create Permission</button>
        </div>

        <div class="bg-white shadow-md rounded-lg overflow-hidden">
            <table class="w-full text-left text-sm text-gray-700">
                <thead class="bg-gray-100 text-gray-600 text-sm uppercase">
                <tr>
                    <th class="px-6 py-3">#</th>
                    <th class="px-6 py-3">Name</th>
                    <th class="px-6 py-3 text-right">Actions</th>
                </tr>
                </thead>
                <tbody>
                <?php $__currentLoopData = $permissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr class="border-b hover:bg-gray-50">
                        <td class="px-6 py-4"><?php echo e($permission->id); ?></td>
                        <td class="px-6 py-4"><?php echo e($permission->name); ?></td>
                        <td class="px-6 py-4 text-right">
                            <button class="text-blue-600 hover:text-blue-800 transition edit-permission-btn" data-permission-id="<?php echo e($permission->id); ?>" data-permission-name="<?php echo e($permission->name); ?>">Edit</button>
                            <form action="<?php echo e(route('permissions.destroy', $permission->id)); ?>" method="POST" class="inline">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit" class="text-red-600 hover:text-red-800 transition ml-4" onclick="return confirm('Are you sure?')">Delete</button>
                            </form>
                        </td>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Modal for creating/editing permission -->
    <div id="permission-modal" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 hidden">
        <div class="bg-white rounded-lg shadow-lg p-6 w-96">
            <h3 id="modal-title" class="text-lg font-semibold text-gray-800 mb-4">Create Permission</h3>
            <form id="permission-form" action="<?php echo e(route('permissions.store')); ?>" method="POST" class="space-y-4">
                <?php echo csrf_field(); ?>
                <input type="hidden" name="_method" id="form-method" value="POST">
                <div>
                    <label for="name" class="block text-gray-700 font-medium">Permission Name</label>
                    <input type="text" name="name" id="permission-name" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring focus:ring-blue-300">
                </div>
                <div class="flex justify-end space-x-2">
                    <button type="button" class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded-lg" id="close-modal">Cancel</button>
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">Save</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        document.getElementById('create-permission-btn').addEventListener('click', function () {
            document.getElementById('permission-modal').classList.remove('hidden');
            document.getElementById('modal-title').textContent = 'Create Permission';
            document.getElementById('permission-form').action = "<?php echo e(route('permissions.store')); ?>";
            document.getElementById('form-method').value = 'POST';
            document.getElementById('permission-name').value = '';
        });

        document.querySelectorAll('.edit-permission-btn').forEach(button => {
            button.addEventListener('click', function () {
                const permissionId = this.getAttribute('data-permission-id');
                const permissionName = this.getAttribute('data-permission-name');
                document.getElementById('permission-modal').classList.remove('hidden');
                document.getElementById('modal-title').textContent = 'Edit Permission';
                document.getElementById('permission-form').action = `/permissions/${permissionId}`;
                document.getElementById('form-method').value = 'PUT';
                document.getElementById('permission-name').value = permissionName;
            });
        });

        document.getElementById('close-modal').addEventListener('click', function () {
            document.getElementById('permission-modal').classList.add('hidden');
        });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $attributes = $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $component = $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\carbnb\resources\views\backend\access-control\permissions.blade.php ENDPATH**/ ?>
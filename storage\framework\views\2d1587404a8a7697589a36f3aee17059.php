<?php if (isset($component)) { $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742 = $attributes; } ?>
<?php $component = App\View\Components\AppBackendLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-backend-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppBackendLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h2 class="text-2xl font-semibold text-gray-800 dark:text-gray-200 leading-tight">
            <?php echo e(__('Dashboard')); ?>

        </h2>
     <?php $__env->endSlot(); ?>

    <!-- FullCalendar CSS -->
    <link href='https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.css' rel='stylesheet' />

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">


            <!-- Admin Dashboard -->
            <?php if($userRoles->contains('admin')): ?>
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4"><?php echo e(__('Admin Dashboard')); ?></h3>
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div class="bg-white overflow-hidden shadow-sm rounded-lg p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-indigo-100 text-indigo-600">
                                    <svg class="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500"><?php echo e(__('Total Users')); ?></p>
                                    <p class="text-2xl font-semibold text-gray-800"><?php echo e($stats['admin']['total_users']); ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white overflow-hidden shadow-sm rounded-lg p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-green-100 text-green-600">
                                    <svg class="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500"><?php echo e(__('Total Bookings')); ?></p>
                                    <p class="text-2xl font-semibold text-gray-800"><?php echo e($stats['admin']['total_bookings']); ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white overflow-hidden shadow-sm rounded-lg p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                                    <svg class="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500"><?php echo e(__('Pending Approvals')); ?></p>
                                    <p class="text-2xl font-semibold text-gray-800"><?php echo e($stats['admin']['pending_approvals']); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Booking Calendar -->
                    <div class="mt-6 bg-white overflow-hidden shadow-sm rounded-lg">
                        <div class="p-6">
                            <h4 class="text-lg font-semibold text-gray-800 mb-4"><?php echo e(__('Booking Calendar')); ?></h4>
                            <div class="flex mb-4 space-x-2 flex-wrap">
                                <div class="flex items-center mr-4 mb-2">
                                    <span class="inline-block w-4 h-4 mr-2 bg-blue-500 rounded-full"></span>
                                    <span class="text-sm text-gray-600">Vehicle Booking</span>
                                </div>
                                <div class="flex items-center mr-4 mb-2">
                                    <span class="inline-block w-4 h-4 mr-2 bg-green-500 rounded-full"></span>
                                    <span class="text-sm text-gray-600">Vehicle with Driver</span>
                                </div>
                                <div class="flex items-center mr-4 mb-2">
                                    <span class="inline-block w-4 h-4 mr-2 border-2 border-yellow-500 rounded-full"></span>
                                    <span class="text-sm text-gray-600">Pending</span>
                                </div>
                                <div class="flex items-center mr-4 mb-2">
                                    <span class="inline-block w-4 h-4 mr-2 border-2 border-green-500 rounded-full"></span>
                                    <span class="text-sm text-gray-600">Confirmed</span>
                                </div>
                                <div class="flex items-center mr-4 mb-2">
                                    <span class="inline-block w-4 h-4 mr-2 border-2 border-purple-500 rounded-full"></span>
                                    <span class="text-sm text-gray-600">In Progress</span>
                                </div>
                                <div class="flex items-center mr-4 mb-2">
                                    <span class="inline-block w-4 h-4 mr-2 bg-red-500 rounded-full"></span>
                                    <span class="text-sm text-gray-600">Cancelled/Rejected</span>
                                </div>
                            </div>
                            <div id="booking-calendar" class="h-96"></div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Agent Dashboard -->
            <?php if($userRoles->contains('agent')): ?>
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4"><?php echo e(__('Agent Dashboard')); ?></h3>
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div class="bg-white overflow-hidden shadow-sm rounded-lg p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                                    <svg class="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500"><?php echo e(__('Managed Vehicles')); ?></p>
                                    <p class="text-2xl font-semibold text-gray-800"><?php echo e($stats['agent']['managed_vehicles']); ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white overflow-hidden shadow-sm rounded-lg p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-green-100 text-green-600">
                                    <svg class="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500"><?php echo e(__('Active Vehicles')); ?></p>
                                    <p class="text-2xl font-semibold text-gray-800"><?php echo e($stats['agent']['active_vehicles']); ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white overflow-hidden shadow-sm rounded-lg p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                                    <svg class="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500"><?php echo e(__('Pending Vehicles')); ?></p>
                                    <p class="text-2xl font-semibold text-gray-800"><?php echo e($stats['agent']['pending_vehicles']); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="mt-6 bg-white overflow-hidden shadow-sm rounded-lg">
                        <div class="p-6">
                            <h4 class="text-lg font-semibold text-gray-800 mb-4"><?php echo e(__('Quick Actions')); ?></h4>
                            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                                <a href="<?php echo e(route('vehicles.create')); ?>" class="flex items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                                    <div class="p-2 rounded-full bg-blue-100 text-blue-600">
                                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-700"><?php echo e(__('Add New Vehicle')); ?></p>
                                    </div>
                                </a>

                                <a href="<?php echo e(route('vehicles.index')); ?>" class="flex items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
                                    <div class="p-2 rounded-full bg-green-100 text-green-600">
                                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-700"><?php echo e(__('Manage Vehicles')); ?></p>
                                    </div>
                                </a>

                                <a href="<?php echo e(route('revenue.dashboard')); ?>" class="flex items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
                                    <div class="p-2 rounded-full bg-purple-100 text-purple-600">
                                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-700"><?php echo e(__('View Revenue')); ?></p>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Booking Calendar -->
                    <div class="mt-6 bg-white overflow-hidden shadow-sm rounded-lg">
                        <div class="p-6">
                            <h4 class="text-lg font-semibold text-gray-800 mb-4"><?php echo e(__('Booking Calendar')); ?></h4>
                            <div class="flex mb-4 space-x-2 flex-wrap">
                                <div class="flex items-center mr-4 mb-2">
                                    <span class="inline-block w-4 h-4 mr-2 bg-blue-500 rounded-full"></span>
                                    <span class="text-sm text-gray-600">Vehicle Booking</span>
                                </div>
                                <div class="flex items-center mr-4 mb-2">
                                    <span class="inline-block w-4 h-4 mr-2 bg-green-500 rounded-full"></span>
                                    <span class="text-sm text-gray-600">Vehicle with Driver</span>
                                </div>
                                <div class="flex items-center mr-4 mb-2">
                                    <span class="inline-block w-4 h-4 mr-2 border-2 border-yellow-500 rounded-full"></span>
                                    <span class="text-sm text-gray-600">Pending</span>
                                </div>
                                <div class="flex items-center mr-4 mb-2">
                                    <span class="inline-block w-4 h-4 mr-2 border-2 border-green-500 rounded-full"></span>
                                    <span class="text-sm text-gray-600">Confirmed</span>
                                </div>
                                <div class="flex items-center mr-4 mb-2">
                                    <span class="inline-block w-4 h-4 mr-2 border-2 border-purple-500 rounded-full"></span>
                                    <span class="text-sm text-gray-600">In Progress</span>
                                </div>
                                <div class="flex items-center mr-4 mb-2">
                                    <span class="inline-block w-4 h-4 mr-2 bg-red-500 rounded-full"></span>
                                    <span class="text-sm text-gray-600">Cancelled/Rejected</span>
                                </div>
                            </div>
                            <div id="agent-booking-calendar" class="h-96"></div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- User Dashboard -->
            <?php if($userRoles->contains('user')): ?>
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4"><?php echo e(__('My Dashboard')); ?></h3>
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div class="bg-white overflow-hidden shadow-sm rounded-lg p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                                    <svg class="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500"><?php echo e(__('My Vehicles')); ?></p>
                                    <p class="text-2xl font-semibold text-gray-800"><?php echo e($stats['user']['my_vehicles']); ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white overflow-hidden shadow-sm rounded-lg p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-green-100 text-green-600">
                                    <svg class="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500"><?php echo e(__('My Bookings')); ?></p>
                                    <p class="text-2xl font-semibold text-gray-800"><?php echo e($stats['user']['my_bookings']); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="mt-6 bg-white overflow-hidden shadow-sm rounded-lg">
                        <div class="p-6">
                            <h4 class="text-lg font-semibold text-gray-800 mb-4"><?php echo e(__('Quick Actions')); ?></h4>
                            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                                <a href="<?php echo e(route('front.vehicles.search')); ?>" class="flex items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                                    <div class="p-2 rounded-full bg-blue-100 text-blue-600">
                                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-700"><?php echo e(__('Find a Vehicle')); ?></p>
                                    </div>
                                </a>

                                <a href="<?php echo e(route('front.bookings.index')); ?>" class="flex items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
                                    <div class="p-2 rounded-full bg-green-100 text-green-600">
                                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-700"><?php echo e(__('My Bookings')); ?></p>
                                    </div>
                                </a>

                                <a href="<?php echo e(route('profile.edit')); ?>" class="flex items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
                                    <div class="p-2 rounded-full bg-purple-100 text-purple-600">
                                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-700"><?php echo e(__('My Profile')); ?></p>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Booking Calendar -->
                    <div class="mt-6 bg-white overflow-hidden shadow-sm rounded-lg">
                        <div class="p-6">
                            <h4 class="text-lg font-semibold text-gray-800 mb-4"><?php echo e(__('Booking Calendar')); ?></h4>
                            <div class="flex mb-4 space-x-2 flex-wrap">
                                <div class="flex items-center mr-4 mb-2">
                                    <span class="inline-block w-4 h-4 mr-2 bg-blue-500 rounded-full"></span>
                                    <span class="text-sm text-gray-600">Vehicle Booking</span>
                                </div>
                                <div class="flex items-center mr-4 mb-2">
                                    <span class="inline-block w-4 h-4 mr-2 bg-green-500 rounded-full"></span>
                                    <span class="text-sm text-gray-600">Vehicle with Driver</span>
                                </div>
                                <div class="flex items-center mr-4 mb-2">
                                    <span class="inline-block w-4 h-4 mr-2 border-2 border-yellow-500 rounded-full"></span>
                                    <span class="text-sm text-gray-600">Pending</span>
                                </div>
                                <div class="flex items-center mr-4 mb-2">
                                    <span class="inline-block w-4 h-4 mr-2 border-2 border-green-500 rounded-full"></span>
                                    <span class="text-sm text-gray-600">Confirmed</span>
                                </div>
                                <div class="flex items-center mr-4 mb-2">
                                    <span class="inline-block w-4 h-4 mr-2 border-2 border-purple-500 rounded-full"></span>
                                    <span class="text-sm text-gray-600">In Progress</span>
                                </div>
                                <div class="flex items-center mr-4 mb-2">
                                    <span class="inline-block w-4 h-4 mr-2 bg-red-500 rounded-full"></span>
                                    <span class="text-sm text-gray-600">Cancelled/Rejected</span>
                                </div>
                            </div>
                            <div id="user-booking-calendar" class="h-96"></div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Driver Dashboard -->
            <?php if($userRoles->contains('driver')): ?>
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4"><?php echo e(__('Driver Dashboard')); ?></h3>
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div class="bg-white overflow-hidden shadow-sm rounded-lg p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                                    <svg class="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500"><?php echo e(__('Completed Trips')); ?></p>
                                    <p class="text-2xl font-semibold text-gray-800"><?php echo e($stats['driver']['completed_trips'] ?? 0); ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white overflow-hidden shadow-sm rounded-lg p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-green-100 text-green-600">
                                    <svg class="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500"><?php echo e(__('Total Earnings')); ?></p>
                                    <p class="text-2xl font-semibold text-gray-800"><?php echo e($stats['driver']['total_earnings'] ?? 0); ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white overflow-hidden shadow-sm rounded-lg p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                                    <svg class="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500"><?php echo e(__('Upcoming Trips')); ?></p>
                                    <p class="text-2xl font-semibold text-gray-800"><?php echo e($stats['driver']['upcoming_trips'] ?? 0); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="mt-6 bg-white overflow-hidden shadow-sm rounded-lg">
                        <div class="p-6">
                            <h4 class="text-lg font-semibold text-gray-800 mb-4"><?php echo e(__('Quick Actions')); ?></h4>
                            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                                <a href="<?php echo e(route('driver.trips')); ?>" class="flex items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                                    <div class="p-2 rounded-full bg-blue-100 text-blue-600">
                                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-700"><?php echo e(__('My Trips')); ?></p>
                                    </div>
                                </a>

                                <a href="<?php echo e(route('driver.profile')); ?>" class="flex items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
                                    <div class="p-2 rounded-full bg-green-100 text-green-600">
                                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-700"><?php echo e(__('Driver Profile')); ?></p>
                                    </div>
                                </a>

                                <a href="<?php echo e(route('driver.earnings')); ?>" class="flex items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
                                    <div class="p-2 rounded-full bg-purple-100 text-purple-600">
                                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-700"><?php echo e(__('My Earnings')); ?></p>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- FullCalendar JS -->
    <script src='https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.js'></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Admin Calendar
            const adminCalendarEl = document.getElementById('booking-calendar');
            if (adminCalendarEl) {
                const adminCalendar = new FullCalendar.Calendar(adminCalendarEl, {
                    initialView: 'dayGridMonth',
                    headerToolbar: {
                        left: 'prev,next today',
                        center: 'title',
                        right: 'dayGridMonth,timeGridWeek,timeGridDay'
                    },
                    events: '<?php echo e(route("admin.bookings.calendar-events")); ?>',
                    eventClick: function(info) {
                        window.location.href = '/admin/bookings/' + info.event.id;
                    }
                });
                adminCalendar.render();
            }

            // Agent Calendar
            const agentCalendarEl = document.getElementById('agent-booking-calendar');
            if (agentCalendarEl) {
                const agentCalendar = new FullCalendar.Calendar(agentCalendarEl, {
                    initialView: 'dayGridMonth',
                    headerToolbar: {
                        left: 'prev,next today',
                        center: 'title',
                        right: 'dayGridMonth,timeGridWeek,timeGridDay'
                    },
                    events: '<?php echo e(route("agent.bookings.calendar-events")); ?>',
                    eventClick: function(info) {
                        window.location.href = '/agent/bookings/' + info.event.id;
                    }
                });
                agentCalendar.render();
            }

            // User Calendar
            const userCalendarEl = document.getElementById('user-booking-calendar');
            if (userCalendarEl) {
                const userCalendar = new FullCalendar.Calendar(userCalendarEl, {
                    initialView: 'dayGridMonth',
                    headerToolbar: {
                        left: 'prev,next today',
                        center: 'title',
                        right: 'dayGridMonth,timeGridWeek,timeGridDay'
                    },
                    events: '<?php echo e(route("user.bookings.calendar-events")); ?>',
                    eventClick: function(info) {
                        window.location.href = '/bookings/' + info.event.id;
                    }
                });
                userCalendar.render();
            }
        });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $attributes = $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $component = $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?><?php /**PATH C:\laragon\www\carbnb\resources\views\dashboard-updated.blade.php ENDPATH**/ ?>
/* Consistent UI Styles */

/* Heading System */
h1, .h1 {
    font-size: 2.25rem; /* 36px */
    line-height: 2.5rem; /* 40px */
    font-weight: 700;
    margin-bottom: 1rem;
    color: #1f2937; /* gray-800 */
}

h2, .h2 {
    font-size: 1.875rem; /* 30px */
    line-height: 2.25rem; /* 36px */
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: #1f2937; /* gray-800 */
}

h3, .h3 {
    font-size: 1.5rem; /* 24px */
    line-height: 2rem; /* 32px */
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: #1f2937; /* gray-800 */
}

h4, .h4 {
    font-size: 1.25rem; /* 20px */
    line-height: 1.75rem; /* 28px */
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1f2937; /* gray-800 */
}

h5, .h5 {
    font-size: 1.125rem; /* 18px */
    line-height: 1.75rem; /* 28px */
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1f2937; /* gray-800 */
}

h6, .h6 {
    font-size: 1rem; /* 16px */
    line-height: 1.5rem; /* 24px */
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1f2937; /* gray-800 */
}

/* Dark mode support */
.dark h1, .dark .h1,
.dark h2, .dark .h2,
.dark h3, .dark .h3,
.dark h4, .dark .h4,
.dark h5, .dark .h5,
.dark h6, .dark .h6 {
    color: #f9fafb; /* gray-50 */
}

/* Page Header Styles */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.page-header h1,
.page-header h2,
.page-header h3 {
    margin-bottom: 0;
}

/* Card Styles */
.content-card {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.dark .content-card {
    background-color: #1f2937; /* gray-800 */
}

/* Section Styles */
.content-section {
    margin-bottom: 2rem;
}

.section-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #4b5563; /* gray-600 */
}

.dark .section-title {
    color: #e5e7eb; /* gray-200 */
}

/* Button Styles */
.btn-primary {
    background-color: var(--primary-color, #ba1c1c);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.btn-primary:hover {
    background-color: var(--primary-color-hover, #a01818);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.btn-outline-primary {
    background-color: transparent;
    color: var(--primary-color, #ba1c1c);
    border: 1px solid var(--primary-color, #ba1c1c);
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color, #ba1c1c);
    color: white;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.btn-secondary {
    background-color: #6b7280; /* gray-500 */
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.btn-secondary:hover {
    background-color: #4b5563; /* gray-600 */
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.btn-outline-secondary {
    background-color: transparent;
    color: #6b7280; /* gray-500 */
    border: 1px solid #6b7280; /* gray-500 */
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.btn-outline-secondary:hover {
    background-color: #6b7280; /* gray-500 */
    color: white;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Icon spacing in buttons and navigation */
.btn-icon, .nav-icon {
    margin-right: 0.75rem;
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #4b5563; /* gray-600 */
}

.dark .form-label {
    color: #e5e7eb; /* gray-200 */
}

.form-input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db; /* gray-300 */
    border-radius: 0.375rem;
    background-color: white;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color, #ba1c1c);
    box-shadow: 0 0 0 3px rgba(186, 28, 28, 0.2);
}

.dark .form-input {
    background-color: #374151; /* gray-700 */
    border-color: #4b5563; /* gray-600 */
    color: #f9fafb; /* gray-50 */
}

/* Table Styles */
.table-container {
    overflow-x: auto;
    margin-bottom: 1.5rem;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    background-color: #f9fafb; /* gray-50 */
    padding: 0.75rem 1rem;
    text-align: left;
    font-weight: 600;
    color: #4b5563; /* gray-600 */
    border-bottom: 1px solid #e5e7eb; /* gray-200 */
}

.data-table td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e5e7eb; /* gray-200 */
}

.data-table tr:hover {
    background-color: #f9fafb; /* gray-50 */
}

.dark .data-table th {
    background-color: #374151; /* gray-700 */
    color: #e5e7eb; /* gray-200 */
    border-bottom: 1px solid #4b5563; /* gray-600 */
}

.dark .data-table td {
    border-bottom: 1px solid #4b5563; /* gray-600 */
}

.dark .data-table tr:hover {
    background-color: #1f2937; /* gray-800 */
}

/* Sidebar Improvements */
.sidebar-nav-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: #e5e7eb; /* gray-200 */
    border-radius: 0.375rem;
    transition: all 0.2s;
}

.sidebar-nav-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-nav-item.active {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
}

.sidebar-nav-icon {
    margin-right: 0.75rem;
    width: 1.25rem;
    text-align: center;
}

/* Fix for any elements with strike-through styling */
.text-primary, .font-bold, .font-extrabold, .font-medium {
    text-decoration: none !important;
    text-decoration-line: none !important;
}

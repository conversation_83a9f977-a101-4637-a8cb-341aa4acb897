<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Conversation extends Model
{
    use HasFactory, SoftDeletes;
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_one_id',
        'user_two_id',
        'booking_id',
        'last_message_at',
        'is_read_user_one',
        'is_read_user_two'
    ];
    
    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'last_message_at' => 'datetime',
        'is_read_user_one' => 'boolean',
        'is_read_user_two' => 'boolean',
    ];
    
    /**
     * Get the first user in the conversation.
     */
    public function userOne()
    {
        return $this->belongsTo(User::class, 'user_one_id');
    }
    
    /**
     * Get the second user in the conversation.
     */
    public function userTwo()
    {
        return $this->belongsTo(User::class, 'user_two_id');
    }
    
    /**
     * Get the booking associated with the conversation, if any.
     */
    public function booking()
    {
        return $this->belongsTo(Booking::class);
    }
    
    /**
     * Get the messages in this conversation.
     */
    public function messages()
    {
        return $this->hasMany(Message::class)->orderBy('created_at', 'asc');
    }
    
    /**
     * Get the latest message in this conversation.
     */
    public function latestMessage()
    {
        return $this->hasOne(Message::class)->latest();
    }
    
    /**
     * Check if the conversation is unread for a specific user.
     */
    public function isUnreadFor($userId)
    {
        if ($userId == $this->user_one_id) {
            return !$this->is_read_user_one;
        } elseif ($userId == $this->user_two_id) {
            return !$this->is_read_user_two;
        }
        
        return false;
    }
    
    /**
     * Mark the conversation as read for a specific user.
     */
    public function markAsReadFor($userId)
    {
        if ($userId == $this->user_one_id) {
            $this->update(['is_read_user_one' => true]);
        } elseif ($userId == $this->user_two_id) {
            $this->update(['is_read_user_two' => true]);
        }
    }
    
    /**
     * Get the other user in the conversation.
     */
    public function getOtherUser($userId)
    {
        if ($userId == $this->user_one_id) {
            return $this->userTwo;
        } elseif ($userId == $this->user_two_id) {
            return $this->userOne;
        }
        
        return null;
    }
    
    /**
     * Scope a query to only include conversations for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_one_id', $userId)->orWhere('user_two_id', $userId);
    }
    
    /**
     * Scope a query to only include unread conversations for a specific user.
     */
    public function scopeUnreadForUser($query, $userId)
    {
        return $query->where(function($query) use ($userId) {
            $query->where('user_one_id', $userId)->where('is_read_user_one', false);
        })->orWhere(function($query) use ($userId) {
            $query->where('user_two_id', $userId)->where('is_read_user_two', false);
        });
    }
}

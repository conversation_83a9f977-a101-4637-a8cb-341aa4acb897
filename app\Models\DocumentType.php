<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DocumentType extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'description',
        'requires_verification',
        'has_expiry',
        'is_required',
        'applies_to',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'requires_verification' => 'boolean',
        'has_expiry' => 'boolean',
        'is_required' => 'boolean',
    ];

    /**
     * Get the documents of this type.
     */
    public function documents()
    {
        return $this->hasMany(Document::class);
    }

    /**
     * Scope a query to only include document types that apply to a specific entity.
     */
    public function scopeAppliesTo($query, $entity)
    {
        return $query->where('applies_to', $entity)->orWhere('applies_to', 'all');
    }

    /**
     * Scope a query to only include required document types.
     */
    public function scopeRequired($query)
    {
        return $query->where('is_required', true);
    }
}

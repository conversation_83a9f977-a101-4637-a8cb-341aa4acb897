<?php
    $navLinks = [
        ['route' => 'dashboard', 'label' => 'dashboard', 'icon' => 'fas fa-home', 'permission' => 'dashboard'],
        ['route' => 'revenue.dashboard', 'label' => 'revenue_dashboard', 'icon' => 'fas fa-chart-line', 'permission' => 'dashboard'],
        ['route' => 'referrals.index', 'label' => 'referrals', 'icon' => 'fas fa-user-friends', 'permission' => 'dashboard'],
        [
            'label' => 'admin',
            'icon' => 'fas fa-user-shield',
            'permission' => 'admin.documents',
            'submenu' => [
                ['route' => 'admin.documents.index', 'label' => 'document_verification', 'icon' => 'fas fa-file-alt', 'permission' => 'admin.documents'],
                ['route' => 'admin.drivers.index', 'label' => 'driver_management', 'icon' => 'fas fa-id-card', 'permission' => 'admin.drivers'],
            ]
        ],
        [
            'label' => 'bookings',
            'icon' => 'fas fa-calendar-alt',
            'permission' => 'bookings',
            'submenu' => [
                ['route' => 'booking.index', 'label' => 'my_bookings', 'icon' => 'fas fa-calendar-check', 'permission' => 'bookings.view'],
                ['route' => 'booking.owner', 'label' => 'received_bookings', 'icon' => 'fas fa-calendar-plus', 'permission' => 'bookings.view'],
            ]
        ],
        [
            'label' => 'vehicles',
            'icon' => 'fas fa-car',
            'permission' => 'vehicles',
            'submenu' => [
                ['route' => 'vehicles.index', 'label' => 'vehicles', 'icon' => 'fas fa-car-side', 'permission' => 'vehicles'],
                ['route' => 'vehicles.create', 'label' => 'add_vehicles', 'icon' => 'fas fa-plus-circle', 'permission' => 'vehicles.create'],
                ['route' => 'vehicle-types.index', 'label' => 'vehicle_type', 'icon' => 'fas fa-tags', 'permission' => 'vehicles'],
            ]
        ],
        [
            'label' => 'driver_account',
            'icon' => 'fas fa-id-card',
            'permission' => 'drivers',
            'submenu' => [
                ['route' => 'drivers.register', 'label' => 'register_as_driver', 'icon' => 'fas fa-user-plus', 'permission' => 'drivers.view'],
                ['route' => 'drivers.profile', 'label' => 'driver_profile', 'icon' => 'fas fa-user-tie', 'permission' => 'drivers.view'],
                ['route' => 'drivers.availability', 'label' => 'availability', 'icon' => 'fas fa-clock', 'permission' => 'drivers.view'],
                ['route' => 'drivers.documents.upload', 'label' => 'documents', 'icon' => 'fas fa-file-alt', 'permission' => 'drivers.view'],
            ]
        ],
        ['route' => 'messages.index', 'label' => 'messages', 'icon' => 'fas fa-envelope', 'permission' => 'messages'],
        ['route' => 'disputes.index', 'label' => 'disputes', 'icon' => 'fas fa-gavel', 'permission' => 'disputes'],
        ['route' => 'pages.index', 'label' => 'pages', 'icon' => 'fas fa-file-alt', 'permission' => 'pages'],
        [
            'label' => 'configurations',
            'icon' => 'fas fa-cogs',
            'permission' => 'settings',
            'submenu' => [
                ['route' => 'activation.index', 'label' => 'features_activation', 'icon' => 'fas fa-toggle-on', 'permission' => 'settings'],
                ['route' => 'theme_settings', 'label' => 'theme_settings', 'icon' => 'fas fa-palette', 'permission' => 'settings'],
            ]
        ],

        // ['route' => 'file-manager.index', 'label' => 'file_manager', 'icon' => 'fas fa-folder', 'permission' => 'file-manager'],

        [
            'label' => 'location',
            'icon' => 'fas fa-map-marker-alt',
            'permission' => 'locations.view',
            'submenu' => [
                ['route' => 'countries.index', 'label' => 'countries', 'icon' => 'fas fa-globe', 'permission' => 'locations.view'],
                ['route' => 'states.index', 'label' => 'states', 'icon' => 'fas fa-map', 'permission' => 'locations.view'],
                ['route' => 'cities.index', 'label' => 'cities', 'icon' => 'fas fa-city', 'permission' => 'locations.view'],
            ]
        ],

        // Main Menu: Access Control
        [
            'label' => 'access_control',
            'icon' => 'fas fa-shield-alt',
            'permission' => 'users.view',
            'submenu' => [
                ['route' => 'users.index', 'label' => 'users', 'icon' => 'fas fa-user', 'permission' => 'users.view'],
                ['route' => 'roles.index', 'label' => 'roles', 'icon' => 'fas fa-user-shield', 'permission' => 'roles.view'],
                ['route' => 'permissions.index', 'label' => 'permissions', 'icon' => 'fas fa-lock', 'permission' => 'permissions.view'],
            ]
        ],
    ];

    // Define which permissions each role has access to
    $roleBasedPermissions = [
        'admin' => [
            'dashboard', 'vehicles', 'vehicles.create', 'vehicles.edit', 'vehicles.delete',
            'pages', 'settings', 'file-manager', 'locations.view', 'users.view', 'roles.view', 'permissions.view',
            'bookings', 'bookings.view', 'drivers', 'drivers.view', 'messages', 'disputes',
            'admin.documents', 'admin.drivers'
        ],
        'agent' => [
            'dashboard', 'vehicles', 'vehicles.create', 'vehicles.edit', 'file-manager',
            'bookings', 'bookings.view', 'messages', 'disputes'
        ],
        'user' => [
            'dashboard', 'vehicles', 'vehicles.create', 'vehicles.edit', 'file-manager',
            'bookings', 'bookings.view', 'drivers', 'drivers.view', 'messages', 'disputes'
        ],
        'driver' => [
            'dashboard', 'drivers', 'drivers.view', 'bookings', 'bookings.view', 'messages', 'disputes'
        ],
    ];

    $userRoles = Auth::check() ? Auth::user()->getRoleNames()->toArray() : [];

    // Function to check if a submenu contains the current route
    function isMenuActive($submenu) {
        foreach ($submenu as $item) {
            if (request()->routeIs($item['route'])) {
                return true;
            }
        }
        return false;
    }
?>

<!-- Sidebar Header -->
<div class="py-6 px-4 flex items-center justify-center border-b border-gray-700">
    <span class="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-red-600 to-red-800"><?php echo e(env('APP_NAME')); ?></span>
</div>

<!-- Navigation Menu -->
<nav class="py-4">
    <div class="space-y-1 px-3">
        <?php $__currentLoopData = $navLinks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $link): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php
                $allowed = false;
                foreach ($userRoles as $role) {
                    if (isset($roleBasedPermissions[$role]) &&
                        in_array(strtolower($link['permission']), $roleBasedPermissions[$role])) {
                        $allowed = true;
                        break;
                    }
                }

                // Check if this menu should be expanded
                $isActive = isset($link['submenu']) ? isMenuActive($link['submenu']) : request()->routeIs($link['route']);
            ?>
            <?php if($allowed): ?>
                <?php if(isset($link['submenu'])): ?>
                    <!-- Dropdown Menu -->
                    <div class="mb-2" x-data="{ open: <?php echo e($isActive ? 'true' : 'false'); ?> }">
                        <button @click="open = !open"
                                class="w-full flex items-center px-4 py-3 text-sm rounded-lg font-medium transition-colors duration-150 ease-in-out
                                <?php echo e($isActive ? 'bg-primary text-white' : 'text-gray-200 hover:bg-gray-700'); ?>">
                            <i class="<?php echo e($link['icon']); ?> mr-4 text-lg <?php echo e($isActive ? 'text-white' : 'text-gray-400'); ?>"></i>
                            <span><?php echo e(backend_trans($link['label'])); ?></span>
                            <i class="fas fa-chevron-down ml-auto transform transition-transform duration-200"
                               :class="open ? 'rotate-180' : ''"></i>
                        </button>
                        <div class="mt-2 pl-4 space-y-1" x-show="open"
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="opacity-0 transform -translate-y-2"
                             x-transition:enter-end="opacity-100 transform translate-y-0"
                             x-transition:leave="transition ease-in duration-150"
                             x-transition:leave-start="opacity-100 transform translate-y-0"
                             x-transition:leave-end="opacity-0 transform -translate-y-2"
                             style="display: none;">
                            <?php $__currentLoopData = $link['submenu']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $submenu): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                    $submenuAllowed = false;
                                    foreach ($userRoles as $role) {
                                        if (isset($roleBasedPermissions[$role]) &&
                                            in_array(strtolower($submenu['permission']), $roleBasedPermissions[$role])) {
                                            $submenuAllowed = true;
                                            break;
                                        }
                                    }
                                ?>
                                <?php if($submenuAllowed): ?>
                                    <?php
                                        try {
                                            $submenuUrl = route($submenu['route']);
                                            $submenuRouteExists = true;
                                        } catch (\Exception $e) {
                                            $submenuUrl = '#';
                                            $submenuRouteExists = false;
                                        }
                                        $isSubmenuActive = $submenuRouteExists && request()->routeIs($submenu['route']);
                                    ?>
                                    <a href="<?php echo e($submenuUrl); ?>"
                                       class="flex items-center pl-3 pr-4 py-2 text-sm rounded-md transition-colors duration-150 ease-in-out
                                       <?php echo e($isSubmenuActive ? 'bg-red-700 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white'); ?>">
                                        <i class="<?php echo e($submenu['icon']); ?> mr-4 text-sm <?php echo e($isSubmenuActive ? 'text-white' : 'text-gray-400'); ?>"></i>
                                        <span><?php echo e(backend_trans($submenu['label'])); ?></span>
                                    </a>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- Regular Menu Item -->
                    <?php
                        try {
                            $linkUrl = route($link['route']);
                            $linkRouteExists = true;
                        } catch (\Exception $e) {
                            $linkUrl = '#';
                            $linkRouteExists = false;
                        }
                        $isLinkActive = $linkRouteExists && request()->routeIs($link['route']);
                    ?>
                    <a href="<?php echo e($linkUrl); ?>"
                       class="flex items-center px-4 py-3 mb-2 text-sm rounded-lg font-medium transition-colors duration-150 ease-in-out
                       <?php echo e($isLinkActive ? 'bg-primary text-white' : 'text-gray-200 hover:bg-gray-700'); ?>">
                        <i class="<?php echo e($link['icon']); ?> mr-4 text-lg <?php echo e($isLinkActive ? 'text-white' : 'text-gray-400'); ?>"></i>
                        <span><?php echo e(backend_trans($link['label'])); ?></span>
                        <?php if($isLinkActive): ?>
                            <span class="ml-auto">
                                <i class="fas fa-circle text-xs text-white"></i>
                            </span>
                        <?php endif; ?>
                    </a>
                <?php endif; ?>
            <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
</nav>

<!-- Sidebar Footer -->
<div class="mt-auto border-t border-gray-700 p-4">
    <div class="text-xs text-gray-400 text-center">
        <p>© <?php echo e(date('Y')); ?> <?php echo e(env('APP_NAME')); ?></p>
    </div>
</div>
<?php /**PATH C:\laragon\www\carbnb\resources\views/backend/layouts/partials/sidebar.blade.php ENDPATH**/ ?>
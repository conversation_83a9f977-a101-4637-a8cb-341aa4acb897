<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Vehicle extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'vehicle_type_id',
        'make',
        'model',
        'year',
        'license_plate',
        'color',
        'mileage',
        'transmission',
        'fuel_type',
        'seats',
        'doors',
        'features',
        'description',
        'availability',
        'advance_notice',
        'daily_rate',
        'weekly_discount',
        'monthly_discount',
        'latitude',
        'longitude',
        'address',
        'city_id',
        'with_driver',
        'status',
        'is_featured',
        'security_deposit',
        'rejection_reason'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'features' => 'array',
        'year' => 'integer',
        'mileage' => 'integer',
        'doors' => 'integer',
        'daily_rate' => 'decimal:2',
        'weekly_discount' => 'decimal:2',
        'monthly_discount' => 'decimal:2',
        'security_deposit' => 'decimal:2',
        'with_driver' => 'boolean',
        'is_featured' => 'boolean',
    ];

    public function city()
    {
        return $this->belongsTo(City::class);
    }

// Scope to find vehicles within a radius (in km)
    public function scopeNearby($query, $lat, $lng, $radius = 10)
    {
        $haversine = "(
        6371 * acos(
            cos(radians($lat))
            * cos(radians(latitude))
            * cos(radians(longitude) - radians($lng))
            + sin(radians($lat))
            * sin(radians(latitude))
        )
    )";

        return $query->selectRaw("*, $haversine AS distance")
            ->whereRaw("$haversine < ?", [$radius])
            ->orderBy('distance');
    }

    /**
     * Get the vehicle type that this vehicle belongs to.
     */
    public function vehicleType()
    {
        return $this->belongsTo(VehicleType::class);
    }

    /**
     * Get the user that owns the vehicle.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the images for the vehicle.
     */
    public function images()
    {
        return $this->hasMany(VehicleImage::class)->with('file');
    }

    /**
     * Get the primary image for the vehicle.
     */
    public function primaryImage()
    {
        return $this->hasOne(VehicleImage::class)->where('is_primary', true)->with('file');
    }

    /**
     * Get formatted daily rate with currency symbol.
     */
    public function getFormattedDailyRateAttribute()
    {
        return '$' . number_format($this->daily_rate, 2);
    }

    /**
     * Get the full name of the vehicle (year, make, model).
     */
    public function getFullNameAttribute()
    {
        return "{$this->year} {$this->make} {$this->model}";
    }

    /**
     * Scope a query to only include active vehicles.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include featured vehicles.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope a query to filter by vehicle type.
     */
    public function scopeOfType($query, $typeId)
    {
        return $query->where('vehicle_type_id', $typeId);
    }

    /**
     * Scope a query to filter by price range.
     */
    public function scopePriceRange($query, $min, $max)
    {
        if ($min !== null) {
            $query->where('daily_rate', '>=', $min);
        }

        if ($max !== null) {
            $query->where('daily_rate', '<=', $max);
        }

        return $query;
    }

    /**
     * Scope a query to filter by transmission type.
     */
    public function scopeTransmissionType($query, $type)
    {
        return $query->where('transmission', $type);
    }

    /**
     * Scope a query to filter by fuel type.
     */
    public function scopeFuelType($query, $type)
    {
        return $query->where('fuel_type', $type);
    }

    /**
     * Scope a query to filter by minimum seats.
     */
    public function scopeMinSeats($query, $seats)
    {
        // Handle the '8+' case
        if ($seats === '8+') {
            return $query->where('seats', '8+');
        }

        // For numeric seats, find vehicles with at least that many seats
        $validSeats = ['2', '4', '5', '6', '7', '8+'];
        $minSeatIndex = array_search($seats, $validSeats);

        if ($minSeatIndex !== false) {
            $validSeatOptions = array_slice($validSeats, $minSeatIndex);
            return $query->whereIn('seats', $validSeatOptions);
        }

        return $query;
    }

    /**
     * Scope a query to limit to those with chauffeur/driver service.
     */
    public function scopeWithDriver($query)
    {
        return $query->where('with_driver', true);
    }

    /**
     * Get the bookings for the vehicle.
     */
    public function bookings()
    {
        return $this->hasMany(Booking::class);
    }

    /**
     * Get the reviews for the vehicle.
     */
    public function reviews()
    {
        return $this->hasMany(Review::class);
    }

    /**
     * Get the documents for the vehicle.
     */
    public function documents()
    {
        return $this->hasMany(VehicleDocument::class)->with('documentType', 'file');
    }
}

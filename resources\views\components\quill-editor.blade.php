@props(['name', 'label', 'value' => ''])

<div class="mb-4">
    @if($label)
        <label for="{{ $name }}" class="block text-gray-700 font-medium mb-2">{{ $label }}</label>
    @endif
    
    <div x-data="quillEditor('{{ $name }}', '{{ old($name, $value) }}')" class="relative">
        <div x-ref="quillContainer" class="bg-white"></div>
        <input type="hidden" name="{{ $name }}" x-ref="input">
        @error($name)
            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
        @enderror
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('alpine:init', () => {
    Alpine.data('quillEditor', (name, initialValue) => ({
        init() {
            // Initialize Quill
            this.quill = new Quill(this.$refs.quillContainer, {
                theme: 'snow',
                modules: {
                    toolbar: [
                        ['bold', 'italic', 'underline', 'strike'],
                        ['blockquote', 'code-block'],
                        [{ 'header': 1 }, { 'header': 2 }],
                        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                        [{ 'script': 'sub'}, { 'script': 'super' }],
                        [{ 'indent': '-1'}, { 'indent': '+1' }],
                        [{ 'direction': 'rtl' }],
                        [{ 'size': ['small', false, 'large', 'huge'] }],
                        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                        [{ 'color': [] }, { 'background': [] }],
                        [{ 'font': [] }],
                        [{ 'align': [] }],
                        ['clean'],
                        ['link', 'image', 'video']
                    ]
                },
                placeholder: 'Write something...',
            });

            // Set initial content
            if (initialValue) {
                this.quill.root.innerHTML = initialValue;
            }

            // Update hidden input on change
            this.quill.on('text-change', () => {
                this.$refs.input.value = this.quill.root.innerHTML;
            });

            // Set initial value to hidden input
            this.$refs.input.value = this.quill.root.innerHTML;

            // Handle form submission
            this.$refs.input.closest('form')?.addEventListener('submit', () => {
                this.$refs.input.value = this.quill.root.innerHTML;
            });
        }
    }));
});
</script>
@endpush
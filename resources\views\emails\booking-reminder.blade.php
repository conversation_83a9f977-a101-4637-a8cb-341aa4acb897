<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Booking Reminder</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #39B7B4;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            padding: 20px;
            background-color: #f9f9f9;
        }
        .footer {
            text-align: center;
            padding: 20px;
            font-size: 12px;
            color: #666;
        }
        .button {
            display: inline-block;
            background-color: #39B7B4;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 4px;
            margin-top: 20px;
        }
        .details {
            background-color: white;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
        }
        .details-row {
            margin-bottom: 10px;
        }
        .details-label {
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Booking Reminder</h1>
        </div>
        
        <div class="content">
            @if($recipientType == 'customer')
                <p>Hello {{ $booking->user->name }},</p>
                <p>This is a friendly reminder that your car rental starts tomorrow!</p>
            @else
                <p>Hello {{ $booking->vehicle->user->name }},</p>
                <p>This is a friendly reminder that you have a car rental starting tomorrow!</p>
            @endif
            
            <div class="details">
                <div class="details-row">
                    <span class="details-label">Vehicle:</span> 
                    <span>{{ $booking->vehicle->make }} {{ $booking->vehicle->model }} ({{ $booking->vehicle->year }})</span>
                </div>
                
                <div class="details-row">
                    <span class="details-label">Pickup Date:</span> 
                    <span>{{ $booking->start_date->format('F j, Y') }} at {{ $booking->start_time }}</span>
                </div>
                
                <div class="details-row">
                    <span class="details-label">Return Date:</span> 
                    <span>{{ $booking->end_date->format('F j, Y') }} at {{ $booking->end_time }}</span>
                </div>
                
                <div class="details-row">
                    <span class="details-label">Pickup Location:</span> 
                    <span>{{ $booking->pickup_location }}</span>
                </div>
                
                <div class="details-row">
                    <span class="details-label">Booking Reference:</span> 
                    <span>#{{ $booking->id }}</span>
                </div>
            </div>
            
            @if($recipientType == 'customer')
                <p>Please make sure you have your driver's license and any other required documents with you at the time of pickup.</p>
                <a href="{{ route('bookings.show', $booking->id) }}" class="button">View Booking Details</a>
            @else
                <p>Please ensure the vehicle is ready for the customer's arrival.</p>
                <a href="{{ route('owner.bookings.show', $booking->id) }}" class="button">View Booking Details</a>
            @endif
        </div>
        
        <div class="footer">
            <p>© {{ date('Y') }} CarBnB. All rights reserved.</p>
            <p>If you have any questions, please contact our support <NAME_EMAIL></p>
        </div>
    </div>
</body>
</html>

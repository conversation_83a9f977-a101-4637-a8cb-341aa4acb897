<?php if (isset($component)) { $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742 = $attributes; } ?>
<?php $component = App\View\Components\AppBackendLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-backend-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppBackendLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                <?php echo e(__('Create Dispute')); ?>

            </h2>
            <a href="<?php echo e(route('disputes.index')); ?>" class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-500 active:bg-gray-700 focus:outline-none focus:border-gray-700 focus:ring ring-gray-300 disabled:opacity-25 transition ease-in-out duration-150">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                <?php echo e(__('Back to Disputes')); ?>

            </a>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <?php if(session('error')): ?>
                        <div class="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
                            <?php echo e(session('error')); ?>

                        </div>
                    <?php endif; ?>

                    <form action="<?php echo e(route('disputes.store')); ?>" method="POST" enctype="multipart/form-data" x-data="{ evidenceCount: 1 }">
                        <?php echo csrf_field(); ?>
                        
                        <div class="space-y-6">
                            <!-- Booking Information -->
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2"><?php echo e(__('Booking Information')); ?></h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <p class="text-sm text-gray-600 dark:text-gray-400"><?php echo e(__('Booking Number')); ?>:</p>
                                        <p class="font-medium"><?php echo e($booking->booking_number); ?></p>
                                        <input type="hidden" name="booking_id" value="<?php echo e($booking->id); ?>">
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-600 dark:text-gray-400"><?php echo e(__('Vehicle')); ?>:</p>
                                        <p class="font-medium"><?php echo e($booking->vehicle->make); ?> <?php echo e($booking->vehicle->model); ?> (<?php echo e($booking->vehicle->year); ?>)</p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-600 dark:text-gray-400"><?php echo e(__('Booking Dates')); ?>:</p>
                                        <p class="font-medium"><?php echo e($booking->start_date->format('M d, Y')); ?> - <?php echo e($booking->end_date->format('M d, Y')); ?></p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-600 dark:text-gray-400"><?php echo e(__('Total Amount')); ?>:</p>
                                        <p class="font-medium">$<?php echo e(number_format($booking->total_amount, 2)); ?></p>
                                    </div>
                                </div>
                            </div>

                            <!-- Dispute Against -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300"><?php echo e(__('Filing Dispute Against')); ?></label>
                                <div class="mt-1 p-3 bg-gray-100 dark:bg-gray-700 rounded-md">
                                    <p class="text-sm">
                                        <span class="font-medium"><?php echo e($againstUser->first_name); ?> <?php echo e($againstUser->last_name); ?></span>
                                        <span class="text-gray-500 dark:text-gray-400">(<?php echo e($againstUser->email); ?>)</span>
                                    </p>
                                    <input type="hidden" name="against_user_id" value="<?php echo e($againstUser->id); ?>">
                                </div>
                            </div>

                            <!-- Dispute Type -->
                            <div>
                                <label for="type" class="block text-sm font-medium text-gray-700 dark:text-gray-300"><?php echo e(__('Dispute Type')); ?></label>
                                <select id="type" name="type" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md" required>
                                    <option value=""><?php echo e(__('Select a dispute type')); ?></option>
                                    <option value="damage" <?php echo e(old('type') == 'damage' ? 'selected' : ''); ?>><?php echo e(__('Vehicle Damage')); ?></option>
                                    <option value="cleanliness" <?php echo e(old('type') == 'cleanliness' ? 'selected' : ''); ?>><?php echo e(__('Cleanliness Issues')); ?></option>
                                    <option value="late_return" <?php echo e(old('type') == 'late_return' ? 'selected' : ''); ?>><?php echo e(__('Late Return')); ?></option>
                                    <option value="no_show" <?php echo e(old('type') == 'no_show' ? 'selected' : ''); ?>><?php echo e(__('No Show')); ?></option>
                                    <option value="extra_charges" <?php echo e(old('type') == 'extra_charges' ? 'selected' : ''); ?>><?php echo e(__('Unexpected Extra Charges')); ?></option>
                                    <option value="incorrect_fuel" <?php echo e(old('type') == 'incorrect_fuel' ? 'selected' : ''); ?>><?php echo e(__('Incorrect Fuel Used')); ?></option>
                                    <option value="other" <?php echo e(old('type') == 'other' ? 'selected' : ''); ?>><?php echo e(__('Other')); ?></option>
                                </select>
                                <?php $__errorArgs = ['type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-2 text-sm text-red-600"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Amount Claimed -->
                            <div>
                                <label for="amount_claimed" class="block text-sm font-medium text-gray-700 dark:text-gray-300"><?php echo e(__('Amount Claimed (if applicable)')); ?></label>
                                <div class="mt-1 relative rounded-md shadow-sm">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 sm:text-sm">$</span>
                                    </div>
                                    <input type="number" name="amount_claimed" id="amount_claimed" step="0.01" min="0" class="focus:ring-blue-500 focus:border-blue-500 block w-full pl-7 pr-12 sm:text-sm border-gray-300 rounded-md" placeholder="0.00" value="<?php echo e(old('amount_claimed')); ?>">
                                </div>
                                <?php $__errorArgs = ['amount_claimed'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-2 text-sm text-red-600"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Description -->
                            <div>
                                <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300"><?php echo e(__('Dispute Description')); ?></label>
                                <textarea id="description" name="description" rows="6" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" placeholder="<?php echo e(__('Describe the issue in detail...')); ?>" required><?php echo e(old('description')); ?></textarea>
                                <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-2 text-sm text-red-600"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Evidence Files -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"><?php echo e(__('Evidence (Photos, Documents, etc.)')); ?></label>
                                
                                <template x-for="i in evidenceCount" :key="i">
                                    <div class="mb-4 p-4 border border-gray-200 dark:border-gray-600 rounded-md">
                                        <div class="mb-3">
                                            <label :for="'evidence_title_' + i" class="block text-sm font-medium text-gray-700 dark:text-gray-300"><?php echo e(__('Title')); ?></label>
                                            <input :id="'evidence_title_' + i" :name="'evidence_titles[' + (i-1) + ']'" type="text" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" placeholder="<?php echo e(__('Evidence Title')); ?>">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label :for="'evidence_file_' + i" class="block text-sm font-medium text-gray-700 dark:text-gray-300"><?php echo e(__('File')); ?></label>
                                            <div class="mt-1 flex items-center">
                                                <input type="hidden" :name="'evidence_files[' + (i-1) + ']'" :id="'evidence_file_id_' + i">
                                                <button type="button" :id="'file_selector_' + i" onclick="openFileManager(this.id)" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                                    <svg class="-ml-1 mr-2 h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                                                    </svg>
                                                    <?php echo e(__('Select File')); ?>

                                                </button>
                                                <span :id="'file_name_' + i" class="ml-3 text-sm text-gray-500"></span>
                                            </div>
                                        </div>
                                        
                                        <div>
                                            <label :for="'evidence_description_' + i" class="block text-sm font-medium text-gray-700 dark:text-gray-300"><?php echo e(__('Description')); ?></label>
                                            <textarea :id="'evidence_description_' + i" :name="'evidence_descriptions[' + (i-1) + ']'" rows="2" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" placeholder="<?php echo e(__('Describe this evidence...')); ?>"></textarea>
                                        </div>
                                    </div>
                                </template>
                                
                                <div class="mt-2">
                                    <button type="button" @click="evidenceCount++" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                        <svg class="-ml-0.5 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                        <?php echo e(__('Add More Evidence')); ?>

                                    </button>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="flex justify-end">
                                <button type="submit" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-500 active:bg-blue-700 focus:outline-none focus:border-blue-700 focus:ring ring-blue-300 disabled:opacity-25 transition ease-in-out duration-150">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <?php echo e(__('Submit Dispute')); ?>

                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function openFileManager(buttonId) {
            const index = buttonId.split('_')[2];
            const fileIdInput = document.getElementById('evidence_file_id_' + index);
            const fileNameSpan = document.getElementById('file_name_' + index);
            
            // Open file manager modal or popup here
            // This is a placeholder - you'll need to implement your file manager integration
            window.fileManagerCallback = function(fileId, fileName) {
                fileIdInput.value = fileId;
                fileNameSpan.textContent = fileName;
            };
            
            // Example: Open a modal or redirect to file manager
            window.open('/admin/file-manager?callback=fileManagerCallback&single=true', 'fileManager', 'width=900,height=600');
        }
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $attributes = $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $component = $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\carbnb\resources\views\disputes\create.blade.php ENDPATH**/ ?>
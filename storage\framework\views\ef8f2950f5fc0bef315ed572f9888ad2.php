<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<div class="container py-5">
    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h4>Book <?php echo e($vehicle->make); ?> <?php echo e($vehicle->model); ?></h4>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <?php if($vehicle->vehicleImages->count() > 0): ?>
                                <img src="<?php echo e(asset('storage/' . $vehicle->vehicleImages->first()->image_path)); ?>" class="img-fluid rounded" alt="<?php echo e($vehicle->make); ?> <?php echo e($vehicle->model); ?>">
                            <?php else: ?>
                                <img src="<?php echo e(asset('assets/images/car-placeholder.jpg')); ?>" class="img-fluid rounded" alt="Car placeholder">
                            <?php endif; ?>
                        </div>
                        <div class="col-md-8">
                            <h5><?php echo e($vehicle->make); ?> <?php echo e($vehicle->model); ?> (<?php echo e($vehicle->year); ?>)</h5>
                            <p><strong>Type:</strong> <?php echo e($vehicle->vehicleType->name); ?></p>
                            <p><strong>Location:</strong> <?php echo e($vehicle->city->name); ?></p>
                            <p><strong>Price:</strong> $<?php echo e(number_format($vehicle->price_per_day, 2)); ?> per day</p>
                        </div>
                    </div>

                    <form action="<?php echo e(route('booking.store')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="vehicle_id" value="<?php echo e($vehicle->id); ?>">

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="pickup_date" class="form-label">Pickup Date</label>
                                <input type="date" class="form-control" id="pickup_date" name="pickup_date" value="<?php echo e($pickup_date); ?>" required min="<?php echo e(date('Y-m-d')); ?>">
                            </div>
                            <div class="col-md-6">
                                <label for="return_date" class="form-label">Return Date</label>
                                <input type="date" class="form-control" id="return_date" name="return_date" value="<?php echo e($return_date); ?>" required min="<?php echo e(date('Y-m-d', strtotime('+1 day'))); ?>">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="pickup_location" class="form-label">Pickup Location</label>
                            <input type="text" class="form-control" id="pickup_location" name="pickup_location" required>
                        </div>

                        <div class="mb-3">
                            <label for="special_requests" class="form-label">Special Requests (Optional)</label>
                            <textarea class="form-control" id="special_requests" name="special_requests" rows="3"></textarea>
                        </div>

                        <button type="submit" class="btn btn-primary">Proceed to Payment</button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5>Booking Summary</h5>
                </div>
                <div class="card-body">
                    <p><strong>Pickup Date:</strong> <span id="summary_pickup"><?php echo e($pickup_date); ?></span></p>
                    <p><strong>Return Date:</strong> <span id="summary_return"><?php echo e($return_date); ?></span></p>

                    <?php
                        $pickup = new DateTime($pickup_date);
                        $return = new DateTime($return_date);
                        $days = $return->diff($pickup)->days;
                        $days = max(1, $days);
                        $subtotal = $vehicle->price_per_day * $days;
                        $tax = $subtotal * 0.1; // 10% tax
                        $total = $subtotal + $tax;
                    ?>

                    <p><strong>Duration:</strong> <span id="summary_days"><?php echo e($days); ?></span> days</p>
                    <hr>
                    <p><strong>Daily Rate:</strong> $<?php echo e(number_format($vehicle->price_per_day, 2)); ?></p>
                    <p><strong>Subtotal:</strong> $<span id="summary_subtotal"><?php echo e(number_format($subtotal, 2)); ?></span></p>
                    <p><strong>Tax (10%):</strong> $<span id="summary_tax"><?php echo e(number_format($tax, 2)); ?></span></p>
                    <hr>
                    <h5><strong>Total:</strong> $<span id="summary_total"><?php echo e(number_format($total, 2)); ?></span></h5>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
    // Update booking summary when dates change
    document.addEventListener('DOMContentLoaded', function() {
        const pickupDateInput = document.getElementById('pickup_date');
        const returnDateInput = document.getElementById('return_date');

        const updateSummary = function() {
            const pickupDate = new Date(pickupDateInput.value);
            const returnDate = new Date(returnDateInput.value);

            if(pickupDate && returnDate && returnDate > pickupDate) {
                const diffTime = Math.abs(returnDate - pickupDate);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                document.getElementById('summary_pickup').textContent = pickupDateInput.value;
                document.getElementById('summary_return').textContent = returnDateInput.value;
                document.getElementById('summary_days').textContent = diffDays;

                const dailyRate = <?php echo e($vehicle->price_per_day); ?>;
                const subtotal = dailyRate * diffDays;
                const tax = subtotal * 0.1;
                const total = subtotal + tax;

                document.getElementById('summary_subtotal').textContent = subtotal.toFixed(2);
                document.getElementById('summary_tax').textContent = tax.toFixed(2);
                document.getElementById('summary_total').textContent = total.toFixed(2);
            }
        };

        pickupDateInput.addEventListener('change', updateSummary);
        returnDateInput.addEventListener('change', updateSummary);
    });
</script>
<?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?><?php /**PATH C:\laragon\www\carbnb\resources\views\front\booking-create.blade.php ENDPATH**/ ?>
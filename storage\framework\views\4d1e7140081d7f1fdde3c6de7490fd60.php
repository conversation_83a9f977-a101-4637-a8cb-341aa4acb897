<?php if (isset($component)) { $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742 = $attributes; } ?>
<?php $component = App\View\Components\AppBackendLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-backend-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppBackendLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                <?php echo e(__('Conversation with')); ?> <?php echo e($otherUser->first_name); ?> <?php echo e($otherUser->last_name); ?>

            </h2>
            <div class="flex space-x-2">
                <a href="<?php echo e(route('messages.index')); ?>" class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-500 active:bg-gray-700 focus:outline-none focus:border-gray-700 focus:ring ring-gray-300 disabled:opacity-25 transition ease-in-out duration-150">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    <?php echo e(__('Back to Messages')); ?>

                </a>
                <?php if($conversation->booking): ?>
                    <a href="<?php echo e(route('booking.show', $conversation->booking->id)); ?>" class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-500 active:bg-green-700 focus:outline-none focus:border-green-700 focus:ring ring-green-300 disabled:opacity-25 transition ease-in-out duration-150">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                        <?php echo e(__('View Booking')); ?>

                    </a>
                <?php endif; ?>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <?php if(session('success')): ?>
                        <div class="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded">
                            <?php echo e(session('success')); ?>

                        </div>
                    <?php endif; ?>

                    <?php if(session('error')): ?>
                        <div class="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
                            <?php echo e(session('error')); ?>

                        </div>
                    <?php endif; ?>

                    <?php if($conversation->booking): ?>
                        <div class="mb-4 p-4 bg-blue-50 border border-blue-200 text-blue-700 rounded flex items-center">
                            <i class="fas fa-info-circle mr-2 text-blue-500"></i>
                            <div>
                                <p class="font-medium">This conversation is related to booking #<?php echo e($conversation->booking->booking_number); ?></p>
                                <p class="text-sm"><?php echo e($conversation->booking->vehicle->make); ?> <?php echo e($conversation->booking->vehicle->model); ?> • <?php echo e($conversation->booking->start_date->format('M d, Y')); ?> - <?php echo e($conversation->booking->end_date->format('M d, Y')); ?></p>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="flex flex-col h-[600px]">
                        <!-- Message List -->
                        <div class="flex-1 overflow-y-auto mb-4 p-4 border border-gray-200 rounded-lg">
                            <div class="space-y-4">
                                <?php if($messages->isEmpty()): ?>
                                    <div class="text-center py-8">
                                        <p class="text-gray-500"><?php echo e(__('No messages yet. Start the conversation!')); ?></p>
                                    </div>
                                <?php else: ?>
                                    <?php $__currentLoopData = $messages->sortBy('created_at'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $message): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="flex <?php echo e($message->sender_id === Auth::id() ? 'justify-end' : 'justify-start'); ?>">
                                            <div class="max-w-[70%] <?php echo e($message->sender_id === Auth::id() ? 'bg-blue-100 text-blue-900' : 'bg-gray-100 text-gray-900'); ?> rounded-lg px-4 py-2 shadow">
                                                <div class="text-sm">
                                                    <?php echo e($message->message); ?>

                                                </div>
                                                <div class="text-xs text-gray-500 mt-1 text-right">
                                                    <?php echo e($message->created_at->format('M d, Y g:i A')); ?>

                                                    <?php if($message->sender_id === Auth::id()): ?>
                                                        <?php if($message->is_read): ?>
                                                            <span class="ml-1 text-blue-600">✓✓</span>
                                                        <?php else: ?>
                                                            <span class="ml-1 text-gray-400">✓</span>
                                                        <?php endif; ?>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Reply Form -->
                        <div class="mt-auto">
                            <form action="<?php echo e(route('messages.reply', $conversation->id)); ?>" method="POST">
                                <?php echo csrf_field(); ?>
                                <div class="flex items-start space-x-4">
                                    <div class="min-w-0 flex-1">
                                        <div class="relative">
                                            <textarea id="message" name="message" rows="3" class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6" placeholder="<?php echo e(__('Type your message...')); ?>" required></textarea>
                                        </div>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <button type="submit" class="inline-flex items-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600">
                                            <svg class="-ml-0.5 mr-1.5 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                            </svg>
                                            <?php echo e(__('Send')); ?>

                                        </button>
                                    </div>
                                </div>
                                <?php $__errorArgs = ['message'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-2 text-sm text-red-600"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Auto-scroll to bottom of messages on page load
        document.addEventListener('DOMContentLoaded', function() {
            const messageContainer = document.querySelector('.overflow-y-auto');
            messageContainer.scrollTop = messageContainer.scrollHeight;
        });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $attributes = $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $component = $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\carbnb\resources\views\messages\show.blade.php ENDPATH**/ ?>
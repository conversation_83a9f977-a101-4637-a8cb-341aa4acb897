<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Use updateOrCreate to avoid errors when running the seeder multiple times
        Role::updateOrCreate(['name' => 'admin']);
        Role::updateOrCreate(['name' => 'agent']);
        Role::updateOrCreate(['name' => 'user']);
        Role::updateOrCreate(['name' => 'driver']);
    }
}

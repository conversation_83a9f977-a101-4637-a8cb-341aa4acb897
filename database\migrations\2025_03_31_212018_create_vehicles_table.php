<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vehicles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('vehicle_type_id')->constrained();
            $table->string('make');
            $table->string('model');
            $table->year('year');
            $table->string('license_plate', 12)->unique();
            $table->string('color');
            $table->integer('mileage');
            $table->enum('transmission', ['automatic', 'manual']);
            $table->enum('fuel_type', ['gasoline', 'diesel', 'hybrid', 'electric', 'plugin_hybrid']);
            $table->enum('seats', ['2', '4', '5', '6', '7', '8+']);
            $table->integer('doors');
            $table->json('features')->nullable();
            $table->text('description')->nullable();
            $table->decimal('latitude', 10, 7)->nullable();
            $table->decimal('longitude', 10, 7)->nullable();
            $table->string('address')->nullable();
            $table->foreignId('city_id')->nullable()->constrained('cities');
            $table->enum('availability', ['weekends', 'weekdays', 'everyday', 'custom']);
            $table->enum('advance_notice', ['instant', '1_hour', '3_hours', '6_hours', '12_hours', '24_hours']);
            $table->decimal('daily_rate', 10, 2);
            $table->decimal('weekly_discount', 5, 2)->nullable()->default(0);
            $table->decimal('monthly_discount', 5, 2)->nullable()->default(0);
            $table->boolean('with_driver')->default(false);
            $table->enum('status', ['pending', 'active', 'rejected', 'inactive'])->default('pending');
            $table->boolean('is_featured')->default(false);
            $table->decimal('security_deposit', 10, 2)->default(0);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::disableForeignKeyConstraints();
        Schema::dropIfExists('vehicles');
        Schema::enableForeignKeyConstraints();
    }
};

<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;

class FileUploadServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Create the uploads directory in public if it doesn't exist
        $uploadsDir = public_path('uploads');
        if (!File::exists($uploadsDir)) {
            File::makeDirectory($uploadsDir, 0755, true);
        }

        // Register a macro for the Storage facade to get a URL that works regardless of symlinks
        Storage::macro('secureUrl', function ($path) {
            // If using the 'public' disk, try to use the direct public path first
            if (Storage::getDefaultDriver() === 'public') {
                $publicPath = public_path('storage/' . $path);
                $uploadsPath = public_path('uploads/' . basename($path));
                
                // If the file exists in public/storage, use that URL
                if (File::exists($publicPath)) {
                    return asset('storage/' . $path);
                }
                
                // If the file exists in public/uploads, use that URL
                if (File::exists($uploadsPath)) {
                    return asset('uploads/' . basename($path));
                }
            }
            
            // Fall back to the standard URL method
            return Storage::url($path);
        });
    }
}

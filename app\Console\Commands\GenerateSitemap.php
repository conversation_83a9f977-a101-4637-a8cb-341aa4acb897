<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\File;
use App\Models\Vehicle;
use App\Models\City;
use App\Models\VehicleType;

class GenerateSitemap extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sitemap:generate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate the sitemap.xml file';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Generating sitemap...');

        $sitemap = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL;
        $sitemap .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . PHP_EOL;

        // Add static pages
        $staticPages = [
            route('home'),
            route('about'),
            route('contact'),
            route('faq'),
            route('terms'),
            route('privacy'),
        ];

        foreach ($staticPages as $url) {
            $sitemap .= $this->addUrl($url, '1.0', 'daily');
        }

        // Add vehicle pages
        $vehicles = Vehicle::where('status', 'active')->get();
        foreach ($vehicles as $vehicle) {
            $url = route('vehicles.show', $vehicle->id);
            $sitemap .= $this->addUrl($url, '0.8', 'weekly');
        }

        // Add city pages
        $cities = City::all();
        foreach ($cities as $city) {
            $url = route('vehicles.by.city', $city->id);
            $sitemap .= $this->addUrl($url, '0.7', 'weekly');
        }

        // Add vehicle type pages
        $vehicleTypes = VehicleType::all();
        foreach ($vehicleTypes as $type) {
            $url = route('vehicles.by.type', $type->id);
            $sitemap .= $this->addUrl($url, '0.7', 'weekly');
        }

        $sitemap .= '</urlset>';

        // Save the sitemap
        File::put(public_path('sitemap.xml'), $sitemap);

        $this->info('Sitemap generated successfully!');
    }

    /**
     * Add a URL to the sitemap
     *
     * @param string $url
     * @param string $priority
     * @param string $changefreq
     * @return string
     */
    private function addUrl($url, $priority = '0.5', $changefreq = 'monthly')
    {
        $url = htmlspecialchars($url);
        $lastmod = date('Y-m-d');

        return "    <url>
        <loc>{$url}</loc>
        <lastmod>{$lastmod}</lastmod>
        <changefreq>{$changefreq}</changefreq>
        <priority>{$priority}</priority>
    </url>" . PHP_EOL;
    }
}

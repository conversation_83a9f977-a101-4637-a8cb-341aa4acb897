<?php

namespace App\Http\Controllers;

use App\Models\VehicleType;
use Illuminate\Http\Request;

class VehicleTypeController extends Controller
{
    public function index()
    {
        $vehicleTypes = VehicleType::all();
        return view('backend.vehicle-type.index', compact('vehicleTypes'));
    }

    public function store(Request $request)
    {
        // Ensure the slug is generated
        $requestData = $request->all();
        if (empty($requestData['slug'])) {
            $requestData['slug'] = generate_unique_slug($requestData['name'], VehicleType::class);
        }

        $vehicleType = VehicleType::create($requestData);
        return response()->json($vehicleType);
    }

    public function update(Request $request, VehicleType $vehicleType)
    {
        // Ensure the slug is generated or updated
        $requestData = $request->all();
        if (empty($requestData['slug'])) {
            $requestData['slug'] = generate_unique_slug($requestData['name'], VehicleType::class);
        }

        $vehicleType->update($requestData);
        return response()->json($vehicleType);
    }

    public function destroy(VehicleType $vehicleType)
    {
        // Check if this vehicle type is being used by any vehicles
        if ($vehicleType->vehicles()->count() > 0) {
            return response()->json([
                'message' => 'This vehicle type is being used by vehicles and cannot be deleted.'
            ], 422);
        }

        $vehicleType->delete();
        return response()->json(null, 204);
    }
}

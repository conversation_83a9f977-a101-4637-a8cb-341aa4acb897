<x-app-backend-layout>
    <div class="container bg-white p-5 mx-auto" x-data="cityApp()">
        <div class="flex justify-between mb-4">
            <h1 class="text-2xl">Cities</h1>
            <button @click="openCreateModal" class="bg-blue-500 text-white px-4 py-2 rounded">Create City</button>
        </div>

        <table class="table-auto w-full mb-4">
            <thead>
            <tr>
                <th class="px-4 py-2">Name</th>
                <th class="px-4 py-2">State</th>
                <th class="px-4 py-2">Latitude</th>
                <th class="px-4 py-2">Longitude</th>
                <th class="px-4 py-2">Cost</th>
                <th class="px-4 py-2">Status</th>
                <th class="px-4 py-2">Actions</th>
            </tr>
            </thead>
            <tbody>
            @foreach($cities as $city)
                <tr>
                    <td class="border px-4 py-2">{{ $city->name }}</td>
                    <td class="border px-4 py-2">{{ $city->state->name }}</td>
                    <td class="border px-4 py-2">{{ $city->latitude }}</td>
                    <td class="border px-4 py-2">{{ $city->longitude }}</td>
                    <td class="border px-4 py-2">${{ $city->cost }}</td>
                    <td class="border px-4 py-2">{{ $city->is_active ? 'Active' : 'Inactive' }}</td>
                    <td class="border px-4 py-2">
                        <button @click="openEditModal({{ $city }})" class="text-blue-500">Edit</button>
                        <button @click="deleteCity({{ $city->id }})" class="text-red-500 ml-2">Delete</button>
                    </td>
                </tr>
            @endforeach
            </tbody>
        </table>

        <!-- Create/Edit Modal -->
        <div x-show="isModalOpen" class="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50" x-cloak>
            <div class="bg-white p-6 rounded-lg w-1/3 relative">
                <button @click="closeModal" class="absolute top-2 right-2 text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>

                <h2 class="text-xl mb-4" x-text="isEditMode ? 'Edit City' : 'Create City'"></h2>

                <form @submit.prevent="isEditMode ? updateCity() : createCity()">
                    <div class="mb-4">
                        <label for="name" class="block text-sm font-bold">Name</label>
                        <input type="text" name="name" id="name" x-model="form.name" class="mt-1 block w-full border border-gray-300 rounded p-2" required>
                    </div>

                    <div class="mb-4">
                        <label for="state_id" class="block text-sm font-bold">State</label>
                        <select name="state_id" id="state_id" x-model="form.state_id" class="mt-1 block w-full border border-gray-300 rounded p-2" required>
                            <option value="" disabled>Select a state</option>
                            @foreach($states as $state)
                                <option value="{{ $state->id }}">{{ $state->name }}</option>
                            @endforeach
                        </select>
                    </div>

                    <div class="mb-4">
                        <label for="latitude" class="block text-sm font-bold">Latitude</label>
                        <input type="number" step="0.0000001" name="latitude" id="latitude" x-model="form.latitude" class="mt-1 block w-full border border-gray-300 rounded p-2">
                    </div>

                    <div class="mb-4">
                        <label for="longitude" class="block text-sm font-bold">Longitude</label>
                        <input type="number" step="0.0000001" name="longitude" id="longitude" x-model="form.longitude" class="mt-1 block w-full border border-gray-300 rounded p-2">
                    </div>

                    <div class="mb-4">
                        <label for="cost" class="block text-sm font-bold">Cost</label>
                        <input type="number" step="0.01" name="cost" id="cost" x-model="form.cost" class="mt-1 block w-full border border-gray-300 rounded p-2" required>
                    </div>

                    <div class="mb-4">
                        <label for="is_active" class="block text-sm font-bold">Status</label>
                        <select name="is_active" id="is_active" x-model="form.is_active" class="mt-1 block w-full border border-gray-300 rounded p-2" required>
                            <option value="0">Inactive</option>
                            <option value="1">Active</option>
                        </select>
                    </div>

                    <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded" x-text="isEditMode ? 'Update' : 'Create'"></button>
                    <button @click="closeModal" type="button" class="bg-gray-500 text-white px-4 py-2 rounded ml-2">Cancel</button>
                </form>
            </div>
        </div>
    </div>

    <script>
        function cityApp() {
            return {
                isModalOpen: false,
                isEditMode: false,
                form: {
                    id: null,
                    name: '',
                    state_id: null,
                    latitude: null,
                    longitude: null,
                    cost: 0.00,
                    is_active: 0
                },

                openCreateModal() {
                    this.isEditMode = false;
                    this.form = { 
                        id: null, 
                        name: '', 
                        state_id: null, 
                        latitude: null,
                        longitude: null,
                        cost: 0.00,
                        is_active: 0
                    };
                    this.isModalOpen = true;
                },

                openEditModal(city) {
                    this.isEditMode = true;
                    this.form = { 
                        id: city.id, 
                        name: city.name, 
                        state_id: city.state_id,
                        latitude: city.latitude,
                        longitude: city.longitude,
                        cost: city.cost,
                        is_active: city.is_active
                    };
                    this.isModalOpen = true;
                },

                closeModal() {
                    this.isModalOpen = false;
                },

                async createCity() {
                    try {
                        const response = await fetch('{{ route('admin.cities.store') }}', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': '{{ csrf_token() }}'
                            },
                            body: JSON.stringify(this.form)
                        });

                        if (!response.ok) throw new Error('Error creating city');
                        window.location.reload();
                    } catch (error) {
                        Swal.fire('Error', error.message, 'error');
                    }
                },

                async updateCity() {
                    try {
                        const response = await fetch(`{{ url('admin/cities') }}/${this.form.id}`, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': '{{ csrf_token() }}'
                            },
                            body: JSON.stringify(this.form)
                        });

                        if (!response.ok) throw new Error('Error updating city');
                        window.location.reload();
                    } catch (error) {
                        Swal.fire('Error', error.message, 'error');
                    }
                },

                async deleteCity(id) {
                    Swal.fire({
                        title: 'Are you sure?',
                        text: "This action cannot be undone.",
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#3085d6',
                        cancelButtonColor: '#d33',
                        confirmButtonText: 'Yes, delete it!'
                    }).then(async (result) => {
                        if (result.isConfirmed) {
                            try {
                                const response = await fetch(`{{ url('admin/cities') }}/${id}`, {
                                    method: 'DELETE',
                                    headers: {
                                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                                    }
                                });

                                if (!response.ok) throw new Error('Error deleting city');
                                Swal.fire('Deleted!', 'City has been deleted.', 'success');
                                window.location.reload();
                            } catch (error) {
                                Swal.fire('Error', error.message, 'error');
                            }
                        }
                    });
                }
            }
        }
    </script>
</x-app-backend-layout>
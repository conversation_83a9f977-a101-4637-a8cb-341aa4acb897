<x-app-backend-layout>
    <!-- Leaflet CSS and JS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

    <!-- For address search/geocoding -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet-control-geocoder/dist/Control.Geocoder.css" />
    <script src="https://unpkg.com/leaflet-control-geocoder/dist/Control.Geocoder.js"></script>

    <!-- Leaflet Locate plugin for current location -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/leaflet.locatecontrol@0.79.0/dist/L.Control.Locate.min.css" />
    <script src="https://cdn.jsdelivr.net/npm/leaflet.locatecontrol@0.79.0/dist/L.Control.Locate.min.js"></script>

    <div class="container mx-auto py-6 px-4">
        <div class="max-w-6xl mx-auto">
            <!-- Header -->
            <div class="mb-6">
                <h1 class="text-2xl font-bold text-gray-800">
                    {{ isset($vehicle) ? 'Edit Vehicle' : 'Add New Vehicle' }}
                </h1>
                @unless(isset($vehicle))
                    <p class="text-gray-600">Enter the vehicle details to add it to your rental inventory</p>
                @endunless
            </div>

            <!-- Main Form Card -->
            <div class="bg-white rounded-xl shadow-md overflow-hidden"
     x-data="vehicleForm({{ isset($vehicle) ? json_encode($vehicle) : 'null' }})"
     @file-ids-updated.window="handleFileIdsUpdated($event)">
     <div x-data="{
        tab: 'general',
        get tabs() {
            // Create base tabs available to all users
            const baseTabs = [
                { id: 'general', name: 'General Information', icon: 'fas fa-car' },
                { id: 'photos', name: 'Photos', icon: 'fas fa-images' },
                { id: 'documents', name: 'Documents', icon: 'fas fa-file-alt' },
                { id: 'pricing', name: 'Pricing', icon: 'fas fa-tag' }
            ];

            // Add admin tab only if user is admin
            if (this.$root.isAdmin) {
                baseTabs.push({ id: 'admin', name: 'Admin Options', icon: 'fas fa-cog' });
            }

            return baseTabs;
        },

        // Initialize the component
        init() {
            // Check if current tab is valid for user
            this.$watch('tabs', () => {
                // If current tab is 'admin' but user is not admin, redirect to first tab
                if (this.tab === 'admin' && !this.$root.isAdmin) {
                    this.tab = 'general';
                }
            });
        }
    }">
         <!-- Tabs Navigation -->
        <div class="flex border-b overflow-x-auto">
            <template x-for="(tabItem, index) in tabs" :key="index">
                <button class="py-4 px-6 focus:outline-none transition-all duration-200 relative whitespace-nowrap"
                        :class="tab === tabItem.id ? 'text-blue-600 font-medium' : 'text-gray-500 hover:text-gray-700'"
                        @click="tab = tabItem.id">
                    <span class="flex items-center">
                        <i :class="tabItem.icon" class="h-5 w-5 mr-2"></i>
                        <span x-text="tabItem.name"></span>
                    </span>
                    <div x-show="tab === tabItem.id" class="absolute bottom-0 left-0 w-full h-0.5 bg-blue-600"></div>
                    <!-- Error indicator -->
                    <span x-show="hasTabErrors(tabItem.id)"
                          class="absolute top-3 right-2 w-2 h-2 bg-red-500 rounded-full"></span>
                </button>
            </template>
        </div>

                    <!-- Success/Error Alert -->
                    <div x-show="message"
                         x-transition:enter="transition ease-out duration-300"
                         x-transition:enter-start="opacity-0 transform translate-y-2"
                         x-transition:enter-end="opacity-100 transform translate-y-0"
                         x-transition:leave="transition ease-in duration-200"
                         x-transition:leave-start="opacity-100 transform translate-y-0"
                         x-transition:leave-end="opacity-0 transform translate-y-2"
                         :class="messageIsError ? 'bg-red-50 border-red-400 text-red-700' : 'bg-green-50 border-green-400 text-green-700'"
                         class="p-4 border-l-4 mx-6 mt-4 mb-2">

                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i :class="messageIsError ? 'fas fa-exclamation-circle text-red-400' : 'fas fa-check-circle text-green-400'"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm" x-html="message"></p>
                            </div>
                            <div class="ml-auto pl-3">
                                <div class="-mx-1.5 -my-1.5">
                                    <button @click="message = ''" class="inline-flex rounded-md p-1.5"
                                            :class="messageIsError ? 'text-red-500 hover:bg-red-100' : 'text-green-500 hover:bg-green-100'">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form @submit.prevent="saveVehicle" id="vehicleForm">
                        <input type="hidden" name="_token" value="{{ csrf_token() }}">
                        <template x-if="isEditing">
                            <input type="hidden" name="_method" value="PUT">
                        </template>

                        <input type="hidden" name="status" :value="form.status">

                        <!-- General Information Tab -->
                        <div x-show="tab === 'general'" x-transition:enter="transition ease-out duration-300"
                             x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Left Column -->
                                <div class="space-y-6">
                                    <!-- Basic Vehicle Info -->
                                    <div>
                                        <label for="vehicle_type_id" class="block text-sm font-medium text-gray-700 mb-1">
                                            Vehicle Type <span class="text-red-500">*</span>
                                        </label>
                                        <select id="vehicle_type_id" x-model="form.vehicle_type_id"
                                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                            <option value="">Select Vehicle Type</option>
                                            @foreach($vehicleTypes as $type)
                                                <option value="{{ $type->id }}">{{ $type->name }}</option>
                                            @endforeach
                                        </select>
                                        <p x-show="errors.vehicle_type_id && errors.vehicle_type_id.length > 0" x-text="errors.vehicle_type_id && errors.vehicle_type_id.length > 0 ? errors.vehicle_type_id[0] : ''" class="mt-1 text-sm text-red-600"></p>
                                    </div>

                                    <div class="grid grid-cols-2 gap-4">
                                        <div>
                                            <label for="make" class="block text-sm font-medium text-gray-700 mb-1">
                                                Make <span class="text-red-500">*</span>
                                            </label>
                                            <input type="text" id="make" x-model="form.make"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                   placeholder="e.g., Toyota, BMW">
                                            <p x-show="errors.make && errors.make.length > 0" x-text="errors.make && errors.make.length > 0 ? errors.make[0] : ''" class="mt-1 text-sm text-red-600"></p>
                                        </div>
                                        <div>
                                            <label for="model" class="block text-sm font-medium text-gray-700 mb-1">
                                                Model <span class="text-red-500">*</span>
                                            </label>
                                            <input type="text" id="model" x-model="form.model"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                   placeholder="e.g., Camry, X5">
                                            <p x-show="errors.model && errors.model.length > 0" x-text="errors.model && errors.model.length > 0 ? errors.model[0] : ''" class="mt-1 text-sm text-red-600"></p>
                                        </div>
                                    </div>

                                    <div class="grid grid-cols-2 gap-4">
                                        <div>
                                            <label for="year" class="block text-sm font-medium text-gray-700 mb-1">
                                                Year <span class="text-red-500">*</span>
                                            </label>
                                            <input type="number" id="year" x-model="form.year" min="1900" :max="currentYear + 1"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                   placeholder="e.g., 2023">
                                            <p x-show="errors.year && errors.year.length > 0" x-text="errors.year && errors.year.length > 0 ? errors.year[0] : ''" class="mt-1 text-sm text-red-600"></p>
                                        </div>
                                        <div>
                                            <label for="license_plate" class="block text-sm font-medium text-gray-700 mb-1">
                                                License Plate <span class="text-red-500">*</span>
                                            </label>
                                            <input type="text" id="license_plate" x-model="form.license_plate"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                   placeholder="License plate number">
                                            <p x-show="errors.license_plate && errors.license_plate.length > 0" x-text="errors.license_plate && errors.license_plate.length > 0 ? errors.license_plate[0] : ''" class="mt-1 text-sm text-red-600"></p>
                                        </div>
                                    </div>

                                    <div class="grid grid-cols-2 gap-4">
                                        <div>
                                            <label for="color" class="block text-sm font-medium text-gray-700 mb-1">
                                                Color <span class="text-red-500">*</span>
                                            </label>
                                            <div class="relative">
                                                <input type="text" id="color" x-model="form.color"
                                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                       placeholder="e.g., Black, Silver">
                                                <div class="absolute right-2 top-2 w-6 h-6 rounded-full border border-gray-300"
                                                     :style="{'background-color': form.color}"
                                                     x-show="form.color && isValidColor(form.color)"></div>
                                            </div>
                                            <p x-show="errors.color && errors.color.length > 0" x-text="errors.color && errors.color.length > 0 ? errors.color[0] : ''" class="mt-1 text-sm text-red-600"></p>
                                        </div>
                                        <div>
                                            <label for="mileage" class="block text-sm font-medium text-gray-700 mb-1">
                                                Mileage <span class="text-red-500">*</span>
                                            </label>
                                            <input type="number" id="mileage" x-model="form.mileage" min="0"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                   placeholder="Current mileage">
                                            <p x-show="errors.mileage && errors.mileage.length > 0" x-text="errors.mileage && errors.mileage.length > 0 ? errors.mileage[0] : ''" class="mt-1 text-sm text-red-600"></p>
                                        </div>
                                    </div>

                                    <div class="grid grid-cols-2 gap-4">
                                        <div>
                                            <label for="transmission" class="block text-sm font-medium text-gray-700 mb-1">
                                                Transmission <span class="text-red-500">*</span>
                                            </label>
                                            <select id="transmission" x-model="form.transmission"
                                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                                <option value="">Select Transmission</option>
                                                <option value="automatic">Automatic</option>
                                                <option value="manual">Manual</option>
                                                <option value="cvt">CVT</option>
                                                <option value="semi-automatic">Semi-Automatic</option>
                                            </select>
                                            <p x-show="errors.transmission && errors.transmission.length > 0" x-text="errors.transmission && errors.transmission.length > 0 ? errors.transmission[0] : ''" class="mt-1 text-sm text-red-600"></p>
                                        </div>
                                        <div>
                                            <label for="fuel_type" class="block text-sm font-medium text-gray-700 mb-1">
                                                Fuel Type <span class="text-red-500">*</span>
                                            </label>
                                            <select id="fuel_type" x-model="form.fuel_type"
                                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                                <option value="">Select Fuel Type</option>
                                                <option value="gasoline">Gasoline</option>
                                                <option value="diesel">Diesel</option>
                                                <option value="hybrid">Hybrid</option>
                                                <option value="electric">Electric</option>
                                                <option value="plugin_hybrid">Plug-in Hybrid</option>
                                                <option value="hydrogen">Hydrogen</option>
                                            </select>
                                            <p x-show="errors.fuel_type && errors.fuel_type.length > 0" x-text="errors.fuel_type && errors.fuel_type.length > 0 ? errors.fuel_type[0] : ''" class="mt-1 text-sm text-red-600"></p>
                                        </div>
                                    </div>

                                    <div class="grid grid-cols-2 gap-4">
                                        <div>
                                            <label for="seats" class="block text-sm font-medium text-gray-700 mb-1">
                                                Seats <span class="text-red-500">*</span>
                                            </label>
                                            <select id="seats" x-model="form.seats"
                                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                                <option value="">Select Number of Seats</option>
                                                <option value="2">2 Seats</option>
                                                <option value="4">4 Seats</option>
                                                <option value="5">5 Seats</option>
                                                <option value="6">6 Seats</option>
                                                <option value="7">7 Seats</option>
                                                <option value="8+">8+ Seats</option>
                                            </select>
                                            <p x-show="errors.seats && errors.seats.length > 0" x-text="errors.seats && errors.seats.length > 0 ? errors.seats[0] : ''" class="mt-1 text-sm text-red-600"></p>
                                        </div>
                                        <div>
                                            <label for="doors" class="block text-sm font-medium text-gray-700 mb-1">
                                                Doors <span class="text-red-500">*</span>
                                            </label>
                                            <select id="doors" x-model="form.doors"
                                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                                <option value="">Select Number of Doors</option>
                                                <option value="2">2 Doors</option>
                                                <option value="3">3 Doors</option>
                                                <option value="4">4 Doors</option>
                                                <option value="5">5 Doors</option>
                                                <option value="6">6 Doors</option>
                                            </select>
                                            <p x-show="errors.doors && errors.doors.length > 0" x-text="errors.doors && errors.doors.length > 0 ? errors.doors[0] : ''" class="mt-1 text-sm text-red-600"></p>
                                        </div>
                                    </div>

                                    <div class="space-y-3 pt-2">
                                        <label class="text-sm font-medium text-gray-700">Additional Options</label>

                                        <div class="flex space-x-6">
                                            <div class="flex items-center">
                                                <input type="checkbox"
                                                       id="with_driver"
                                                       x-model="form.with_driver"
                                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                                <label for="with_driver" class="ml-2 text-sm text-gray-700">With Driver</label>
                                            </div>

                                            <div class="flex items-center">
                                                <input type="checkbox"
                                                       id="is_featured"
                                                       x-model="form.is_featured"
                                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                                <label for="is_featured" class="ml-2 text-sm text-gray-700">Featured Vehicle</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Right Column -->
                                <div class="space-y-6">
                                    <!-- Location -->
                                    <div class="space-y-6">
                                        <div>
                                            <label for="city_id" class="block text-sm font-medium text-gray-700 mb-1">
                                                City <span class="text-red-500">*</span>
                                            </label>
                                            <select id="city_id" x-model="form.city_id" @change="loadCityOnMap()"
                                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                                <option value="">Select City</option>
                                                @foreach($cities as $city)
                                                    <option value="{{ $city->id }}"
                                                            data-lat="{{ $city->latitude }}"
                                                            data-lng="{{ $city->longitude }}">{{ $city->name }}</option>
                                                @endforeach
                                            </select>
                                            <p x-show="errors.city_id && errors.city_id.length > 0" x-text="errors.city_id && errors.city_id.length > 0 ? errors.city_id[0] : ''" class="mt-1 text-sm text-red-600"></p>
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                                Vehicle Location <span class="text-red-500">*</span>
                                            </label>

                                            <!-- Hidden inputs to store the location data -->
                                            <input type="hidden" id="latitude" name="latitude" x-model="form.latitude">
                                            <input type="hidden" id="longitude" name="longitude" x-model="form.longitude">
                                            <input type="hidden" id="address" name="address" x-model="form.address">

                                            <!-- Address search input -->
                                            <div class="mb-2">
                                                <div class="relative">
                                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                        <i class="fas fa-search text-gray-400"></i>
                                                    </div>
                                                    <input type="text" id="location-search"
                                                           @keydown.enter.prevent="searchLocation($event.target.value)"
                                                           class="w-full pl-10 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                           placeholder="Search for address or location...">
                                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                                        <button type="button" @click="searchLocation(document.getElementById('location-search').value)"
                                                                class="text-gray-400 hover:text-blue-500">
                                                            <i class="fas fa-arrow-right"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Map container -->
                                            <div id="location-map" class="w-full h-64 rounded-md border border-gray-300 mb-2"
                                                 x-init="initMap()"></div>

                                            <!-- Display the selected address -->
                                            <div x-show="form.address" class="p-2 bg-blue-50 border border-blue-200 rounded-md">
                                                <p class="text-sm"><span class="font-medium">Selected Location:</span> <span x-text="form.address"></span></p>
                                                <div class="flex justify-end mt-1">
                                                    <button type="button" @click="clearLocation()"
                                                            class="text-xs text-red-600 hover:text-red-800">
                                                        <i class="fas fa-times mr-1"></i> Clear
                                                    </button>
                                                </div>
                                            </div>

                                            <p x-show="errors.latitude || errors.longitude" class="mt-1 text-sm text-red-600">
                                                Please select a valid location on the map
                                            </p>
                                        </div>
                                    </div>

                                    <!-- Vehicle Features -->
                                    <div class="border rounded-md shadow-sm p-4">
                                        <h3 class="text-sm font-medium text-gray-700 mb-3">Features & Amenities</h3>
                                        <div class="grid grid-cols-2 gap-y-2">
                                            <template x-for="(feature, index) in availableFeatures" :key="index">
                                                <div class="flex items-center">
                                                    <input type="checkbox"
                                                           :id="'feature_' + feature.value"
                                                           :value="feature.value"
                                                           x-model="form.features"
                                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                                    <label :for="'feature_' + feature.value" class="ml-2 text-sm text-gray-700"
                                                           x-text="feature.label"></label>
                                                </div>
                                            </template>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Description Section -->
                            <div class="mt-8 space-y-6">
                                <div>
                                    <label for="description"
                                           class="block text-sm font-medium text-gray-700 mb-1">Description</label>

                                    <textarea id="description" x-model="form.description" rows="6"
                                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                              placeholder="Provide a detailed description of the vehicle..."></textarea>
                                    <div class="mt-1 text-xs text-gray-500 flex justify-between">
                                        <span>Enter a detailed description of the vehicle's condition and special features</span>
                                        <span x-text="form.description ? form.description.length : 0"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Photos Tab -->
                        <div x-show="tab === 'photos'" x-transition:enter="transition ease-out duration-300"
                             x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" class="p-6 space-y-6">

                            <!-- Main Vehicle Image -->
                            <div class="bg-gray-50 p-5 rounded-lg border border-gray-200">
                                <x-file-upload-enhanced
                                    name="primary_image"
                                    label="Main Vehicle Image"
                                    :multiple="false"
                                    accept="image/*"
                                    :required="true"
                                    :fileIds="isset($vehicle) && $vehicle->primaryImage ? $vehicle->primaryImage->file_id : ''"
                                />
                                <p x-show="errors.primary_image && errors.primary_image.length > 0" x-text="errors.primary_image && errors.primary_image.length > 0 ? errors.primary_image[0] : ''" class="mt-1 text-sm text-red-600"></p>
                                <!-- Debug info -->
                                <p class="mt-1 text-xs text-gray-500">Selected primary image ID: <span x-text="form.primary_image || 'None'"></span></p>
                            </div>

                            <!-- Vehicle Gallery -->
                            <div class="bg-gray-50 p-5 rounded-lg border border-gray-200">
                                @php
                                    $galleryImageIds = '';
                                    $primaryImageId = '';

                                    if(isset($vehicle) && $vehicle->images->count() > 0) {
                                        $galleryImageIds = $vehicle->images->pluck('file_id')->join(',');
                                        $primaryImage = $vehicle->images->where('is_primary', true)->first();
                                        if($primaryImage) {
                                            $primaryImageId = $primaryImage->file_id;
                                        }
                                    }
                                @endphp

                                <x-file-upload-enhanced
                                    name="images"
                                    label="Vehicle Gallery (max 5 images)"
                                    :multiple="true"
                                    accept="image/*"
                                    :required="true"
                                    :fileIds="$galleryImageIds"
                                    :primaryFileId="$primaryImageId"
                                />
                                <p x-show="errors.images && errors.images.length > 0" x-text="errors.images && errors.images.length > 0 ? errors.images[0] : ''" class="mt-1 text-sm text-red-600"></p>
                            </div>
                        </div>

                        <!-- Documents Tab -->
                        <div x-show="tab === 'documents'" x-transition:enter="transition ease-out duration-300"
                             x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" class="p-6 space-y-6">
                            <div class="bg-gray-50 p-5 rounded-lg border border-gray-200">
                                <h3 class="text-lg font-medium text-gray-800 mb-4">Required Documents</h3>
                                <p class="text-sm text-gray-600 mb-4">Please upload the following documents to verify your vehicle. All documents must be approved before your vehicle can be listed.</p>

                                @php
                                    // Get vehicle document types
                                    $documentTypes = \App\Models\DocumentType::where('applies_to', 'vehicle')
                                        ->orWhere('applies_to', 'all')
                                        ->orderBy('is_required', 'desc')
                                        ->get();

                                    // Get existing documents if editing
                                    $existingDocuments = isset($vehicle) ? $vehicle->documents : collect([]);
                                @endphp

                                <div class="space-y-6">
                                    @foreach($documentTypes as $documentType)
                                        <div class="bg-white p-4 rounded-lg border border-gray-200">
                                            @php
                                                // Check if vehicle already has this document type
                                                $existingDocument = $existingDocuments->where('document_type_id', $documentType->id)->first();
                                                $fileId = $existingDocument ? $existingDocument->file_id : '';
                                                $status = $existingDocument ? $existingDocument->status : '';
                                            @endphp

                                            <div class="flex justify-between items-start mb-2">
                                                <div>
                                                    <h4 class="text-md font-medium text-gray-900">{{ $documentType->name }}</h4>
                                                    <p class="text-sm text-gray-500">{{ $documentType->description }}</p>
                                                </div>
                                                @if($status)
                                                    <div>
                                                        @if($status === 'pending')
                                                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Pending</span>
                                                        @elseif($status === 'approved')
                                                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Approved</span>
                                                        @elseif($status === 'rejected')
                                                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Rejected</span>
                                                        @endif
                                                    </div>
                                                @endif
                                            </div>

                                            @if($status === 'rejected' && $existingDocument && $existingDocument->rejection_reason)
                                                <div class="mb-3 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                                                    <strong>Rejection reason:</strong> {{ $existingDocument->rejection_reason }}
                                                </div>
                                            @endif

                                            <x-simple-upload-enhanced
                                                name="documents[{{ $documentType->id }}][file_id]"
                                                label="Upload {{ $documentType->name }} (Image or PDF)"
                                                :required="$documentType->is_required"
                                            />

                                            <!-- Error display for document file -->
                                            <div x-show="errors['documents.{{ $documentType->id }}.file_id']" class="mt-1 text-sm text-red-600">
                                                <template x-for="(error, index) in errors['documents.{{ $documentType->id }}.file_id'] || []" :key="index">
                                                    <div x-text="error"></div>
                                                </template>
                                            </div>

                                            @if($documentType->has_expiry)
                                                <div class="mt-3">
                                                    <label for="documents_{{ $documentType->id }}_expiry_date" class="block text-sm font-medium text-gray-700 mb-1">
                                                        Expiry Date
                                                        @if($documentType->is_required)
                                                            <span class="text-red-500">*</span>
                                                        @endif
                                                    </label>
                                                    <input type="date"
                                                           id="documents_{{ $documentType->id }}_expiry_date"
                                                           name="documents[{{ $documentType->id }}][expiry_date]"
                                                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                           @if($existingDocument && $existingDocument->expiry_date)
                                                               value="{{ $existingDocument->expiry_date->format('Y-m-d') }}"
                                                           @endif
                                                           min="{{ date('Y-m-d') }}"
                                                           @if($documentType->is_required) required @endif
                                                           @change="if (!form.documents) form.documents = {};
                                                                   if (!form.documents[{{ $documentType->id }}]) form.documents[{{ $documentType->id }}] = {};
                                                                   form.documents[{{ $documentType->id }}].expiry_date = $event.target.value;
                                                                   console.log('Updated document {{ $documentType->id }} expiry_date:', $event.target.value);"
                                                    >

                                                    <!-- Error display for document expiry date -->
                                                    <div x-show="errors['documents.{{ $documentType->id }}.expiry_date']" class="mt-1 text-sm text-red-600">
                                                        <template x-for="(error, index) in errors['documents.{{ $documentType->id }}.expiry_date'] || []" :key="index">
                                                            <div x-text="error"></div>
                                                        </template>
                                                    </div>
                                                </div>
                                            @endif
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>

                        <!-- Pricing Tab -->
                        <div x-show="tab === 'pricing'" x-transition:enter="transition ease-out duration-300"
                             x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" class="p-6">
                            <div class="space-y-8">
                                <!-- Pricing Options -->
                                <div class="bg-gray-50 rounded-lg p-5 border border-gray-200">
                                    <h3 class="text-lg font-medium text-gray-800 mb-4">Pricing Options</h3>

                                    <!-- Price & Discount -->
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        <div>
                                            <label for="daily_rate" class="block text-sm font-medium text-gray-700 mb-1">
                                                Daily Rate (USD) <span class="text-red-500">*</span>
                                            </label>
                                            <div class="relative">
                                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                    <span class="text-gray-500 sm:text-sm">$</span>
                                                </div>
                                                <input type="text" id="daily_rate" x-model="form.daily_rate" @input="validateNumber($event, 'daily_rate')"
                                                       class="w-full pl-7 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                       placeholder="0.00">
                                            </div>
                                            <p x-show="errors.daily_rate && errors.daily_rate.length > 0" x-text="errors.daily_rate && errors.daily_rate.length > 0 ? errors.daily_rate[0] : ''" class="mt-1 text-sm text-red-600"></p>
                                        </div>

                                        <div>
                                            <label for="weekly_discount" class="block text-sm font-medium text-gray-700 mb-1">
                                                Weekly Discount (%)
                                            </label>
                                            <div class="relative">
                                                <input type="text" id="weekly_discount" x-model="form.weekly_discount" @input="validateNumber($event, 'weekly_discount', true)"
                                                       min="0" max="100"
                                                       class="w-full pr-7 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                       placeholder="0">
                                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                                    <span class="text-gray-500 sm:text-sm">%</span>
                                                </div>
                                            </div>
                                            <p class="mt-1 text-xs text-gray-500">Discount for weekly rentals</p>

                                            <!-- Weekly savings calculation -->
                                            <div class="text-xs mt-1 text-indigo-600" x-show="form.daily_rate && form.weekly_discount > 0">
                                                Weekly savings: $<span x-text="calculateWeeklySavings()"></span>
                                            </div>
                                        </div>

                                        <div>
                                            <label for="monthly_discount" class="block text-sm font-medium text-gray-700 mb-1">
                                                Monthly Discount (%)
                                            </label>
                                            <div class="relative">
                                                <input type="text" id="monthly_discount" x-model="form.monthly_discount" @input="validateNumber($event, 'monthly_discount', true)"
                                                       min="0" max="100"
                                                       class="w-full pr-7 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                       placeholder="0">
                                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                                    <span class="text-gray-500 sm:text-sm">%</span>
                                                </div>
                                            </div>
                                            <p class="mt-1 text-xs text-gray-500">Discount for monthly rentals</p>

                                            <!-- Monthly savings calculation -->
                                            <div class="text-xs mt-1 text-indigo-600" x-show="form.daily_rate && form.monthly_discount > 0">
                                                Monthly savings: $<span x-text="calculateMonthlySavings()"></span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label for="security_deposit" class="block text-sm font-medium text-gray-700 mb-1">
                                                Security Deposit (USD)
                                            </label>
                                            <div class="relative">
                                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                    <span class="text-gray-500 sm:text-sm">$</span>
                                                </div>
                                                <input type="text" id="security_deposit" x-model="form.security_deposit" @input="validateNumber($event, 'security_deposit')"
                                                       class="w-full pl-7 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                       placeholder="0.00">
                                            </div>
                                            <p class="mt-1 text-xs text-gray-500">Refundable security deposit amount</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Availability Options -->
                                <div class="bg-gray-50 rounded-lg p-5 border border-gray-200">
                                    <h3 class="text-lg font-medium text-gray-800 mb-4">Availability Options</h3>

                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
                                        <div>
                                            <label for="availability" class="block text-sm font-medium text-gray-700 mb-1">
                                                Availability <span class="text-red-500">*</span>
                                            </label>
                                            <select id="availability" x-model="form.availability"
                                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                                <option value="">Select Availability</option>
                                                <option value="everyday">Every Day</option>
                                                <option value="weekdays">Weekdays Only</option>
                                                <option value="weekends">Weekends Only</option>
                                                <option value="custom">Custom Schedule</option>
                                            </select>
                                            <p x-show="errors.availability && errors.availability.length > 0" x-text="errors.availability && errors.availability.length > 0 ? errors.availability[0] : ''" class="mt-1 text-sm text-red-600"></p>
                                        </div>

                                        <div>
                                            <label for="advance_notice" class="block text-sm font-medium text-gray-700 mb-1">
                                                Advance Notice <span class="text-red-500">*</span>
                                            </label>
                                            <select id="advance_notice" x-model="form.advance_notice"
                                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                                <option value="">Select Advance Notice</option>
                                                <option value="instant">Instant (No Notice)</option>
                                                <option value="1_hour">1 Hour</option>
                                                <option value="3_hours">3 Hours</option>
                                                <option value="6_hours">6 Hours</option>
                                                <option value="12_hours">12 Hours</option>
                                                <option value="24_hours">24 Hours</option>
                                                <option value="48_hours">48 Hours</option>
                                            </select>
                                            <p x-show="errors.advance_notice && errors.advance_notice.length > 0" x-text="errors.advance_notice && errors.advance_notice.length > 0 ? errors.advance_notice[0] : ''" class="mt-1 text-sm text-red-600"></p>
                                        </div>
                                    </div>

                                    <!-- Availability visual calendar (if custom is selected) -->
                                    <div x-show="form.availability === 'custom'" class="mt-4">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Custom Schedule</label>
                                        <div class="grid grid-cols-7 gap-2">
                                            <template x-for="(day, i) in ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']" :key="i">
                                                <div>
                                                    <button type="button"
                                                            @click="toggleCustomDay(day.toLowerCase())"
                                                            class="w-full text-center py-2 rounded-md text-sm transition-colors"
                                                            :class="isCustomDayAvailable(day.toLowerCase()) ?
                                                                   'bg-green-100 text-green-800 border border-green-200' :
                                                                   'bg-gray-100 text-gray-500 border border-gray-200'"
                                                    >
                                                        <span x-text="day"></span>
                                                    </button>
                                                </div>
                                            </template>
                                        </div>
                                        <p class="mt-1 text-xs text-gray-500">Click on days to toggle availability</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Admin Options Tab (only shows for admins) -->
                        <div x-show="tab === 'admin' && isAdmin" x-transition:enter="transition ease-out duration-300"
                             x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" class="p-6">
                            <div class="space-y-8">
                                <!-- Status & Administration -->
                                <div class="bg-gray-50 rounded-lg p-5 border border-gray-200">
                                    <h3 class="text-lg font-medium text-gray-800 mb-4">Administrative Options</h3>

                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
                                        <div>
                                            <label for="status" class="block text-sm font-medium text-gray-700 mb-1">
                                                Status
                                            </label>
                                            <select id="status" x-model="form.status"
                                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                                <option value="pending">Pending</option>
                                                <option value="active">Active</option>
                                                <option value="rejected">Rejected</option>
                                                <option value="inactive">Inactive</option>
                                            </select>
                                        </div>

                                        <div x-show="form.status === 'rejected'">
                                            <label for="rejection_reason" class="block text-sm font-medium text-gray-700 mb-1">
                                                Rejection Reason
                                            </label>
                                            <textarea id="rejection_reason" x-model="form.rejection_reason" rows="3"
                                                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                      placeholder="Reason for rejecting this vehicle..."></textarea>
                                        </div>
                                    </div>

                                    <div class="mt-4">
                                        <div class="flex items-center">
                                            <input type="checkbox"
                                                   id="is_featured_admin"
                                                   x-model="form.is_featured"
                                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                            <label for="is_featured_admin" class="ml-2 text-sm text-gray-700">Featured Vehicle (shown prominently on homepage)</label>
                                        </div>
                                    </div>
                                </div>

                                <!-- User Assignment (for admin) -->
                                <div class="bg-gray-50 rounded-lg p-5 border border-gray-200">
                                    <h3 class="text-lg font-medium text-gray-800 mb-4">Owner Information</h3>

                                    <div class="grid grid-cols-1 gap-5">
                                        <div>
                                            <label for="user_id" class="block text-sm font-medium text-gray-700 mb-1">
                                                Vehicle Owner
                                            </label>
                                            <select id="user_id" x-model="form.user_id"
                                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                                @foreach($users ?? [] as $user)
                                                    <option value="{{ $user->id }}">{{ $user->name }} ({{ $user->email }})</option>
                                                @endforeach
                                            </select>
                                            <p class="mt-1 text-xs text-gray-500">User who owns this vehicle</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Form Buttons -->
                        <div class="px-6 py-4 bg-gray-50 flex items-center justify-end space-x-4">
                            <a href="{{ route('vehicles.index') }}"
                               class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                                <i class="fas fa-arrow-left mr-2 text-gray-600"></i>
                                Cancel
                            </a>

                            <div class="flex items-center space-x-4">
                                <!-- Save Draft Button -->
                                <button type="button" @click="form.status = 'draft'; saveVehicle()"
                                        class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                                    <i class="fas fa-save mr-2 text-gray-600"></i>
                                    Save Draft
                                </button>

                                <!-- Final Submit Button -->
                                <button type="button" @click="form.status = isAdmin ? 'active' : 'pending'; saveVehicle()"
                                        class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                                    <i class="fas fa-paper-plane mr-2"></i>
                                    <span x-text="isAdmin ? (isEditing ? 'Update & Publish' : 'Save & Publish') : (isEditing ? 'Update & Submit' : 'Submit for Approval')"></span>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- File Management Modal will be included at the end of the file -->

    <script>
        const userIsAdmin = {{ auth()->check() && auth()->user()->hasRole('admin') ? 'true' : 'false' }};
        let currentFileManagerTarget = null;

        function vehicleForm(existingVehicle = null) {
            return {
                // Core properties
                csrfToken: "{{ csrf_token() }}",
                isEditing: existingVehicle !== null,
                isAdmin: userIsAdmin, // This should be set from a global variable in your blade file
                currentYear: new Date().getFullYear(),
                message: '',
                messageIsError: false,
                errors: {
                    // Initialize with empty arrays for fields that are checked in the UI
                    primary_image: [],
                    images: [],
                    daily_rate: [],
                    availability: [],
                    advance_notice: [],
                    vehicle_type_id: [],
                    make: [],
                    model: [],
                    year: [],
                    license_plate: [],
                    color: [],
                    mileage: [],
                    transmission: [],
                    fuel_type: [],
                    seats: [],
                    doors: [],
                    city_id: []
                },
                // Initialize form data
                form: {
                    // Default values
                    vehicle_type_id: '',
                    make: '',
                    model: '',
                    year: '',
                    color: '',
                    license_plate: '',
                    mileage: '',
                    transmission: '',
                    fuel_type: '',
                    seats: '',
                    doors: '',
                    description: '',
                    with_driver: false,
                    is_featured: false,
                    city_id: '',
                    latitude: '',
                    longitude: '',
                    address: '',
                    features: [],
                    daily_rate: '',
                    weekly_discount: '',
                    monthly_discount: '',
                    security_deposit: '',
                    availability: 'everyday', // Default to everyday
                    advance_notice: 'instant', // Default to instant
                    status: 'pending',
                    rejection_reason: '',
                    user_id: document.querySelector('meta[name="user-id"]')?.getAttribute('content') || '',
                    primary_image: '',
                    images: '',
                    custom_days: '',
                    documents: {}, // Initialize documents object to store document file IDs and expiry dates

                    // Initialize with existing vehicle data if editing
                    ...(existingVehicle || {})
                },
                customAvailableDays: [],
                map: null,
                marker: null,
                geocoder: null,

                // Available features for the vehicle
                availableFeatures: [
                    { value: 'air_conditioning', label: 'Air Conditioning' },
                    { value: 'navigation', label: 'Navigation' },
                    { value: 'bluetooth', label: 'Bluetooth' },
                    { value: 'backup_camera', label: 'Backup Camera' },
                    { value: 'keyless_entry', label: 'Keyless Entry' },
                    { value: 'sunroof', label: 'Sunroof' },
                    { value: 'heated_seats', label: 'Heated Seats' },
                    { value: 'leather_seats', label: 'Leather Seats' },
                    { value: 'cruise_control', label: 'Cruise Control' },
                    { value: 'apple_carplay', label: 'Apple CarPlay' },
                    { value: 'android_auto', label: 'Android Auto' },
                    { value: 'usb_ports', label: 'USB Ports' },
                    { value: 'wireless_charging', label: 'Wireless Charging' },
                    { value: 'parking_sensors', label: 'Parking Sensors' },
                    { value: 'blind_spot_monitoring', label: 'Blind Spot Monitoring' },
                    { value: 'lane_assist', label: 'Lane Assist' }
                ],

                // Initialization
                init() {
                    // If we have an existing vehicle, populate the form
                    if (existingVehicle) {
                        // Copy existing vehicle properties to the form
                        Object.keys(this.form).forEach(key => {
                            if (existingVehicle[key] !== undefined) {
                                this.form[key] = existingVehicle[key];
                            }
                        });

                        // Store the ID for edit operations
                        this.form.id = existingVehicle.id;

                        // Process features from string to array if needed
                        if (existingVehicle.features) {
                            if (typeof existingVehicle.features === 'string') {
                                try {
                                    const featuresObj = JSON.parse(existingVehicle.features);
                                    if (Array.isArray(featuresObj)) {
                                        this.form.features = featuresObj;
                                    } else {
                                        this.form.features = Object.keys(featuresObj).filter(key => featuresObj[key]);
                                    }
                                } catch(e) {
                                    // If JSON parsing fails, try to split by comma
                                    this.form.features = existingVehicle.features.split(',').filter(feature => feature.trim() !== '');
                                }
                            } else if (Array.isArray(existingVehicle.features)) {
                                this.form.features = existingVehicle.features;
                            }
                        }

                        // Handle primary image
                        if (existingVehicle.primary_image && existingVehicle.primary_image.file_id) {
                            this.form.primary_image = existingVehicle.primary_image.file_id;
                        }

                        // Handle gallery images
                        if (existingVehicle.images && Array.isArray(existingVehicle.images)) {
                            this.form.images = existingVehicle.images.map(img => img.file_id).join(',');
                        }

                        // Initialize documents object with existing document data
                        if (existingVehicle.documents && Array.isArray(existingVehicle.documents)) {
                            existingVehicle.documents.forEach(doc => {
                                if (!this.form.documents) this.form.documents = {};
                                if (!this.form.documents[doc.document_type_id]) this.form.documents[doc.document_type_id] = {};

                                this.form.documents[doc.document_type_id].file_id = doc.file_id;
                                if (doc.expiry_date) {
                                    this.form.documents[doc.document_type_id].expiry_date = doc.expiry_date;
                                }
                            });
                        }
                    }

                    // Initialize custom available days
                    if (existingVehicle && existingVehicle.custom_days) {
                        if (typeof existingVehicle.custom_days === 'string') {
                            this.customAvailableDays = existingVehicle.custom_days.split(',');
                        } else if (Array.isArray(existingVehicle.custom_days)) {
                            this.customAvailableDays = existingVehicle.custom_days;
                        }
                    } else {
                        // Default to weekdays if nothing is set
                        this.customAvailableDays = ['mon', 'tue', 'wed', 'thu', 'fri'];
                    }

                    // Set up event listener for file uploads
                    window.addEventListener('file-ids-updated', this.handleFileIdsUpdated.bind(this));

                    // Log initial form data
                    console.log('Initial form data:', {
                        primary_image: this.form.primary_image,
                        images: this.form.images,
                        documents: this.form.documents
                    });

                    // Initialize documents object if it doesn't exist
                    if (!this.form.documents) {
                        this.form.documents = {};
                    }

                    // Initialize custom available days if editing a vehicle with custom availability
                    if (this.isEditing && this.form.availability === 'custom' && this.form.custom_days) {
                        this.customAvailableDays = this.form.custom_days.split(',');
                    }

                    // Initialize form with default values if not editing
                    if (!this.isEditing) {
                        this.form = {
                            ...this.form,
                            status: 'draft',
                            with_driver: false,
                            is_featured: false,
                            weekly_discount: 0,
                            monthly_discount: 0,
                            security_deposit: 0,
                            features: [],
                            documents: {}
                        };
                    }

                    // Normalize boolean values
                    this.form.with_driver = !!this.form.with_driver;
                    this.form.is_featured = !!this.form.is_featured;

                    // Log initial form state for debugging
                    console.log('Initial form state:', this.form);
                },

                // Tab error indicator
                hasTabErrors(tabId) {
                    if (Object.keys(this.errors).length === 0) return false;

                    const tabFieldMapping = {
                        'general': ['vehicle_type_id', 'make', 'model', 'year', 'color', 'license_plate', 'mileage',
                            'transmission', 'fuel_type', 'seats', 'doors', 'city_id', 'latitude', 'longitude'],
                        'photos': ['primary_image', 'images'],
                        'documents': ['documents'],
                        'pricing': ['daily_rate', 'availability', 'advance_notice'],
                        'admin': ['status', 'user_id']
                    };

                    return tabFieldMapping[tabId].some(field => this.errors[field]);
                },

                // Validation and number formatting
                validateNumber(event, field, isInteger = false) {
                    let value = event.target.value;

                    // Remove non-digit characters except decimal point (if not integer)
                    if (isInteger) {
                        value = value.replace(/[^\d]/g, '');

                        // Ensure value is within 0-100 for percentage fields
                        if (field.includes('discount') && parseInt(value) > 100) {
                            value = '100';
                        }
                    } else {
                        value = value.replace(/[^\d.]/g, '');

                        // Ensure only one decimal point
                        const decimalPoints = value.match(/\./g);
                        if (decimalPoints && decimalPoints.length > 1) {
                            value = value.substring(0, value.lastIndexOf('.'));
                        }
                    }

                    // Update the model with clean value
                    this.form[field] = value;

                    // Format to 2 decimal places for currency
                    if (!isInteger && (field === 'daily_rate' || field === 'security_deposit') && value.includes('.')) {
                        const parts = value.split('.');
                        if (parts[1] && parts[1].length > 2) {
                            this.form[field] = `${parts[0]}.${parts[1].substring(0, 2)}`;
                        }
                    }
                },

                // Check if a color name is valid CSS color
                isValidColor(color) {
                    if (!color) return false;
                    const s = new Option().style;
                    s.color = color;
                    return s.color !== '';
                },

                // Check if a date is in the future
                isFutureDate(dateStr) {
                    if (!dateStr) return false;
                    const inputDate = new Date(dateStr);
                    const today = new Date();
                    today.setHours(0, 0, 0, 0); // Set to beginning of day for fair comparison
                    return inputDate > today;
                },

                // Pricing calculations
                calculateWeeklySavings() {
                    if (!this.form.daily_rate || !this.form.weekly_discount) return 0;

                    const dailyRate = parseFloat(this.form.daily_rate);
                    const weeklyDiscount = parseFloat(this.form.weekly_discount);

                    if (isNaN(dailyRate) || isNaN(weeklyDiscount)) return 0;

                    const regularWeeklyPrice = dailyRate * 7;
                    const discountedWeeklyPrice = regularWeeklyPrice * (1 - weeklyDiscount / 100);

                    return (regularWeeklyPrice - discountedWeeklyPrice).toFixed(2);
                },

                calculateMonthlySavings() {
                    if (!this.form.daily_rate || !this.form.monthly_discount) return 0;

                    const dailyRate = parseFloat(this.form.daily_rate);
                    const monthlyDiscount = parseFloat(this.form.monthly_discount);

                    if (isNaN(dailyRate) || isNaN(monthlyDiscount)) return 0;

                    const regularMonthlyPrice = dailyRate * 30;
                    const discountedMonthlyPrice = regularMonthlyPrice * (1 - monthlyDiscount / 100);

                    return (regularMonthlyPrice - discountedMonthlyPrice).toFixed(2);
                },

                // Custom availability management
                toggleCustomDay(day) {
                    const index = this.customAvailableDays.indexOf(day);
                    if (index === -1) {
                        // Add the day
                        this.customAvailableDays.push(day);
                    } else {
                        // Remove the day
                        this.customAvailableDays.splice(index, 1);
                    }

                    // Update the form field
                    this.form.custom_days = this.customAvailableDays.join(',');
                },

                isCustomDayAvailable(day) {
                    return this.customAvailableDays.includes(day);
                },

                // Map functions
                initMap() {
                    // Check if Leaflet is loaded
                    if (typeof L === 'undefined') {
                        console.error('Leaflet library not loaded');
                        return;
                    }
                    // Wait for the DOM element to be available
                    setTimeout(() => {
                        const mapContainer = document.getElementById('location-map');
                        if (!mapContainer) return;

                        // Initialize the map if it doesn't exist
                        if (!this.map) {
                            try {
                                // Default view - center on a default location
                                this.map = L.map('location-map').setView([40.7128, -74.0060], 13);

                                // Add OpenStreetMap tiles
                                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                                    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                                }).addTo(this.map);

                                // Add geocoder control if it exists
                                if (L.Control.Geocoder) {
                                    this.geocoder = L.Control.Geocoder.nominatim({
                                        defaultMarkGeocode: false
                                    });

                                    L.Control.geocoder({
                                        geocoder: this.geocoder,
                                        placeholder: 'Search for address...',
                                        errorMessage: 'Nothing found.',
                                        suggestMinLength: 3,
                                    }).on('markgeocode', (e) => {
                                        const { center, name } = e.geocode;
                                        this.setMarkerAndGetAddress(center.lat, center.lng, name);
                                    }).addTo(this.map);
                                }

                                // Add map click handler
                                this.map.on('click', (e) => {
                                    this.setMarkerAndGetAddress(e.latlng.lat, e.latlng.lng);
                                });

                                // Add locate control (to find user's location) if it exists
                                if (L.control.locate) {
                                    L.control.locate({
                                        position: 'topright',
                                        strings: {
                                            title: "Show my location"
                                        },
                                        locateOptions: {
                                            enableHighAccuracy: true
                                        },
                                        // On location found, set marker
                                        onLocationFound: (e) => {
                                            this.setMarkerAndGetAddress(e.latlng.lat, e.latlng.lng);
                                        }
                                    }).addTo(this.map);
                                }

                                // Initialize with existing location if available
                                if (this.form.latitude && this.form.longitude) {
                                    this.setMarkerAndGetAddress(
                                        this.form.latitude,
                                        this.form.longitude,
                                        this.form.address
                                    );
                                }

                                // Handle initial city
                                if (this.form.city_id) {
                                    this.loadCityOnMap();
                                }

                                // Force map to recalculate its size
                                this.map.invalidateSize();
                            } catch (error) {
                                console.error('Error initializing map:', error);
                            }
                        }
                    }, 200); // Short delay to ensure the container is rendered
                },

                loadCityOnMap() {
                    const citySelect = document.getElementById('city_id');
                    if (!citySelect || !citySelect.value || !this.map) return;

                    const option = citySelect.options[citySelect.selectedIndex];
                    const lat = option.getAttribute('data-lat');
                    const lng = option.getAttribute('data-lng');

                    if (lat && lng) {
                        this.map.setView([lat, lng], 13);
                        // We don't set a marker - let user choose the exact location
                    }
                },

                searchLocation(query) {
                    if (!query || !this.map) return;

                    // Use the geocoder control if available
                    if (this.geocoder && typeof this.geocoder.geocode === 'function') {
                        this.geocoder.geocode(query);
                    } else {
                        // Fallback to Nominatim API for geocoding
                        fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}`)
                            .then(response => response.json())
                            .then(data => {
                                if (data && data.length > 0) {
                                    const result = data[0];
                                    const lat = parseFloat(result.lat);
                                    const lng = parseFloat(result.lon);

                                    this.map.setView([lat, lng], 16);
                                    this.setMarkerAndGetAddress(lat, lng, result.display_name);
                                } else {
                                    // Show an error message
                                    alert('Location not found. Please try a different search term.');
                                }
                            })
                            .catch(error => {
                                console.error('Error searching for location:', error);
                                alert('Error searching for location. Please try again.');
                            });
                    }
                },

                setMarkerAndGetAddress(lat, lng, addressText = null) {
                    if (!this.map) return;

                    // Round coordinates to 6 decimal places for consistency
                    lat = parseFloat(parseFloat(lat).toFixed(6));
                    lng = parseFloat(parseFloat(lng).toFixed(6));

                    // Remove existing marker
                    if (this.marker) {
                        this.map.removeLayer(this.marker);
                    }

                    // Add new marker
                    this.marker = L.marker([lat, lng], {
                        draggable: true // Make marker draggable
                    }).addTo(this.map);

                    // Update position when marker is dragged
                    this.marker.on('dragend', (e) => {
                        const newPos = e.target.getLatLng();
                        this.setMarkerAndGetAddress(newPos.lat, newPos.lng);
                    });

                    // Update form values
                    this.form.latitude = lat;
                    this.form.longitude = lng;

                    // If address is provided, use it, otherwise do reverse geocoding
                    if (addressText) {
                        this.form.address = addressText;
                        this.marker.bindPopup(addressText).openPopup();
                    } else {
                        // Use Nominatim for reverse geocoding
                        fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}`)
                            .then(response => response.json())
                            .then(data => {
                                const address = data.display_name;
                                this.form.address = address;
                                this.marker.bindPopup(address).openPopup();
                            })
                            .catch(error => {
                                console.error('Error getting address:', error);
                                this.form.address = `Location at ${lat.toFixed(6)}, ${lng.toFixed(6)}`;
                            });
                    }
                },

                clearLocation() {
                    if (this.marker && this.map) {
                        this.map.removeLayer(this.marker);
                        this.marker = null;
                    }

                    this.form.latitude = '';
                    this.form.longitude = '';
                    this.form.address = '';
                    this.form.location_id = '';
                },

                // Save vehicle data
                saveVehicle() {
                    this.message = '';
                    // Reset errors with empty arrays for all fields
                    this.errors = {
                        primary_image: [],
                        images: [],
                        daily_rate: [],
                        availability: [],
                        advance_notice: [],
                        vehicle_type_id: [],
                        make: [],
                        model: [],
                        year: [],
                        license_plate: [],
                        color: [],
                        mileage: [],
                        transmission: [],
                        fuel_type: [],
                        seats: [],
                        doors: [],
                        city_id: []
                    };

                    // Build form data from the form object
                    const formData = new FormData();

                    // Add all form fields
                    Object.keys(this.form).forEach(key => {
                        // Special handling for documents object
                        if (key === 'documents' && typeof this.form[key] === 'object') {
                            // Process each document type
                            Object.keys(this.form.documents).forEach(docTypeId => {
                                const docData = this.form.documents[docTypeId];

                                // Add file_id if it exists
                                if (docData.file_id) {
                                    formData.append(`documents[${docTypeId}][file_id]`, docData.file_id);
                                }

                                // Add expiry_date if it exists
                                if (docData.expiry_date) {
                                    formData.append(`documents[${docTypeId}][expiry_date]`, docData.expiry_date);
                                }
                            });
                        }
                        // Handle arrays (like features)
                        else if (Array.isArray(this.form[key])) {
                            if (this.form[key].length > 0) {
                                // For features, we need to append each value separately to create an array in PHP
                                if (key === 'features') {
                                    this.form[key].forEach((value, index) => {
                                        formData.append(`${key}[]`, value);
                                    });
                                } else {
                                    formData.append(key, this.form[key].join(','));
                                }
                            }
                        } else if (this.form[key] !== null && this.form[key] !== undefined) {
                            // Convert boolean to 0/1
                            if (typeof this.form[key] === 'boolean') {
                                formData.append(key, this.form[key] ? 1 : 0);
                            } else {
                                formData.append(key, this.form[key]);
                            }
                        }
                    });

                    // Validate required fields before submission
                    let hasErrors = false;

                    // Validate primary image
                    if (!this.form.primary_image) {
                        this.errors.primary_image = ['Please upload a main vehicle image'];
                        hasErrors = true;
                    }

                    // Validate gallery images
                    if (!this.form.images) {
                        this.errors.images = ['Please upload at least one vehicle image'];
                        hasErrors = true;
                    }

                    // Validate required documents
                    // Get all document type IDs from the DOM
                    const documentTypeElements = document.querySelectorAll('[name^="documents["][name$="][file_id]"]');
                    const requiredDocumentTypes = Array.from(documentTypeElements).map(element => {
                        const matches = element.name.match(/documents\[(\d+)\]/);
                        return matches && matches[1] ? parseInt(matches[1]) : null;
                    }).filter(id => id !== null);

                    // Get document type names from the DOM
                    const documentTypeNames = {};
                    requiredDocumentTypes.forEach(id => {
                        const labelElement = document.querySelector(`label[for="documents_${id}_expiry_date"]`);
                        if (labelElement) {
                            const parentElement = labelElement.closest('.bg-white');
                            if (parentElement) {
                                const nameElement = parentElement.querySelector('h4');
                                if (nameElement) {
                                    documentTypeNames[id] = nameElement.textContent.trim();
                                }
                            }
                        }
                        // If we couldn't find the name, use a default
                        if (!documentTypeNames[id]) {
                            documentTypeNames[id] = `Document ${id}`;
                        }
                    });

                    // Log the current document values for debugging
                    console.log('Validating documents:', this.form.documents);

                    requiredDocumentTypes.forEach(docTypeId => {
                        // Check if document exists and has a file_id
                        if (!this.form.documents ||
                            !this.form.documents[docTypeId] ||
                            !this.form.documents[docTypeId].file_id ||
                            this.form.documents[docTypeId].file_id === '') {

                            // Create the error key if it doesn't exist
                            if (!this.errors[`documents.${docTypeId}.file_id`]) {
                                this.errors[`documents.${docTypeId}.file_id`] = [];
                            }

                            const docName = documentTypeNames[docTypeId] || `Document ${docTypeId}`;
                            this.errors[`documents.${docTypeId}.file_id`].push(`${docName} is required`);
                            hasErrors = true;

                            console.log(`Document ${docTypeId} (${docName}) is missing or invalid:`,
                                this.form.documents && this.form.documents[docTypeId] ?
                                this.form.documents[docTypeId].file_id : 'undefined');
                        } else {
                            console.log(`Document ${docTypeId} is valid:`, this.form.documents[docTypeId].file_id);
                        }

                        // Check if document requires expiry date by looking for the expiry date input field
                        const expiryDateInput = document.querySelector(`input[name="documents[${docTypeId}][expiry_date]"]`);
                        if (expiryDateInput) {
                            // This document type requires an expiry date
                            if (!this.form.documents ||
                                !this.form.documents[docTypeId] ||
                                !this.form.documents[docTypeId].expiry_date ||
                                this.form.documents[docTypeId].expiry_date === '') {

                                // Create the error key if it doesn't exist
                                if (!this.errors[`documents.${docTypeId}.expiry_date`]) {
                                    this.errors[`documents.${docTypeId}.expiry_date`] = [];
                                }

                                const docName = documentTypeNames[docTypeId] || `Document ${docTypeId}`;
                                this.errors[`documents.${docTypeId}.expiry_date`].push(`${docName} expiry date is required`);
                                hasErrors = true;

                                console.log(`Document ${docTypeId} (${docName}) expiry date is missing or invalid`);
                            }
                            // Check if expiry date is in the future
                            else if (!this.isFutureDate(this.form.documents[docTypeId].expiry_date)) {
                                // Create the error key if it doesn't exist
                                if (!this.errors[`documents.${docTypeId}.expiry_date`]) {
                                    this.errors[`documents.${docTypeId}.expiry_date`] = [];
                                }

                                const docName = documentTypeNames[docTypeId] || `Document ${docTypeId}`;
                                this.errors[`documents.${docTypeId}.expiry_date`].push(`${docName} expiry date must be a future date`);
                                hasErrors = true;

                                console.log(`Document ${docTypeId} (${docName}) expiry date is not in the future:`,
                                    this.form.documents[docTypeId].expiry_date);
                            } else {
                                console.log(`Document ${docTypeId} expiry date is valid:`,
                                    this.form.documents[docTypeId].expiry_date);
                            }
                        }
                    });

                    // If there are errors, show the message and return
                    if (hasErrors) {
                        this.message = 'Please correct the errors below.';
                        this.messageIsError = true;

                        // Switch to the documents tab if there are document errors
                        if (requiredDocumentTypes.some(docTypeId =>
                            this.errors[`documents.${docTypeId}.file_id`] ||
                            this.errors[`documents.${docTypeId}.expiry_date`])) {

                            try {
                                const tabController = document.querySelector('[x-data*="tab"]');
                                if (tabController && tabController.__x) {
                                    tabController.__x.$data.tab = 'documents';
                                }
                            } catch (e) {
                                console.error('Error switching tab:', e);
                            }
                        }

                        return;
                    }

                    // Debug: Log form data for images and documents
                    console.log('Form data - primary_image:', this.form.primary_image);
                    console.log('Form data - images:', this.form.images);
                    console.log('Form data - documents:', this.form.documents);

                    // Log FormData entries for debugging
                    console.log('FormData entries:');
                    for (let pair of formData.entries()) {
                        console.log(pair[0] + ': ' + pair[1]);
                    }

                    // Add method and CSRF token
                    formData.append('_token', "{{ csrf_token() }}");
                    if (this.isEditing) {
                        formData.append('_method', 'PUT');
                    }

                    // Add custom days if using custom availability
                    if (this.form.availability === 'custom') {
                        formData.append('custom_days', this.customAvailableDays.join(','));
                    }

                    // Show loading state
                    this.message = 'Saving vehicle information...';
                    this.messageIsError = false;

                    // Determine URL with correct prefix
                    const url = this.isEditing
                        ? `/management/vehicles/${this.form.id}`
                        : '/management/vehicles';

                    // Submit the form
                    fetch(url, {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        }
                    })
                        .then(response => {
                            // First check if we can parse as JSON
                            const contentType = response.headers.get('content-type');
                            if (contentType && contentType.includes('application/json')) {
                                return response.json().then(data => {
                                    // If we have JSON with error status, throw it to be caught
                                    if (!response.ok) {
                                        return Promise.reject(data);
                                    }
                                    return data;
                                });
                            }

                            // If not JSON and not OK, throw generic error
                            if (!response.ok) {
                                throw new Error(`HTTP error! Status: ${response.status}`);
                            }

                            // If not JSON but OK, return empty success object
                            return { success: true };
                        })
                        .then(data => {
                            if (data.success) {
                                this.message = data.message || 'Vehicle saved successfully!';
                                this.messageIsError = false;

                                // Redirect after delay if ID is provided
                                if (data.redirect) {
                                    setTimeout(() => {
                                        window.location.href = data.redirect;
                                    }, 1500);
                                } else if (data.vehicle && data.vehicle.id) {
                                    // If no redirect but we have a vehicle ID, redirect to the vehicles list
                                    setTimeout(() => {
                                        window.location.href = '/management/vehicles';
                                    }, 1500);
                                }

                                // Scroll to the top of the form to show the success message
                                window.scrollTo({ top: 0, behavior: 'smooth' });
                            } else {
                                this.message = data.message || 'There was an error saving the vehicle.';
                                this.messageIsError = true;

                                if (data.errors) {
                                    // Merge received errors with our initialized errors object
                                    Object.keys(data.errors).forEach(key => {
                                        this.errors[key] = data.errors[key];
                                    });

                                    // Find first tab with error and switch to it
                                    const tabs = ['general', 'photos', 'documents', 'pricing', 'admin'];
                                    for (const tab of tabs) {
                                        if (this.hasTabErrors(tab)) {
                                            try {
                                                // We need to set the tab in the parent scope
                                                const tabController = document.querySelector('[x-data*="tab"]');
                                                if (tabController && tabController.__x) {
                                                    tabController.__x.$data.tab = tab;
                                                }
                                            } catch (e) {
                                                console.error('Error switching tab:', e);
                                            }
                                            break;
                                        }
                                    }

                                    // Scroll to the top of the form to show the error message
                                    window.scrollTo({ top: 0, behavior: 'smooth' });
                                }
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);

                            // Handle validation errors from Laravel
                            if (error && error.errors) {
                                // Merge received errors with our initialized errors object
                                Object.keys(error.errors).forEach(key => {
                                    this.errors[key] = error.errors[key];
                                });
                                this.message = error.message || 'Please correct the errors below.';
                                this.messageIsError = true;

                                // Find first tab with error and switch to it
                                const tabs = ['general', 'photos', 'documents', 'pricing', 'admin'];
                                for (const tab of tabs) {
                                    if (this.hasTabErrors(tab)) {
                                        try {
                                            // Set the tab
                                            const tabController = document.querySelector('[x-data*="tab"]');
                                            if (tabController && tabController.__x) {
                                                tabController.__x.$data.tab = tab;
                                            }
                                        } catch (e) {
                                            console.error('Error switching tab:', e);
                                        }
                                        break;
                                    }
                                }
                            } else {
                                // Generic error
                                this.message = error.message || 'An unexpected error occurred. Please try again.';
                                this.messageIsError = true;
                            }

                            // Scroll to the top of the form to show the error message
                            window.scrollTo({ top: 0, behavior: 'smooth' });
                        });
                },

                // No longer need file manager functions as we're using the file-upload component

                // Handle file-ids-updated events from file-upload components
                handleFileIdsUpdated(event) {
                    console.log('File IDs updated event:', event.detail);

                    try {
                        if (event.detail.name === 'primary_image') {
                            // Ensure value is a string
                            this.form.primary_image = String(event.detail.value || '');
                            console.log('Updated form.primary_image from event:', this.form.primary_image);

                            // Force UI update
                            document.querySelectorAll('[x-text="form.primary_image || \'None\'"').forEach(el => {
                                el.textContent = this.form.primary_image || 'None';
                            });
                        } else if (event.detail.name === 'images') {
                            // Ensure value is a string
                            this.form.images = String(event.detail.value || '');
                            console.log('Updated form.images from event:', this.form.images);

                            // If primary file ID is provided for gallery images
                            if (event.detail.primaryFileId) {
                                // Make sure this image is also set as the primary image
                                this.form.primary_image = String(event.detail.primaryFileId || '');
                                console.log('Updated primary_image from gallery primaryFileId:', this.form.primary_image);

                                // Force UI update
                                document.querySelectorAll('[x-text="form.primary_image || \'None\'"').forEach(el => {
                                    el.textContent = this.form.primary_image || 'None';
                                });
                            }
                        } else if (event.detail.name.startsWith('documents[')) {
                            // Extract document type ID from the name (format: documents[ID][file_id])
                            const matches = event.detail.name.match(/documents\[(\d+)\]/);
                            if (matches && matches[1]) {
                                const documentTypeId = matches[1];
                                if (!this.form.documents) this.form.documents = {};
                                if (!this.form.documents[documentTypeId]) this.form.documents[documentTypeId] = {};
                                this.form.documents[documentTypeId].file_id = String(event.detail.value || '');
                                console.log(`Updated document ${documentTypeId} file_id:`, this.form.documents[documentTypeId].file_id);

                                // Clear the error for this document if it exists
                                if (this.errors[`documents.${documentTypeId}.file_id`]) {
                                    this.errors[`documents.${documentTypeId}.file_id`] = [];
                                }

                                // Log all document updates
                                console.log(`Updated document ${documentTypeId} file_id:`, this.form.documents[documentTypeId].file_id);
                            }
                        }

                        // Force Alpine to update the UI
                        this.$nextTick(() => {
                            console.log('UI updated with new values:', {
                                primary_image: this.form.primary_image,
                                images: this.form.images,
                                documents: this.form.documents
                            });
                        });
                    } catch (error) {
                        console.error('Error handling file-ids-updated event:', error);
                    }
                }
            };
        }
    </script>

<!-- Include File Management Modal -->
{{-- @include('components.file-management.file-management-modal', ['allowedTypes' => ['image']]) --}}

<!-- Include test script for file upload -->
<script src="{{ asset('js/test-file-upload.js') }}"></script>

<!-- Include document validation fix -->
<script src="{{ asset('js/vehicle-document-fix.js') }}"></script>

<!-- Include document debug helper -->
<script src="{{ asset('js/document-debug.js') }}"></script>

</x-app-backend-layout>
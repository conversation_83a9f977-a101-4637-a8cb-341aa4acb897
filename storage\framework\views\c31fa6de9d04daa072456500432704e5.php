<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test File Upload</title>
</head>
<body>
    <h1>Test File Upload</h1>
    <form action="#" method="POST" enctype="multipart/form-data">
        <?php echo csrf_field(); ?>
        <?php if (isset($component)) { $__componentOriginalc550aafe82c2f476cc6661ebacbf2c71 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc550aafe82c2f476cc6661ebacbf2c71 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.basic-file-upload','data' => ['name' => 'test_file','label' => 'Test File Upload','accept' => 'image/*','required' => true]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('basic-file-upload'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'test_file','label' => 'Test File Upload','accept' => 'image/*','required' => true]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc550aafe82c2f476cc6661ebacbf2c71)): ?>
<?php $attributes = $__attributesOriginalc550aafe82c2f476cc6661ebacbf2c71; ?>
<?php unset($__attributesOriginalc550aafe82c2f476cc6661ebacbf2c71); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc550aafe82c2f476cc6661ebacbf2c71)): ?>
<?php $component = $__componentOriginalc550aafe82c2f476cc6661ebacbf2c71; ?>
<?php unset($__componentOriginalc550aafe82c2f476cc6661ebacbf2c71); ?>
<?php endif; ?>
        <button type="submit">Submit</button>
    </form>
</body>
</html>
<?php /**PATH C:\laragon\www\carbnb\resources\views\test-file-upload.blade.php ENDPATH**/ ?>
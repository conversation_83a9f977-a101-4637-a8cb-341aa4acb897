<?php

use App\Models\User;
use App\Models\Vehicle;
use App\Models\VehicleType;
use App\Models\City;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Create roles and permissions
    $adminRole = Role::create(['name' => 'admin']);
    $userRole = Role::create(['name' => 'user']);

    // Create permissions
    Permission::create(['name' => 'dashboard']);
    Permission::create(['name' => 'vehicles.manage']);

    // Assign permissions to roles
    $adminRole->givePermissionTo(['dashboard', 'vehicles.manage']);

    // Create admin user
    $this->admin = User::factory()->create([
        'first_name' => 'Admin',
        'last_name' => 'User',
        'email' => '<EMAIL>',
    ]);
    $this->admin->assignRole('admin');

    // Create regular user
    $this->user = User::factory()->create([
        'first_name' => 'Regular',
        'last_name' => 'User',
        'email' => '<EMAIL>',
    ]);
    $this->user->assignRole('user');

    // Create vehicle type
    $this->vehicleType = VehicleType::create([
        'name' => 'Sedan',
        'slug' => 'sedan',
        'description' => 'A standard sedan',
        'is_active' => true
    ]);

    // Create city
    $this->city = City::create([
        'name' => 'New York',
        'state_id' => 1,
        'latitude' => 40.7128,
        'longitude' => -74.0060,
        'is_active' => true
    ]);
});

test('admin can view vehicle management page', function () {
    $response = $this->actingAs($this->admin)->get('/management/vehicles');

    $response->assertStatus(200);
});

test('regular user cannot access vehicle management page', function () {
    $response = $this->actingAs($this->user)->get('/management/vehicles');

    $response->assertStatus(403);
});

test('admin can create a new vehicle', function () {
    $vehicleData = [
        'user_id' => $this->admin->id,
        'vehicle_type_id' => $this->vehicleType->id,
        'make' => 'Toyota',
        'model' => 'Camry',
        'year' => 2022,
        'license_plate' => 'ABC123',
        'color' => 'Blue',
        'mileage' => 15000,
        'transmission' => 'automatic',
        'fuel_type' => 'gasoline',
        'seats' => '5',
        'doors' => 4,
        'features' => json_encode(['bluetooth', 'air_conditioning']),
        'description' => 'A comfortable sedan for your journey',
        'daily_rate' => 50.00,
        'city_id' => $this->city->id,
        'status' => 'active',
    ];

    $response = $this->actingAs($this->admin)->post('/management/vehicles', $vehicleData);

    $response->assertRedirect();
    $this->assertDatabaseHas('vehicles', [
        'make' => 'Toyota',
        'model' => 'Camry',
        'license_plate' => 'ABC123',
    ]);
});

test('admin can update an existing vehicle', function () {
    // Create a vehicle
    $vehicle = Vehicle::create([
        'user_id' => $this->admin->id,
        'vehicle_type_id' => $this->vehicleType->id,
        'make' => 'Toyota',
        'model' => 'Camry',
        'year' => 2022,
        'license_plate' => 'ABC123',
        'color' => 'Blue',
        'mileage' => 15000,
        'transmission' => 'automatic',
        'fuel_type' => 'gasoline',
        'seats' => '5',
        'doors' => 4,
        'features' => ['bluetooth', 'air_conditioning'],
        'description' => 'A comfortable sedan for your journey',
        'daily_rate' => 50.00,
        'city_id' => $this->city->id,
        'status' => 'active',
    ]);

    $updatedData = [
        'user_id' => $this->admin->id,
        'vehicle_type_id' => $this->vehicleType->id,
        'make' => 'Toyota',
        'model' => 'Corolla', // Changed model
        'year' => 2023, // Changed year
        'license_plate' => 'ABC123',
        'color' => 'Red', // Changed color
        'mileage' => 15000,
        'transmission' => 'automatic',
        'fuel_type' => 'gasoline',
        'seats' => '5',
        'doors' => 4,
        'features' => json_encode(['bluetooth', 'air_conditioning', 'gps']), // Added feature
        'description' => 'A comfortable sedan for your journey',
        'daily_rate' => 55.00, // Changed rate
        'city_id' => $this->city->id,
        'status' => 'active',
    ];

    $response = $this->actingAs($this->admin)->put("/management/vehicles/{$vehicle->id}", $updatedData);

    $response->assertRedirect();
    $this->assertDatabaseHas('vehicles', [
        'id' => $vehicle->id,
        'model' => 'Corolla',
        'year' => 2023,
        'color' => 'Red',
        'daily_rate' => 55.00,
    ]);
});

test('admin can delete a vehicle', function () {
    // Create a vehicle
    $vehicle = Vehicle::create([
        'user_id' => $this->admin->id,
        'vehicle_type_id' => $this->vehicleType->id,
        'make' => 'Toyota',
        'model' => 'Camry',
        'year' => 2022,
        'license_plate' => 'ABC123',
        'color' => 'Blue',
        'mileage' => 15000,
        'transmission' => 'automatic',
        'fuel_type' => 'gasoline',
        'seats' => '5',
        'doors' => 4,
        'features' => ['bluetooth', 'air_conditioning'],
        'description' => 'A comfortable sedan for your journey',
        'daily_rate' => 50.00,
        'city_id' => $this->city->id,
        'status' => 'active',
    ]);

    $response = $this->actingAs($this->admin)->delete("/management/vehicles/{$vehicle->id}");

    $response->assertRedirect();
    $this->assertSoftDeleted('vehicles', [
        'id' => $vehicle->id,
    ]);
});

test('admin can toggle featured status of a vehicle', function () {
    // Create a vehicle
    $vehicle = Vehicle::create([
        'user_id' => $this->admin->id,
        'vehicle_type_id' => $this->vehicleType->id,
        'make' => 'Toyota',
        'model' => 'Camry',
        'year' => 2022,
        'license_plate' => 'ABC123',
        'color' => 'Blue',
        'mileage' => 15000,
        'transmission' => 'automatic',
        'fuel_type' => 'gasoline',
        'seats' => '5',
        'doors' => 4,
        'features' => ['bluetooth', 'air_conditioning'],
        'description' => 'A comfortable sedan for your journey',
        'daily_rate' => 50.00,
        'city_id' => $this->city->id,
        'status' => 'active',
        'is_featured' => false,
    ]);

    $response = $this->actingAs($this->admin)->post("/management/vehicles/{$vehicle->id}/toggle-featured");

    $response->assertRedirect();
    $this->assertDatabaseHas('vehicles', [
        'id' => $vehicle->id,
        'is_featured' => true,
    ]);

    // Toggle again to set back to false
    $response = $this->actingAs($this->admin)->post("/management/vehicles/{$vehicle->id}/toggle-featured");

    $response->assertRedirect();
    $this->assertDatabaseHas('vehicles', [
        'id' => $vehicle->id,
        'is_featured' => false,
    ]);
});

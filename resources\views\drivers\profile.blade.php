<x-app-backend-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Driver Profile') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="mb-6">
                        <h3 class="text-lg font-medium text-gray-900">{{ __('Your Driver Profile') }}</h3>

                        @if($driver->status === 'pending')
                            <div class="mt-2 bg-yellow-50 border-l-4 border-yellow-400 p-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-yellow-700">
                                            {{ __('Your driver application is pending approval. We will review your information and documents as soon as possible.') }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        @elseif($driver->status === 'rejected')
                            <div class="mt-2 bg-red-50 border-l-4 border-red-400 p-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-red-700">
                                            {{ __('Your driver application has been rejected. Please contact support for more information.') }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        @elseif($driver->status === 'active')
                            <div class="mt-2 bg-green-50 border-l-4 border-green-400 p-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-green-700">
                                            {{ __('Your driver account is active. You can now receive booking requests.') }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Profile Information -->
                        <div class="md:col-span-2">
                            <div class="bg-gray-50 p-6 rounded-lg">
                                <h4 class="text-md font-medium text-gray-900 mb-4">{{ __('Profile Information') }}</h4>

                                <dl class="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-6">
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">{{ __('Name') }}</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $driver->user->first_name }} {{ $driver->user->last_name }}</dd>
                                    </div>

                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">{{ __('Email') }}</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $driver->user->email }}</dd>
                                    </div>

                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">{{ __('Experience') }}</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $driver->experience_years }} {{ Str::plural('year', $driver->experience_years) }}</dd>
                                    </div>

                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">{{ __('Languages') }}</dt>
                                        <dd class="mt-1 text-sm text-gray-900">
                                            @php
                                                $languages = json_decode($driver->languages, true);
                                            @endphp
                                            {{ implode(', ', $languages) }}
                                        </dd>
                                    </div>

                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">{{ __('Daily Rate') }}</dt>
                                        <dd class="mt-1 text-sm text-gray-900">${{ number_format($driver->daily_rate, 2) }}</dd>
                                    </div>

                                    @if($driver->hourly_rate)
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">{{ __('Hourly Rate') }}</dt>
                                            <dd class="mt-1 text-sm text-gray-900">${{ number_format($driver->hourly_rate, 2) }}</dd>
                                        </div>
                                    @endif

                                    @if($driver->city)
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">{{ __('City') }}</dt>
                                            <dd class="mt-1 text-sm text-gray-900">{{ $driver->city->name }}</dd>
                                        </div>
                                    @endif

                                    <div class="md:col-span-2">
                                        <dt class="text-sm font-medium text-gray-500">{{ __('Bio') }}</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $driver->bio ?? __('No bio provided.') }}</dd>
                                    </div>
                                </dl>

                                <div class="mt-6">
                                    <a href="{{ route('drivers.edit-profile') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                        <svg class="-ml-1 mr-2 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                                        </svg>
                                        {{ __('Edit Profile') }}
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Profile Image -->
                        <div>
                            <div class="bg-gray-50 p-6 rounded-lg">
                                <h4 class="text-md font-medium text-gray-900 mb-4">{{ __('Profile Image') }}</h4>

                                <div class="flex justify-center">
                                    @if($driver->user->profile_image)
                                        <img src="{{ $driver->user->profile_image }}" alt="{{ $driver->user->first_name }}" class="h-48 w-48 rounded-full object-cover">
                                    @else
                                        <div class="h-48 w-48 rounded-full bg-gray-200 flex items-center justify-center">
                                            <svg class="h-24 w-24 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                            </svg>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- License Information -->
                    <div class="mt-6">
                        <div class="bg-gray-50 p-6 rounded-lg">
                            <h4 class="text-md font-medium text-gray-900 mb-4">{{ __('License Information') }}</h4>

                            <dl class="grid grid-cols-1 md:grid-cols-3 gap-x-4 gap-y-6">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">{{ __('License Number') }}</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $driver->license->license_number }}</dd>
                                </div>

                                <div>
                                    <dt class="text-sm font-medium text-gray-500">{{ __('Expiry Date') }}</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $driver->license->expiry_date->format('M d, Y') }}</dd>
                                </div>

                                <div>
                                    <dt class="text-sm font-medium text-gray-500">{{ __('Status') }}</dt>
                                    <dd class="mt-1 text-sm">
                                        @if($driver->license->status === 'pending')
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                {{ __('Pending') }}
                                            </span>
                                        @elseif($driver->license->status === 'approved')
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                {{ __('Approved') }}
                                            </span>
                                        @elseif($driver->license->status === 'rejected')
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                {{ __('Rejected') }}
                                            </span>
                                        @endif
                                    </dd>
                                </div>
                            </dl>

                            <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <h5 class="text-sm font-medium text-gray-700 mb-2">{{ __('License Front') }}</h5>
                                    <img src="{{ $driver->license->front_image }}" alt="License Front" class="h-auto w-full rounded-md border border-gray-200">
                                </div>

                                @if($driver->license->back_image)
                                    <div>
                                        <h5 class="text-sm font-medium text-gray-700 mb-2">{{ __('License Back') }}</h5>
                                        <img src="{{ $driver->license->back_image }}" alt="License Back" class="h-auto w-full rounded-md border border-gray-200">
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Documents -->
                    <div class="mt-6">
                        <div class="bg-gray-50 p-6 rounded-lg">
                            <h4 class="text-md font-medium text-gray-900 mb-4">{{ __('Documents') }}</h4>

                            @if($documents->count() > 0)
                                <div class="overflow-x-auto">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Document Type') }}</th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Status') }}</th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Uploaded') }}</th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Actions') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200">
                                            @foreach($documents as $document)
                                                <tr>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ $document->documentType->name }}</td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                        @if($document->status === 'pending')
                                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                                {{ __('Pending') }}
                                                            </span>
                                                        @elseif($document->status === 'approved')
                                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                                {{ __('Approved') }}
                                                            </span>
                                                        @elseif($document->status === 'rejected')
                                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                                {{ __('Rejected') }}
                                                            </span>
                                                        @endif
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ $document->created_at->format('M d, Y') }}</td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                        <a href="{{ $document->file ? $document->file->url : '#' }}" target="_blank" class="text-indigo-600 hover:text-indigo-900">{{ __('View') }}</a>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <p class="text-sm text-gray-500">{{ __('No documents uploaded yet.') }}</p>
                            @endif

                            <div class="mt-6">
                                <a href="{{ route('drivers.documents.upload') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                    <svg class="-ml-1 mr-2 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                    </svg>
                                    {{ __('Upload Documents') }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-backend-layout>

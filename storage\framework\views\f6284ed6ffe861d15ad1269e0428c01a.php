<?php if (isset($component)) { $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742 = $attributes; } ?>
<?php $component = App\View\Components\AppBackendLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-backend-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppBackendLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container bg-white p-6 mx-auto rounded-lg shadow-lg" x-data="vehicleTypeApp()">
        <div class="flex justify-between items-center mb-4">
            <h1 class="text-3xl font-semibold text-gray-800">Vehicle Types</h1>
            <button @click="openCreateModal" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition duration-200">Create Vehicle Type</button>
        </div>

        <table class="table-auto w-full mb-6 border-collapse">
            <thead>
            <tr class="bg-gray-100 text-gray-600">
                <th class="px-6 py-3 text-left font-medium text-sm uppercase">Name</th>
                <th class="px-6 py-3 text-left font-medium text-sm uppercase">Description</th>
                <th class="px-6 py-3 text-left font-medium text-sm uppercase">Icon</th>
                <th class="px-6 py-3 text-left font-medium text-sm uppercase">Status</th>
                <th class="px-6 py-3 text-left font-medium text-sm uppercase">Actions</th>
            </tr>
            </thead>
            <tbody class="text-gray-600">
            <?php $__currentLoopData = $vehicleTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vehicleType): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr class="border-b hover:bg-gray-50 transition duration-150">
                    <td class="px-6 py-4"><?php echo e($vehicleType->name); ?></td>
                    <td class="px-6 py-4"><?php echo e($vehicleType->description); ?></td>
                    <td class="px-6 py-4">
                        <?php if($vehicleType->icon): ?>
                            <i class="fas <?php echo e($vehicleType->icon); ?> text-xl"></i>
                        <?php else: ?>
                            <span class="text-gray-400">No icon</span>
                        <?php endif; ?>
                    </td>
                    <td class="px-6 py-4">
                        <span class="px-2 py-1 rounded-full text-xs font-semibold <?php echo e($vehicleType->is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'); ?>">
                            <?php echo e($vehicleType->is_active ? 'Active' : 'Inactive'); ?>

                        </span>
                    </td>
                    <td class="px-6 py-4 space-x-4">
                        <button @click="openEditModal(<?php echo e($vehicleType); ?>)" class="text-blue-500 hover:text-blue-700">Edit</button>
                        <button @click="deleteVehicleType(<?php echo e($vehicleType->id); ?>)" class="text-red-500 hover:text-red-700">Delete</button>
                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tbody>
        </table>

        <!-- Create/Edit Modal -->
        <div x-show="isModalOpen" class="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50 z-50" x-cloak>
            <div class="bg-white p-8 rounded-lg w-1/3 relative shadow-lg">
                <button @click="closeModal" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>

                <h2 class="text-xl font-semibold mb-6" x-text="isEditMode ? 'Edit Vehicle Type' : 'Create Vehicle Type'"></h2>

                <form @submit.prevent="isEditMode ? updateVehicleType() : createVehicleType()">
                    <div class="mb-6">
                        <label for="name" class="block text-sm font-medium text-gray-700">Name</label>
                        <input type="text" name="name" id="name" x-model="form.name" class="mt-1 block w-full border border-gray-300 rounded-md p-3 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                    </div>

                    <div class="mb-6">
                        <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                        <textarea name="description" id="description" x-model="form.description" class="mt-1 block w-full border border-gray-300 rounded-md p-3 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" rows="3"></textarea>
                    </div>

                    <div class="mb-6">
                        <label for="icon" class="block text-sm font-medium text-gray-700">Icon (FontAwesome class)</label>
                        <div class="mt-1 flex">
                            <input type="text" name="icon" id="icon" x-model="form.icon" class="block w-full border border-gray-300 rounded-md p-3 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="fa-car">
                            <div class="ml-2 flex items-center">
                                <i :class="'fas ' + (form.icon || 'fa-car')" class="text-xl"></i>
                            </div>
                        </div>
                        <p class="text-sm text-gray-500 mt-1">Enter FontAwesome class name (e.g. fa-car, fa-truck)</p>
                    </div>

                    <div class="mb-6">
                        <label class="flex items-center">
                            <input type="checkbox" x-model="form.is_active" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-gray-700">Active</span>
                        </label>
                    </div>

                    <div class="flex justify-between">
                        <button type="submit" class="bg-blue-500 text-white px-6 py-2 rounded-md hover:bg-blue-600 transition duration-200" x-text="isEditMode ? 'Update' : 'Create'"></button>
                        <button @click="closeModal" type="button" class="bg-gray-500 text-white px-6 py-2 rounded-md hover:bg-gray-600 transition duration-200">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function vehicleTypeApp() {
            return {
                isModalOpen: false,
                isEditMode: false,
                form: {
                    id: null,
                    name: '',
                    description: '',
                    icon: '',
                    is_active: true
                },

                openCreateModal() {
                    this.isEditMode = false;
                    this.form = { id: null, name: '', description: '', icon: '', is_active: true };
                    this.isModalOpen = true;
                },

                openEditModal(vehicleType) {
                    this.isEditMode = true;
                    this.form = {
                        id: vehicleType.id,
                        name: vehicleType.name,
                        description: vehicleType.description || '',
                        icon: vehicleType.icon || '',
                        is_active: vehicleType.is_active
                    };
                    this.isModalOpen = true;
                },

                closeModal() {
                    this.isModalOpen = false;
                },

                async createVehicleType() {
                    try {
                        const response = await fetch('<?php echo e(route('vehicle-types.store')); ?>', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                            },
                            body: JSON.stringify(this.form)
                        });

                        const data = await response.json();

                        if (!response.ok) {
                            throw new Error(data.message || 'Error creating vehicle type');
                        }

                        Swal.fire({
                            title: 'Success!',
                            text: 'Vehicle type created successfully',
                            icon: 'success',
                            confirmButtonText: 'OK'
                        }).then(() => {
                            window.location.reload();
                        });
                    } catch (error) {
                        Swal.fire('Error', error.message, 'error');
                    }
                },

                async updateVehicleType() {
                    try {
                        const response = await fetch(`<?php echo e(url('/vehicle-types')); ?>/${this.form.id}`, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                            },
                            body: JSON.stringify(this.form)
                        });

                        const data = await response.json();

                        if (!response.ok) {
                            throw new Error(data.message || 'Error updating vehicle type');
                        }

                        Swal.fire({
                            title: 'Success!',
                            text: 'Vehicle type updated successfully',
                            icon: 'success',
                            confirmButtonText: 'OK'
                        }).then(() => {
                            window.location.reload();
                        });
                    } catch (error) {
                        Swal.fire('Error', error.message, 'error');
                    }
                },

                async deleteVehicleType(id) {
                    Swal.fire({
                        title: 'Are you sure?',
                        text: "This action cannot be undone.",
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#3085d6',
                        cancelButtonColor: '#d33',
                        confirmButtonText: 'Yes, delete it!'
                    }).then(async (result) => {
                        if (result.isConfirmed) {
                            try {
                                const response = await fetch(`<?php echo e(url('/vehicle-types')); ?>/${id}`, {
                                    method: 'DELETE',
                                    headers: {
                                        'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                                    }
                                });

                                const data = await response.json();

                                if (!response.ok) {
                                    throw new Error(data.message || 'Error deleting vehicle type');
                                }

                                Swal.fire('Deleted!', 'Vehicle type has been deleted.', 'success');
                                window.location.reload();
                            } catch (error) {
                                Swal.fire('Error', error.message, 'error');
                            }
                        }
                    });
                }
            }
        }
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $attributes = $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $component = $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\carbnb\resources\views\backend\vehicle-type\index.blade.php ENDPATH**/ ?>
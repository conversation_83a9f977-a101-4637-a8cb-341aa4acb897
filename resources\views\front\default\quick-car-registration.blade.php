<x-app-layout>
    <!-- Hero Section -->
    <section class="relative h-[400px] overflow-hidden bg-gradient-to-r from-gray-900 to-red-900">
        <div class="absolute inset-0 opacity-40">
            <img src="https://images.unsplash.com/photo-1580273916550-e323be2ae537?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80" alt="Car keys handover" class="w-full h-full object-cover">
        </div>
        <div class="absolute inset-0 bg-gradient-to-b from-transparent to-black opacity-40"></div>
        <div class="relative container mx-auto px-4 h-full flex flex-col justify-center">
            <div class="max-w-xl">
                <h1 class="text-4xl md:text-5xl font-extrabold text-white mb-6 leading-tight">Quick Car Registration</h1>
                <p class="text-xl text-white/90 mb-6 leading-relaxed">Get started in just 2 minutes. Our team will help you complete your listing.</p>
            </div>
        </div>
        <div class="absolute bottom-0 left-0 right-0">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 120" class="w-full h-auto">
                <path fill="#ffffff" fill-opacity="1" d="M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z"></path>
            </svg>
        </div>
    </section>

    <!-- Registration Form Section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="max-w-3xl mx-auto">
                <div class="bg-white rounded-2xl shadow-xl p-8 border border-gray-100">
                    <div class="text-center mb-8">
                        <div class="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-car text-3xl text-primary"></i>
                        </div>
                        <h2 class="text-3xl font-bold text-gray-900 mb-2">Start Earning with Your Car</h2>
                        <p class="text-gray-600">Fill in these basic details and we'll help you with the rest.</p>
                    </div>

                    @if ($errors->any())
                        <div class="mb-8 bg-red-50 border-l-4 border-red-500 p-4 rounded-md">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-exclamation-circle text-red-500"></i>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-red-800">Please fix the following errors:</h3>
                                    <ul class="mt-2 text-sm text-red-700 list-disc list-inside">
                                        @foreach ($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                        </div>
                    @endif

                    <form id="quick-registration-form" action="{{ route('store-quick-car-registration') }}" method="POST" class="space-y-8">
                        @csrf

                        <!-- Personal Information -->
                        <div>
                            <h3 class="text-xl font-bold mb-4 text-gray-900 border-b pb-2">Your Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="first_name" class="block text-gray-700 font-medium mb-2">First Name *</label>
                                    <input type="text" id="first_name" name="first_name" required
                                        class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:outline-none"
                                        value="{{ old('first_name') }}">
                                </div>
                                <div>
                                    <label for="last_name" class="block text-gray-700 font-medium mb-2">Last Name *</label>
                                    <input type="text" id="last_name" name="last_name" required
                                        class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:outline-none"
                                        value="{{ old('last_name') }}">
                                </div>
                                <div>
                                    <label for="email" class="block text-gray-700 font-medium mb-2">Email *</label>
                                    <input type="email" id="email" name="email" required
                                        class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:outline-none"
                                        value="{{ old('email') }}">
                                </div>
                                <div>
                                    <label for="phone" class="block text-gray-700 font-medium mb-2">Phone Number *</label>
                                    <input type="tel" id="phone" name="phone" required
                                        class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:outline-none"
                                        value="{{ old('phone') }}">
                                </div>
                            </div>
                        </div>

                        <!-- Car Information -->
                        <div>
                            <h3 class="text-xl font-bold mb-4 text-gray-900 border-b pb-2">Car Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="vehicle_type_id" class="block text-gray-700 font-medium mb-2">Vehicle Type *</label>
                                    <select id="vehicle_type_id" name="vehicle_type_id" required
                                        class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:outline-none appearance-none bg-white">
                                        <option value="">Select Vehicle Type</option>
                                        @foreach($vehicleTypes as $type)
                                            <option value="{{ $type->id }}" {{ old('vehicle_type_id') == $type->id ? 'selected' : '' }}>
                                                {{ $type->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div>
                                    <label for="make" class="block text-gray-700 font-medium mb-2">Make *</label>
                                    <input type="text" id="make" name="make" required
                                        class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:outline-none"
                                        value="{{ old('make') }}" placeholder="e.g. Toyota, Honda, BMW">
                                </div>
                                <div>
                                    <label for="model" class="block text-gray-700 font-medium mb-2">Model *</label>
                                    <input type="text" id="model" name="model" required
                                        class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:outline-none"
                                        value="{{ old('model') }}" placeholder="e.g. Corolla, Civic, X5">
                                </div>
                                <div>
                                    <label for="year" class="block text-gray-700 font-medium mb-2">Year *</label>
                                    <select id="year" name="year" required
                                        class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:outline-none appearance-none bg-white">
                                        <option value="">Select Year</option>
                                        @for($i = date('Y') + 1; $i >= 2000; $i--)
                                            <option value="{{ $i }}" {{ old('year') == $i ? 'selected' : '' }}>{{ $i }}</option>
                                        @endfor
                                    </select>
                                </div>
                                <div class="md:col-span-2">
                                    <label for="city_id" class="block text-gray-700 font-medium mb-2">City *</label>
                                    <select id="city_id" name="city_id" required
                                        class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:outline-none appearance-none bg-white">
                                        <option value="">Select City</option>
                                        @foreach($cities as $city)
                                            <option value="{{ $city->id }}" {{ old('city_id') == $city->id ? 'selected' : '' }}>
                                                {{ $city->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Terms and Submit -->
                        <div class="pt-4">
                            <div class="flex items-start mb-6">
                                <div class="flex items-center h-5">
                                    <input id="terms" name="terms" type="checkbox" required
                                        class="w-4 h-4 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-primary">
                                </div>
                                <label for="terms" class="ml-2 text-sm text-gray-600">
                                    I agree to the <a href="#" class="text-primary hover:underline">Terms of Service</a> and <a href="#" class="text-primary hover:underline">Privacy Policy</a>
                                </label>
                            </div>

                            <div class="text-center">
                                <button type="submit" class="inline-block px-8 py-4 bg-primary text-white font-bold rounded-full hover:bg-red-700 transition transform hover:scale-105 shadow-lg">
                                    <i class="fas fa-car-side mr-2"></i> Register My Car
                                </button>
                                <p class="text-sm text-gray-500 mt-4">
                                    Our team will contact you to complete your listing and help you start earning.
                                </p>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Video Section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">See How It Works</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">Watch our short video to learn how easy it is to list your car and start earning.</p>
            </div>

            <div class="max-w-4xl mx-auto rounded-2xl overflow-hidden shadow-2xl video-section">
                <div class="relative pb-[56.25%] h-0">
                    <video class="absolute inset-0 w-full h-full object-cover" controls poster="{{ asset('video/videoPoster.png') }}">
                        <source src="{{ asset('video/carbnbVideo.mp4') }}" type="video/mp4">
                        Your browser does not support the video tag.
                    </video>
                    <div class="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center group transition-opacity duration-500">
                        <button class="w-20 h-20 bg-primary bg-opacity-80 rounded-full flex items-center justify-center transition-transform transform group-hover:scale-110 focus:outline-none">
                            <i class="fas fa-play text-white text-3xl pl-1"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Benefits Section -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Why List Your Car With Us?</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">Join thousands of car owners who are already earning extra income.</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
                <div class="bg-white p-6 rounded-xl shadow-md text-center">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-euro-sign text-2xl text-green-600"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2 text-gray-900">Earn Extra Income</h3>
                    <p class="text-gray-600">Make up to €500+ per month by sharing your car when you're not using it.</p>
                </div>

                <div class="bg-white p-6 rounded-xl shadow-md text-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-shield-alt text-2xl text-blue-600"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2 text-gray-900">Insurance Coverage</h3>
                    <p class="text-gray-600">Every trip includes €1M in liability insurance and comprehensive coverage.</p>
                </div>

                <div class="bg-white p-6 rounded-xl shadow-md text-center">
                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-calendar-alt text-2xl text-purple-600"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2 text-gray-900">Flexible Schedule</h3>
                    <p class="text-gray-600">You control when your car is available. Share only when it works for you.</p>
                </div>
            </div>
        </div>
    </section>

    @push('scripts')
    <script>
        $(document).ready(function() {
            // Form validation with SweetAlert
            $('#quick-registration-form').submit(function(e) {
                const form = this;
                const requiredFields = $(form).find('input[required], select[required]');
                let valid = true;
                let firstError = null;

                requiredFields.each(function() {
                    if (!$(this).val()) {
                        valid = false;
                        $(this).addClass('border-red-500');

                        if (!firstError) {
                            firstError = $(this);
                        }
                    } else {
                        $(this).removeClass('border-red-500');
                    }
                });

                if (!valid) {
                    e.preventDefault();

                    // Scroll to the first error
                    if (firstError) {
                        $('html, body').animate({
                            scrollTop: firstError.offset().top - 100
                        }, 500);
                    }

                    Swal.fire({
                        title: 'Please fill in all required fields',
                        icon: 'error',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#FF0000'
                    });
                }
            });

            // Video player functionality
            const videoContainer = document.querySelector('.video-section');
            const video = document.querySelector('.video-section video');
            const playButton = document.querySelector('.video-section .group button');

            if (video && playButton) {
                // Make sure video is loaded
                video.addEventListener('loadedmetadata', function() {
                    console.log('Video metadata loaded');
                });

                // Play button click handler
                playButton.addEventListener('click', function() {
                    console.log('Play button clicked');
                    try {
                        const playPromise = video.play();

                        if (playPromise !== undefined) {
                            playPromise.then(_ => {
                                // Video playback started successfully
                                console.log('Video playback started');
                                this.parentElement.classList.add('opacity-0');
                                setTimeout(() => {
                                    this.parentElement.style.display = 'none';
                                }, 500);
                            })
                            .catch(error => {
                                // Auto-play was prevented
                                console.log('Playback error:', error);
                            });
                        }
                    } catch (e) {
                        console.error('Error playing video:', e);
                    }
                });

                // Handle video pause
                video.addEventListener('pause', function() {
                    console.log('Video paused');
                    playButton.parentElement.style.display = 'flex';
                    setTimeout(() => {
                        playButton.parentElement.classList.remove('opacity-0');
                    }, 10);
                });

                // Handle video end
                video.addEventListener('ended', function() {
                    console.log('Video ended');
                    playButton.parentElement.style.display = 'flex';
                    setTimeout(() => {
                        playButton.parentElement.classList.remove('opacity-0');
                    }, 10);
                });
            }
        });
    </script>
    @endpush
</x-app-layout>

<?php if (isset($component)) { $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742 = $attributes; } ?>
<?php $component = App\View\Components\AppBackendLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-backend-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppBackendLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container py-8">
        <div class="mb-6">
            <?php if(Auth::id() === $booking->user_id): ?>
                <a href="<?php echo e(route('booking.index')); ?>" class="text-primary hover:text-primary-dark flex items-center">
                    <i class="fas fa-arrow-left mr-1"></i>
                    Back to My Bookings
                </a>
            <?php else: ?>
                <a href="<?php echo e(route('booking.owner')); ?>" class="text-primary hover:text-primary-dark flex items-center">
                    <i class="fas fa-arrow-left mr-1"></i>
                    Back to Received Bookings
                </a>
            <?php endif; ?>
        </div>

        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                <div class="flex flex-wrap items-center justify-between">
                    <div>
                        <h2 class="text-xl font-bold text-gray-800">Booking #<?php echo e($booking->booking_number); ?></h2>
                        <p class="text-gray-600 mt-1">Created on <?php echo e($booking->created_at->format('M d, Y')); ?></p>
                    </div>
                    <div>
                        <?php
                            $statusColors = [
                                'pending' => 'bg-yellow-100 text-yellow-800',
                                'confirmed' => 'bg-blue-100 text-blue-800',
                                'in_progress' => 'bg-indigo-100 text-indigo-800',
                                'completed' => 'bg-green-100 text-green-800',
                                'cancelled' => 'bg-red-100 text-red-800',
                                'rejected' => 'bg-gray-100 text-gray-800',
                            ];
                            $statusColor = $statusColors[$booking->status] ?? 'bg-gray-100 text-gray-800';
                        ?>
                        <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full <?php echo e($statusColor); ?>">
                            <?php echo e(ucfirst(str_replace('_', ' ', $booking->status))); ?>

                        </span>
                    </div>
                </div>
            </div>

            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Vehicle Information -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="text-lg font-semibold text-gray-800 mb-3">Vehicle Information</h3>
                        <div class="flex items-start">
                            <div class="flex-shrink-0 h-20 w-20 mr-4">
                                <?php if($booking->vehicle->primaryImage): ?>
                                    <img class="h-20 w-20 rounded-md object-cover" src="<?php echo e(asset('storage/' . $booking->vehicle->primaryImage->file->path)); ?>" alt="<?php echo e($booking->vehicle->make); ?> <?php echo e($booking->vehicle->model); ?>">
                                <?php else: ?>
                                    <div class="h-20 w-20 rounded-md bg-gray-200 flex items-center justify-center">
                                        <i class="fas fa-car text-gray-400 text-2xl"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div>
                                <h4 class="text-md font-medium text-gray-900"><?php echo e($booking->vehicle->make); ?> <?php echo e($booking->vehicle->model); ?></h4>
                                <p class="text-sm text-gray-600"><?php echo e($booking->vehicle->year); ?> · <?php echo e($booking->vehicle->color); ?></p>
                                <p class="text-sm text-gray-600 mt-1">
                                    <span class="font-medium">Owner:</span> <?php echo e($booking->vehicle->user->first_name); ?> <?php echo e($booking->vehicle->user->last_name); ?>

                                </p>
                                <p class="text-sm text-gray-600">
                                    <span class="font-medium">License Plate:</span> <?php echo e($booking->vehicle->license_plate); ?>

                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Booking Details -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="text-lg font-semibold text-gray-800 mb-3">Booking Details</h3>
                        <div class="grid grid-cols-2 gap-3">
                            <div>
                                <p class="text-sm text-gray-600">Pickup Date</p>
                                <p class="text-md font-medium"><?php echo e($booking->start_date->format('M d, Y')); ?></p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">Return Date</p>
                                <p class="text-md font-medium"><?php echo e($booking->end_date->format('M d, Y')); ?></p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">Duration</p>
                                <p class="text-md font-medium"><?php echo e($booking->duration_in_days); ?> days</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">Status</p>
                                <p class="text-md font-medium"><?php echo e(ucfirst(str_replace('_', ' ', $booking->status))); ?></p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">Pickup Location</p>
                                <p class="text-md font-medium"><?php echo e($booking->pickup_location); ?></p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">Return Location</p>
                                <p class="text-md font-medium"><?php echo e($booking->return_location); ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- Driver Information (if applicable) -->
                    <?php if($booking->driver): ?>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="text-lg font-semibold text-gray-800 mb-3">Driver Information</h3>
                        <div class="flex items-start">
                            <div class="flex-shrink-0 h-16 w-16 mr-4">
                                <div class="h-16 w-16 rounded-full bg-gray-200 flex items-center justify-center">
                                    <i class="fas fa-user text-gray-400 text-2xl"></i>
                                </div>
                            </div>
                            <div>
                                <h4 class="text-md font-medium text-gray-900"><?php echo e($booking->driver->user->first_name); ?> <?php echo e($booking->driver->user->last_name); ?></h4>
                                <p class="text-sm text-gray-600"><?php echo e($booking->driver->license_number); ?></p>
                                <p class="text-sm text-gray-600 mt-1">
                                    <span class="font-medium">Experience:</span> <?php echo e($booking->driver->experience_years); ?> years
                                </p>
                                <p class="text-sm text-gray-600">
                                    <span class="font-medium">Daily Rate:</span> <?php echo e(config('payment.currency_symbol')); ?><?php echo e(number_format($booking->driver->daily_rate, 2)); ?>

                                </p>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Customer Information -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="text-lg font-semibold text-gray-800 mb-3">Customer Information</h3>
                        <div class="flex items-start">
                            <div class="flex-shrink-0 h-16 w-16 mr-4">
                                <div class="h-16 w-16 rounded-full bg-gray-200 flex items-center justify-center">
                                    <i class="fas fa-user text-gray-400 text-2xl"></i>
                                </div>
                            </div>
                            <div>
                                <h4 class="text-md font-medium text-gray-900"><?php echo e($booking->user->first_name); ?> <?php echo e($booking->user->last_name); ?></h4>
                                <p class="text-sm text-gray-600"><?php echo e($booking->user->email); ?></p>
                                <p class="text-sm text-gray-600 mt-1">
                                    <span class="font-medium">Phone:</span> <?php echo e($booking->user->phone ?? 'Not provided'); ?>

                                </p>
                                <p class="text-sm text-gray-600">
                                    <span class="font-medium">Member since:</span> <?php echo e($booking->user->created_at->format('M Y')); ?>

                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Information -->
                <div class="mt-6 bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold text-gray-800 mb-3">Payment Information</h3>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-100">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Price</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php $__currentLoopData = $booking->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo e($item->item_name); ?></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right"><?php echo e(config('payment.currency_symbol')); ?><?php echo e(number_format($item->unit_price, 2)); ?></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right"><?php echo e($item->quantity); ?></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right"><?php echo e(config('payment.currency_symbol')); ?><?php echo e(number_format($item->total_price, 2)); ?></td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php if($booking->discount_amount > 0): ?>
                                <tr>
                                    <td colspan="3" class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-right">Discount</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">-<?php echo e(config('payment.currency_symbol')); ?><?php echo e(number_format($booking->discount_amount, 2)); ?></td>
                                </tr>
                                <?php endif; ?>
                                <tr>
                                    <td colspan="3" class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-right">Subtotal</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right"><?php echo e(config('payment.currency_symbol')); ?><?php echo e(number_format($booking->subtotal, 2)); ?></td>
                                </tr>
                                <tr>
                                    <td colspan="3" class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-right">Tax (10%)</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right"><?php echo e(config('payment.currency_symbol')); ?><?php echo e(number_format($booking->tax_amount, 2)); ?></td>
                                </tr>
                                <tr class="bg-gray-50">
                                    <td colspan="3" class="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900 text-right">Total</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900 text-right"><?php echo e(config('payment.currency_symbol')); ?><?php echo e(number_format($booking->total_amount, 2)); ?></td>
                                </tr>
                                <tr>
                                    <td colspan="3" class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-right">Security Deposit (Refundable)</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right"><?php echo e(config('payment.currency_symbol')); ?><?php echo e(number_format($booking->security_deposit, 2)); ?></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Payment Status -->
                    <div class="mt-4 p-4 border rounded-lg">
                        <div class="flex justify-between items-center">
                            <div>
                                <h4 class="text-md font-medium text-gray-900">Payment Status</h4>
                                <?php if($booking->payments->where('status', 'completed')->count() > 0): ?>
                                    <p class="text-sm text-green-600">Paid on <?php echo e($booking->payments->where('status', 'completed')->first()->created_at->format('M d, Y')); ?></p>
                                <?php else: ?>
                                    <p class="text-sm text-red-600">Unpaid</p>
                                <?php endif; ?>
                            </div>
                            <?php if($booking->status === 'pending' || $booking->status === 'confirmed'): ?>
                                <?php if($booking->payments->where('status', 'completed')->count() === 0 && Auth::id() === $booking->user_id): ?>
                                    <a href="<?php echo e(route('booking.payment', $booking->id)); ?>" class="inline-flex items-center px-4 py-2 bg-primary border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-primary-dark active:bg-primary-dark focus:outline-none focus:border-primary-dark focus:ring ring-primary-light disabled:opacity-25 transition ease-in-out duration-150">
                                        Pay Now
                                    </a>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Status History -->
                <div class="mt-6 bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold text-gray-800 mb-3">Status History</h3>
                    <div class="space-y-4">
                        <?php $__currentLoopData = $booking->statusHistory->sortByDesc('created_at'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <div class="flex items-center justify-center h-8 w-8 rounded-full bg-blue-500 text-white">
                                        <i class="fas fa-history"></i>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <h4 class="text-md font-medium text-gray-900">
                                        Status changed to <?php echo e(ucfirst(str_replace('_', ' ', $status->status))); ?>

                                    </h4>
                                    <p class="text-sm text-gray-600">
                                        <?php echo e($status->created_at->format('M d, Y H:i')); ?> by <?php echo e($status->updatedBy->first_name); ?> <?php echo e($status->updatedBy->last_name); ?>

                                    </p>
                                    <?php if($status->notes): ?>
                                        <p class="text-sm text-gray-800 mt-1"><?php echo e($status->notes); ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>

                <!-- Special Requests -->
                <?php if($booking->special_requests): ?>
                    <div class="mt-6 bg-gray-50 p-4 rounded-lg">
                        <h3 class="text-lg font-semibold text-gray-800 mb-3">Special Requests</h3>
                        <p class="text-sm text-gray-800"><?php echo e($booking->special_requests); ?></p>
                    </div>
                <?php endif; ?>

                <!-- Actions -->
                <div class="mt-6 flex flex-wrap justify-end space-x-2">
                    <?php if(Auth::id() === $booking->user_id): ?>
                        <!-- Renter Actions -->
                        <?php if($booking->status === 'pending' || $booking->status === 'confirmed'): ?>
                            <form action="<?php echo e(route('booking.update-status', $booking->id)); ?>" method="POST" class="inline-block">
                                <?php echo csrf_field(); ?>
                                <input type="hidden" name="status" value="cancelled">
                                <button type="button" class="inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700 active:bg-red-700 focus:outline-none focus:border-red-700 focus:ring ring-red-300 disabled:opacity-25 transition ease-in-out duration-150" onclick="showCancellationModal()">
                                    Cancel Booking
                                </button>
                            </form>
                        <?php endif; ?>
                    <?php elseif(Auth::id() === $booking->vehicle->user_id): ?>
                        <!-- Owner Actions -->
                        <?php if($booking->status === 'pending'): ?>
                            <form action="<?php echo e(route('booking.update-status', $booking->id)); ?>" method="POST" class="inline-block">
                                <?php echo csrf_field(); ?>
                                <input type="hidden" name="status" value="confirmed">
                                <button type="submit" class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 active:bg-green-700 focus:outline-none focus:border-green-700 focus:ring ring-green-300 disabled:opacity-25 transition ease-in-out duration-150">
                                    Confirm Booking
                                </button>
                            </form>
                            <form action="<?php echo e(route('booking.update-status', $booking->id)); ?>" method="POST" class="inline-block">
                                <?php echo csrf_field(); ?>
                                <input type="hidden" name="status" value="rejected">
                                <button type="button" class="inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700 active:bg-red-700 focus:outline-none focus:border-red-700 focus:ring ring-red-300 disabled:opacity-25 transition ease-in-out duration-150" onclick="showRejectionModal()">
                                    Reject Booking
                                </button>
                            </form>
                        <?php elseif($booking->status === 'confirmed'): ?>
                            <form action="<?php echo e(route('booking.update-status', $booking->id)); ?>" method="POST" class="inline-block">
                                <?php echo csrf_field(); ?>
                                <input type="hidden" name="status" value="in_progress">
                                <button type="submit" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 active:bg-blue-700 focus:outline-none focus:border-blue-700 focus:ring ring-blue-300 disabled:opacity-25 transition ease-in-out duration-150">
                                    Mark as In Progress
                                </button>
                            </form>
                        <?php elseif($booking->status === 'in_progress'): ?>
                            <form action="<?php echo e(route('booking.update-status', $booking->id)); ?>" method="POST" class="inline-block">
                                <?php echo csrf_field(); ?>
                                <input type="hidden" name="status" value="completed">
                                <button type="submit" class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 active:bg-green-700 focus:outline-none focus:border-green-700 focus:ring ring-green-300 disabled:opacity-25 transition ease-in-out duration-150">
                                    Mark as Completed
                                </button>
                            </form>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Cancellation Modal -->
    <div id="cancellationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full" style="z-index: 100;">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3 text-center">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Cancel Booking</h3>
                <div class="mt-2 px-7 py-3">
                    <p class="text-sm text-gray-500">
                        Please provide a reason for cancelling this booking.
                    </p>
                    <form id="cancellationForm" action="<?php echo e(route('booking.update-status', $booking->id)); ?>" method="POST" class="mt-4">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="status" value="cancelled">
                        <textarea name="notes" rows="4" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 mt-1 block w-full sm:text-sm border border-gray-300 rounded-md" placeholder="Cancellation reason" required></textarea>
                        <div class="items-center px-4 py-3">
                            <button type="submit" class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500">
                                Cancel Booking
                            </button>
                        </div>
                    </form>
                    <button id="cancelCancellation" class="mt-3 text-sm text-gray-600 hover:text-gray-800">
                        Never mind
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Rejection Modal -->
    <div id="rejectionModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full" style="z-index: 100;">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3 text-center">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Reject Booking</h3>
                <div class="mt-2 px-7 py-3">
                    <p class="text-sm text-gray-500">
                        Please provide a reason for rejecting this booking.
                    </p>
                    <form id="rejectionForm" action="<?php echo e(route('booking.update-status', $booking->id)); ?>" method="POST" class="mt-4">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="status" value="rejected">
                        <textarea name="notes" rows="4" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 mt-1 block w-full sm:text-sm border border-gray-300 rounded-md" placeholder="Rejection reason" required></textarea>
                        <div class="items-center px-4 py-3">
                            <button type="submit" class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500">
                                Reject Booking
                            </button>
                        </div>
                    </form>
                    <button id="cancelRejection" class="mt-3 text-sm text-gray-600 hover:text-gray-800">
                        Never mind
                    </button>
                </div>
            </div>
        </div>
    </div>

    <?php $__env->startPush('scripts'); ?>
    <script>
        function showCancellationModal() {
            document.getElementById('cancellationModal').classList.remove('hidden');
        }

        function showRejectionModal() {
            document.getElementById('rejectionModal').classList.remove('hidden');
        }

        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('cancelCancellation').addEventListener('click', function() {
                document.getElementById('cancellationModal').classList.add('hidden');
            });

            document.getElementById('cancelRejection').addEventListener('click', function() {
                document.getElementById('rejectionModal').classList.add('hidden');
            });
        });
    </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $attributes = $__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__attributesOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742)): ?>
<?php $component = $__componentOriginal2081d1ee3a292cbf1968b0a611ec0742; ?>
<?php unset($__componentOriginal2081d1ee3a292cbf1968b0a611ec0742); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\carbnb\resources\views\backend\bookings\show.blade.php ENDPATH**/ ?>
<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();

//        User::factory()->create([
//            'name' => 'Test User',
//            'email' => '<EMAIL>',
//        ]);

        $this->call([
            RoleSeeder::class,             // First create roles
            PermissionSeeder::class,       // Then create and assign permissions
            UserSeeder::class,             // Then create users with roles
            PagesSeeder::class,            // Seed pages
            DocumentTypeSeeder::class,     // Seed document types
            LocationSeeder::class,         // Seed countries, states, cities
            VehicleTypeSeeder::class,      // Seed vehicle types
        ]);
    }
}

<x-app-backend-layout>
    <div x-data="stateCrud()" class="p-6">
        <div class="mb-4">
            <div class="flex justify-between items-center mb-3">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4">State Management</h2>
                <button @click="openModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg shadow">Add State</button>
            </div>

            <div class="overflow-x-auto">
                <table class="w-full table-auto border-collapse bg-white shadow-md rounded-lg">
                    <thead>
                        <tr class="bg-gray-200 text-gray-600 uppercase text-sm leading-normal">
                            <th class="py-3 px-6 text-left">#</th>
                            <th class="py-3 px-6 text-left">Name</th>
                            <th class="py-3 px-6 text-left">Country</th>
                            <th class="py-3 px-6 text-left">Status</th>
                            <th class="py-3 px-6 text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <template x-for="(state, index) in states" :key="state.id">
                            <tr class="border-b border-gray-200 hover:bg-gray-100">
                                <td class="py-3 px-6 text-left" x-text="index + 1"></td>
                                <td class="py-3 px-6 text-left" x-text="state.name"></td>
                                <td class="py-3 px-6 text-left" x-text="state.country.name"></td>
                                <td class="py-3 px-6 text-left">

                                    <span x-text="state.status == 1 ? 'Active' : 'Inactive'"></span>
                                </td>
                                <td class="py-3 px-6 text-center">
                                    <button @click="openModal(state)" class="bg-yellow-500 hover:bg-yellow-600 text-white px-3 py-1 rounded-lg shadow">Edit</button>
                                    <button @click="deleteState(state.id)" class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded-lg shadow ml-2">Delete</button>
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Modal -->
        <div x-show="showModal" class="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50" style="z-index: 100">
            <div class="bg-white p-6 rounded-lg shadow-lg w-96">
                <h3 class="text-lg font-semibold text-gray-700 mb-4" x-text="form.id ? 'Edit State' : 'Add State'"></h3>
                <input type="text" placeholder="Name" x-model="form.name" class="border p-2 w-full mb-2 rounded-lg shadow-sm">
                <select x-model="form.country_id" class="border p-2 w-full mb-2 rounded-lg shadow-sm">
                    <option value="">Select Country</option>
                    <template x-for="country in countries" :key="country.id">
                        <option :value="country.id" x-text="country.name"></option>
                    </template>
                </select>
                <select x-model="form.status" class="border p-2 w-full mb-2 rounded-lg shadow-sm">
                    <option value="">Select Status</option>
                    <option value="1">Active</option>
                    <option value="0">Inactive</option>
                </select>

                <div class="flex justify-end space-x-2">
                    <button @click="showModal = false" class="bg-gray-400 hover:bg-gray-500 text-white px-4 py-2 rounded-lg shadow">Cancel</button>
                    <button @click="saveState()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg shadow">
                        <span x-text="form.id ? 'Update' : 'Save'"></span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function stateCrud() {
            return {
                states: @json($states),
                countries: @json($countries),
                showModal: false,
                form: { id: null, name: '', country_id: '', status: '' },

                openModal(state = null) {
                    this.form = state ? { ...state } : { id: null, name: '', country_id: '', status: '' };
                    this.showModal = true;
                },

                saveState() {
                    let url = this.form.id ? `/states/${this.form.id}` : '/states';
                    let method = this.form.id ? 'PUT' : 'POST';

                    fetch(url, {
                        method: method,
                        headers: { 'Content-Type': 'application/json', 'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content },
                        body: JSON.stringify(this.form),
                    })
                    .then(res => res.json())
                    .then(data => {
                        // SweetAlert success message
                        Swal.fire('Success!', data.message, 'success');
                        if (!this.form.id) {
                            this.states.push(data.state);
                        } else {
                            let index = this.states.findIndex(s => s.id === this.form.id);
                            this.states[index] = data.state;
                        }
                        this.showModal = false;
                    });
                },

                deleteState(id) {
                    Swal.fire({
                        title: 'Are you sure?',
                        text: "You won't be able to revert this!",
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#d33',
                        cancelButtonColor: '#3085d6',
                        confirmButtonText: 'Yes, delete it!'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            fetch(`/states/${id}`, {
                                method: 'DELETE',
                                headers: {'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content}
                            })
                            .then(() => {
                                // SweetAlert success message
                                Swal.fire('Deleted!', 'State has been deleted.', 'success');
                                this.states = this.states.filter(s => s.id !== id);
                            });
                        }
                    });
                }
            };
        }
    </script>
</x-app-backend-layout>

<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Exception;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Lara<PERSON>\Socialite\Facades\Socialite;

class SocialiteController extends Controller
{
    /**
     * Redirect the user to the Google authentication page.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function redirectToGoogle()
    {
        return Socialite::driver('google')->redirect();
    }

    /**
     * Obtain the user information from Google.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function handleGoogleCallback()
    {
        try {
            $googleUser = Socialite::driver('google')->user();
            
            // Check if user already exists
            $user = User::where('email', $googleUser->getEmail())->first();
            
            if (!$user) {
                // Split the name into first and last name
                $nameParts = explode(' ', $googleUser->getName());
                $firstName = $nameParts[0];
                $lastName = count($nameParts) > 1 ? end($nameParts) : '';
                
                // Create a new user
                $user = User::create([
                    'first_name' => $firstName,
                    'last_name' => $lastName,
                    'email' => $googleUser->getEmail(),
                    'password' => Hash::make(Str::random(16)), // Random secure password
                    'email_verified_at' => now(), // Google accounts are already verified
                ]);
                
                // Generate a referral code for the new user
                $user->generateReferralCode();
            }
            
            // Login the user
            Auth::login($user);
            
            // Regenerate session
            session()->regenerate();
            
            return redirect()->intended(route('dashboard', absolute: false));
            
        } catch (Exception $e) {
            return redirect()->route('login')
                ->with('error', 'Google login failed. Please try again or use email login.');
        }
    }
}

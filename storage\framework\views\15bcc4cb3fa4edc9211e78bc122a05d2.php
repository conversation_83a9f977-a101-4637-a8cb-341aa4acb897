<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['name', 'label', 'required' => false]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['name', 'label', 'required' => false]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div x-data="simpleUploadEnhanced('<?php echo e($name); ?>')">
    <label for="<?php echo e($name); ?>_input" class="block text-sm font-medium text-gray-700 mb-1"><?php echo e($label); ?></label>
    
    <div class="flex items-center space-x-2">
        <input
            type="file"
            id="<?php echo e($name); ?>_input"
            x-ref="fileInput"
            class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
            <?php echo e($required ? 'required' : ''); ?>

            @change="handleFileUpload"
        >
        
        <div x-show="isUploading" class="text-sm text-blue-600">
            <i class="fas fa-spinner fa-spin"></i> Uploading...
        </div>
    </div>
    
    <!-- Preview of selected file -->
    <div class="mt-2" x-show="fileName">
        <div class="flex items-center space-x-2">
            <div class="text-sm text-gray-600">
                <i class="fas fa-check-circle text-green-500 mr-1"></i>
                <span x-text="fileName"></span>
            </div>
            <button type="button" @click="clearFile" class="text-red-500 hover:text-red-700">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>
    
    <!-- Hidden input to store file ID -->
    <input type="hidden" :name="name" x-model="fileId">
</div>

<script>
    function simpleUploadEnhanced(name) {
        return {
            name: name,
            fileId: '',
            fileName: '',
            isUploading: false,
            
            handleFileUpload() {
                const file = this.$refs.fileInput.files[0];
                if (!file) return;
                
                this.isUploading = true;
                
                // Create FormData
                const formData = new FormData();
                formData.append('files[]', file);
                
                // Add CSRF token
                const csrfToken = document.querySelector('meta[name="csrf-token"]');
                if (csrfToken) {
                    formData.append('_token', csrfToken.getAttribute('content'));
                }
                
                // Upload file
                fetch('/admin/file-manager', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': csrfToken ? csrfToken.getAttribute('content') : ''
                    }
                })
                .then(response => response.json())
                .then(data => {
                    this.isUploading = false;
                    
                    if (data.success && data.files && data.files.length > 0) {
                        // Set file ID and name
                        this.fileId = data.files[0].id;
                        this.fileName = data.files[0].title || data.files[0].filename;
                        
                        // Dispatch event to update form data
                        this.dispatchUpdateEvent();
                        
                        console.log(`File uploaded successfully: ${this.fileName} (ID: ${this.fileId})`);
                    } else {
                        console.error('Upload failed:', data.message || 'Unknown error');
                        alert('Upload failed: ' + (data.message || 'Unknown error'));
                    }
                })
                .catch(error => {
                    this.isUploading = false;
                    console.error('Error uploading file:', error);
                    alert('Error uploading file: ' + error.message);
                });
            },
            
            clearFile() {
                this.fileId = '';
                this.fileName = '';
                this.$refs.fileInput.value = '';
                
                // Dispatch event to update form data
                this.dispatchUpdateEvent();
            },
            
            dispatchUpdateEvent() {
                // Extract document type ID from name if it's a document
                let documentTypeId = null;
                const matches = this.name.match(/documents\[(\d+)\]/);
                if (matches && matches[1]) {
                    documentTypeId = matches[1];
                }
                
                // Create custom event
                const event = new CustomEvent('file-ids-updated', {
                    detail: {
                        name: this.name,
                        value: this.fileId,
                        documentTypeId: documentTypeId
                    }
                });
                
                // Dispatch event
                window.dispatchEvent(event);
                
                console.log('Dispatched file-ids-updated event:', {
                    name: this.name,
                    value: this.fileId,
                    documentTypeId: documentTypeId
                });
            }
        };
    }
</script>
<?php /**PATH C:\laragon\www\carbnb\resources\views\components\simple-upload-enhanced.blade.php ENDPATH**/ ?>
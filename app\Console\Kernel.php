<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // Generate sitemap weekly
        $schedule->command('sitemap:generate')->weekly();
        
        // Run database backups daily
        $schedule->command('backup:run')->daily()->at('01:00');
        
        // Clean old backups weekly
        $schedule->command('backup:clean')->weekly()->at('02:00');
        
        // Prune old telescope entries daily
        $schedule->command('telescope:prune --hours=48')->daily();
        
        // Send reminder emails for upcoming bookings
        $schedule->command('bookings:remind')->dailyAt('09:00');
        
        // Update currency exchange rates daily
        $schedule->command('currency:update')->daily()->at('03:00');
        
        // Clean expired sessions weekly
        $schedule->command('session:gc')->weekly();
        
        // Optimize database weekly
        $schedule->command('db:optimize')->weekly()->at('03:30');
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}

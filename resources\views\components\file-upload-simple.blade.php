@props([
    'name' => 'file', 
    'fieldName' => null,
    'label' => 'Upload File',
    'multiple' => false,
    'accept' => '*/*',
    'required' => false,
    'fileIds' => '',
    'primaryFileId' => '',
])

<div>
    <!-- Label -->
    <label for="{{ $name }}" class="block text-sm font-medium text-gray-700 mb-1">{{ $label }}</label>

    <!-- Hidden inputs to store file IDs -->
    <input type="hidden" name="{{ $fieldName ? $fieldName : $name }}" value="{{ $fileIds }}">
    @if($multiple)
        <input type="hidden" name="{{ $fieldName ? $fieldName . '_primary' : $name . '_primary' }}" value="{{ $primaryFileId }}">
    @endif

    <!-- File input -->
    <div class="flex flex-wrap gap-2 mb-2">
        <div class="flex-grow">
            <input
                type="file"
                id="{{ $name }}"
                name="{{ $fieldName ? $fieldName . '_file' : $name . '_file' }}"
                class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                {{ $multiple ? 'multiple' : '' }}
                accept="{{ $accept }}"
                {{ $required ? 'required' : '' }}
            >
        </div>
    </div>
</div>

<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\BookingItem;
use App\Models\BookingStatus;
use App\Models\Vehicle;
use App\Models\Driver;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class BookingController extends Controller
{
    /**
     * Display a listing of the bookings for the authenticated user.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $bookings = Booking::where('user_id', Auth::id())
            ->with(['vehicle', 'vehicle.primaryImage', 'vehicle.user', 'payments'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('backend.bookings.index', compact('bookings'));
    }

    /**
     * Display a listing of the bookings for the authenticated user's vehicles.
     *
     * @return \Illuminate\View\View
     */
    public function ownerBookings()
    {
        // Get all vehicles owned by the user
        $vehicleIds = Vehicle::where('user_id', Auth::id())->pluck('id');

        // Get all bookings for those vehicles
        $bookings = Booking::whereIn('vehicle_id', $vehicleIds)
            ->with(['user', 'vehicle', 'vehicle.primaryImage', 'payments'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('backend.bookings.owner-index', compact('bookings'));
    }

    /**
     * Show the form for creating a new booking.
     *
     * @param  int  $vehicleId
     * @param  Request  $request
     * @return \Illuminate\View\View
     */
    public function create($vehicleId, Request $request)
    {
        $vehicle = Vehicle::with(['user', 'vehicleType', 'primaryImage'])
            ->where('status', 'active')
            ->findOrFail($vehicleId);

        // Validate dates
        $validator = Validator::make($request->all(), [
            'pickup_date' => 'required|date|after_or_equal:today',
            'return_date' => 'required|date|after_or_equal:pickup_date',
        ]);

        if ($validator->fails()) {
            return redirect()->route('vehicle.details', $vehicleId)
                ->withErrors($validator)
                ->withInput();
        }

        $pickupDate = Carbon::parse($request->pickup_date)->startOfDay();
        $returnDate = Carbon::parse($request->return_date)->startOfDay();
        // Ensure dates are in the correct order
        if ($pickupDate->gt($returnDate)) {
            $temp = $pickupDate;
            $pickupDate = $returnDate;
            $returnDate = $temp;
        }
        $days = $returnDate->diffInDays($pickupDate) + 1; // Including the start day

        // Ensure we have at least 1 day
        $days = max(1, $days);

        // Calculate pricing
        $vehiclePrice = $vehicle->daily_rate * $days;

        // Apply discounts if applicable
        if ($days >= 30 && $vehicle->monthly_discount > 0) {
            $discountAmount = $vehiclePrice * ($vehicle->monthly_discount / 100);
            // Ensure discount doesn't exceed the vehicle price
            $discountAmount = min($discountAmount, $vehiclePrice * 0.9); // Max 90% discount
            $vehiclePrice -= $discountAmount;
        } elseif ($days >= 7 && $vehicle->weekly_discount > 0) {
            $discountAmount = $vehiclePrice * ($vehicle->weekly_discount / 100);
            // Ensure discount doesn't exceed the vehicle price
            $discountAmount = min($discountAmount, $vehiclePrice * 0.9); // Max 90% discount
            $vehiclePrice -= $discountAmount;
        } else {
            $discountAmount = 0;
        }

        // Ensure vehicle price is never negative
        $vehiclePrice = max(0, $vehiclePrice);

        // Calculate tax (10%)
        $taxRate = 0.10;
        $taxAmount = $vehiclePrice * $taxRate;
        $totalAmount = $vehiclePrice + $taxAmount;

        // Get available drivers if the vehicle can be rented with a driver
        $drivers = collect([]);
        if ($vehicle->with_driver) {
            // Get all active drivers
            $allDrivers = Driver::where('status', 'active')->with('user')->get();

            // Filter drivers based on availability for the requested date range
            $drivers = $allDrivers->filter(function($driver) use ($pickupDate, $returnDate) {
                return $driver->isAvailableForDateRange($pickupDate, $returnDate);
            });

            // Load driver availability for the date range to calculate pricing
            foreach ($drivers as $driver) {
                $driver->dateRangeAvailability = $driver->availability()
                    ->whereBetween('date', [$pickupDate->format('Y-m-d'), $returnDate->format('Y-m-d')])
                    ->get()
                    ->keyBy('date');
            }
        }

        return view('front.bookings.create', compact(
            'vehicle',
            'pickupDate',
            'returnDate',
            'days',
            'vehiclePrice',
            'discountAmount',
            'taxAmount',
            'totalAmount',
            'drivers'
        ));
    }

    /**
     * Show the booking summary before proceeding to payment.
     *
     * @param  Request  $request
     * @return \Illuminate\View\View|\Illuminate\Http\RedirectResponse
     */
    public function summary(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'vehicle_id' => 'required|exists:vehicles,id',
            'pickup_date' => 'required|date|after_or_equal:today',
            'return_date' => 'required|date|after_or_equal:pickup_date',
            'pickup_location' => 'required|string|max:255',
            'return_location' => 'required|string|max:255',
            'driver_id' => 'nullable|exists:drivers,id',
            'special_requests' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $vehicle = Vehicle::with(['user', 'vehicleType', 'primaryImage'])
            ->findOrFail($request->vehicle_id);

        $pickupDate = Carbon::parse($request->pickup_date)->startOfDay();
        $returnDate = Carbon::parse($request->return_date)->startOfDay();
        // Ensure dates are in the correct order
        if ($pickupDate->gt($returnDate)) {
            $temp = $pickupDate;
            $pickupDate = $returnDate;
            $returnDate = $temp;
        }
        $days = $returnDate->diffInDays($pickupDate) + 1; // Including the start day

        // Ensure we have at least 1 day
        $days = max(1, $days);

        // Calculate vehicle price
        $vehiclePrice = $vehicle->daily_rate * $days;

        // Apply discounts if applicable
        if ($days >= 30 && $vehicle->monthly_discount > 0) {
            $discountAmount = $vehiclePrice * ($vehicle->monthly_discount / 100);
            // Ensure discount doesn't exceed the vehicle price
            $discountAmount = min($discountAmount, $vehiclePrice * 0.9); // Max 90% discount
            $vehiclePrice -= $discountAmount;
        } elseif ($days >= 7 && $vehicle->weekly_discount > 0) {
            $discountAmount = $vehiclePrice * ($vehicle->weekly_discount / 100);
            // Ensure discount doesn't exceed the vehicle price
            $discountAmount = min($discountAmount, $vehiclePrice * 0.9); // Max 90% discount
            $vehiclePrice -= $discountAmount;
        } else {
            $discountAmount = 0;
        }

        // Ensure vehicle price is never negative
        $vehiclePrice = max(0, $vehiclePrice);

        // Calculate driver price if a driver is selected
        $driver = null;
        $driverPrice = 0;
        if ($request->driver_id) {
            $driver = Driver::with('user')->findOrFail($request->driver_id);
            $driverPrice = $driver->daily_rate * $days;
        }

        // Calculate security deposit
        $securityDeposit = $vehicle->security_deposit;

        // Calculate subtotal
        $subtotal = $vehiclePrice + $driverPrice;

        // Calculate tax (10%)
        $taxRate = 0.10;
        $taxAmount = $subtotal * $taxRate;

        // Calculate total
        $totalAmount = $subtotal + $taxAmount;

        // Store booking data in session
        $bookingData = [
            'vehicle_id' => $vehicle->id,
            'driver_id' => $request->driver_id,
            'pickup_date' => $pickupDate->format('Y-m-d H:i:s'),
            'return_date' => $returnDate->format('Y-m-d H:i:s'),
            'pickup_location' => $request->pickup_location,
            'return_location' => $request->return_location,
            'vehicle_price' => $vehiclePrice,
            'driver_price' => $driverPrice,
            'discount_amount' => $discountAmount,
            'subtotal' => $subtotal,
            'tax_amount' => $taxAmount,
            'total_amount' => $totalAmount,
            'security_deposit' => $securityDeposit,
            'special_requests' => $request->special_requests,
        ];

        session(['booking_data' => $bookingData]);

        return view('front.bookings.summary', compact(
            'vehicle',
            'driver',
            'pickupDate',
            'returnDate',
            'days',
            'vehiclePrice',
            'driverPrice',
            'discountAmount',
            'subtotal',
            'taxAmount',
            'totalAmount',
            'securityDeposit',
            'bookingData'
        ));
    }

    /**
     * Store a newly created booking in the database.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store()
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', 'You must be logged in to make a booking.');
        }

        // Get booking data from session
        $bookingData = session('booking_data');
        if (!$bookingData) {
            return redirect()->route('home')->with('error', 'Booking data not found.');
        }

        // Start database transaction
        DB::beginTransaction();

        try {
            // Create the booking
            $booking = Booking::create([
                'booking_number' => Booking::generateBookingNumber(),
                'user_id' => Auth::id(),
                'vehicle_id' => $bookingData['vehicle_id'],
                'driver_id' => $bookingData['driver_id'],
                'start_date' => $bookingData['pickup_date'],
                'end_date' => $bookingData['return_date'],
                'pickup_location' => $bookingData['pickup_location'],
                'return_location' => $bookingData['return_location'],
                'vehicle_price' => $bookingData['vehicle_price'],
                'driver_price' => $bookingData['driver_price'],
                'discount_amount' => $bookingData['discount_amount'],
                'subtotal' => $bookingData['subtotal'],
                'tax_amount' => $bookingData['tax_amount'],
                'total_amount' => $bookingData['total_amount'],
                'security_deposit' => $bookingData['security_deposit'],
                'status' => 'pending',
                'special_requests' => $bookingData['special_requests'],
            ]);

            // Create booking items
            // Vehicle item
            BookingItem::create([
                'booking_id' => $booking->id,
                'itemable_id' => $bookingData['vehicle_id'],
                'itemable_type' => Vehicle::class,
                'item_type' => 'vehicle',
                'item_name' => Vehicle::find($bookingData['vehicle_id'])->full_name,
                'unit_price' => $bookingData['vehicle_price'] / $booking->duration_in_days,
                'quantity' => $booking->duration_in_days,
                'total_price' => $bookingData['vehicle_price'],
            ]);

            // Driver item (if applicable)
            if ($bookingData['driver_id']) {
                $driver = Driver::find($bookingData['driver_id']);
                BookingItem::create([
                    'booking_id' => $booking->id,
                    'itemable_id' => $bookingData['driver_id'],
                    'itemable_type' => Driver::class,
                    'item_type' => 'driver',
                    'item_name' => $driver->user->name . ' (Driver)',
                    'unit_price' => $bookingData['driver_price'] / $booking->duration_in_days,
                    'quantity' => $booking->duration_in_days,
                    'total_price' => $bookingData['driver_price'],
                ]);
            }

            // Create initial booking status
            BookingStatus::create([
                'booking_id' => $booking->id,
                'status' => 'pending',
                'updated_by' => Auth::id(),
                'notes' => 'Booking created',
            ]);

            // Commit transaction
            DB::commit();

            // Store booking ID in session for payment
            session(['booking_id' => $booking->id]);

            // Redirect to payment page
            return redirect()->route('booking.payment', $booking->id);
        } catch (\Exception $e) {
            // Rollback transaction on error
            DB::rollBack();

            return redirect()->route('home')->with('error', 'An error occurred while creating your booking: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified booking.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $booking = Booking::with([
                'user',
                'vehicle',
                'vehicle.primaryImage',
                'vehicle.user',
                'driver',
                'driver.user',
                'payments',
                'statusHistory',
                'statusHistory.updatedBy',
                'items'
            ])
            ->findOrFail($id);

        // Check if the user is authorized to view this booking
        if (Auth::id() !== $booking->user_id && Auth::id() !== $booking->vehicle->user_id && !Auth::user()->hasRole('admin')) {
            abort(403, 'You are not authorized to view this booking.');
        }

        return view('backend.bookings.show', compact('booking'));
    }

    /**
     * Update the booking status.
     *
     * @param  Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateStatus(Request $request, $id)
    {
        $booking = Booking::findOrFail($id);

        // Check if the user is authorized to update this booking
        $isOwner = Auth::id() === $booking->vehicle->user_id;
        $isRenter = Auth::id() === $booking->user_id;
        $isAdmin = Auth::user()->hasRole('admin');

        if (!$isOwner && !$isRenter && !$isAdmin) {
            abort(403, 'You are not authorized to update this booking.');
        }

        // Validate the request
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:confirmed,in_progress,completed,cancelled,rejected',
            'notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Check if the status change is allowed
        $allowedStatusChanges = [
            // Owner can confirm or reject pending bookings
            'owner' => [
                'pending' => ['confirmed', 'rejected'],
                'confirmed' => ['in_progress'],
                'in_progress' => ['completed'],
            ],
            // Renter can cancel pending or confirmed bookings
            'renter' => [
                'pending' => ['cancelled'],
                'confirmed' => ['cancelled'],
            ],
            // Admin can change to any status
            'admin' => [
                'pending' => ['confirmed', 'cancelled', 'rejected'],
                'confirmed' => ['in_progress', 'cancelled'],
                'in_progress' => ['completed', 'cancelled'],
            ],
        ];

        $userRole = $isAdmin ? 'admin' : ($isOwner ? 'owner' : 'renter');

        if (!isset($allowedStatusChanges[$userRole][$booking->status]) ||
            !in_array($request->status, $allowedStatusChanges[$userRole][$booking->status])) {
            return back()->with('error', 'You are not allowed to change the booking status from ' . $booking->status . ' to ' . $request->status);
        }

        // If cancelling or rejecting, require a reason
        if (in_array($request->status, ['cancelled', 'rejected']) && empty($request->notes)) {
            return back()->with('error', 'Please provide a reason for cancelling or rejecting the booking.');
        }

        // Update the booking status
        $booking->status = $request->status;
        if (in_array($request->status, ['cancelled', 'rejected'])) {
            $booking->cancellation_reason = $request->notes;
        }
        $booking->save();

        // Create a new booking status record
        BookingStatus::create([
            'booking_id' => $booking->id,
            'status' => $request->status,
            'updated_by' => Auth::id(),
            'notes' => $request->notes,
        ]);

        // Redirect back with success message
        $statusMessages = [
            'confirmed' => 'Booking has been confirmed.',
            'in_progress' => 'Booking has been marked as in progress.',
            'completed' => 'Booking has been marked as completed.',
            'cancelled' => 'Booking has been cancelled.',
            'rejected' => 'Booking has been rejected.',
        ];

        return redirect()->route('booking.show', $booking->id)
            ->with('success', $statusMessages[$request->status]);
    }

    /**
     * Show the payment page for a booking.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function payment($id)
    {
        $booking = Booking::with(['vehicle', 'vehicle.primaryImage', 'vehicle.user'])
            ->findOrFail($id);

        // Check if the user is authorized to pay for this booking
        if (Auth::id() !== $booking->user_id) {
            abort(403, 'You are not authorized to pay for this booking.');
        }

        // Check if the booking is already paid
        if ($booking->payments()->where('status', 'completed')->exists()) {
            return redirect()->route('booking.show', $booking->id)
                ->with('info', 'This booking has already been paid for.');
        }

        return view('backend.bookings.payment', compact('booking'));
    }
}

<?php

namespace Database\Seeders;

use App\Models\Page;
use Illuminate\Database\Seeder;

class StaticPagesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Terms of Service page
        Page::create([
            'title' => 'Terms of Service',
            'slug' => 'terms',
            'content' => '<h1 class="text-3xl font-bold mb-6">Terms of Service</h1>
                <p class="mb-4">Welcome to CARBNB. By using our services, you agree to these terms.</p>
                
                <h2 class="text-2xl font-bold mt-8 mb-4">1. Acceptance of Terms</h2>
                <p class="mb-4">By accessing or using the CARBNB platform, you agree to be bound by these Terms of Service.</p>
                
                <h2 class="text-2xl font-bold mt-8 mb-4">2. User Accounts</h2>
                <p class="mb-4">You must create an account to use certain features of our platform. You are responsible for maintaining the confidentiality of your account information.</p>
                
                <h2 class="text-2xl font-bold mt-8 mb-4">3. Vehicle Listings</h2>
                <p class="mb-4">Vehicle owners are responsible for providing accurate information about their vehicles. CARBNB reserves the right to remove any listing that violates our policies.</p>
                
                <h2 class="text-2xl font-bold mt-8 mb-4">4. Booking and Payments</h2>
                <p class="mb-4">All bookings are subject to availability and confirmation. Payments are processed securely through our platform.</p>
                
                <h2 class="text-2xl font-bold mt-8 mb-4">5. Cancellations and Refunds</h2>
                <p class="mb-4">Cancellation policies vary depending on the vehicle and booking terms. Please review the specific cancellation policy before making a booking.</p>
                
                <h2 class="text-2xl font-bold mt-8 mb-4">6. Insurance and Liability</h2>
                <p class="mb-4">CARBNB provides insurance coverage for eligible bookings. However, users are responsible for understanding the coverage limits and exclusions.</p>
                
                <h2 class="text-2xl font-bold mt-8 mb-4">7. Dispute Resolution</h2>
                <p class="mb-4">In the event of a dispute, users agree to first attempt to resolve the issue through our platform\'s dispute resolution process.</p>
                
                <h2 class="text-2xl font-bold mt-8 mb-4">8. Modifications to Terms</h2>
                <p class="mb-4">CARBNB reserves the right to modify these terms at any time. Users will be notified of significant changes.</p>
                
                <h2 class="text-2xl font-bold mt-8 mb-4">9. Termination</h2>
                <p class="mb-4">CARBNB reserves the right to terminate or suspend accounts that violate our terms or policies.</p>
                
                <h2 class="text-2xl font-bold mt-8 mb-4">10. Governing Law</h2>
                <p class="mb-4">These terms are governed by the laws of the jurisdiction in which CARBNB operates.</p>',
            'status' => 1, // Published
        ]);

        // Create Privacy Policy page
        Page::create([
            'title' => 'Privacy Policy',
            'slug' => 'privacy',
            'content' => '<h1 class="text-3xl font-bold mb-6">Privacy Policy</h1>
                <p class="mb-4">At CARBNB, we take your privacy seriously. This policy explains how we collect, use, and protect your personal information.</p>
                
                <h2 class="text-2xl font-bold mt-8 mb-4">1. Information We Collect</h2>
                <p class="mb-4">We collect personal information such as your name, email address, phone number, and payment information when you create an account or make a booking.</p>
                
                <h2 class="text-2xl font-bold mt-8 mb-4">2. How We Use Your Information</h2>
                <p class="mb-4">We use your information to provide our services, process payments, communicate with you, and improve our platform.</p>
                
                <h2 class="text-2xl font-bold mt-8 mb-4">3. Information Sharing</h2>
                <p class="mb-4">We share your information with vehicle owners and renters as necessary to facilitate bookings. We may also share information with service providers who help us operate our platform.</p>
                
                <h2 class="text-2xl font-bold mt-8 mb-4">4. Data Security</h2>
                <p class="mb-4">We implement appropriate security measures to protect your personal information from unauthorized access, alteration, or disclosure.</p>
                
                <h2 class="text-2xl font-bold mt-8 mb-4">5. Your Rights</h2>
                <p class="mb-4">You have the right to access, correct, or delete your personal information. You may also have the right to object to or restrict certain processing of your information.</p>
                
                <h2 class="text-2xl font-bold mt-8 mb-4">6. Cookies and Tracking</h2>
                <p class="mb-4">We use cookies and similar technologies to enhance your experience on our platform. You can manage your cookie preferences through your browser settings.</p>
                
                <h2 class="text-2xl font-bold mt-8 mb-4">7. Third-Party Links</h2>
                <p class="mb-4">Our platform may contain links to third-party websites. We are not responsible for the privacy practices of these websites.</p>
                
                <h2 class="text-2xl font-bold mt-8 mb-4">8. Children\'s Privacy</h2>
                <p class="mb-4">Our services are not intended for individuals under the age of 18. We do not knowingly collect personal information from children.</p>
                
                <h2 class="text-2xl font-bold mt-8 mb-4">9. Changes to This Policy</h2>
                <p class="mb-4">We may update this privacy policy from time to time. We will notify you of significant changes by posting the new policy on our platform.</p>
                
                <h2 class="text-2xl font-bold mt-8 mb-4">10. Contact Us</h2>
                <p class="mb-4">If you have any questions or concerns about our privacy policy, please contact us through our platform.</p>',
            'status' => 1, // Published
        ]);

        // Create Cookie Policy page
        Page::create([
            'title' => 'Cookie Policy',
            'slug' => 'cookies',
            'content' => '<h1 class="text-3xl font-bold mb-6">Cookie Policy</h1>
                <p class="mb-4">This Cookie Policy explains how CARBNB uses cookies and similar technologies to recognize you when you visit our website or use our services.</p>
                
                <h2 class="text-2xl font-bold mt-8 mb-4">1. What Are Cookies</h2>
                <p class="mb-4">Cookies are small data files that are placed on your computer or mobile device when you visit a website. Cookies are widely used by website owners to make their websites work efficiently and provide analytical information.</p>
                
                <h2 class="text-2xl font-bold mt-8 mb-4">2. How We Use Cookies</h2>
                <p class="mb-4">We use cookies for the following purposes:</p>
                <ul class="list-disc ml-8 mb-4">
                    <li class="mb-2">To enable certain functions of the Service</li>
                    <li class="mb-2">To provide analytics</li>
                    <li class="mb-2">To store your preferences</li>
                    <li class="mb-2">To enable advertisements delivery, including behavioral advertising</li>
                </ul>
                
                <h2 class="text-2xl font-bold mt-8 mb-4">3. Types of Cookies We Use</h2>
                <p class="mb-4">We use both session cookies and persistent cookies on our Service:</p>
                <ul class="list-disc ml-8 mb-4">
                    <li class="mb-2"><strong>Session Cookies:</strong> These cookies are temporary and are erased when you close your browser.</li>
                    <li class="mb-2"><strong>Persistent Cookies:</strong> These cookies remain on your device until they expire or you delete them.</li>
                    <li class="mb-2"><strong>Essential Cookies:</strong> These cookies are necessary for the Service to function properly.</li>
                    <li class="mb-2"><strong>Functionality Cookies:</strong> These cookies allow us to remember choices you make and provide enhanced features.</li>
                    <li class="mb-2"><strong>Analytics Cookies:</strong> These cookies help us understand how visitors interact with our Service.</li>
                    <li class="mb-2"><strong>Advertising Cookies:</strong> These cookies are used to deliver advertisements relevant to you.</li>
                </ul>
                
                <h2 class="text-2xl font-bold mt-8 mb-4">4. Your Choices Regarding Cookies</h2>
                <p class="mb-4">If you prefer to avoid the use of cookies on our website, you can first set your browser to refuse cookies. However, please note that if you do this, you may not be able to use the full functionality of our website.</p>
                
                <h2 class="text-2xl font-bold mt-8 mb-4">5. Third-Party Cookies</h2>
                <p class="mb-4">In addition to our own cookies, we may also use various third-party cookies to report usage statistics of the Service and deliver advertisements on and through the Service.</p>
                
                <h2 class="text-2xl font-bold mt-8 mb-4">6. Changes to This Cookie Policy</h2>
                <p class="mb-4">We may update our Cookie Policy from time to time. We will notify you of any changes by posting the new Cookie Policy on this page.</p>
                
                <h2 class="text-2xl font-bold mt-8 mb-4">7. Contact Us</h2>
                <p class="mb-4">If you have any questions about our Cookie Policy, please contact us through our platform.</p>',
            'status' => 1, // Published
        ]);
    }
}

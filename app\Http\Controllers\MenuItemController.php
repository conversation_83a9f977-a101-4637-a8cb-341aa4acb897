<?php

namespace App\Http\Controllers;

use App\Models\MenuItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

class MenuItemController extends Controller
{
    public function index()
    {
        $menuItems = MenuItem::with('children')->whereNull('parent_id')->orderBy('order')->get();
        return view('backend.menu-items.index', compact('menuItems'));
    }

    public function create()
    {
        $menuItems = MenuItem::all();
        $routeNames = $this->getRouteNames();
        return view('backend.menu-items.create', compact('menuItems', 'routeNames'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'url' => 'nullable|string|max:255',
            'route_name' => 'nullable|string|max:255',
            'parent_id' => 'nullable|exists:menu_items,id',
            'is_active' => 'boolean',
        ]);

        $validated['order'] = MenuItem::max('order') + 1;
        $validated['is_active'] = $request->has('is_active');

        MenuItem::create($validated);

        return redirect()->route('menu-items.index')->with('success', 'Menu item created successfully.');
    }

    public function edit(MenuItem $menuItem)
    {
        $menuItems = MenuItem::where('id', '!=', $menuItem->id)->get();
        $routeNames = $this->getRouteNames();
        return view('backend.menu-items.edit', compact('menuItem', 'menuItems', 'routeNames'));
    }

    public function update(Request $request, MenuItem $menuItem)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'url' => 'nullable|string|max:255',
            'route_name' => 'nullable|string|max:255',
            'parent_id' => 'nullable|exists:menu_items,id',
            'is_active' => 'boolean',
        ]);

        $validated['is_active'] = $request->has('is_active');

        $menuItem->update($validated);

        return redirect()->route('menu-items.index')->with('success', 'Menu item updated successfully.');
    }

    public function destroy(MenuItem $menuItem)
    {
        $menuItem->delete();
        return redirect()->route('menu-items.index')->with('success', 'Menu item deleted successfully.');
    }

    public function updateOrder(Request $request)
    {
        $items = $request->input('items', []);
        
        foreach ($items as $index => $item) {
            MenuItem::where('id', $item['id'])->update([
                'order' => $index + 1,
                'parent_id' => $item['parent_id'] ?? null,
            ]);
        }

        return response()->json(['success' => true, 'message' => 'Menu order updated successfully.']);
    }

    private function getRouteNames()
    {
        $routes = Route::getRoutes();
        $routeNames = [];

        foreach ($routes as $route) {
            if ($route->getName()) {
                $routeNames[] = $route->getName();
            }
        }

        return collect($routeNames)->sort()->values()->all();
    }
}

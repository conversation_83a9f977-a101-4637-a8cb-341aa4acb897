<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class OptimizeDatabase extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:optimize';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Optimize database tables';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting database optimization...');

        // Get all tables
        $tables = $this->getTables();
        
        $bar = $this->output->createProgressBar(count($tables));
        $bar->start();
        
        foreach ($tables as $table) {
            $this->optimizeTable($table);
            $bar->advance();
        }
        
        $bar->finish();
        $this->newLine();
        $this->info('Database optimization completed successfully!');
    }
    
    /**
     * Get all tables in the database
     *
     * @return array
     */
    private function getTables()
    {
        $tables = [];
        
        // Get the database connection name
        $connection = config('database.default');
        
        // Get the database name
        $database = config("database.connections.{$connection}.database");
        
        // Get all tables
        $results = DB::select("SHOW TABLES FROM `{$database}`");
        
        // Extract table names
        foreach ($results as $result) {
            $property = "Tables_in_{$database}";
            $tables[] = $result->$property;
        }
        
        return $tables;
    }
    
    /**
     * Optimize a table
     *
     * @param string $table
     * @return void
     */
    private function optimizeTable($table)
    {
        try {
            // Check if the table exists
            if (!Schema::hasTable($table)) {
                $this->warn("Table {$table} does not exist. Skipping...");
                return;
            }
            
            // Optimize the table
            DB::statement("OPTIMIZE TABLE `{$table}`");
            
            // Analyze the table
            DB::statement("ANALYZE TABLE `{$table}`");
            
            $this->line("Optimized table: {$table}");
        } catch (\Exception $e) {
            $this->error("Failed to optimize table {$table}: " . $e->getMessage());
        }
    }
}

<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;

class RolesController extends Controller
{
    // Display all roles
    public function index()
    {
        $roles = Role::all();
        return view('backend.access-control.roles', compact('roles'));
    }

    // Show create form
    public function create()
    {
        return view('backend.roles.create');
    }

    // Store new role
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|unique:roles,name',
        ]);

        Role::create(['name' => $request->name]);

        return redirect()->route('backend.roles.index')->with('success', 'Role created successfully.');
    }

    // Show edit form
    public function edit(Role $role)
    {
        return view('backend.roles.edit', compact('role'));
    }

    // Update existing role
    public function update(Request $request, Role $role)
    {
        $request->validate([
            'name' => 'required|unique:roles,name,' . $role->id,
        ]);

        $role->update(['name' => $request->name]);

        return redirect()->route('backend.roles.index')->with('success', 'Role updated successfully.');
    }

    // Delete role
    public function destroy(Role $role)
    {
        $role->delete();

        return redirect()->route('backend.roles.index')->with('success', 'Role deleted successfully.');
    }
}
